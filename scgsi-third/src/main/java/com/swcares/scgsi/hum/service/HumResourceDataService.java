package com.swcares.scgsi.hum.service;

/**
 * ClassName：com.swcares.scgsi.hum.service.HumResourceDataService <br>
 * Description：人力资源接口 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月12日 下午4:51:05 <br>
 * @version v1.0 <br>
 */
public interface HumResourceDataService {

    /**
     * Title：initBuilderDepartmentData <br>
     * Description：初始化部门数据，用于系统初始化全量同步<br>
     * author：夏阳 <br>
     * date：2020年3月12日 下午1:37:09 <br> <br>
     */
    void initBuilderDepartmentData();

    /**
     * Title：departmentIncrementalData <br>
     * Description：部门增量数据更新<br>
     * author：夏阳 <br>
     * date：2020年3月12日 下午1:36:50 <br> <br>
     */
    void departmentIncrementalData();

    /**
     * Title：initBuilderEmployeeData <br>
     * Description：初始化员工数据，用于系统初始化全量同步<br>
     * author：夏阳 <br>
     * date：2020年3月12日 下午4:54:51 <br> <br>
     */
    void initBuilderEmployeeData();

    /**
     * Title：employeeIncrementalData <br>
     * Description：员工增量数据更新<br>
     * author：夏阳 <br>
     * date：2020年3月12日 下午1:36:50 <br> <br>
     */
    void employeeIncrementalData();
}
