package com.swcares.scgsi.hum.service.impl;

import java.util.concurrent.atomic.AtomicInteger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import com.swcares.scgsi.hum.department.service.DepartmentResourceHandler;
import com.swcares.scgsi.hum.employee.service.EmployeeResourceHandler;
import com.swcares.scgsi.hum.employee.vo.PageDataVO;
import com.swcares.scgsi.hum.employee.vo.RecordsVO;
import com.swcares.scgsi.hum.service.HumResourceDataService;
import org.springframework.transaction.annotation.Transactional;

/**
 * ClassName：com.swcares.scgsi.hum.service.impl.HumResourceDataServiceImpl <br>
 * Description：人力数据同步接口实现类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月12日 下午4:53:16 <br>
 * @version v1.0 <br>
 */
@Getter
@Setter
@Service
@Slf4j
@ConfigurationProperties(prefix = "hum.inits")
@EnableConfigurationProperties(HumResourceDataServiceImpl.class)
@Primary
public class HumResourceDataServiceImpl implements HumResourceDataService {
    
    /** 是否需要初始化数据 */
    private boolean inits;
    
    /** 同步数据的起始记录  */
    private int start = 0;

    /** 步长 */
    private int limit = 50;

    /** 步长 */
    private int totle = 1;

    @Autowired
    private EmployeeResourceHandler employeeResourceHandler;

    @Autowired
    private DepartmentResourceHandler departmentResourceHandler;

    @Override
    public void initBuilderDepartmentData() {
        log.info("=========>项目启动，initBuilderDepartmentData读取hum.inits的值：{}", inits);
        if(!inits) {
            return;
        }
       /* while (start < totle) {
        if(!inits) {
            return;
        }
        while (start < totle) {
            PageDataVO pageDataVO = departmentResourceHandler
                    .synchronousDepartmentInterface(start + "", limit + "");

            if (ObjectUtils.isNotEmpty(pageDataVO)
                    && ObjectUtils.isNotEmpty(pageDataVO.getTotal())) {
                totle = pageDataVO.getTotal();
                start = start + limit;
            } else {
                break;
            }
        }*/
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void departmentIncrementalData() {
        log.info("【部门资源增量同步】增量同步部门信息--------begin");
        int i = 0 ;
        while (true) {
            RecordsVO recordsVO = departmentResourceHandler.queryOrgBySequence(limit + "");
            if (ObjectUtils.isEmpty(recordsVO) || ObjectUtils.isEmpty(recordsVO.getRecord())) {
                break;
            }
            i++;
            if(i >= 200){
                break;
            }
        }
        log.info("【部门资源增量同步】增量同步部门信息--------end");
    }

    @Override
    public void initBuilderEmployeeData() {

        log.info("=========>项目启动，initBuilderEmployeeData.inits的值：{}", inits);
        if(!inits) {
            return;
        }

      /*  while (start < totle) {
        if(!inits) {
            return;
        }
            PageDataVO pageDataVO =
                    employeeResourceHandler.synchronousHumanInterface(start + "", limit + "");

            if (ObjectUtils.isNotEmpty(pageDataVO)
                    && ObjectUtils.isNotEmpty(pageDataVO.getTotal())) {
                totle = pageDataVO.getTotal();
                start = start + limit;
            } else {
                break;
            }
        }*/
    }

    @Override
    public void employeeIncrementalData() {
        int i = 0 ;
        AtomicInteger count = new AtomicInteger(0) ;
        while (true) {
            RecordsVO recordsVO = employeeResourceHandler.queryUserBySequence(limit + "",count);
            if (ObjectUtils.isEmpty(recordsVO) || ObjectUtils.isEmpty(recordsVO.getRecord())) {
                break;
            }
            i++;
            if(i >= 500){
                break;
            }
        }
        log.info("异步增量更新用户完成更新{}条数据", count.get());
    }

}
