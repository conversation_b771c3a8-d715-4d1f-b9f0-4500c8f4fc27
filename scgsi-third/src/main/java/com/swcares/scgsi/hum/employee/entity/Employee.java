/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：Employee.java <br>
 * Package：com.swcares.scgsi.hum.entity <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 * 
 * <AUTHOR> <br>
 *         date 2020年3月5日 下午5:56:43 <br>
 * @version v1.0 <br>
 */
package com.swcares.scgsi.hum.employee.entity;

import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**   
 * ClassName：com.swcares.scgsi.hum.entity.Employee <br>
 * Description：TODO(这里用一句话描述这个类的作用) <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月5日 下午5:56:43 <br>
 * @version v1.0 <br>  
 */
@Data
@Entity
@Table(name = "EMPLOYEE")
@EncryptionClassz
public class Employee {

    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    /**
     * 员工编号
     */
    @Column(name = "TUNO")
    private String tuNo;

    /**
     * 系统账号
     */
    @Column(name = "TU_ACCT")
    private String tuAcct;

    /**
     * 人员照片
     */
    @Column(name = "PHOTO")
    private String photo;

    /**
     * 中文名
     */
    @Column(name = "TU_CNAME")
    private String tuCname;

    /**
     * 中文拼音简拼
     */
    @Column(name = "TU_Ename")
    private String TUEname;

    /**
     * 性别
     */
    @Column(name = "TU_SEX")
    private String tuSex;

    /**
     * 员工岗位2
     */
    @Column(name = "TU_EXT2")
    private String tuExt2;

    /**
     * 部门ID
     */
    @Column(name = "TOID")
    private String toId;

    /**
     * 专业
     */
    @Column(name = "TU_SPECIALTY")
    private String tuSpecialty;

    /**
     * 员工归属属性
     */
    @Column(name = "TU_OWNPERTITY")
    private String tuOwnpertity;

    /**
     * 更改状态原因
     */
    @Column(name = "TU_STATEREASON")
    private String tuStateReason;

    /**
     * 国家/地区
     */
    @Column(name = "TU_REGION")
    private String tuRegion;

    /**
     * 联系电话 
     */
    @Column(name = "TU_TEL")
    @Encryption
    private String tuTel;

    /**
     * 手机
     */
    @Column(name = "TU_MOBILE")
    @Encryption
    private String tuMobile;

    /**
     * 邮件地址
     */
    @Column(name = "TU_EMAIL")
    private String tuEmail;

    /**
     * 别名
     */
    @Column(name = "TU_OTHERNAME")
    private String tuOtherName;

    /**
     * 身份证号
     */
    @Column(name = "TU_IDC")
    @Encryption
    private String tuIdc;

    /**
     * 状态((0 启用、1 禁用))
     */
    @Column(name = "TU_STATE")
    private String tuState = "1";

    /**
     * 生日
     */
    @Column(name = "TU_BDATE")
    private String tuBdate;

    /**
     * 住址
     */
    @Column(name = "TU_ADDRE")
    private String tuAddre;

    /**
     * 民族
     */
    @Column(name = "TU_NATION")
    private String tuNation;

    /**
     * 国籍
     */
    @Column(name = "TU_NATIONAL")
    private String tuNational;

    /**
     * 政治面貌
     */
    @Column(name = "TU_POLITICALSTATUS")
    private String tuPoliticalstatus;

    /**
     * 参加工作时间
     */
    @Column(name = "TU_WORK_DATE")
    private String tuWorkDate;

    /**
     * 入职日期
     */
    @Column(name = "TU_FSTDATE")
    private String tuFstdate;

    /**
     * 籍贯
     */
    @Column(name = "TU_ORIGIN")
    private String tuOrigin;

    /**
     * 学历
     */
    @Column(name = "TU_EXT1")
    private String tuExt1;

    /**
     * 本系统用户状态((0 启用、1 禁用))
     */
    @Column(name = "USER_STATE")
    private Integer userState = 1;

    /**
     * 锁定状态(0正常,1锁定)
     */
    @Column(name = "LOCK_OUT_STATE")
    private Integer lockOutState = 0;

    /**
     * 锁定时间
     */
    @Column(name = "LOCK_OUT_DATE")
    private Timestamp lockOutDate;

    /**
     * 密码
     */
    @Column(name = "PASS_WORD")
    private String passWord;

    /**
     * 密码更新时间
     */
    @Column(name = "PWD_UPD_DATE")
    private Timestamp pwdUpdDate;

    /**
     * 最后登录时间
     */
    @Column(name = "LOGIN_DATE")
    private Timestamp loginDate;

    /**
     * 值班状态(0 未值班、1 值班)
     */
    @Column(name = "IS_ON_DUTY")
    private Integer isOnDuty = 0;

    /**
     * 最后一次值班时间
     */
    @Column(name = "ON_DUTY_DATE")
    private Timestamp onDutyDate;

    /**
     * 最后一次解除值班时间
     */
    @Column(name = "OFF_DUTY_DATE")
    private Timestamp offDutyDate;

    /**
     * 工作航站(机场3字码)
     */
    @Column(name = "CITY_3CODE")
    private String airport3code;

    /**
     * 最后修改时间
     */
    @Column(name = "MODIFI_DATE")
    private Timestamp modifiDate;

    /**
     * 最后修改人
     */
    @Column(name = "MODIFI_USER")
    private String modifiUser;

    /**
     * 是否首次登陆(0否,1是)
     */
    @Column(name = "IS_FIRST_LOGIN")
    private Integer isFirstLogin = 1;

    /**
     * 其它系统编码
     */
    @Column(name = "OTHER_SYSTEM_NO")
    private String otherSystemNo;

    @Override
    public String toString() {
        return "id:" + id + " tuNo:" + tuNo + " tuAcct:" + tuAcct + " tuCname:" + tuCname + " TUEname:"
                + TUEname + " tuSex:" + tuSex + " tuExt2:" + tuExt2 + " toId:" + toId + " tuSpecialty:"
                + tuSpecialty + " tuOwnpertity:" + tuOwnpertity + " tuStateReason:"
                + tuStateReason + " tuRegion:" + tuRegion + " tuTel:" + tuTel + " tuMobile:"
                + tuMobile + " tuEmail:" + tuEmail + " tuOtherName:" + tuOtherName + " tuIdc:" + tuIdc
                + " tuState:" + tuState + "tuBdate:" + tuBdate + "tuAddre:" + tuAddre + "tuNation:"
                + tuNation + " tuNational:" + tuNational + " tuPoliticalstatus:" + tuPoliticalstatus
                + " tuWorkDate:" + tuWorkDate + " tuFstdate:" + tuFstdate + " tuOrigin:" + tuOrigin
                + " tuExt1:" + tuExt1 + " userState:" + userState + " lockOutState:" + lockOutState
                + " lockOutDate:" + lockOutDate + " passWord:" + passWord + " loginDate:" + loginDate
                + " isOnDuty:" + isOnDuty + " onDutyDate:" + onDutyDate + " offDutyDate:"
                + offDutyDate + " airport3code:" + airport3code + " modifiDate:" + modifiDate
                + " modifiUser:" + modifiUser + " isFirstLogin:" + isFirstLogin + " otherSystemNo:"
                + otherSystemNo;
    }

}
