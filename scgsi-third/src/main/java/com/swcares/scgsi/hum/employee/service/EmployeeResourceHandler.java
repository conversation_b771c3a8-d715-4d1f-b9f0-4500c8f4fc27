/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：HumResourceHandler.java <br>
 * Package：com.swcares.scgsi.hum.service <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 * 
 * <AUTHOR> <br>
 *         date 2020年3月6日 上午10:18:36 <br>
 * @version v1.0 <br>
 */
package com.swcares.scgsi.hum.employee.service;

import java.lang.reflect.Field;
import java.sql.Timestamp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import com.swcares.scgsi.util.AesEncryptUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.XML;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.hum.employee.dao.EmployeeDao;
import com.swcares.scgsi.hum.employee.entity.Employee;
import com.swcares.scgsi.hum.employee.vo.EmployeeVO;
import com.swcares.scgsi.hum.employee.vo.PageDataVO;
import com.swcares.scgsi.hum.employee.vo.RecordsVO;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.util.PinyinUtil;
import com.swcares.scgsi.wsdl.AxisUtil;
import com.swcares.scgsi.wsdl.UserWsdl;

/**   
 * ClassName：com.swcares.scgsi.hum.service.HumResourceHandler <br>
 * Description：人力资源同步 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月6日 上午10:18:36 <br>
 * @version v1.0 <br>  
 */
@Getter
@Setter
@Slf4j
@Component
@ConfigurationProperties(prefix = "hum.employeeresources")
@EnableConfigurationProperties(EmployeeResourceHandler.class)
public class EmployeeResourceHandler {

    // 存入redis中序列的缓存前缀
    private static final String EMP_PREFIX = "EMPLOYEE_SEQUENCE";

    @Autowired
    private EmployeeDao employeeDao;

    @Autowired
    private BaseDAO baseDao;

    @Autowired
    private RedisService redisService;

    /** 员工数据的url */
    private String url;

    /** 用于webservice认证的账密 */
    private UserWsdl userWsdl;

    private String authCode;

    private Map<String, String> map;

    /** 处理数据的节点，用于初始化 */
    private final String HANDLERNODE = "pageData";

    /** 处理数据的节点  ，用于增量数据更新 */
    private final String RECORDS = "records";

    /** 请求的员工信息的xml */
    private String reqXml = "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
            + "<root><auth appCode=\"gssp\" authCode=\"%s\" ip=\"***********\" />"
            + "<pageQuery start=\"%s\" limit=\"%s\">%s</pageQuery></root>";

    /** 请求序列的xml */
    private String reqSeqXml =
            "<?xml version=\"1.0\" encoding=\"utf-8\"?><root><auth appCode =\"gssp\" authCode=\"%s\" ip=\"***********\" /></root>";

    /** 增量更新员工信息 */
    private String queryUserBySequence = "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
            + "<root><auth appCode =\"gssp\" authCode=\"%s\" ip=\"***********\" />"
            + "<query startSequence=\"%s\" limit=\"%s\">%s</query></root>";


    /**
     * Title：synchronousHumanInterface <br>
     * Description：同步员工信息的接口 - 初始化用<br>
     * author：夏阳 <br>
     * date：2020年3月12日 下午4:46:01 <br>
     * @param start 同步的起始记录数
     * @param limit 步长
     * @return <br>
     */
    public PageDataVO synchronousHumanInterface(String start, String limit) {
        Map<String, String> reqMap = new HashMap<String, String>();
        reqMap.put(AxisUtil.INTERFACE_NAME, map.get("pageQueryUser"));
        reqMap.put(AxisUtil.REQ_XML_CONTENT, builderEeqXml(reqXml, start, limit));

        // 1.调用远程
        Object resXml = AxisUtil.doUrl(reqMap, url, userWsdl);
        if (ObjectUtils.isEmpty(resXml)) {
            log.error("同步人力资源接口返回数据为空，请求url:{}", url);
            return null;
        }

        // 2.处理xml
        org.json.JSONObject xmlJSONObj = XML.toJSONObject(resXml.toString(), true);
        JSONObject resJson = JSON.parseObject(xmlJSONObj.toString());

        JSONObject pageDataObject = AxisUtil.getJsonNode(resJson, HANDLERNODE);

        PageDataVO pageDataVO = JSON.parseObject(pageDataObject.toString(), PageDataVO.class);

        if (ObjectUtils.isEmpty(pageDataVO.getRecord())) {
            log.info("同步人力资源接口返回数据为空，pageDataVO:{}", pageDataVO);
            return null;
        }

        // 3.处理json返回员工集合
        List<Employee> list = handlerEmployee(pageDataVO.getRecord());

        // 4.入库
        employeeDao.saveAll(list);

        // 5.获取tag时的seq
        handlerCurrentSequence();

        return pageDataVO;
    }

    /**
     * Title：handlerEmployee <br>
     * Description：处理员工信息的方法；将员工信息从json数组转换为list集合，用于入库 <br>
     * author：夏阳 <br>
     * date：2020年3月9日 下午1:26:35 <br>
     * @param record
     * @return <br>
     */
    private List<Employee> handlerEmployee(List<JSONObject> record) {
        Map<String, Object> map = new HashMap<String, Object>();
        List<Employee> list = new ArrayList<Employee>();
        for (JSONObject tempObj : record) {
            JSONArray array = tempObj.getJSONArray("field");
            for (Object target : array) {
                JSONObject jsonField = (JSONObject) JSONObject.parse(target.toString());
                map.put(jsonField.getString("name"), jsonField.getString("value"));
            }

            // 在组装好单个对象数据Map转换为json对象
            JSONObject j = new JSONObject(map);
            Employee employee = JSON.parseObject(j.toString(), Employee.class);
            employee.setTuAcct(employee.getTuAcct().trim());
            // 最初用tuno座位工号同用户沟通后发现不正确,为了尽量少修改代码所以tuno的值改成tuacct的值,tuno值存入OtherSystemNo,生产环境再更换
            employee.setOtherSystemNo(employee.getTuNo());
            employee.setTuNo(employee.getTuAcct());
            // 说明该员工在山航已经删除了
            if ("true".equals(tempObj.getString("deleted"))) {
                // 禁用该员工
                employee.setUserState(1);
            }
            // 将员工的中文名转换为拼音
            employee.setTUEname(PinyinUtil.getChineseCharacters(employee.getTuCname()));
            list.add(employee);
        }
        return list;
    }

    /**
     * Title：builderEeqXml <br>
     * Description：创建请求员工信息的xml<br>
     * author：夏阳 <br>
     * date：2020年3月9日 下午1:28:04 <br>
     * @param reqXml
     * @param start
     * @param limit
     * @return <br>
     */
    public String builderEeqXml(String reqXml, String start, String limit) {
        StringBuilder sb = new StringBuilder();
        // java反射拿到EmployeeVO的所有属性，拼接到请求xml中
        Field[] fields = EmployeeVO.class.getDeclaredFields();
        for (Field field : fields) {
            if(field.getName().equals("photo")){//暂时添加来不更新员工照片
                continue;
            }
            sb.append("<return field=\"");
            sb.append(field.getName());
            sb.append("\" />");
        }
        return String.format(reqXml, authCode, start, limit, sb.toString());
    }

    /**
     * Title：handlerCurrentSequence <br>
     * Description：初始化员工数据后需要获取Sequence，用于增量更新<br>
     * author：夏阳 <br>
     * date：2020年3月17日 上午9:32:31 <br>
     */
    public void handlerCurrentSequence() {
        Map<String, String> reqMap = new HashMap<String, String>();
        reqMap.put(AxisUtil.INTERFACE_NAME, "getCurrentSequence");
        reqMap.put(AxisUtil.REQ_XML_CONTENT, String.format(reqSeqXml, authCode));

        // 1.调用远程
        Object resXml = AxisUtil.doUrl(reqMap, url, userWsdl);

        // 2.返回报文解析，获取目标节点数据
        org.json.JSONObject xmlJSONObj = XML.toJSONObject(resXml.toString(), true);
        JSONObject resJson = JSON.parseObject(xmlJSONObj.toString());
        JSONObject pageDataObject = AxisUtil.getJsonNode(resJson, "result");

        redisService.set(EMP_PREFIX, pageDataObject.getString("sequence"));
    }

    /**
     * Title：queryUserBySequence <br>
     * Description：根据步长增量更新员工数据 <br>
     * author：夏阳 <br>
     * date：2020年3月20日 下午1:29:49 <br>
     * @param limit
     * @param count 计数
     * @return <br>
     */
    public RecordsVO queryUserBySequence(String limit,AtomicInteger count) {
        Map<String, String> reqMap = new HashMap<String, String>();
        reqMap.put(AxisUtil.INTERFACE_NAME, "queryUserBySequence");
        reqMap.put(AxisUtil.REQ_XML_CONTENT,
                builderEeqXml(queryUserBySequence, redisService.get(EMP_PREFIX) + "", limit));

        // 1.调用远程
        log.info("调用人力资源增量同步员工接口请求，请求url:【{}】, userWsdl:【{}】, reqMap:【{}】", url, userWsdl, reqMap);
        Object resXml = AxisUtil.doUrl(reqMap, url, userWsdl);
        log.info("调用人力资源增量同步员工接口返回，原始数据内容为{}", resXml);
        if (ObjectUtils.isEmpty(resXml)) {
            return null;
        }
        // 2.处理xml
        org.json.JSONObject xmlJSONObj = XML.toJSONObject(resXml.toString(), true);
        JSONObject resJson = JSON.parseObject(xmlJSONObj.toString());

        JSONObject records = AxisUtil.getJsonNode(resJson, RECORDS);

        if (ObjectUtils.isEmpty(records)) {
            log.info("增量同步员工接口返回数据为空，records:{}", records);
            return null;
        }

        RecordsVO recordsVO = JSON.parseObject(records.toString(), RecordsVO.class);

        if (StringUtils.equals(recordsVO.getStartSequence(), recordsVO.getEndSequence())) {
            log.info("增量同步员工接口返回数据为空，recordsVO:{}", recordsVO);
            return null;
        }
        // log.info("员工数据增量更新，记录对象{}", recordsVO);
        // 3.处理json返回员工数据集合
        List<Employee> list = handlerEmployee(recordsVO.getRecord());

        // 4.覆盖更新[根据toid轮询出每条数据，然后覆盖更新]
        if (ObjectUtils.isEmpty(list)) {
            return null;
        }
        List<Employee> result = new ArrayList<Employee>();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        for (Employee newEmployee : list) {
            paramsMap.put("tuNo", newEmployee.getTuNo());
            Employee oldEmployee = baseDao.findByProperty(Employee.class, paramsMap);
            if (Objects.nonNull(oldEmployee)) {
                paramsMap.clear();
                // 山航的员工数据更新是整条记录直接更新，所以自定义的字段需要拿出来单独赋值，不然会变成空
                newEmployee.setId(oldEmployee.getId());
                newEmployee.setLoginDate(oldEmployee.getLoginDate());
                newEmployee.setLockOutState(oldEmployee.getLockOutState());
                newEmployee.setPassWord(oldEmployee.getPassWord());
                newEmployee.setLockOutDate(oldEmployee.getLockOutDate());
                newEmployee.setTUEname(oldEmployee.getTUEname());
                newEmployee.setOtherSystemNo(oldEmployee.getOtherSystemNo());
                newEmployee.setAirport3code(oldEmployee.getAirport3code());
                newEmployee.setUserState(oldEmployee.getUserState());
                newEmployee.setIsFirstLogin(oldEmployee.getIsFirstLogin());
                newEmployee.setModifiDate(oldEmployee.getModifiDate());
                newEmployee.setModifiUser(oldEmployee.getModifiUser());
                newEmployee.setIsOnDuty(oldEmployee.getIsOnDuty());
                newEmployee.setOnDutyDate(oldEmployee.getOnDutyDate());
                newEmployee.setOffDutyDate(oldEmployee.getOffDutyDate());
                newEmployee.setPhoto(oldEmployee.getPhoto());//暂时不更新员工照片
                newEmployee.setPwdUpdDate(oldEmployee.getPwdUpdDate());
                newEmployee.setTuIdc(AesEncryptUtil.aesEncryptScgsi(newEmployee.getTuIdc()));
                newEmployee.setTuMobile(AesEncryptUtil.aesEncryptScgsi(newEmployee.getTuMobile()));
            }else{
                newEmployee.setPwdUpdDate(new Timestamp(System.currentTimeMillis()));
                newEmployee.setModifiDate(new Timestamp(System.currentTimeMillis()));
                newEmployee.setTuIdc(AesEncryptUtil.aesEncryptScgsi(newEmployee.getTuIdc()));
                newEmployee.setTuMobile(AesEncryptUtil.aesEncryptScgsi(newEmployee.getTuMobile()));
            }
            //安全加固需求：密码清空，由门户登录
            newEmployee.setPassWord(null);
            log.info("员工数据增量更新，新对象{}，旧对象{}", newEmployee, oldEmployee);
            result.add(newEmployee);
            count.incrementAndGet();
        }
        employeeDao.saveAll(result);

        // 5.获取新的序列号存入redis
        redisService.set(EMP_PREFIX, recordsVO.getEndSequence());
        log.info("员工数据增量更新,存入redis新的序列{}成功", recordsVO.getEndSequence());
        return recordsVO;
    }

}
