package com.swcares.scgsi.hum.employee.vo;

import java.util.List;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.hum.employee.vo.RecordsVO <br>
 * Description：增量数据更新时返回的对象 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月19日 下午4:37:56 <br>
 * @version v1.0 <br>
 */
@Data
public class RecordsVO {
    
    private String startSequence;
    
    private String endSequence;
    
    private List<JSONObject> record;
}
