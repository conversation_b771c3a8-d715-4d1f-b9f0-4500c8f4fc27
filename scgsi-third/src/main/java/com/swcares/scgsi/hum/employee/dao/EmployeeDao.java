/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：EmployeeDAO.java <br>
 * Package：com.swcares.scgsi.hum.employee.dao <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 * 
 * <AUTHOR> <br>
 *         date 2020年3月9日 上午10:07:52 <br>
 * @version v1.0 <br>
 */
package com.swcares.scgsi.hum.employee.dao;

import java.io.Serializable;
import org.springframework.data.repository.query.Param;
import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.hum.employee.entity.Employee;

/**   
 * ClassName：com.swcares.scgsi.hum.employee.dao.EmployeeDAO <br>
 * Description：TODO(这里用一句话描述这个类的作用) <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月9日 上午10:07:52 <br>
 * @version v1.0 <br>  
 */
public interface EmployeeDao extends BaseJpaDao<Employee, Serializable> {

    /**
     * Title：findByTuNo <br>
     * Description：TODO(通过工号获取用户) <br>
     * author：王磊 <br>
     * date：2020年4月3日 上午10:58:35 <br>
     * @param tuNo
     * @return
     * @throws Exception <br>
     */
    Employee findByTuNo(@Param("tuNo") String tuNo) throws Exception;
}
