package com.swcares.scgsi.hum.department.service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.XML;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.hum.department.dao.DepartmentDao;
import com.swcares.scgsi.hum.department.entity.Department;
import com.swcares.scgsi.hum.department.vo.DepartmentVO;
import com.swcares.scgsi.hum.employee.vo.PageDataVO;
import com.swcares.scgsi.hum.employee.vo.RecordsVO;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.wsdl.AxisUtil;
import com.swcares.scgsi.wsdl.UserWsdl;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * ClassName：com.swcares.scgsi.hum.department.service.DepartmentResourceHandler <br>
 * Description：机构数据同步<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月10日 上午11:14:10 <br>
 * @version v1.0 <br>
 */
@Getter
@Setter
@Slf4j
@Component
@ConfigurationProperties(prefix = "hum.departmentresources")
@EnableConfigurationProperties(DepartmentResourceHandler.class)
public class DepartmentResourceHandler {

    // 存入redis中序列的缓存前缀
    private static final String DEPT_PREFIX = "DEPARTMENT_SEQUENCE";

    @Autowired
    private DepartmentDao departmentDao;

    @Autowired
    private BaseDAO baseDao;

    @Autowired
    private RedisService redisService;

    /** 机构数据的url */
    private String url;

    /** 用于webservice认证的账密  */
    private UserWsdl userWsdl;

    private String authCode;

    private Map<String, String> map;

    /** 处理数据的节点，用于初始化 */
    private final String PAGEDATA = "pageData";

    /** 处理数据的节点 ，用于增量数据更新 */
    private final String RECORDS = "records";

    /** 请求的部门信息的xml */
    private String reqDepartXml = "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
            + "<root><auth appCode=\"gssp\" authCode=\"%s\" ip=\"***********\" />"
            + "<pageQuery start=\"%s\" limit=\"%s\">%s</pageQuery></root>";

    /** 请求序列的xml */
    private String reqSeqXml =
            "<?xml version=\"1.0\" encoding=\"utf-8\"?><root><auth appCode =\"gssp\" authCode=\"%s\" ip=\"***********\" /></root>";

    /** 增量更新部门信息 */
    private String queryOrgBySequence = "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
            + "<root><auth appCode =\"gssp\" authCode=\"%s\" ip=\"***********\" />"
            + "<query startSequence=\"%s\" limit=\"%s\">%s</query></root>";

    /**
     * Title：synchronousDepartmentInterface <br>
     * Description：同步部门信息的接口 <br>
     * author：夏阳 <br>
     * date：2020年3月12日 下午4:45:19 <br>
     * @param start 同步的起始记录数
     * @param limit 步长
     * @return <br>
     */
    public PageDataVO synchronousDepartmentInterface(String start, String limit) {
        Map<String, String> reqMap = new HashMap<String, String>();
        reqMap.put(AxisUtil.INTERFACE_NAME, map.get("pageQueryOrgan"));
        reqMap.put(AxisUtil.REQ_XML_CONTENT, builderEeqXml(reqDepartXml, start, limit));

        // 1.调用远程
        Object resXml = AxisUtil.doUrl(reqMap, url, userWsdl);
        if (ObjectUtils.isEmpty(resXml)) {
            log.error("同步部门资源接口返回数据为空，请求url:{}", url);
            return null;
        }
        log.info("同步部门资源接口返回数据，原始xml报文为：{}", resXml);
        // 2.处理xml
        org.json.JSONObject xmlJSONObj = XML.toJSONObject(resXml.toString(), true);
        JSONObject resJson = JSON.parseObject(xmlJSONObj.toString());

        JSONObject records = AxisUtil.getJsonNode(resJson, PAGEDATA);

        if (ObjectUtils.isEmpty(records)) {
            log.info("同步机构接口返回数据为空，pageDataObject:{}", records);
            return null;
        }

        PageDataVO pageDataVO = JSON.parseObject(records.toString(), PageDataVO.class);

        if (ObjectUtils.isEmpty(pageDataVO.getRecord())) {
            log.info("同步机构接口返回数据为空，pageDataVO:{}", pageDataVO);
            return null;
        }

        // 3.处理json返回部门机构数据集合
        List<Department> list = handlerDepartment(pageDataVO.getRecord());

        // 4.入库
        departmentDao.saveAll(list);

        // 5.获取tag时的seq
        handlerCurrentSequence();

        return pageDataVO;
    }

    /**
     * Title：handlerDepartment <br>
     * Description：处理员工信息的方法；将员工信息从json数组转换为list集合，用于入库 <br>
     * author：夏阳 <br>
     * date：2020年3月9日 下午1:26:35 <br>
     * @param record
     * @return <br>
     */
    private List<Department> handlerDepartment(List<JSONObject> record) {
        Map<String, Object> map = new HashMap<String, Object>();
        List<Department> list = new ArrayList<Department>();
        for (JSONObject tempObj : record) {
            JSONArray array = tempObj.getJSONArray("field");
            for (Object target : array) {
                JSONObject jsonField = (JSONObject) JSONObject.parse(target.toString());
                // 山航生产和测试库该字段类型和值不一致，该字段未使用，不用同步
                if ("toType".equals(jsonField.getString("name"))) {
                    continue;
                }
                map.put(jsonField.getString("name"), jsonField.getString("value"));
            }

            // 在组装好单个对象数据Map转换为json对象
            JSONObject j = new JSONObject(map);
            Department department = JSON.parseObject(j.toString(), Department.class);
            // 说明该部门在山航已经删除了
            if ("true".equals(tempObj.getString("deleted"))) {
                // 禁用该部门，禁用的是在我们系统的部门状态，不是山航自带的字段
                department.setDepartState(1);
            }
            list.add(department);
        }
        return list;
    }

    /**
     * Title：builderEeqXml <br>
     * Description：创建请求机构信息的xml<br>
     * author：夏阳 <br>
     * date：2020年3月9日 下午1:28:04 <br>
     * @param reqXml
     * @param start
     * @param limit
     * @return <br>
     */
    private String builderEeqXml(String reqXml, String start, String limit) {
        StringBuilder sb = new StringBuilder();
        // java反射拿到DepartmentVO的所有属性，拼接到请求xml中
        Field[] fields = DepartmentVO.class.getDeclaredFields();
        for (Field field : fields) {
            sb.append("<return field=\"");
            sb.append(field.getName());
            sb.append("\" />");
        }
        return String.format(reqXml, authCode, start, limit, sb.toString());
    }

    /**
     * Title：handlerCurrentSequence <br>
     * Description：初始化部门数据后需要获取Sequence，用于增量更新<br>
     * author：夏阳 <br>
     * date：2020年3月17日 上午9:32:31 <br>
     */
    public void handlerCurrentSequence() {
        Map<String, String> reqMap = new HashMap<String, String>();
        reqMap.put(AxisUtil.INTERFACE_NAME, "getCurrentSequence");
        reqMap.put(AxisUtil.REQ_XML_CONTENT, String.format(reqSeqXml, authCode));

        // 1.调用远程
        Object resXml = AxisUtil.doUrl(reqMap, url, userWsdl);

        // 2.返回报文解析，获取目标节点数据
        org.json.JSONObject xmlJSONObj = XML.toJSONObject(resXml.toString(), true);
        JSONObject resJson = JSON.parseObject(xmlJSONObj.toString());
        JSONObject pageDataObject = AxisUtil.getJsonNode(resJson, "result");

        redisService.set(DEPT_PREFIX, pageDataObject.getString("sequence"));
    }

    /**
     * Title：queryOrgBySequence <br>
     * Description：机构数据的增量更新 <br>
     * author：夏阳 <br>
     * date：2020年3月17日 下午3:14:37 <br>
     * @param limit <br>
     */
    public RecordsVO queryOrgBySequence(String limit) {
        Map<String, String> reqMap = new HashMap<String, String>();
        reqMap.put(AxisUtil.INTERFACE_NAME, "queryOrgBySequence");
        reqMap.put(AxisUtil.REQ_XML_CONTENT,
                builderEeqXml(queryOrgBySequence, redisService.get(DEPT_PREFIX) + "", limit));

        // 1.调用远程
        log.info("【部门资源增量同步】增量同步部门资源接口返回数据，请求url:【{}】, userWsdl:【{}】, reqMap:【{}】", url, userWsdl, reqMap);
        Object resXml = AxisUtil.doUrl(reqMap, url, userWsdl);
        log.info("【部门资源增量同步】增量同步部门资源接口返回数据，原始xml报文为：{}", resXml);

        // 2.处理xml
        org.json.JSONObject xmlJSONObj = XML.toJSONObject(resXml.toString(), true);
        JSONObject resJson = JSON.parseObject(xmlJSONObj.toString());

        JSONObject records = AxisUtil.getJsonNode(resJson, RECORDS);

        if (ObjectUtils.isEmpty(records)) {
            log.info("【部门资源增量同步】增量同步机构接口返回数据为空，records:{}", records);
            return null;
        }

        RecordsVO recordsVO = JSON.parseObject(records.toString(), RecordsVO.class);

        if (StringUtils.equals(recordsVO.getStartSequence(), recordsVO.getEndSequence())) {
            log.info("【部门资源增量同步】增量同步机构接口返回数据为空，recordsVO:{}", recordsVO);
            return null;
        }

        // 3.处理json返回部门机构数据集合
        List<Department> list = handlerDepartment(recordsVO.getRecord());
        log.info("【部门资源增量同步】增量同步机构接口，处理后的部门条数为{}", list.size());
        // 4.覆盖更新[根据toid轮询出每条数据，然后覆盖更新]
        if (ObjectUtils.isEmpty(list)) {
            return null;
        }
        List<Department> result = new ArrayList<Department>();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        int i = 0;
        for (Department newDepartment : list) {
            paramsMap.put("toId", newDepartment.getToId());
            Department oldDepartment = baseDao.findByProperty(Department.class, paramsMap);
            paramsMap.clear();
            if(ObjectUtils.isNotEmpty(oldDepartment)){
                newDepartment.setId(oldDepartment.getId());
                if("@ROOT".equals(oldDepartment.getToPid())){
                    newDepartment.setToPid(oldDepartment.getToPid());
                }
                i++;
            }
            log.info("【部门资源增量同步】部门数据增量更新，新对象{}，旧对象{}", newDepartment, oldDepartment);
            result.add(newDepartment);
        }
        log.info("【部门资源增量同步】部门数据增量更新统计：修改{}个，总计{}个", i, list.size());
        departmentDao.saveAll(result);

        // 5.获取新的序列号存入redis
        redisService.set(DEPT_PREFIX, recordsVO.getEndSequence());
        log.info("【部门资源增量同步】部门数据增量更新,存入redis新的序列{}成功", recordsVO.getEndSequence());
        return recordsVO;
    }

}
