package com.swcares.scgsi.hum.department.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import org.hibernate.annotations.GenericGenerator;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.hum.department.entity.Department <br>
 * Description：部门信息实体类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月1日 上午11:27:24 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "DEPARTMENT")
public class Department {

    /**
     * 主键
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    /**
     * 组织代码
     */
    @Column(name = "TOID")
    private String toId;

    /**
     * 上级组织代码
     */
    @Column(name = "TOPID")
    private String toPid;

    /**
     * 组织编码
     */
    @Column(name = "TOBM")
    private String toBm;

    /**
     * 组织全称 
     */
    @Column(name = "TOFNAME")
    private String toFname;

    /**
     * 组织简称 
     */
    @Column(name = "TOSNAME")
    private String toSname;

    /**
     * 机构类型
     */
    @Column(name = "TOTYPE")
    private Integer toType;

    /**
     * 组织排序
     */
    @Column(name = "TOORDER")
    private Integer toOrder;

    /**
     * 启用标志  0 启用 1禁用
     */
    @Column(name = "TOBGNF")
    private Integer toBgnF;
    
    /**
     * 我们系统的部门启用标志  0 启用 1禁用
     */
    @Column(name = "DEPART_STATE")
    private Integer departState;

}
