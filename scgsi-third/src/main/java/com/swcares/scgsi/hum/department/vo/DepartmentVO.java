package com.swcares.scgsi.hum.department.vo;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.hum.department.vo.DepartmentVO <br>
 * Description：只能适用于山航获取部门信息，反射会获取每个字段，如果不是山航的字段，会报错，需要vo自己创建<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月21日 下午4:14:16 <br>
 * @version v1.0 <br>
 */
@Data
public class DepartmentVO {
    private String toId;
    private String toPid;
    private String toBm;
    private String toFname;
    private String toSname;
    private Integer toType;
    private Integer toOrder;
    private Integer toBgnF;
}
