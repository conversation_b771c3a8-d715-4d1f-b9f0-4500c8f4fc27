package com.swcares.scgsi.constant;

/**
 * ClassName：com.swcares.scgsi.constant.SmsTypeConstant <br>
 * Description：TODO(这里用一句话描述这个类的作用) <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月13日 下午7:32:07 <br>
 * @version v1.0 <br>
 */
public class SmsConstant {
    
    //微信登录发送验证码发短信的情况(这种情况暂时不用存入短信列表)
    @Deprecated
    public static final String WECHAT_VALIDATECODE = "0";
    
    //代领审核发送的情况
    public static final String SUBSTITUTE_AUDIT = "1";
    
    //行李审核发送的情况
    public static final String BAGGAGE_AUDIT = "2";
    
    //超售审核发送的情况
    public static final String OVERBOOKING_AUDIT  = "3";
    
    //系统自动发送采用该类
    public static final String SYS_SENDER  = "system";

    //发送短信统计次数key
    public static final String SEND_PHONE_NUM_KEY = "SEND_PHONE_NUM_KEY";

    //发送短信统计上限次数key
    public static final String SYS_SMS_SEND_UPPER_LIMIT = "SYS_SMS_SEND_UPPER_LIMIT";



}
