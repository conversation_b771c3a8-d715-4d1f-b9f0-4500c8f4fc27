package com.swcares.scgsi.sms.entity;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import org.hibernate.annotations.GenericGenerator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.sms.bean.SmsTemplate <br>
 * Description：短信模板 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月15日 下午1:15:49 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "SMS_TEMPLATE")
public class SmsTemplate {
    
    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;
    
    /**
     * 短信末班状态0：启用  1禁用
     */
    @Column(name = "STATE", length = 2)
    private String state;
    
    /**
     * 模板名称
     */
    @Column(name = "TEMPLATE_NAME", length = 20)
    private String templateName;
    
    /**
     * 模板编码
     */
    @Column(name = "TEMPLATE_CODE", length = 20)
    private String templateCode;
    
    /**
     * 模板内容
     */
    @Column(name = "TEMPLATE_CONTENT")
    private String templateContent;
    
    /**
     * 修改者
     */
    @Column(name = "UPDATERS", length = 20)
    private String updaters;
    
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "UPDATER")
    private Date updateTime;

}
