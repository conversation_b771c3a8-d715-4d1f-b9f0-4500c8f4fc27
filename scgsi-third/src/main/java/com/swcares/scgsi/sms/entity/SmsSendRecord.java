package com.swcares.scgsi.sms.entity;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.sms.bean <br>
 * Description：短信记录表<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月05日 19:56 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "SMS_SEND_RECORD")
@EncryptionClassz
public class SmsSendRecord {

    /**
    * 主键
    */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String Id;

    /**
     * 电话号码
     */
    @Column(name = "MOBILE")
    @Encryption
    private String mobile;

    /**
     * 短信内容
     */
    @Column(name = "MSG")
    private String msg;

    /**
     * 短信发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "SEND_TIME")
    private Date sendTime;

    /**
     * 短信发送状态 0-成功 1-失败
     */
    @Column(name = "SEND_STATE")
    private String sendState;

    /**
     * 返回code
     */
    @Column(name = "RESULT_CODE")
    private String resultCode;

    /**
     * 返回msg
     */
    @Column(name = "RESULT_MSG")
    private String resultMsg;

    /**
     * 短信类型   @link SmsTypeConstant
     */
    @Column(name = "SMS_TYPE")
    private String smsType;

    /**
     * 航班日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "FLIGHT_DATE")
    private Date flightDate;

    /**
     * 航班号
     */
    @Column(name = "FLIGHT_NO")
    private String flightNo;

    /**
     * 发送者 @link SmsConstant
     */
    @Column(name = "SENDER")
    private String sender;
}
