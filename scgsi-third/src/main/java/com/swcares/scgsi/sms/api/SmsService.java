package com.swcares.scgsi.sms.api;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.sms.entity.SmsSendResult;
import com.swcares.scgsi.sms.entity.SmsTemplate;
import com.swcares.scgsi.sms.form.SmsSendRecordForm;
import com.swcares.scgsi.sms.form.SmsTemplateForm;

/**
 * ClassName：com.swcares.scgsi.sms.api <br>
 * Description：短信服务接口 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月05日 16:00 <br>
 * @version v1.0 <br>
 */
public interface SmsService{

    /**
     * Title：smsSendResultObj <br>
     * Description：发起短信请求，反馈短信结果类<br>
     * author：傅欣荣 <br>
     * date：2020/3/5 20:00 <br>
     * @param
     * @return
     */
    SmsSendResult smsSendResultObj(String mobile, String msg);

    /**
     * Title：smsSend <br>
     * Description：发起短信请求，并将短信记录保存入库<br>
     * author：傅欣荣 <br>
     * date：2020/3/5 18:44 <br>
     * @param  mobile 电话号码 格式： 电话1 或者 电话1,电话2,电话N
     * @param  msg 短信内容
     * @return void
     */
    void smsSend(String mobile, String msg);


    /**
     * Title：smsSend <br>
     * Description：发起短信请求，并将短信记录保存入库<br>
     * author：傅欣荣 <br>
     * date：2020/3/5 18:44 <br>
     * @param  mobile 电话号码 格式： 电话1 或者 电话1,电话2,电话N
     * @param  msg 短信内容
     *             flightNo 航班号
     *             flightDate 航班日期
     *             smsType  类型 异常行李2、超售旅客3、
     *             sender 发送人
     * @return void
     */
    void smsSend(String mobile, String msg,String flightNo,String flightDate, String smsType,String sender);


    /**
     * Title：batchSmsSend <br>
     * Description： 批量发起短信请求<br>
     * author：傅欣荣 <br>
     * date：2020/3/5 18:49 <br>
     * @param  smsMap 短信集合，key：电话号码 格式:多个电话号码用逗号分隔 ; value：短信内容
     * @return void
     */
    void batchSmsSend(Map<String, String> smsMap);

    /**
     * Title：getSmsSendRecords <br>
     * Description：条件查询短信信息 <br>
     * author：夏阳 <br>
     * date：2020年4月13日 下午7:41:44 <br>
     * @param smsSendRecordVO
     * @return QueryResults<br>
     */
    QueryResults getSmsSendRecords(SmsSendRecordForm smsSendRecordForm);

    /**
     * Title：exportSmsRecoreds <br>
     * Description：根据条件查询出短信记录并下载（条件不包含分页信息）<br>
     * author：夏阳 <br>
     * date：2020年4月14日 下午1:19:47 <br>
     * @param smsSendRecordForm
     * @param response
     */
    void exportSmsRecoreds(SmsSendRecordForm smsSendRecordForm, HttpServletResponse response);
    
    /**
     * Title：resendSms <br>
     * Description：针对失败短信，重新发送短信信息 <br>
     * author：夏阳 <br>
     * date：2020年4月14日 下午2:27:49 <br>
     * @param ids 需要重复发送的短信主键集合
     * @return <br>
     */
    boolean resendSms(List<Serializable> ids);
    
    /**
     * Title：saveTemplate <br>
     * Description：保存模板信息<br>
     * author：夏阳 <br>
     * date：2020年4月15日 下午1:44:40 <br>
     * @param smsTemplateForm <br>
     */
    void saveTemplate(SmsTemplateForm smsTemplateForm);
    
    /**
     * Title：updateTemplate <br>
     * Description：修改模板信息 <br>
     * author：夏阳 <br>
     * date：2020年4月15日 下午1:45:11 <br>
     * @param smsTemplateForm <br>
     */
    void updateTemplate(SmsTemplateForm smsTemplateForm);
    
    /**
     * Title：deleteTemplate <br>
     * Description：删除模板信息 <br>
     * author：夏阳 <br>
     * date：2020年4月15日 下午1:40:52 <br>
     * @param ids <br>
     */
    void deleteTemplate(String[] ids);
    
    /**
     * Title：queryTemplates <br>
     * Description：分页查询模板信息 <br>
     * author：夏阳 <br>
     * date：2020年4月15日 下午1:43:55 <br>
     * @param smsTemplateForm
     * @return <br>
     */
    QueryResults queryTemplates(SmsTemplateForm smsTemplateForm);

    /**
     * Title：queryTemplateByType <br>
     * Description：通过模板类型获取当前模板对象<br>
     * author：夏阳 <br>
     * date：2020年4月20日 上午9:57:49 <br>
     * @param smsTemplateForm <br>
     */
    SmsTemplate queryTemplateByType(SmsTemplateForm smsTemplateForm);

    /**
     * Title：updateTemplatesState <br>
     * Description：批量修改模板状态，批量禁用启用模板<br>
     * author：夏阳 <br>
     * date：2020年4月20日 上午9:57:49 <br>
     * @param ids  所有主键集合
     * @param state  启用禁用状态<br>
     */
    void updateTemplatesState(String[] ids, String state);
    
}
