package com.swcares.scgsi.sms.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.aop.EncryptFieldAop;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.constant.SmsConstant;
import com.swcares.scgsi.dict.service.SysDictDataService;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.sms.api.SmsService;
import com.swcares.scgsi.sms.dao.SmsDao;
import com.swcares.scgsi.sms.dao.SmsDaoImpl;
import com.swcares.scgsi.sms.dao.SmsTemplateDao;
import com.swcares.scgsi.sms.entity.SmsSendRecord;
import com.swcares.scgsi.sms.entity.SmsSendResult;
import com.swcares.scgsi.sms.entity.SmsTemplate;
import com.swcares.scgsi.sms.form.SmsSendRecordForm;
import com.swcares.scgsi.sms.form.SmsTemplateForm;
import com.swcares.scgsi.sms.vo.SmsExportVO;
import com.swcares.scgsi.util.*;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Consts;
import org.apache.http.Header;
import org.apache.http.entity.ContentType;
import org.apache.http.message.BasicHeader;
import org.json.XML;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.swcares.scgsi.constant.SmsConstant.SEND_PHONE_NUM_KEY;

/**
 * ClassName：com.swcares.scgsi.sms.service <br>
 * Description：短信实现类 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 03月05日 16:02 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Getter
@Setter
@Service
public class SmsServiceImpl implements SmsService {

    @Value("${sms.url}")
    private String url;
    @Value("${sms.username}")
    private String username;
    @Value("${sms.password}")
    private String password;
    @Value("${sms.clientId}")
    private String clientId;
    @Value("${sms.operationCode}")
    private String operationCode;

    protected String[] columnNames = {"短信类别【1代领审核 2行李审核 3超售审核】", "航班号", "航班日期", "短信内容", "接收号码",
            "发送状态【0成功 1失败】", "发送人", "发送时间"};

    protected String[] keys = {"smsType", "flightNo", "flightDate", "msg", "mobile", "sendState",
            "sender", "sendTime"};

    @Autowired
    private SmsDaoImpl smsDaoImpl;

    @Autowired
    private SmsDao smsDao;

    @Autowired
    private BaseDAO baseDAO;

    @Autowired
    private SmsTemplateDao smsTemplateDao;
    @Autowired
    private RedisService redisService;
    @Resource
    private SysDictDataService dictDataService;

    private String getSmsHttpSend(String parameter) {
        log.info("SmsServiceImpl 短信服务方法：smsSend ,发起短信请求！ 参数{}", parameter);
        String result;
        try {
            BasicHeader header = new BasicHeader("Content-Type", "text/xml");
            BasicHeader header1 = new BasicHeader("ClientId", clientId);
            BasicHeader header2 = new BasicHeader("OperationCode", operationCode);
            result = HttpConnectionUtils.doPost(url, parameter, false,
                    ContentType.create("text/xml", Consts.UTF_8),
                    new Header[] {header, header1, header2});
        } catch (IOException e) {
            log.error("SmsServiceImpl 短信服务方法：smsSend , 异常！ 参数{}，异常信息{}", parameter, e);
            // 是否需要保存异常信息 ： 短信发送失败
            return null;
        }
        log.info("SmsServiceImpl 短信服务方法：smsSend ,短信接口返回参数！ 请求参数{} ,返回参数{}", parameter, result);
        return result;
    }
    /**
     * @title getSmsHttpSend
     * @description 发送短信，并记录电话号码（当天）发送次数
     * <AUTHOR>
     * @date 2024/5/22 11:22
     * @param parameter
     * @param mobile
     * @return java.lang.String
     */
    private String getSmsHttpSend(String parameter,String mobile) {
        log.info("SmsServiceImpl 短信服务方法：smsSend ,发起短信请求！ 参数{}", parameter);
        //判断电话号码是否达到上限
        smsSendUpperLimit(mobile);
        String result;
        try {
            BasicHeader header = new BasicHeader("Content-Type", "text/xml");
            BasicHeader header1 = new BasicHeader("ClientId", clientId);
            BasicHeader header2 = new BasicHeader("OperationCode", operationCode);
            result = HttpConnectionUtils.doPost(url, parameter, false,
                    ContentType.create("text/xml", Consts.UTF_8),
                    new Header[] {header, header1, header2});
        } catch (IOException e) {
            log.error("SmsServiceImpl 短信服务方法：smsSend , 异常！ 参数{}，异常信息{}", parameter, e);
            // 是否需要保存异常信息 ： 短信发送失败
            return null;
        }
        log.info("SmsServiceImpl 短信服务方法：smsSend ,短信接口返回参数！ 请求参数{} ,返回参数{}", parameter, result);
        //记录电话号码发送次数【按天统计】
        sendStatisticsByMobile(result,mobile);
        return result;
    }

    private void smsSendUpperLimit(String mobile){
        int value = 0;
        Object restrictNum = redisService.get(SmsConstant.SYS_SMS_SEND_UPPER_LIMIT);
        if(restrictNum == null){
            List<Map<String, String>> dict =
                    dictDataService.getSelectDict(SmsConstant.SYS_SMS_SEND_UPPER_LIMIT);
            if(ObjectUtils.isEmpty(dict)){
                return;
            }//数据字典：发送短信上限次数
            value = Integer.valueOf(dict.get(0).get("value"));
            redisService.set(SmsConstant.SYS_SMS_SEND_UPPER_LIMIT,value);
        }else {
            value = (int) restrictNum;
        }


        //判断电话号码当天，发送短信是否达到上限
        Object numObj = redisService.get(SEND_PHONE_NUM_KEY + mobile);
        //发送成功，短信发送次数+1
        int num =  numObj==null?0: (int) numObj;
        if(num >= value){
            throw new BusinessException(MessageCode.SMS_SEND_UPPER_LIMIT.getCode());
        }

    }

    private void sendStatisticsByMobile(String result,String mobile){

        SmsSendResult sendResult = this.parseSmsContent(result);
        //短信发送状态 0-成功 1-失败

        if (null != sendResult) {
            if (sendResult.getCode().equals("0")){
                Object numObj = redisService.get(SEND_PHONE_NUM_KEY + mobile);
                //发送成功，短信发送次数+1
                int num =  numObj==null?0: (int) numObj;
                num+=1;

                // 获取当前时间
                LocalDateTime now = LocalDateTime.now();
                // 获取明天的0点时间
                LocalDateTime tomorrowMidnight = now.plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                // 计算两者间相差的秒数
                long secondsUntilMidnight = ChronoUnit.SECONDS.between(now, tomorrowMidnight);

                redisService.set(SEND_PHONE_NUM_KEY + mobile,num,secondsUntilMidnight);
            }

        }

    }

    @Override
    public SmsSendResult smsSendResultObj(String mobile, String msg) {
        String result = this.getSmsHttpSend(getSmsParameters(mobile, msg),mobile);
        return this.parseSmsContent(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void smsSend(String mobile, String msg) {
        String result = this.getSmsHttpSend(getSmsParameters(mobile, msg),mobile);
        SmsSendRecord record = new SmsSendRecord();
        record.setMobile(mobile);
        record.setMsg(msg);
        record.setSendTime(new Date());
        this.resultParseSave(result, record);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void smsSend(String mobile, String msg, String flightNo, String flightDate, String smsType, String sender) {
        String result = this.getSmsHttpSend(getSmsParameters(mobile, msg),mobile);
        SmsSendRecord record = new SmsSendRecord();
        record.setMobile(mobile);
        record.setMsg(msg);
        record.setFlightDate(DateUtils.parseStrToDate(flightDate,DateUtils.YYYY_MM_DD));
        record.setFlightNo(flightNo);
        record.setSmsType(smsType);
        record.setSender(sender);
        record.setSendTime(new Date());
        this.resultParseSave(result, record);
    }

    @Override
    public void batchSmsSend(Map<String, String> smsMap) {
        smsMap.forEach((key, value) -> {
            this.smsSend(key, value);
        });
    }


    /**
     * Title：getSmsParameters <br>
     * Description： 获取短信发送xml <br>
     * author：傅欣荣 <br>
     * date：2020/3/5 17:39 <br>
     * @param  mobile 电话号码。 格式:电话1 或者 电话1,电话2
     * @param  msg 短信内容
     * @return java.lang.String
     */
    private String getSmsParameters(String mobile, String msg) {
        StringBuffer param =
                new StringBuffer("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap"
                        + ".org/soap/envelope/\" xmlns:web=\"http://webservice.sms.dx.com/\">>\n");
        param.append("<soapenv:Header/>\n");
        param.append("<soapenv:Body>\n");
        param.append("<web:submit>\n");
        param.append("<arg0>\n");
        param.append("<uc>000000</uc>\n");
        param.append("<mobile>{0}</mobile>\n");
        param.append("<msg>{1}</msg>\n");
        param.append("<password>").append(password).append("</password>\n");
        param.append("<username>").append(username).append("</username>\n");
        param.append("</arg0>\n");
        param.append("</web:submit>\n");
        param.append("</soapenv:Body>\n");
        param.append("</soapenv:Envelope>\n");
        return MessageFormat.format(param.toString(), mobile, msg);
    }


    /**
     * Title：parseSmsContent <br>
     * Description： 解析短信反馈内容<br>
     * author：傅欣荣 <br>
     * date：2020/3/5 20:27 <br>
     * @param  result
     * @return com.swcares.scgsi.sms.bean.SmsSendResult
     */
    private SmsSendResult parseSmsContent(String result) {
        // 根据业务逻辑此处根据节点获取的值不会为空
        org.json.JSONObject xmlJSONObj = XML.toJSONObject(result);
        xmlJSONObj = xmlJSONObj.getJSONObject("soap:Envelope");
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(xmlJSONObj.toString());
        JSONObject body = jsonObject.getJSONObject("soap:Body");
        JSONObject dynamicResponse = body.getJSONObject("ns1:submitResponse");
        JSONObject returnR = dynamicResponse.getJSONObject("return");
        return JSONObject.parseObject(returnR.toJSONString(), SmsSendResult.class);
    }

    /**
     * Title：resultParseSave <br>
     * Description： 解析数据并保存入库<br>
     * author：傅欣荣 <br>
     * date：2020/3/5 20:26 <br>
     * @param  result 短信返回内容, record 保存类
     * @return void
     */
    private void resultParseSave(String result, SmsSendRecord record) {
        SmsSendResult smsResult = this.parseSmsContent(result);
        if (null != smsResult) {
            if (smsResult.getCode().equals("0")){
                record.setSendState(smsResult.getCode());
            }else{
                record.setSendState("1");
            }
            record.setResultCode(smsResult.getCode());
            record.setResultMsg(smsResult.getMessage());
        } else{
            record.setSendState("1");
        }
        // 保存入库
        record.setMobile(AesEncryptUtil.aesEncryptScgsi(record.getMobile()));
        smsDaoImpl.saveSmsRecord(record);
    }


    @Override
    public QueryResults getSmsSendRecords(SmsSendRecordForm smsSendRecordForm) {
        QueryInfo qi = builderQueryInfo(smsSendRecordForm);
        QueryResults qr = baseDAO.findBySQLPage_comm(qi.getHql(), smsSendRecordForm.getCurrent(),
                smsSendRecordForm.getPageSize(), qi.getQueryMap(), SmsSendRecord.class);
        List<SmsSendRecord> list = (List<SmsSendRecord>) qr.getList();
        if(ObjectUtils.isNotEmpty(list)){
            list.forEach(e->{
                e.setMobile(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, e.getMobile()));
            });
        }
        return qr;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void exportSmsRecoreds(SmsSendRecordForm smsSendRecordForm,
            HttpServletResponse response) {
        QueryInfo qi = builderQueryInfo(smsSendRecordForm);
        List<SmsSendRecord> list = (List<SmsSendRecord>) baseDAO.findBySQL_comm(qi.getHql(),
                qi.getQueryMap(), SmsSendRecord.class);

        List<SmsExportVO> exportList = new ArrayList<SmsExportVO>(list.size());
        for (SmsSendRecord ssr : list) {
            SmsExportVO SmsExportVO = new SmsExportVO();
            try {
                BeanUtils.copyProperties(SmsExportVO, ssr);
            } catch (IllegalAccessException | InvocationTargetException e) {
                log.error("短信列表复制VO出错，复制源对象内容为[{}]，异常信息为:{}", ssr, e);
            }
            exportList.add(SmsExportVO);
        }

        try {
            ExcelImportExportUtil.exportExcel(response, "sms-" + System.currentTimeMillis(),
                    exportList, columnNames, keys);
        } catch (IOException e) {
            log.error("短信列表下载出错:{}", e);
        }
    }

    /**
     * Title：builderQueryInfo <br>
     * Description：短信查询和短信下载共用的构建方法<br>
     * author：夏阳 <br>
     * date：2020年4月14日 下午2:00:23 <br>
     * @param smsSendRecordForm
     * @return <br>
     */
    private QueryInfo builderQueryInfo(SmsSendRecordForm smsSendRecordForm) {
        StringBuffer sb = new StringBuffer(
                "select ssr.id, ssr.mobile, ssr.msg, ssr.send_Time, ssr.send_State, ssr.result_Code, ssr.result_Msg, ssr.sms_Type, ssr.flight_Date, ssr.flight_No, nvl((select emp.tu_Cname from Employee emp where emp.tuNo = ssr.sender),ssr.sender) as sender From Sms_Send_Record ssr where 1=1");

        Map<String, Object> queryMap = new HashMap<String, Object>();

        if (ObjectUtils.isNotEmpty(smsSendRecordForm.getFlightDateBegin())) {
            sb.append(" and ssr.flight_Date >= :flightDateBegin");
            queryMap.put("flightDateBegin", smsSendRecordForm.getFlightDateBegin());
        }
        
        if (ObjectUtils.isNotEmpty(smsSendRecordForm.getFlightDateEnd())) {
            sb.append(" and ssr.flight_Date <= :flightDateEnd");
            queryMap.put("flightDateEnd", smsSendRecordForm.getFlightDateEnd());
        }

        if (ObjectUtils.isNotEmpty(smsSendRecordForm.getFlightNo())) {
            sb.append(" and ssr.flight_No =:flightNo ");
            queryMap.put("flightNo", smsSendRecordForm.getFlightNo());
        }

        if (ObjectUtils.isNotEmpty(smsSendRecordForm.getMobile())) {
            smsSendRecordForm.setMobile(AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, smsSendRecordForm.getMobile()));
            sb.append(" and ssr.mobile =:mobile ");
            queryMap.put("mobile", AesEncryptUtil.aesEncryptScgsi(smsSendRecordForm.getMobile()));
        }

        if (ObjectUtils.isNotEmpty(smsSendRecordForm.getSendTimeBegin())) {
            sb.append(" and ssr.send_Time >= :sendTimeBegin ");
            queryMap.put("sendTimeBegin", smsSendRecordForm.getSendTimeBegin());
        }
        
        if (ObjectUtils.isNotEmpty(smsSendRecordForm.getSendTimeEnd())) {
            sb.append(" and ssr.send_Time <= :sendTimeEnd ");
            queryMap.put("sendTimeEnd", smsSendRecordForm.getSendTimeEnd());
        }

        if (ObjectUtils.isNotEmpty(smsSendRecordForm.getSmsType())) {
            sb.append(" and ssr.sms_Type" + " =:smsType ");
            queryMap.put("smsType", smsSendRecordForm.getSmsType());
        }

        if (ObjectUtils.isNotEmpty(smsSendRecordForm.getSendState())) {
            sb.append(" and ssr.send_State" + " =:sendState ");
            queryMap.put("sendState", smsSendRecordForm.getSendState());
        }
        sb.append(" order by ssr.send_Time desc");
        QueryInfo qi = new QueryInfo();
        qi.setHql(sb.toString());
        qi.setQueryMap(queryMap);
        return qi;
    }

    class QueryInfo {
        /**
         * 查询的hql
         */
        private String hql;

        /**
         * 封装查询条件
         */
        private Map<String, Object> queryMap;

        public String getHql() {
            return hql;
        }

        public void setHql(String hql) {
            this.hql = hql;
        }

        public Map<String, Object> getQueryMap() {
            return queryMap;
        }

        public void setQueryMap(Map<String, Object> queryMap) {
            this.queryMap = queryMap;
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resendSms(List<Serializable> ids) {
        boolean mark = false;
        // 根据传入的短信主键获取对应记录集合
        List<SmsSendRecord> list = smsDao.findAllById(ids);

        // 记录不存在抛出异常
        Asserts.notNull(list, MessageCode.SMS_RECORD_NOT_EXIST.getCode());

        for (SmsSendRecord smsSendRecord : list) {
            // 状态为失败才去发送短信
            if ("0".equals(smsSendRecord.getSendState())) {
                continue;
            }
            SmsSendResult smsResult =
                    this.smsSendResultObj(AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_KEY,smsSendRecord.getMobile()), smsSendRecord.getMsg());

            // 重发后会生成新的记录，原来记录不处理
            // 调用三方接口回来后数据入库存储
            SmsSendRecord record = new SmsSendRecord();
            try {
                BeanUtils.copyProperties(record, smsSendRecord);
                record.setId(null);
                record.setSendTime(new Date());
                record.setSender((String) AuthenticationUtil.getAuthentication().getName());
            } catch (IllegalAccessException | InvocationTargetException e) {
                log.error("失败短信重发异常，重发短信对象[{}]，异常堆栈{}", smsSendRecord, e);
            }
            if (null != smsResult && "0".equals(smsResult.getCode())) {
                record.setSendState(smsResult.getCode());
                record.setResultCode(smsResult.getCode());
                record.setResultMsg(smsResult.getMessage());
                mark = true;
            } else {
                record.setSendState("1");
                mark = false;
            }

            // 保存入库
            smsDaoImpl.saveSmsRecord(record);
        }
        return mark;
    }

    /**
     * Title：validateTempCode <br>
     * Description：入库前根据templatecode判断该条数据是否存在 [若存在直接抛出异常]<br>
     * author：夏阳 <br>
     * date：2020年4月23日 上午9:24:33 <br>
     * @param smsTemplateForm <br>
     */
    private void validateTempCode(SmsTemplateForm smsTemplateForm) {
        // 入库前根据templatecode判断该条数据是否存在
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("templateCode", smsTemplateForm.getTemplateCode());
        SmsTemplate smsTemplate = baseDAO.findByProperty(SmsTemplate.class, params);
        Asserts.isNull(smsTemplate, MessageCode.SMS_TEMPLATE_IS_EXIST.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTemplate(SmsTemplateForm smsTemplateForm) {
        // 通过短信模板编码校验是否存在
        validateTempCode(smsTemplateForm);

        SmsTemplate smsTemplate = new SmsTemplate();
        try {
            BeanUtils.copyProperties(smsTemplate, smsTemplateForm);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error("对象复制异常,提交参数信息为[{}], 异常内容：{}", smsTemplateForm, e);
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        }
        smsTemplate.setUpdateTime(new Date());
        String user = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        smsTemplate.setUpdaters(user);
        baseDAO.save(smsTemplate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTemplate(SmsTemplateForm smsTemplateForm) {

        SmsTemplate smsTemplate = new SmsTemplate();
        try {
            BeanUtils.copyProperties(smsTemplate, smsTemplateForm);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error("对象复制异常,提交参数信息为[{}], 异常内容：{}", smsTemplateForm, e);
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        }
        smsTemplate.setUpdateTime(new Date());
        String user = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        smsTemplate.setUpdaters(user);
        baseDAO.update(smsTemplate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTemplate(String[] ids) {
        Asserts.notNull(ids, MessageCode.SMS_TEMPLATE_ID_IS_NULL.getCode());

        StringBuffer sb = new StringBuffer();
        sb.append("delete from SMS_TEMPLATE where id in (");
        for (@SuppressWarnings("unused")
        String str : ids) {
            sb.append(" ?, ");
        }
        String sql = sb.subSequence(0, sb.length() - 2).toString();
        sql += ")";

        baseDAO.batchUpdate(sql, ids);
    }

    @Override
    public QueryResults queryTemplates(SmsTemplateForm smsTemplateForm) {
        String sql = "select st.id, st.state, st.template_Name, st.template_Code, st.template_Content, (select emp.tu_Cname from Employee emp where emp.tuNo = st.updaters ) as updaters, st.updater " + 
                " from SMS_TEMPLATE st";
        return baseDAO.findBySQLPage_comm(sql, smsTemplateForm.getCurrent(), smsTemplateForm.getPageSize(), null, SmsTemplate.class);
    }

    @Override
    @SuppressWarnings("unchecked")
    public SmsTemplate queryTemplateByType(SmsTemplateForm smsTemplateForm) {
        Map<String, Object> map = new HashMap();
        if (smsTemplateForm == null || ObjectUtils.isEmpty(smsTemplateForm.getTemplateCode())) {
            return null;
        }
        map.put("templateCode", smsTemplateForm.getTemplateCode());
        map.put("state", "0");
        return baseDAO.findByProperty(SmsTemplate.class, map);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTemplatesState(String[] ids, String state) {
        Asserts.notNull(ids, MessageCode.SMS_TEMPLATE_ID_IS_NULL.getCode());
        Asserts.notNull(state, MessageCode.SMS_TEMPLATE_ID_IS_NULL.getCode());

        StringBuffer sb = new StringBuffer();
        String[] tempParam = new String[ids.length + 1];
        tempParam[0] = state;
        sb.append("update SMS_TEMPLATE set state = ? where id in (");
        for (int i = 0; i < ids.length; i++) {
            tempParam[i + 1] = ids[i];
            sb.append("?, ");
        }
        String sql = sb.subSequence(0, sb.length() - 2).toString();
        sql += ")";

        baseDAO.batchUpdate(sql, tempParam);
    }

}
