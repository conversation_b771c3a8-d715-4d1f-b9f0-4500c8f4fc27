package com.swcares.scgsi.sms.dao;

import cn.hutool.core.date.DateUtil;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.sms.entity.SmsSendRecord;
import com.swcares.scgsi.util.AesEncryptUtil;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.sms.dao <br>
 * Description：短信记录发送的操作类 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月05日 19:54 <br>
 * @version v1.0 <br>
 */
@Repository
public class SmsDaoImpl {
    
    @Resource
    private BaseDAO baseDAO;

    /**
     * Title：saveSmsRecord <br>
     * Description：保存短信记录 <br>
     * author：傅欣荣 <br>
     * date：2020/3/5 19:57 <br>
     * @param
     * @return
     */
    public void saveSmsRecord(SmsSendRecord smsSendRecord) {
        baseDAO.save(smsSendRecord);
    }

    /**
     * Title：querySmsSendRecord <br>
     * Description：查询短信发送记录 <br>
     * author：于琦海 <br>
     * date：2021/1/12 15:18 <br>
     * @params mobile String
     * @params msg String
     * @return List<SmsSendRecord>
     */ 
    public List<SmsSendRecord> querySmsSendRecord(String mobile,String msg){
        StringBuilder sql = new StringBuilder("SELECT * FROM SMS_SEND_RECORD WHERE MOBILE=:mobile AND MSG=:msg AND SEND_TIME BETWEEN to_date(:startTime,'yyyy-mm-dd hh24:mi:ss') AND to_date(:endTime,'yyyy-mm-dd hh24:mi:ss')");
        Map<String,Object> params = new HashMap<>();
        params.put("mobile", AesEncryptUtil.aesEncryptScgsi(mobile));
        params.put("msg", msg);
        params.put("startTime", DateUtil.today() + " :00:00:00");
        params.put("endTime", DateUtil.today() + " :23:59:59");
        return (List<SmsSendRecord>) baseDAO.findBySQL_comm(sql.toString(), params, SmsSendRecord.class);
    }

}
