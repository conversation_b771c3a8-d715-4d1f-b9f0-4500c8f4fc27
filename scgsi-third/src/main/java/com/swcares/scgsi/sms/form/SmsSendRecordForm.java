package com.swcares.scgsi.sms.form;

import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.swcares.scgsi.form.PageForm;
import lombok.Getter;
import lombok.Setter;

/**
 * ClassName：com.swcares.scgsi.sms.vo.SmsSendRecordVO <br>
 * Description：短信查询的VO信息<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月13日 下午7:53:24 <br>
 * @version v1.0 <br>
 */
@Getter
@Setter
public class SmsSendRecordForm extends PageForm{

    /**
     * 电话号码
     */
    private String mobile;
    
    /**
     * 短信发送开始时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date sendTimeBegin;
    
    /**
     * 短信发送结束时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date sendTimeEnd;
    
    /** 
     * 短信发送状态 0-成功 1-失败
     */
    private String sendState;

    /**
     * 短信类型   @link SmsConstant
     */
    private String smsType;
    
    /**
     * 航班开始日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date flightDateBegin;
    
    /**
     * 航班结束日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date flightDateEnd;
    
    /**
     *  航班号
     */
    private String flightNo;
    
    
}
