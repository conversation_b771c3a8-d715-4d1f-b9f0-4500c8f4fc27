package com.swcares.scgsi.alarm;

import cn.hutool.core.util.ObjectUtil;
import com.swcares.scgsi.service.impl.FocAnalyze;
import com.swcares.scgsi.sms.api.SmsService;
import com.swcares.scgsi.sms.dao.SmsDaoImpl;
import com.swcares.scgsi.sms.entity.SmsSendRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicInteger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FocAlarm <br>
 * Package：com.swcares.scgsi.alarm <br> 
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年 01月12日 14:41 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class FocAlarm {

    @Autowired
    private FocAnalyze focAnalyze;

    // 创建redis的目录结构，10分钟过期，此定时任务五分钟一次，超过三次发短信报警。
    final String DIRECTORY = "Alarm:FocAnalyze";

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    @Value("${alarm.mobile}")
    private String mobile;

    private final String MSG = "警告:FOC数据接口异常,请及时排查处理!";

    // FOC数据接口异常报警阈值
    static final int threshold = 3;

    @Autowired
    private SmsService smsService;

    @Resource
    private SmsDaoImpl smsDaoImpl;

    /**
     * Title：FOC定时任务入口 <br>
     * Description：修改定时任务的类名和方法名为这个；因为data和third存在相互依赖 <br>
     * author：于琦海 <br>
     * date：2021/1/12 14:49 <br>
     */
    public void init() {
        try {
            focAnalyze.linkFocClient();
        } catch (Exception e) {
            // 发短信
            alarmInfo();
        }
    }

    /**
     * Title：alarmInfo() <br>
     * Description：报警提醒 <br>
     * author：于琦海 <br>
     * date：2021/1/11 9:27 <br>
     * @return void
     */
    public void alarmInfo() {
        // 首先获取报警目录key中的value值是否存在
        Object alarmValue = redisTemplate.opsForValue().get(DIRECTORY);
        if (ObjectUtil.isEmpty(alarmValue)) {
            RedisAtomicInteger redisAtomicInteger = new RedisAtomicInteger(DIRECTORY,redisTemplate.getConnectionFactory());
            int incrementValue = redisAtomicInteger.incrementAndGet();
            redisTemplate.opsForValue().set(DIRECTORY, incrementValue, 10, TimeUnit.MINUTES);
            return;
        }
        // 大于报警阈值，删除key，同时发送短信
        if (Integer.valueOf(String.valueOf(alarmValue)) > threshold) {
            redisTemplate.delete(DIRECTORY);
            List<SmsSendRecord> records = smsDaoImpl.querySmsSendRecord(mobile, MSG);
            if (ObjectUtil.isEmpty(records) || records.size() <= threshold) {
                // 发短信,调用短信接口
                smsService.smsSend(mobile, MSG);
            }
            return;
        }
        AtomicInteger atomicInteger = new AtomicInteger(Integer.valueOf(String.valueOf(alarmValue)));
        int resultValue = atomicInteger.incrementAndGet();
        redisTemplate.opsForValue().set(DIRECTORY, resultValue, 10, TimeUnit.MINUTES);
    }
}
