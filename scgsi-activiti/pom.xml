<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.swcares</groupId>
		<artifactId>scgsi</artifactId>
        <version>1.1.20</version>
	</parent>

	<artifactId>scgsi-activiti</artifactId>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<activiti.version>5.22.0</activiti.version>
	</properties>

	<dependencies>
		<!--其他模块依赖引入 -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-exception</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-common</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.5</version>
		</dependency>
		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-spring-boot-starter-basic</artifactId>
			<version>${activiti.version}</version>
		</dependency>
        <dependency>
            <groupId>com.swcares</groupId>
            <artifactId>scgsi-message</artifactId>
        </dependency>
    </dependencies>
</project>

