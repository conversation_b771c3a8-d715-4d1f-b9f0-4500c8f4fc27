package com.swcares.scgsi.workflow.model.vo;

import cn.hutool.json.JSONObject;
import com.swcares.scgsi.workflow.model.dto.HistoryTaskAuditActivityDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * ClassName：HistoryTaskAuditActivityVO <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/9/30 <br>
 * @version v1.0 <br>
 */
@Data
public class HistoryTaskAuditActivityVO {

    @ApiModelProperty(value = "模型类型")
    private String modelCode;

    @ApiModelProperty(value = "模型名称")
    private String modelName;

    @ApiModelProperty(value = "流程启动id，只存于发起人")
    private String processInstanceId;

    @ApiModelProperty(value = "整个流程的businessKey")
    private String busiKey;

    @ApiModelProperty(value = "整个流程的business数据")
    private JSONObject busiData;


    @ApiModelProperty(value = "历史任务列表")
    private List<HistoryTaskAuditActivityDTO> historyTaskAuditActivityDTOS;
}
