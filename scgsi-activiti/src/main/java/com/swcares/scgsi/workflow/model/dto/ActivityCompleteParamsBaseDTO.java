package com.swcares.scgsi.workflow.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.workflow.model.dto <br>
 * Description：审核处理对象父类 - 其他模块审核继承此对象<br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 09月30日 16:44 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="ActivityCompleteParamsBaseDTO对象", description="审核处理对象父类。")
public class ActivityCompleteParamsBaseDTO {

    @ApiModelProperty(value = "该流程定义下业务数据实例唯一键",required = false)
    private String businessKey;

    @ApiModelProperty(value = "审核状态- 同意AGREE、拒绝REJECT、驳回BACK")
    private String optionCode;

    @ApiModelProperty(value = "审核备注")
    private String remarks;

    @ApiModelProperty(value = "审核taskId")
    private String taskId;

    @ApiModelProperty(value = "审核人userId-前端不用传",hidden = true)
    private String userId;

    @ApiModelProperty(value = "HotelWorkflowBusinessEnum.getKey 业务标记,若多个模块调用传入",hidden = true)
    private String business;

    @ApiModelProperty(value = "下一节点处理人【1.节点没有配置审核人参数，通过界面选择下一节点审批人传。2.驳回到上一审核人传】")
    private List<String> nextAssignee;
}
