package com.swcares.scgsi.workflow.service;

import com.swcares.scgsi.workflow.model.dto.NodeExtVarsDTO;
import com.swcares.scgsi.workflow.model.vo.CurrentTaskActivityVO;

import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.hotel.service <br>
 * Description：NodeNoticeProcessProxy审核节点驱动服务类<br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 09月30日 16:59 <br>
 * @version v1.0 <br>
 */
public interface WorkflowNodeDriveService {

    NodeExtVarsDTO createNodeExtVarsDTO(String business);

    Map<String, Object> triggerNextNodeNotice(String busiKey, NodeExtVarsDTO nodeExtVarsDTO);

    Map<String, Object> triggerNextNodeNotice(CurrentTaskActivityVO currentTaskActivityVO, NodeExtVarsDTO nodeExtVarsDTO);

    Map<String, Object> triggerNextNodeNotice(CurrentTaskActivityVO currentTaskActivityVO,NodeExtVarsDTO nodeExtVarsDTO,Object obj);

    CurrentTaskActivityVO getNextNodeTaskVo(String busiKey);
}
