package com.swcares.scgsi.workflow.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：CurrentTaskWorkflowDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/8/1 10:25
 * @version： v1.0
 */
@Data
public class CurrentTaskActivityDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "流程节点id")
    private String nodeKey;

    @ApiModelProperty(value = "流程节点名称")
    private String nodeName;

    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "是否是结束节点")
    private Boolean isEndActivity;

    @ApiModelProperty(value = "处理岗列表，可能是人，也可能是岗位")
    private List<String> assignees;

//    @ApiModelProperty(value = "上一次该节点的处理人（驳回流程时该节点有值）")
//    private String lastAssignee;
}
