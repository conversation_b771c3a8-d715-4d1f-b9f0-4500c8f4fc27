package com.swcares.scgsi.workflow.service;

import com.swcares.scgsi.workflow.model.dto.BaseQueryParamDTO;
import com.swcares.scgsi.workflow.model.dto.CurrentTaskActivityDTO;
import com.swcares.scgsi.workflow.model.vo.CurrentTaskActivityVO;
import org.activiti.engine.history.HistoricProcessInstance;

import java.util.List;
import java.util.Map;

/**
 * @ClassName：WorkflowTaskService
 * @Description：审核流-任务api
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/28 10:09
 * @version： v1.0
 */
public interface WorkflowTaskService {

    /**
     * Title：complementTask <br>
     * Description： 流程处理 <br>
     * author：傅欣荣 <br>
     * date：2020/2/28 18:10 <br>
     * @param  taskId 任务id
     * @param  userId 任务审批者
     * @param  description 处理批注
     * @param  variables 流程变量
     * @return
     */
    boolean complementTask(String taskId,String userId, String description, Map<String, Object> variables);


    /***
     * 设置下一节点审批人
     * @param businessKey
     * @param nextAssignee
     */
    void addNextAssignee(String businessKey,List<String> nextAssignee);
    /**
     * 获取审核人列表
     * @param
     * @return
     */
    List<String> getAuditorPosition(String taskId);

    /**
     * 获取task审核用户组
     * @param taskId
     * @return
     */
    public List<String> getCandidateUser(String taskId);

    /**
     * 获取task审核用户候选组
     * @param taskId
     * @return
     */
    public List<String> getCandidateGroup(String taskId);

    /***
     * @title currentUserTask
     * @description 查询待办任务
     * <AUTHOR>
     * @date 2022/8/2 14:20
     * @param dto
     * @return com.swcares.scgsi.audit.dto.CurrentTaskActivityVo
     */
    CurrentTaskActivityVO currentUserTask(BaseQueryParamDTO dto);

    /***
     * @title getCurrentTaskWorkflow
     * @description 根据流程实例对象查询任务集合
     * <AUTHOR>
     * @date 2022/9/28 10:13
     * @param historicProcessInstance
     * @return java.util.List<com.swcares.scgsi.workflow.model.dto.CurrentTaskWorkflowDTO>
     */
    List<CurrentTaskActivityDTO> getCurrentTaskActivity(HistoricProcessInstance historicProcessInstance);

    /***
     * @title isProcessEnd
     * @description 流程是否结束
     * <AUTHOR>
     * @date 2022/8/2 14:32
     * @param dto
     * @return boolean
     */
    boolean isProcessEnd(BaseQueryParamDTO dto);




}
