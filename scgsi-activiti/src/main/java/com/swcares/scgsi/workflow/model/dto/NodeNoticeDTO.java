package com.swcares.scgsi.workflow.model.dto;

import lombok.Data;

import java.util.List;

/**
 * ClassName：NodeNoticeDTO <br>
 * Description：节点通知对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/17 <br>
 * @version v1.0 <br>
 */
@Data
public class NodeNoticeDTO {


    /**
     * 节点具体的业务ID
     */
    private String busiKey;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 流程节点id
     */
    private String nodeKey;

    /**
     * 流程节点名称
     */
    private String nodeName;

    /**
     * 处理岗列表，可能是人，也可能是岗位
     */
    private List<String> assignees;

    /**
     * nodeKey 对应的node_business_type
     */
    private String nodeBusinessType;

    /**
     * 流程启动、审批时业务端传给流程引擎的额外参数
     */
    private NodeExtVarsDTO extVars;
    /**
     * 上一个节点审批code
     */
    private String optionCode;

    /**
     * 上一个节点审批人
     */
    private String reviewer;


}
