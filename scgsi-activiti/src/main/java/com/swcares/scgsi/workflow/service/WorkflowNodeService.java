package com.swcares.scgsi.workflow.service;

import com.swcares.scgsi.workflow.model.dto.NodeNoticeDTO;

import java.util.Map;

/**
 * @ClassName：HotelWorkflowService
 * @Description：审核父类
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/27 17:49
 * @version： v1.0
 */
public interface WorkflowNodeService {

    Map<String,Object> submitterWorkflow(NodeNoticeDTO nodeNoticeDTO);

    Map<String,Object> commonWorkflow(NodeNoticeDTO nodeNoticeDTO);

    Map<String,Object> endWorkflow(NodeNoticeDTO nodeNoticeDTO);


}
