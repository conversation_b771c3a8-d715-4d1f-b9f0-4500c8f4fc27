package com.swcares.scgsi.workflow.dao.impl;

import com.swcares.scgsi.audit.vo.AocOndutyUserVo;
import com.swcares.scgsi.audit.vo.OrderAuditRecordVo;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.hum.employee.entity.Employee;
import com.swcares.scgsi.workflow.model.entity.WorkflowAuditorIdInfoDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.workflow.dao.impl <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 10月09日 14:12 <br>
 * @version v1.0 <br>
 */
@Repository
public class WorkflowAuditorIdInfoDaoImpl{
    @Resource
    private BaseDAO baseDAO;


    /**
     * Title：getAocOndutyUserInfo <br>
     * Description： 角色下用户值班人员<br>
     * author：傅欣荣 <br>
     * date：2020/4/10 11:00 <br>
     * @param
     * @return
     */
    public List<Map<String,Object>> getAuditorOndutyByRoleName(String roleId , Boolean isOnDuty){
        if(StringUtils.isEmpty(roleId)){
            return Collections.EMPTY_LIST;
        }
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT to_char(wm_concat(EO.TUNO)) userId");
        sql.append(" FROM ROLE ros LEFT JOIN USER_ROLE uro on ros.id = uro.role_id");
        sql.append(" LEFT JOIN EMPLOYEE eo on EO.ID = uro.EMPLOYEE_id ");
        sql.append(" where  ros.FOUNDER = 'superadmin山东地服超级管理员用户'");
        sql.append(" and ros.STATUS = 1 ");
        sql.append(" and EO.TUNO is not null");
        sql.append(" and ros.name IN ");
        sql.append("  (SELECT REGEXP_SUBSTR('"+roleId+"','[^,]+', 1, LEVEL) FROM DUAL ");
        sql.append(" connect by regexp_substr('"+roleId+"', '[^,]+', 1, level) is not null) ");

        //是否值班
        if(isOnDuty){
            sql.append("  and EO.IS_ON_DUTY = 1");
        }
        return (List<Map<String, Object>>) baseDAO.findBySQL_comm(sql.toString(), new HashMap<>(), null);
    }

    public List<Map<String,Object>> getAuditorOndutyByTuNo(String userId , Boolean isOnDuty){
        if(StringUtils.isEmpty(userId)){
            return Collections.EMPTY_LIST;
        }
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT to_char(wm_concat(EO.TUNO)) userId ");
        sql.append(" FROM EMPLOYEE eo ");
        sql.append(" WHERE 1=1");
        sql.append(" AND EO.TUNO IN ");
        sql.append("  (SELECT REGEXP_SUBSTR('"+userId+"','[^,]+', 1, LEVEL) FROM DUAL ");
        sql.append(" connect by regexp_substr('"+userId+"', '[^,]+', 1, level) is not null) ");
        //是否值班
        if(isOnDuty){
            sql.append("  and EO.IS_ON_DUTY = 1");
        }
        return (List<Map<String, Object>>) baseDAO.findBySQL_comm(sql.toString(), new HashMap<>(), null);
    }

    public List<Map<String,Object>> getAuditorByBusinessKey(String businessKey){
        if(StringUtils.isEmpty(businessKey)){
            return Collections.EMPTY_LIST;
        }
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT to_char(wm_concat(AUDITOR_ID)) userId ");
        sql.append(" FROM WORKFLOW_AUDITOR_ID_INFO  ");
        sql.append(" WHERE BUSINESS_VALUE = '"+businessKey+"'");

        return (List<Map<String, Object>>) baseDAO.findBySQL_comm(sql.toString(), new HashMap<>(), null);

    }

    public List<Map<String,Object>> getEmployeeByTuno(String tuno){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT  ");
        sql.append(" EO.TU_CNAME tuCname ,EO.TUNO tuno,get_user_position(EO.TUNO) job ");
        sql.append(" FROM EMPLOYEE EO ");
        sql.append(" WHERE ");
        sql.append(" EO.TUNO = :tuno ");
        paramsMap.put("tuno",tuno);
        return (List<Map<String, Object>>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, null);

    }







}
