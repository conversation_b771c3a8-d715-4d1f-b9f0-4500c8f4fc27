package com.swcares.scgsi.workflow.model.vo;

import com.swcares.scgsi.workflow.model.dto.CurrentTaskActivityDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：CurrentTaskActivityDto
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/8/1 10:03
 * @version： v1.0
 */
@Builder
@Data
public class CurrentTaskActivityVO {
    @ApiModelProperty(value = "模型类型")
    private String modelCode;

    @ApiModelProperty(value = "模型名称")
    private String modelName;

    @ApiModelProperty(value = "流程启动id，只存于发起人")
    private String processInstanceId;

    @ApiModelProperty(value = "整个流程的businessKey")
    private String busiKey;

    private List<CurrentTaskActivityDTO> currentTaskActivityDTOS;
}
