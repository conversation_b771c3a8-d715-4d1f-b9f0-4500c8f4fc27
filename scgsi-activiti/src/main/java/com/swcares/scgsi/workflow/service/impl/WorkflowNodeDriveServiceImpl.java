package com.swcares.scgsi.workflow.service.impl;

import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.workflow.model.dto.*;
import com.swcares.scgsi.workflow.model.vo.CurrentTaskActivityVO;
import com.swcares.scgsi.workflow.model.vo.NodeNoticeProcessResult;
import com.swcares.scgsi.workflow.proxy.NodeNoticeProcessProxy;
import com.swcares.scgsi.workflow.service.WorkflowNodeDriveService;
import com.swcares.scgsi.workflow.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * ClassName：com.swcares.scgsi.hotel.service.impl <br>
 * Description：审核节点驱动服务类<<br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 09月30日 17:01 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class WorkflowNodeDriveServiceImpl implements WorkflowNodeDriveService {


    @Autowired
    WorkflowService workflowService;
    @Autowired
    NodeNoticeProcessProxy nodeNoticeProcessProxy;


    @Override
    public Map<String, Object> triggerNextNodeNotice(String busiKey,NodeExtVarsDTO nodeExtVarsDTO ) {
        Optional<NodeNoticeProcessResult<Object>> process = this.triggerNextNodeNotices(this.getNextNodeTaskVo(busiKey),nodeExtVarsDTO, null);
        return (Map<String, Object>) process.get().getData();
    }
    @Override
    public Map<String, Object> triggerNextNodeNotice(CurrentTaskActivityVO currentTaskActivityVO,NodeExtVarsDTO nodeExtVarsDTO ) {
        Optional<NodeNoticeProcessResult<Object>> process = this.triggerNextNodeNotices(currentTaskActivityVO, nodeExtVarsDTO,nodeExtVarsDTO.getExtVars());
        return (Map<String, Object>) process.get().getData();
    }

    @Override
    public Map<String, Object> triggerNextNodeNotice(CurrentTaskActivityVO currentTaskActivityVO,NodeExtVarsDTO nodeExtVarsDTO,Object obj) {
        Optional<NodeNoticeProcessResult<Object>> process = this.triggerNextNodeNotices(currentTaskActivityVO, nodeExtVarsDTO,obj);
        return (Map<String, Object>) process.get().getData();
    }


    @Override
    public NodeExtVarsDTO createNodeExtVarsDTO(String businessKey) {
        /**设置系统业务标记*/
        NodeExtVarsDTO nodeExtVars = new NodeExtVarsDTO();
        nodeExtVars.setBusiness(businessKey);
//        nodeExtVars.setExtVars(obj);
        return nodeExtVars;
    }




    private Optional<NodeNoticeProcessResult<Object>> triggerNextNodeNotices(CurrentTaskActivityVO currentTaskActivityVO, NodeExtVarsDTO nodeExtVarsDTO , Object obj) {
        CurrentTaskActivityDTO taskActivityDTO = currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0);
        NodeNoticeDTO nodeNoticeDTO = new NodeNoticeDTO();
        try {
            BeanUtils.copyProperties(nodeNoticeDTO, taskActivityDTO);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error("---【scgsi-hotel】审核方法triggerNextNodeNotice复制VO出错，复制源对象内容为[{}]，异常信息为:{}", taskActivityDTO, e);
        }
        /**设置业务标记和业务数据*/
        nodeExtVarsDTO.setExtVars(obj);
        nodeNoticeDTO.setExtVars(nodeExtVarsDTO);
        nodeNoticeDTO.setBusiKey(currentTaskActivityVO.getBusiKey());
        /**设置上一个节点的审批人、审核状态*/
        if(obj instanceof ActivityCompleteParamsBaseDTO){
            nodeNoticeDTO.setOptionCode(((ActivityCompleteParamsBaseDTO) obj).getOptionCode());
            nodeNoticeDTO.setReviewer(((ActivityCompleteParamsBaseDTO) obj).getUserId());
        }
        return nodeNoticeProcessProxy.process(nodeNoticeDTO);
    }
    /**
     * @title getNextNodeTaskVo
     * @description 查询下一个节点
     * <AUTHOR>
     * @date 2022/8/2 15:21
     * @param busiKey
     * @return com.swcares.scgsi.audit.dto.CurrentTaskActivityVo
     */
    @Override
    public CurrentTaskActivityVO getNextNodeTaskVo(String busiKey) {
        CurrentTaskActivityVO activityVo = workflowService.currentUserTask(BaseQueryParamDTO.builder().businessKey(busiKey).build());
        if(null == activityVo){
            log.info("审核调度--方法[getNextNodeTaskVo]--查下一个节点任务，返回结果:null");
            throw new BusinessException(MessageCode.AUDIT_REPEAT_ERROR.getCode());
        }
        return activityVo;
    }

}
