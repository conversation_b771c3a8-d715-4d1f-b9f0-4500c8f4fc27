package com.swcares.scgsi.workflow.dao;

import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.workflow.model.entity.WorkflowAuditorIdInfoDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.workflow.dao.impl <br>
 * Description：待审核记录表（公共） <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 10月09日 14:12 <br>
 * @version v1.0 <br>
 */

public interface WorkflowAuditorIdInfoDao  extends BaseJpaDao<WorkflowAuditorIdInfoDO,String> {


    int deleteByBusinessValue(@Param("businessValue") String businessValue);


    List<WorkflowAuditorIdInfoDO> findAllByBusinessValue(@Param("businessValue") String businessValue);

}
