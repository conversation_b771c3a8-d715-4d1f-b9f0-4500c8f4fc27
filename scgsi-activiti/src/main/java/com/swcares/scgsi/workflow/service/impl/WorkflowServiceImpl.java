package com.swcares.scgsi.workflow.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.swcares.scgsi.api.ActivitiService;
import com.swcares.scgsi.workflow.common.ProcessParamsConstants;
import com.swcares.scgsi.workflow.model.dto.AuditStartProcessDTO;
import com.swcares.scgsi.workflow.model.dto.BaseQueryParamDTO;
import com.swcares.scgsi.workflow.model.dto.CompleteProcessParamsDTO;
import com.swcares.scgsi.workflow.model.dto.CurrentTaskActivityDTO;
import com.swcares.scgsi.workflow.model.vo.CurrentTaskActivityVO;
import com.swcares.scgsi.workflow.model.vo.HistoryTaskAuditActivityVO;
import com.swcares.scgsi.workflow.model.vo.HistoryTaskAuditBusiInfoVO;
import com.swcares.scgsi.workflow.service.WorkflowHistoryService;
import com.swcares.scgsi.workflow.service.WorkflowService;
import com.swcares.scgsi.workflow.service.WorkflowTaskService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.runtime.ProcessInstance;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：WorkflowServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/28 13:40
 * @version： v1.0
 */
@Slf4j
@Service
public class WorkflowServiceImpl implements WorkflowService{

    @Autowired
    WorkflowTaskService workflowTaskService;
    @Autowired
    ActivitiService activitiService;
    @Autowired
    RuntimeService runtimeService;
    @Autowired
    HistoryService historyService ;
    @Autowired
    private WorkflowHistoryService workflowHistoryService;

    final String USER_ID_PF = "userId:";
    @Override
    public CurrentTaskActivityVO currentUserTask(BaseQueryParamDTO dto) {
        return workflowTaskService.currentUserTask(dto);
    }

    @Override
    public CurrentTaskActivityVO startProcessResult(AuditStartProcessDTO dto) {
        log.info("[scgsi-activiti]--------方法：[startProcessResult]--->>请求参数：[{}]",dto.toString());
        if(ObjectUtil.isEmpty(dto)){
            return null;
        }
        //发起人拼接前缀
        Authentication.setAuthenticatedUserId(USER_ID_PF+dto.getAssignee());

        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(dto.getProcessKey(), dto.getBusinessKey(), dto.getBusinessData());
        //判断下一个节点是否为发起人审批，是自动过审 TODO 发起审核就传参数？
//        completeUserIdTask(dto.getAssignee(),processInstance.getId());

        HistoricProcessInstance historicProcessInstance = historyService.
                createHistoricProcessInstanceQuery().
                processInstanceId(processInstance.getProcessInstanceId())
                .singleResult();
        //查询下一个节点
        List<CurrentTaskActivityDTO> currentTaskActivity = workflowTaskService.getCurrentTaskActivity(historicProcessInstance);
        return CurrentTaskActivityVO.builder()
                .busiKey(historicProcessInstance.getBusinessKey())
                .processInstanceId(historicProcessInstance.getId())
                .modelCode(historicProcessInstance.getProcessDefinitionKey())
                .modelName(historicProcessInstance.getProcessDefinitionName())
                .currentTaskActivityDTOS(currentTaskActivity)
                .build();
    }

    @Override
    public CurrentTaskActivityVO complementTask(CompleteProcessParamsDTO dto) {
        //将optionCode参数添加到业务map中
        Map<String, Object> businessData =new HashMap<>();
        businessData.put(ProcessParamsConstants.OPTION_CODE,dto.getOptionCode());
        if(ObjectUtils.isNotEmpty(dto.getBusinessData())){
            businessData.putAll(dto.getBusinessData());
        }

        //审批人统一增加userId的前缀
        //拼接审核备注 ：审核code+备注
        String comment = dto.getComment()+":"+dto.getOptionCode();
        boolean flag = workflowTaskService.complementTask(dto.getTaskId(),USER_ID_PF+dto.getUserId(),comment,businessData);
        //添加下一节点审批人
        workflowTaskService.addNextAssignee(dto.getBusinessKey(),dto.getNextAssignee());
        return currentUserTask(BaseQueryParamDTO.builder().businessKey(dto.getBusinessKey()).build());
    }

    @Override
    public HistoryTaskAuditActivityVO historyTaskAuditActivity(BaseQueryParamDTO paramDTO) {
        return workflowHistoryService.historyTaskAuditActivity(paramDTO);
    }

    @Override
    public void addNextAssignee(String businessKey, List<String> nextAssignee) {
        workflowTaskService.addNextAssignee(businessKey,nextAssignee);
    }

    @Override
    public List<HistoryTaskAuditBusiInfoVO> historyTaskAuditByAssignee(String assignee) {
        List<HistoricProcessInstance> historicProcessInstances = workflowHistoryService.historyTaskAuditByAssignee(assignee);
        if(!ObjectUtil.isEmpty(historicProcessInstances)){
            List<HistoryTaskAuditBusiInfoVO> busiInfoVOList = new ArrayList<>();
            for(HistoricProcessInstance instance:historicProcessInstances){
                HistoryTaskAuditBusiInfoVO busiInfoVO = new HistoryTaskAuditBusiInfoVO();
                busiInfoVO.setAssignee(assignee);
                busiInfoVO.setBusiKey(instance.getBusinessKey());
                busiInfoVOList.add(busiInfoVO);
            }
            return busiInfoVOList;
        }
        return null;
    }


}
