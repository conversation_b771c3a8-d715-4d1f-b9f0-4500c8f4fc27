package com.swcares.scgsi.workflow.service.impl;

import com.swcares.scgsi.workflow.dao.impl.WorkflowModeInfoDaoImpl;
import com.swcares.scgsi.workflow.model.entity.WorkflowModeNodeInfoDO;
import com.swcares.scgsi.workflow.service.WorkflowModeNodeInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName：ActivitiModeNodeInfoServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/8/2 11:16
 * @version： v1.0
 */
@Service
public class WorkflowModeNodeInfoServiceImpl implements WorkflowModeNodeInfoService {
    @Resource
    private WorkflowModeInfoDaoImpl workflowModeInfoDaoImpl;
    @Override
    public WorkflowModeNodeInfoDO findByProjectAndBusiness(String business, String nodeKey) {
        return workflowModeInfoDaoImpl.findByProjectAndBusiness(business,nodeKey);
    }
}
