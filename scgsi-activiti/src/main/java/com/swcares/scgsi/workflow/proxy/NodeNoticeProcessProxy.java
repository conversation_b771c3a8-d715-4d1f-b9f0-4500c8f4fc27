package com.swcares.scgsi.workflow.proxy;

import cn.hutool.json.JSONUtil;
import com.swcares.scgsi.workflow.model.dto.NodeNoticeDTO;
import com.swcares.scgsi.workflow.model.vo.NodeNoticeProcessResult;
import com.swcares.scgsi.workflow.model.entity.WorkflowModeNodeInfoDO;
import com.swcares.scgsi.workflow.service.WorkflowModeNodeInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * ClassName：NodeNoticeProcessProxy <br>
 * Description：工作流节点处理代理 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/17 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class NodeNoticeProcessProxy {

    final String NODE_DEFAULT_BUSINESS_TYPE = "DEFAULT";
    @Autowired(required=false)
    private List<NodeNoticeProcess> nodeNoticeProcesses;
    @Autowired
    private WorkflowModeNodeInfoService workflowModeNodeInfoService;

    public <T> Optional<NodeNoticeProcessResult<T>> process(NodeNoticeDTO noticeDTO){
        log.info("【scgsi-activiti】收到工作流节点处理调用,param:{}", JSONUtil.toJsonStr(noticeDTO));
        if(CollectionUtils.isEmpty(nodeNoticeProcesses)){
            log.info("【scgsi-activiti】工作流节点处理还没有注册处理器,param:{}", JSONUtil.toJsonStr(noticeDTO));
            return Optional.empty();
        }
        WorkflowModeNodeInfoDO nodeInfoDO=workflowModeNodeInfoService
                .findByProjectAndBusiness(noticeDTO.getExtVars().getBusiness(),noticeDTO.getNodeKey());

        if(nodeInfoDO==null){
            noticeDTO.setNodeBusinessType(NODE_DEFAULT_BUSINESS_TYPE);
        }else{
            noticeDTO.setNodeBusinessType(nodeInfoDO.getNodeBusinessType());
        }

        for(NodeNoticeProcess process: nodeNoticeProcesses){
            if(process.canProcess(noticeDTO)){
                NodeNoticeProcessResult result = process.process(noticeDTO);
                log.info("【scgsi-activiti】工作流节点处理调用完成,param:{},result:{}", JSONUtil.toJsonStr(noticeDTO), JSONUtil.toJsonStr(result));
                return Optional.of(result);
            }
        }
        log.info("【scgsi-activiti】工作流节点处理没要找到处理器,param:{}");
        return Optional.empty();
    }

}
