package com.swcares.scgsi.workflow.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * ClassName：com.swcares.component.workflow.workflow.entity.ModeNodeInfo <br>
 * Description：工作流模型节点配置表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-20 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "WORKFLOW_MODE_NODE_INFO")
@ApiModel(value="ModeNodeInfo对象", description="工作流模型节点配置表")
public class WorkflowModeNodeInfoDO {
    @Id
    @Column(name = "ID")
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty(value = "业务类型；比如apply")
    @Column(name = "BUSINESS")
    private String business;

    @ApiModelProperty(value = "业务所属系统；apply-impl、compensation-impl")
    @Column(name = "PROJECT")
    private String project;

    @ApiModelProperty(value = "模型类型CODE")
    @Column(name = "MODEL_CODE")
    private String modelCode;

    @ApiModelProperty(value = "流程节点ID_对应工作流模型节点ID")
    @Column(name = "NODE_KEY")
    private String nodeKey;

    @ApiModelProperty(value = "流程节点业务类型_业务自由定义")
    @Column(name = "NODE_BUSINESS_TYPE")
    private String nodeBusinessType;


}
