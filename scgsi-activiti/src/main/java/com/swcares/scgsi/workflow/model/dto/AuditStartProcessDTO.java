package com.swcares.scgsi.workflow.model.dto;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;


/**
 * @ClassName：AuditStartProcessDto
 * @Description：审核流程启动参数DTO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/8/1 9:45
 * @version： v1.0
 */
@Data
public class AuditStartProcessDTO extends BaseAuditProcessParamDTO{
    //默认从开始节点流转到提交人节点提交。所需的业务流程变量，可能包含提交人后面的一个节点的信息。
    @ApiModelProperty(value = "流程key",required = false)
    private String processKey;
    @ApiModelProperty(value = "发起人",required = false)
    private String assignee;
//    @ApiModelProperty(value = "流程变量（启动流程所需的业务参数。）",required = false)
//    private JSONObject businessData;


}
