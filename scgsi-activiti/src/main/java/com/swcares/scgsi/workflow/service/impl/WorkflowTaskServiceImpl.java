package com.swcares.scgsi.workflow.service.impl;

import com.alibaba.fastjson.JSON;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.api.ActivitiService;
import com.swcares.scgsi.workflow.model.dto.BaseQueryParamDTO;
import com.swcares.scgsi.workflow.model.dto.CurrentTaskActivityDTO;
import com.swcares.scgsi.workflow.model.vo.CurrentTaskActivityVO;
import com.swcares.scgsi.workflow.service.WorkflowHistoryService;
import com.swcares.scgsi.workflow.service.WorkflowTaskService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ActivitiException;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricIdentityLink;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.runtime.ProcessInstanceQuery;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @ClassName：ActivitiTaskServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/8/1 10:35
 * @version： v1.0
 */
@Slf4j
@Service
public class WorkflowTaskServiceImpl implements WorkflowTaskService {

    @Autowired
    private HistoryService historyService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private ActivitiService activitiService;
    @Autowired
    private WorkflowHistoryService workflowHistoryService;

    @Override
    public boolean complementTask(String taskId, String userId, String description, Map<String, Object> variables){
        if (variables == null) {
            variables = new HashMap<String, Object>();
        }
        // 使用任务id,获取任务对象，获取流程实例id
        Task task= taskService.createTaskQuery().taskId(taskId).singleResult();
        if(task!=null){
            //利用任务对象，获取流程实例id
            String processInstancesId=task.getProcessInstanceId();
            //添加处理批注
            if(StringUtils.isNotBlank(description)) taskService.addComment(taskId,processInstancesId,description);
            //设置当前任务处理的审批人，方便驳回
            if(StringUtils.isNotEmpty(userId)) taskService.setAssignee(taskId,userId);
            //处理任务，传入流程变量
            taskService.complete(taskId,variables);
        }

        //如果task为空；有可能是客户端重复提交
        HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
        if(historicTaskInstance==null){
            log.error("activiti服务方法：{}任务不存在",taskId);
            throw new BusinessException(MessageCode.TASK_NOT_EXIST.getCode());
        }
        return true;
    }

    @Override
    public void addNextAssignee(String businessKey, List<String> nextAssignee) {
        if(StringUtils.isEmpty(businessKey) || ObjectUtils.isEmpty(nextAssignee)){
            log.info("【scgsi-activiti】-方法addNextAssignee ： 参数【businessKey或nextAssignee】不能为空，直接返回");
            return;
        }
        boolean processEnd = this.isProcessEnd(BaseQueryParamDTO.builder().businessKey(businessKey).build());
        if(processEnd){
            return;
        }
        Task task = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
        List<String> candidateUser = getCandidateUser(task.getId());
        List<String> candidateGroup = getCandidateGroup(task.getId());
        taskService.setAssignee(task.getId(),null);
        for(String userId:candidateUser){
            taskService.deleteCandidateUser(task.getId(),userId);
        }
        for(String userId:candidateGroup){
            taskService.deleteCandidateGroup(task.getId(),userId);
        }

        for(String assignee:nextAssignee){
            taskService.addCandidateUser(task.getId(),"userId:"+assignee);
        }
    }


    @Override
    public List<String> getAuditorPosition(String taskId) {
        List<String> assignees=new ArrayList<>();
        List<HistoricIdentityLink> identityLinks = historyService.getHistoricIdentityLinksForTask(taskId);
        if(CollectionUtils.isEmpty(identityLinks)){
            return Collections.EMPTY_LIST;
        }

        for(HistoricIdentityLink historicIdentityLink:identityLinks){
            String userId = historicIdentityLink.getUserId();
            if(StringUtils.isNotEmpty(userId)){
                assignees.add(userId);
            }
            String groupId = historicIdentityLink.getGroupId();
            if(StringUtils.isNotEmpty(groupId)){
                assignees.add(groupId);
            }
        }
        return assignees;
    }

    @Override
    public List<String> getCandidateGroup(String taskId) {
        List<String> assignees=new ArrayList<>();
        List<HistoricIdentityLink> identityLinks = historyService.getHistoricIdentityLinksForTask(taskId);
        if(CollectionUtils.isEmpty(identityLinks)){
            return Collections.EMPTY_LIST;
        }
        for(HistoricIdentityLink historicIdentityLink:identityLinks){
            String groupId = historicIdentityLink.getGroupId();
            if(StringUtils.isNotEmpty(groupId)){
                assignees.add(groupId);
            }
        }
        return assignees;
    }

    @Override
    public List<String> getCandidateUser(String taskId) {
        List<String> assignees=new ArrayList<>();
        List<HistoricIdentityLink> identityLinks = historyService.getHistoricIdentityLinksForTask(taskId);
        if(CollectionUtils.isEmpty(identityLinks)){
            return Collections.EMPTY_LIST;
        }
        for(HistoricIdentityLink historicIdentityLink:identityLinks){
            String userId = historicIdentityLink.getUserId();
            if(StringUtils.isNotEmpty(userId)){
                assignees.add(userId);
            }
        }
        return assignees;
    }

    @Override
    public CurrentTaskActivityVO currentUserTask(BaseQueryParamDTO dto) {
        HistoricProcessInstance historicProcessInstance = workflowHistoryService.historicProcessInstance(dto);
        List<CurrentTaskActivityDTO> currentTaskActivity = getCurrentTaskActivity(historicProcessInstance);

        return CurrentTaskActivityVO.builder()
                .busiKey(historicProcessInstance.getBusinessKey())
                .processInstanceId(historicProcessInstance.getId())
                .modelCode(historicProcessInstance.getProcessDefinitionKey())
                .modelName(historicProcessInstance.getProcessDefinitionName())
                .currentTaskActivityDTOS(currentTaskActivity)
                .build();
    }




    /**
     * @title getCurrentTaskActivity
     * @description 查询流程待处理任务
     * <AUTHOR>
     * @date 2022/8/1 10:17
     * @param historicProcessInstance
     * @return void
     */
    @Override
    public List<CurrentTaskActivityDTO> getCurrentTaskActivity(HistoricProcessInstance historicProcessInstance){
        if(StringUtils.isNotEmpty(historicProcessInstance.getEndActivityId()) || isProcessEnd(BaseQueryParamDTO.builder().processInstanceId(historicProcessInstance.getId()).build())){
            return getEndActivityInfo(historicProcessInstance);
        }else {
            return getTaskActivityInfo(historicProcessInstance);
        }
    }





    private List<CurrentTaskActivityDTO> getEndActivityInfo(HistoricProcessInstance historicProcessInstance){
        List<CurrentTaskActivityDTO> currentTaskActivityDTOS =new ArrayList<>(1);
        String endActivityId = historicProcessInstance.getEndActivityId();
        HistoricActivityInstance historicActivityInstance = historyService.createHistoricActivityInstanceQuery()
                .activityId(endActivityId)
                .processInstanceId(historicProcessInstance.getId())
                .singleResult();

        CurrentTaskActivityDTO currentTaskActivityDTO = new CurrentTaskActivityDTO();
        currentTaskActivityDTO.setTaskId(null);
        currentTaskActivityDTO.setNodeKey(historicActivityInstance.getActivityId());
        currentTaskActivityDTO.setNodeName(historicActivityInstance.getActivityName());
        currentTaskActivityDTO.setAssignees(null);
        currentTaskActivityDTO.setIsEndActivity(true);
        /*HistoricActivityInstance taskInfo = activitiService.getDestinationTaskInfo(historicProcessInstance.getId(),"");
        if(null != taskInfo && StringUtils.isNotEmpty(taskInfo.getAssignee())) {
            currentTaskActivityDTO.setLastAssignee(taskInfo.getAssignee());
        }*/
        currentTaskActivityDTOS.add(currentTaskActivityDTO);

        return currentTaskActivityDTOS;

    }


    private List<CurrentTaskActivityDTO> getTaskActivityInfo(HistoricProcessInstance historicProcessInstance) {
        String processInstanceId=historicProcessInstance.getId();
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();

        List<CurrentTaskActivityDTO> currentTaskActivityDTOS =new ArrayList<>(taskList.size());
        for(Task task:taskList){

            CurrentTaskActivityDTO currentTaskActivityDTO = new CurrentTaskActivityDTO();
            currentTaskActivityDTO.setTaskId(task.getId());
            currentTaskActivityDTO.setNodeKey(task.getTaskDefinitionKey());
            currentTaskActivityDTO.setNodeName(task.getName());
            currentTaskActivityDTO.setAssignees(getAuditorPosition(task.getId()));
            currentTaskActivityDTO.setIsEndActivity(false);
          /*  HistoricActivityInstance taskInfo = .getDestinationTaskInfo(processInstanceId,"");
            if(null != taskInfo && StringUtils.isNotEmpty(taskInfo.getAssignee())) {
                currentTaskActivityDTO.setLastAssignee(taskInfo.getAssignee());
            }*/
            currentTaskActivityDTOS.add(currentTaskActivityDTO);
        }

        return currentTaskActivityDTOS;
    }

    @Override
    public boolean isProcessEnd(BaseQueryParamDTO dto) {
        ProcessInstanceQuery processInstanceQuery = runtimeService//processEngine.getRuntimeService()//表示正在执行的流程实例和执行对象
                .createProcessInstanceQuery();//创建流程实例查询
        if(StringUtils.isNotBlank(dto.getProcessInstanceId())){
            processInstanceQuery.processInstanceId(dto.getProcessInstanceId());
        }
        if(StringUtils.isNotBlank(dto.getBusinessKey())){
            processInstanceQuery.processInstanceBusinessKey(dto.getBusinessKey());
        }
        ProcessInstance pi = processInstanceQuery.singleResult();
        if (null != pi) {
            return false;
        }
        log.info("【scgsi-activiti】-方法isProcessEnd ： 参数【{}】流程实例的任务已经结束", JSON.toJSONString(dto));
        return true;
    }
}
