package com.swcares.scgsi.workflow.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @ClassName：CompleteProcessParamsDto
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/8/3 10:31
 * @version： v1.0
 */
@Data
public class CompleteProcessParamsDTO extends BaseAuditProcessParamDTO{
    @ApiModelProperty(value = "任务执行状态")
    private String optionCode;

    @ApiModelProperty(value = "任务id",required = true)
    private String taskId;

    @ApiModelProperty(value = "审核备注")
    private String comment;

    @ApiModelProperty(value = "当前处理人",required = true)
    private String userId;

    @ApiModelProperty(value = "下一节点处理人【1.节点没有配置审核人参数，通过界面选择下一节点审批人传。2.驳回到指定人，不传默认驳回到上一个处理人】")
    private List<String> nextAssignee;


}
