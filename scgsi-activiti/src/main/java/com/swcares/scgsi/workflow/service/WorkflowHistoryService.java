package com.swcares.scgsi.workflow.service;

import com.swcares.scgsi.workflow.model.dto.BaseQueryParamDTO;
import com.swcares.scgsi.workflow.model.vo.HistoryTaskAuditActivityVO;
import org.activiti.engine.history.HistoricProcessInstance;

import java.util.List;

/**
 * @ClassName：WorkflowProcess
 * @Description：审批流程记录查询
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/28 10:07
 * @version： v1.0
 */
public interface WorkflowHistoryService {
    /**
     * @title queryHistoricByBusinessKey
     * @description 根据业务id查询审批流程
     * <AUTHOR>
     * @date 2022/9/28 10:07
     * @param paramDTO
     * @return void
     */
    HistoryTaskAuditActivityVO historyTaskAuditActivity(BaseQueryParamDTO paramDTO);


    /***
     * 根据参数查询流程实例
     * @param params
     * @return
     */
    public HistoricProcessInstance historicProcessInstance(BaseQueryParamDTO params);

    /***
     * @title historyTaskAuditByAssignee
     * @description 根据用户查询已办任务
     * <AUTHOR>
     * @date 2022/11/11 16:44
     * @param assignee
     * @return java.util.List<org.activiti.engine.history.HistoricProcessInstance>
     */
    List<HistoricProcessInstance> historyTaskAuditByAssignee(String assignee);



}
