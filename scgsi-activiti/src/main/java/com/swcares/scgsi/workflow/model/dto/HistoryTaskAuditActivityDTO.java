package com.swcares.scgsi.workflow.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * ClassName：HsitoryTaskAuditActivityDTO <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/9/30 <br>
 * @version v1.0 <br>
 */
@Data
public class HistoryTaskAuditActivityDTO extends BaseActivityDTO{

    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "任务状态 true已处理 false未处理")
    private Boolean taskStatus;

    @ApiModelProperty(value = "任务创建时间")
    private Date taskCreateTime;

    @ApiModelProperty(value = "任务完成时间")
    private Date taskEndTime;

    @ApiModelProperty(value = "任务审核人")
    private String assignee;

    @ApiModelProperty(value = "任务备注")
    private String comment;

    @ApiModelProperty(value = "任务操作code")
    private String optionCode;

}
