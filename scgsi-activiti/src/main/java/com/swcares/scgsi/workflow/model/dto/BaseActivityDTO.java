package com.swcares.scgsi.workflow.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * ClassName：BaseActivityDTO <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/11 <br>
 * @version v1.0 <br>
 */
@Data
public abstract class BaseActivityDTO {

    @ApiModelProperty(value = "流程节点id")
    private String nodeKey;

    @ApiModelProperty(value = "流程节点名称")
    private String nodeName;

}
