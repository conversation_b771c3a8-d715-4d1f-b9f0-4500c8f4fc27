package com.swcares.scgsi.workflow.dao;

import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.workflow.model.entity.WorkflowAuditorIdInfoDO;
import com.swcares.scgsi.workflow.model.entity.WorkflowModelCodeInfoDO;

/**
 * ClassName：com.swcares.scgsi.workflow.dao.impl <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 10月09日 14:12 <br>
 * @version v1.0 <br>
 */
public interface WorkflowModelCodeInfoDao extends BaseJpaDao<WorkflowModelCodeInfoDO,String> {


    WorkflowModelCodeInfoDO findByBusiness(String business);

}
