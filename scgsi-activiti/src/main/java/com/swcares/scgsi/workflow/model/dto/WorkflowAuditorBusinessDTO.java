package com.swcares.scgsi.workflow.model.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * ClassName：WorkflowAuditorVerfiyDTO <br>
 * Description： 查询verifyAuditorId可以审核的businessValue<br>
 *     查询出传入的businessValues有哪些businessValue是verifyAuditorId有权限审核
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/20 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
public class WorkflowAuditorBusinessDTO {

    /**
     * 业务关键字，比如说为赔偿单ID、申领单ID
     */
    private List<String> businessValues;

    /**
     * 业务类型；比如apply
     */
    private String business;

    /**
     * 业务所属系统；apply-impl、compensation-impl
     */
    private String project;

    /**
     * 审核人用户id
     */
    private String verifyAuditorId;
}
