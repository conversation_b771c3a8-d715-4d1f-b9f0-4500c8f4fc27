package com.swcares.scgsi.workflow.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * ClassName：com.swcares.component.workflow.workflow.entity.ModeNodeInfo <br>
 * Description：工作流模型配置表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-20 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "WORKFLOW_MODEL_CODE_INFO")
@ApiModel(value="WorkflowModelCodeInfoDO对象", description="工作流模型配置表")
public class WorkflowModelCodeInfoDO {
    @Id
    @Column(name = "ID")
    @ApiModelProperty("id")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @ApiModelProperty(value = "业务所属系统；apply-impl、compensation-impl  随便填")
    @Column(name = "PROJECT")
    private String project;

    @ApiModelProperty(value = "业务类型；比如apply")
    @Column(name = "BUSINESS")
    private String business;

    @ApiModelProperty(value = "模型code")
    @Column(name = "MODEL_CODE")
    private String modelCode;


}
