package com.swcares.scgsi.workflow.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * ClassName：com.swcares.component.workflow.workflow.entity.ModeNodeInfo <br>
 * Description：工作流待审核记录表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-20 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "WORKFLOW_AUDITOR_ID_INFO")
@ApiModel(value="WorkflowAuditorIdInfoDO对象", description="工作流待审核记录表")
public class WorkflowAuditorIdInfoDO {
    @Id
    @Column(name = "ID")
    @ApiModelProperty("id")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @ApiModelProperty(value = "业务审核单ID（可以是结算审核id 或保障单id）")
    @Column(name = "BUSINESS_VALUE")
    private String businessValue;

    @ApiModelProperty(value = "业务类型；比如apply")
    @Column(name = "BUSINESS")
    private String business;

    @ApiModelProperty(value = "审核人")
    @Column(name = "AUDITOR_ID")
    private String auditorId;

    @ApiModelProperty(value = "任务id")
    @Column(name = "TASK_ID")
    private String taskId;


}
