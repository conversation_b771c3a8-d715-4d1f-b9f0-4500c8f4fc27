package com.swcares.scgsi.workflow.service;

import com.swcares.scgsi.workflow.model.dto.AuditStartProcessDTO;
import com.swcares.scgsi.workflow.model.dto.BaseQueryParamDTO;
import com.swcares.scgsi.workflow.model.dto.CompleteProcessParamsDTO;
import com.swcares.scgsi.workflow.model.dto.HistoryTaskAuditActivityDTO;
import com.swcares.scgsi.workflow.model.vo.CurrentTaskActivityVO;
import com.swcares.scgsi.workflow.model.vo.HistoryTaskAuditActivityVO;
import com.swcares.scgsi.workflow.model.vo.HistoryTaskAuditBusiInfoVO;
import org.activiti.engine.history.HistoricProcessInstance;

import java.util.List;

/**
 * @ClassName：ActivitiService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/28 9:58
 * @version： v1.0
 */
public interface WorkflowService {



    /***
     * @title startProcessResult
     * @description 发起审核流程
     * <AUTHOR>
     * @date 2022/8/1 9:47
     * @param dto
     * @return org.activiti.engine.runtime.ProcessInstance
     */
    CurrentTaskActivityVO startProcessResult(AuditStartProcessDTO dto);


    /***
     * @title currentUserTask
     * @description 查询下一节点任务【单个】
     * <AUTHOR>
     * @date 2022/8/2 14:44
     * @param dto
     * @return com.swcares.scgsi.audit.dto.CurrentTaskActivityVo
     */
    CurrentTaskActivityVO currentUserTask(BaseQueryParamDTO dto);

    /***
     * @title complementTask
     * @description 流程处理
     * <AUTHOR>
     * @date 2022/8/3 11:14
     * @param dto
     * @return boolean
     */
    CurrentTaskActivityVO complementTask(CompleteProcessParamsDTO dto);


    /***
     * @title complementTask
     * @description 历史审核记录
     * <AUTHOR>
     * @date 2022/10/9 11:14
     * @param paramDTO
     * @return com.swcares.scgsi.workflow.model.vo.HistoryTaskAuditActivityVO
     */
    HistoryTaskAuditActivityVO historyTaskAuditActivity(BaseQueryParamDTO paramDTO);


    /***
     * 设置下一节点审批人
     * @param businessKey
     * @param nextAssignee
     */
    void addNextAssignee(String businessKey, List<String> nextAssignee);


    /***
     * @title historyTaskAuditByAssignee
     * @description 查询已办任务
     * <AUTHOR>
     * @date 2022/11/11 16:46
     * @param assignee
     * @return java.util.List<com.swcares.scgsi.workflow.model.dto.HistoryTaskAuditActivityDTO>
     */
    List<HistoryTaskAuditBusiInfoVO> historyTaskAuditByAssignee(String assignee);
}
