package com.swcares.scgsi.workflow.proxy;

import com.swcares.scgsi.workflow.model.dto.NodeNoticeDTO;
import com.swcares.scgsi.workflow.model.vo.NodeNoticeProcessResult;

/**
 * ClassName：com.swcares.component.workflow.NodeNoticeProcess <br>
 * Description：工作流节点处理抽象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/17 <br>
 * @version v1.0 <br>
 */

public interface NodeNoticeProcess {
    NodeNoticeProcessResult process(NodeNoticeDTO noticeDTO);
    boolean canProcess(NodeNoticeDTO noticeDTO);
}
