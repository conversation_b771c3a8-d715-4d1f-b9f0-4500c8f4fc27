package com.swcares.scgsi.workflow.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.workflow.model.entity.WorkflowModeNodeInfoDO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName：ActivitiModeInfoDaoImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/8/2 11:21
 * @version： v1.0
 */
@Repository
public class WorkflowModeInfoDaoImpl {
    @Resource
    private BaseDAO baseDAO;

    public WorkflowModeNodeInfoDO findByProjectAndBusiness(String business, String nodeKey){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT * from WORKFLOW_MODE_NODE_INFO ");
        sql.append(" WHERE BUSINESS =  :business");
        sql.append(" AND NODE_KEY = :node_key  ");
        paramsMap.put("business",business);
        paramsMap.put("node_key",nodeKey);
        return baseDAO.findOneBySql(sql.toString(), paramsMap, WorkflowModeNodeInfoDO.class);
    }
}
