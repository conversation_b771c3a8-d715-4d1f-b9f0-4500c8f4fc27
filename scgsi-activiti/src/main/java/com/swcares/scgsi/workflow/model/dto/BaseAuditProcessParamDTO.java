package com.swcares.scgsi.workflow.model.dto;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @ClassName：AuditProcessParamSuperDto
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/8/2 13:47
 * @version： v1.0
 */
@Data
public class BaseAuditProcessParamDTO {

    @ApiModelProperty(value = "该流程定义下业务数据实例唯一键",required = false)
    private String businessKey;

    @ApiModelProperty(value = "流程变量（启动流程所需的业务参数。）",required = false)
    private Map<String, Object> businessData;

    @ApiModelProperty(value = "流程启动、审批时业务端传给流程引擎的额外参数，通知业务端时会回传这个参数",required = false)
    private JSONObject extVars;
}
