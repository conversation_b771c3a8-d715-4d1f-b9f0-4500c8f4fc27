package com.swcares.scgsi.workflow.enums;

/**
 * @ClassName：WorkflowStatusEnum
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/28 16:17
 * @version： v1.0
 */
public enum  WorkflowStatusEnum {

    //同意AGREE、拒绝REJECT、驳回BACK
    SUBMIT("SUBMIT","发起流程"),
    AGREE("AGREE", "同意"),
    DISAGREE("REJECT", "不同意"),
    REJECT("BACK", "驳回");

    private WorkflowStatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static WorkflowStatusEnum build(String key) {
        return build(key, true);
    }

    public static WorkflowStatusEnum build(String key, boolean throwEx) {
        WorkflowStatusEnum typeEnum = null;
        for (WorkflowStatusEnum element : WorkflowStatusEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + WorkflowStatusEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
