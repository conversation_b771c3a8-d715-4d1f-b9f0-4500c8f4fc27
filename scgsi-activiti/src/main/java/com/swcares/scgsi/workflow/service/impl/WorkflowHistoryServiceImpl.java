package com.swcares.scgsi.workflow.service.impl;

import com.swcares.exception.BusinessException;
import com.swcares.scgsi.workflow.model.dto.BaseQueryParamDTO;
import com.swcares.scgsi.workflow.model.dto.HistoryTaskAuditActivityDTO;
import com.swcares.scgsi.workflow.model.vo.HistoryTaskAuditActivityVO;
import com.swcares.scgsi.workflow.service.WorkflowHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricProcessInstanceQuery;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricTaskInstanceQuery;
import org.activiti.engine.impl.persistence.entity.CommentEntity;
import org.activiti.engine.task.Comment;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName：WorkflowProcessImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/28 13:38
 * @version： v1.0
 */
@Service
@Slf4j
public class WorkflowHistoryServiceImpl implements WorkflowHistoryService {
    @Autowired
    private HistoryService historyService;
    @Autowired
    private TaskService taskService;

    @Override
    public HistoryTaskAuditActivityVO historyTaskAuditActivity(BaseQueryParamDTO paramDTO) {
        HistoricProcessInstance historicProcessInstance = historicProcessInstance(paramDTO);
        HistoryTaskAuditActivityVO historyTaskAuditActivityVO=new HistoryTaskAuditActivityVO();
        historyTaskAuditActivityVO.setHistoryTaskAuditActivityDTOS(getHistoryTaskAuditActivity(historicProcessInstance));
        historyTaskAuditActivityVO.setBusiKey(historicProcessInstance.getBusinessKey());
        historyTaskAuditActivityVO.setProcessInstanceId(historicProcessInstance.getId());
        historyTaskAuditActivityVO.setModelCode(historicProcessInstance.getProcessDefinitionKey());
        historyTaskAuditActivityVO.setModelName(historicProcessInstance.getProcessDefinitionName());
        return historyTaskAuditActivityVO;
    }
    @Override
    public HistoricProcessInstance historicProcessInstance(BaseQueryParamDTO params) {
        HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery();

        if(StringUtils.isNotEmpty(params.getBusinessKey())){
            historicProcessInstanceQuery.processInstanceBusinessKey(params.getBusinessKey());
        }
        if(StringUtils.isNotEmpty(params.getProcessInstanceId())){
            historicProcessInstanceQuery.processInstanceId(params.getProcessInstanceId());
        }

        List<HistoricProcessInstance> historicProcessInstances = historicProcessInstanceQuery.list();
        if(CollectionUtils.isEmpty(historicProcessInstances)){
            log.error("审核--->>方法：[historicProcessInstance] 异常原因：流程实例不存在！！");
            throw new BusinessException("流程实例不存在");
        }
        if(historicProcessInstances.size()>1){
            log.error("审核--->>方法：[historicProcessInstance] 异常原因：流程实例存在多个,请检查查询参数！！");
            throw new BusinessException("流程实例存在多个,请检查查询参数");
        }

        return historicProcessInstances.get(0);
    }

    @Override
    public List<HistoricProcessInstance> historyTaskAuditByAssignee(String assignee) {
        List<HistoricProcessInstance> list = new ArrayList<>();
        HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery();
        historicTaskInstanceQuery.taskAssignee("userId:" + assignee);
        historicTaskInstanceQuery.orderByHistoricTaskInstanceEndTime().desc();
        List<HistoricTaskInstance> instanceList = historicTaskInstanceQuery.list().stream().filter(d -> !"submitter".equals(d.getTaskDefinitionKey())).collect(Collectors.toList());
        ;
        HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery();
        for (HistoricTaskInstance instance : instanceList) {
            historicProcessInstanceQuery.processInstanceId(instance.getProcessInstanceId());
            List<HistoricProcessInstance> processInstances = historicProcessInstanceQuery.list();
            if (processInstances.size() > 0) {
                list.add(processInstances.get(0));
            }
        }
        return list;
    }


    public List<HistoryTaskAuditActivityDTO> getHistoryTaskAuditActivity(HistoricProcessInstance historicProcessInstance) {

        String processInstanceId = historicProcessInstance.getId();
        List<HistoricTaskInstance> taskInstances = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByTaskCreateTime()
                .asc()
                .list();

        if (CollectionUtils.isEmpty(taskInstances)) {
            return Collections.EMPTY_LIST;
        }

        List<Comment> processInstanceComments = taskService.getProcessInstanceComments(processInstanceId);
        Map<String, List<Comment>> processInstanceCommentMp =CollectionUtils.isNotEmpty(processInstanceComments)?
                processInstanceComments.stream().collect(Collectors.groupingBy(Comment::getTaskId)):Collections.EMPTY_MAP;

        List<HistoryTaskAuditActivityDTO> historyTaskAuditActivityDTOS=new ArrayList<>(taskInstances.size());
        Set<String> taskIds = taskInstances.stream().map(HistoricTaskInstance::getId).collect(Collectors.toSet());
        for(HistoricTaskInstance taskInstance:taskInstances){
            HistoryTaskAuditActivityDTO taskAuditActivity=new HistoryTaskAuditActivityDTO();

            taskAuditActivity.setAssignee(taskInstance.getAssignee());
            taskAuditActivity.setTaskId(taskInstance.getId());
            taskAuditActivity.setTaskCreateTime(taskInstance.getCreateTime());
            taskAuditActivity.setTaskEndTime(taskInstance.getEndTime());
            taskAuditActivity.setNodeKey(taskInstance.getTaskDefinitionKey());
            taskAuditActivity.setNodeName(taskInstance.getName());
            List<Comment> comments = processInstanceCommentMp.get(taskInstance.getId());
            if(CollectionUtils.isNotEmpty(comments)){
                CommentEntity comment = (CommentEntity) comments.get(0);
                String fullCommentMessage = comment.getMessage();
                taskAuditActivity.setComment(
                       ("null".equals(fullCommentMessage.substring(0,fullCommentMessage.indexOf(":"))) ||
                    StringUtils.isEmpty(fullCommentMessage.substring(0,fullCommentMessage.indexOf(":")))) ?
                               null : fullCommentMessage.substring(0,fullCommentMessage.indexOf(":"))
                );
                taskAuditActivity.setOptionCode(fullCommentMessage.substring(fullCommentMessage.indexOf(":")+1));
            }
            taskAuditActivity.setTaskStatus(Boolean.FALSE);
            if(null != taskInstance.getEndTime()){
                taskAuditActivity.setTaskStatus(Boolean.TRUE);
            }
            historyTaskAuditActivityDTOS.add(taskAuditActivity);
        }
        return historyTaskAuditActivityDTOS;

    }

}
