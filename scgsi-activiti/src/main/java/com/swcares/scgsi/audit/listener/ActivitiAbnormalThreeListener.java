package com.swcares.scgsi.audit.listener;

import com.swcares.scgsi.audit.dao.impl.OrderAuditDaoImpl;
import com.swcares.scgsi.audit.enums.AuditNodeAssigneeEnum;
import com.swcares.scgsi.audit.vo.AocOndutyUserVo;
import com.swcares.scgsi.base.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

/**
 * ClassName：com.swcares.scgsi.listener <br>
 * Description：赔偿金额审核流程一级审核监听器（分配一级审核审核人）<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月06日 14:37 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class ActivitiAbnormalThreeListener implements TaskListener {


    private static final long serialVersionUID = -5567342136440785126L;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateTask delegateTask) {

        OrderAuditDaoImpl orderAuditDaoImpl = SpringUtil.getBean(OrderAuditDaoImpl.class);
        log.info("-------------->>>审核-执行【异常行李总经理】节点监听器-正在执行,taskId{},流程实例key{}",delegateTask.getId(),delegateTask.getProcessInstanceId());

        //根据航延补偿业务查询 审批人员角色为AOC值班人员
        AocOndutyUserVo onDutyUser = orderAuditDaoImpl.getRoleOndutyUserInfo(AuditNodeAssigneeEnum.GENERAL_MANAGER_NODE_ASSIGNEE.getRole()
                ,AuditNodeAssigneeEnum.GENERAL_MANAGER_NODE_ASSIGNEE.getOnDuty());
        log.info("-------------->>>审核-执行【异常行李总经理】监听器-【查询审核人员】结果：{}",onDutyUser.toString());
        if(ObjectUtils.isEmpty(onDutyUser.getUserId())) return;
        //添加审批的人员，以下任何一人通过即可进入下一环节
        String userId = onDutyUser.getUserId();
        String[] empLoyees =userId.split(",");
        delegateTask.addCandidateUsers(Arrays.asList(empLoyees));
    }
}