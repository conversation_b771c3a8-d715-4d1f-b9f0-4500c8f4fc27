package com.swcares.scgsi.audit.service;

import com.swcares.scgsi.audit.dto.*;
import com.swcares.scgsi.audit.vo.CompensationProgressVo;
import com.swcares.scgsi.audit.vo.OrderAuditProgressVo;
import com.swcares.scgsi.audit.vo.OrderAuditRecordVo;
import com.swcares.scgsi.base.QueryResults;
import org.activiti.engine.delegate.DelegateTask;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.audit.service <br>
 * Description：补偿申请单审核 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月11日 10:09 <br>
 * @version v1.0 <br>
 */
public interface OrderAuditService {


    /**
     * Title：deployAuditProcess <br>
     * Description： 部署审核流程文件<br>
     * author：傅欣荣 <br>
     * date：2020/3/11 15:03 <br>
     * @param
     * @return
     */
    boolean deployAuditProcess(String delployName, String bpmnFielUrl, String pngFielUrl);

    /**
     * Title：launchAuditProcess <br>
     * Description：发起审核流程<br>
     * author：傅欣荣 <br>
     * date：2020/3/11 15:02 <br>
     * @param  userId 发起人, actKey 审核流程key，不传入值则根据用户id判断进入对应的审核流程，
     * @param  orderId 服务单id ，handleUser 发起人aoc人员，该值不为空，值班经理人员，以逗号分割
     * @return void
     */
    void launchAuditProcess(String userId, String actKey , String orderId, String handleUser);


    /**
     * Title：launchAuditProcess <br>
     * Description：发起审核流程（异常）<br>
     * author：傅欣荣 <br>
     * date：2020/3/11 15:02 <br>
     * @param  userId 发起人, actKey 审核流程key，不传入值则根据用户id判断进入对应的审核流程，
     * @param  orderId 服务单id ，handleUser 发起人aoc人员，该值不为空，值班经理人员，以逗号分割
     *                 type 赔偿单类型 、amount 赔偿单金额
     * @return void
     */
    void launchAuditProcess(String userId, String actKey , String orderId, String handleUser,String type,String amount);

    /**
     * Title：handleAuditProcess <br>
     * Description： 审核提交，处理任务并保存业务数据{审批记录}<br>
     * author：傅欣荣 <br>
     * date：2020/3/11 15:01 <br>
     * @param  orderAuditPd
     * @return void
     */
    void handleAuditProcess(OrderAuditProcessDto orderAuditPd);

    /**
     * Title：FindOrderAuditRecord <br>
     * Description： 根据服务单id 查审核记录详情<br>
     * author：傅欣荣 <br>
     * date：2020/3/11 15:00 <br>
     * @param  orderId 服务单id
     * @return java.util.List<com.swcares.scgsi.audit.vo.OrderAuditRecordVo>
     */
    List<OrderAuditRecordVo> findOrderAuditRecord(String orderId);


    /**
     * Title：findAuditProcessByUserId <br>
     * Description：根据用户id查询 待审核任务{航班信息+流程任务信息}<br>
     * author：傅欣荣 <br>
     * date：2020/3/12 11:26 <br>
     * @param  orderAuditDataDto
     * @return void
     */
    QueryResults findAuditProcessByUserId(OrderAuditDataDto orderAuditDataDto) ;



    /**
     * Title：queryOrderAuditList <br>
     * Description： H5- 赔偿单审核列表查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/26 10:06 <br>
     * @param
     * @return
     */
    QueryResults queryOrderAuditList(OrderAuditQueryParams orderAuditQueryParams);

    /**
     * Title： getAuditNode<br>
     * Description：  根据taskid 获取当前节点 2-aoc 3值班经理 4异常行李值班主任，5异常行李经理，6异常行李总经理 <br>
     * author：傅欣荣 <br>
     * date：2020/4/15 10:28 <br>
     * @param
     * @return
     */
    Map<String, Object> getAuditNode(String taskId);

    /**
     * Title： getIsAocByUserId<br>
     * Description：  查询userId 是否为aoc人员  0 存在，1不存在<br>
     * author：傅欣荣 <br>
     * date：2020/4/15 10:28 <br>
     * @param
     * @return
     */
    Map<String, Object> getIsAocByUserId(String userId);


    /**
     * Title： getOrderPaxInfo<br>
     * Description：赔偿单审核-旅客列表<br>
     * author：傅欣荣 <br>
     * date：2020/4/7 15:56 <br>
     * @param
     * @return
     */
    Map<String, Object> getOrderPaxInfo(AuditOrderPaxInfo auditOrderPaxInfo);

    /**
     * Title：getAuditProgressInfo <br>
     * Description： H5-【通用】 赔偿单-审核进度展示数据查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/26 18:41 <br>
     * @param
     * @return
     */
    List<OrderAuditProgressVo> getAuditProgressInfo(String orderId);

    /**
     * Title：queryCpsProgressQuery <br>
     * Description： 赔偿进度-信息查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/26 19:56 <br>
     * @param
     * @return
     */
    CompensationProgressVo queryCpsProgressQuery(String orderId);


    /**
     * Title： verifyIsShowReEdit<br>
     * Description： 判断是否显示重新编辑按钮<br>
     * author：傅欣荣 <br>
     * date：2020/6/3 14:04 <br>
     * @param
     * @return 1
     */
    String verifyIsShowReEdit(String orderId,String createUser,String status);

    /**
     * Title： getDeptUserInfo<br>
     * Description： 获取用户信息 <br>
     * author：傅欣荣 <br>
     * date：2020/4/9 18:55 <br>
     * @param
     * @return
     */
    QueryResults getDeptUserInfo(DeptUserInfoDto deptUserInfoDto);

    /**
     * Title：sendAuditMessage <br>
     * Description： 发送审核消息 <br>
     * author：傅欣荣 <br>
     * date：2020/4/10 14:29 <br>
     * @param
     * @return
     */
    void sendAuditMessage(String orderId,String userId);

    /**
     * Title： sendAuditResultMessage<br>
     * Description： 审核结果-通知到发起人<br>
     * author：傅欣荣 <br>
     * date：2020/4/20 15:00 <br>
     * @param
     * @return
     */
    void sendAuditResultMessage(String orderId);

    /**
     * Title：getUserTaskIdByOrderId <br>
     * Description： WEB- 审核详情界面- 根据用户id 、赔偿单id查询流程审核任务id<br>
     * author：傅欣荣 <br>
     * date：2020/5/20 14:08 <br>
     * @param
     * @return
     */
    String getUserTaskIdByOrderId(String orderId,String userId);


    /**
     * Title：chcekRejectaddHandler <br>
     * Description： 验证是否为驳回状态，并赋值节点处理人（上一级原审批人）<br>
     * author：傅欣荣 <br>
     * date：2020/6/26 14:50 <br>
     * @param
     * @return
     */
    boolean chcekRejectaddHandler(DelegateTask delegateTask) throws Exception;

    /**
     * Title：getAuditKeyByServiceCity <br>
     * Description：根据类型服务航站，查询对应审核流程key（目前行李单适用）<br>
     * author：傅欣荣 <br>
     * date：2020/8/27 11:21 <br>
     * @param
     * @return
     */
    String getAuditKeyByServiceCity(String type,String serviceCity);
}
