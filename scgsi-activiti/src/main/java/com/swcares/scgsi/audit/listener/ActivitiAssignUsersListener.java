package com.swcares.scgsi.audit.listener;

import com.swcares.scgsi.audit.dao.impl.OrderAuditDaoImpl;
import com.swcares.scgsi.audit.service.OrderAuditService;
import com.swcares.scgsi.audit.vo.AocOndutyUserVo;
import com.swcares.scgsi.base.SpringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.Expression;
import org.activiti.engine.delegate.TaskListener;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

/**
 * ClassName：com.swcares.scgsi.listener <br>
 * Description：审核流程监听器-主要功能：分配userTask执行人<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月06日 14:37 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class ActivitiAssignUsersListener implements TaskListener {

    private static final long serialVersionUID = 8593338519734684918L;
    //执行人角色参数
    private Expression assigneeUser;
    //值班参数
    private Expression onDuty;

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateTask delegateTask) {
        String assigneeUserRole = (String) assigneeUser.getValue(delegateTask);
        Boolean isOnDuty =false;
        if(null != onDuty){
            if(ObjectUtils.isNotEmpty(onDuty.getValue(delegateTask))){
                isOnDuty = Boolean.parseBoolean((String) onDuty.getValue(delegateTask)) ;
             }
        }

        log.info("-------------->>>执行【审核节点TaskListener】监听器-【赋予节点审批用户】-->流程实例id[{}],审核角色[{}],值班条件[{}]",
                delegateTask.getProcessInstanceId(),assigneeUserRole,isOnDuty);

        if(ObjectUtils.isEmpty(assigneeUserRole)) return;
        OrderAuditDaoImpl orderAuditDaoImpl = SpringUtil.getBean(OrderAuditDaoImpl.class);
        OrderAuditService orderAuditService = SpringUtil.getBean(OrderAuditService.class);
        //判断驳回状态并添加处理人
        if(orderAuditService.chcekRejectaddHandler(delegateTask)) return;
        //根据角色、值班条件查询审批执行人集合
        AocOndutyUserVo onDutyUser = orderAuditDaoImpl.getRoleOndutyUserInfo(assigneeUserRole,isOnDuty);
        log.info("-------------->>>执行【审核节点TaskListener】监听器-【赋予节点审批用户】-->流程实例id[{}],审核角色[{}],值班条件[{}],审批执行人用户集合[{}]",
                delegateTask.getProcessInstanceId(),assigneeUserRole,isOnDuty,onDutyUser.toString());
        if(ObjectUtils.isEmpty(onDutyUser.getUserId())) return;
        //添加审批的人员，以下任何一人通过即可进入下一环节
        String userId = onDutyUser.getUserId();
        String[] empLoyees =userId.split(",");
        delegateTask.addCandidateUsers(Arrays.asList(empLoyees));
    }
}