package com.swcares.scgsi.audit.dto;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.dto <br>
 * Description：申请单审核提交 - 与前端交互实体 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月11日 13:21 <br>
 * @version v1.0 <br>
 */
@Data
public class OrderAuditProcessDto {

    /**
     *  服务单id
     */
    private String orderId;

    /**
     * 流程任务id
     */
    private String taskId;

    /**
     *  审核意见 0通过、1不同意 2驳回
     */
    private String opinion;

    /**
     *  审核备注
     */
    private String remark;

    /**
     *  审批人id
     */
    private String auditor;

    /**
     *  审批节点 1流程发起 2AOC审核 3值班审核 4异常行李值班主任，5异常行李经理，6异常行李总经理
     */
    private String processNode;

    /**
     *  处理人集合 【集合:分两种类型用户和部门，目前只能选用户】
     */
    private String handleUser;

    /**
     * 赔偿金额
     */
    private String amount;

}
