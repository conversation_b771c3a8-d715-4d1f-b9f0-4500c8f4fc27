package com.swcares.scgsi.audit.service.impl;

import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.api.ActivitiService;
import com.swcares.scgsi.api.ProcessService;
import com.swcares.scgsi.audit.constant.DpOrderConstant;
import com.swcares.scgsi.audit.dao.OrderActMiddleDao;
import com.swcares.scgsi.audit.dao.OrderAuditDao;
import com.swcares.scgsi.audit.dao.impl.OrderAuditDaoImpl;
import com.swcares.scgsi.audit.dto.*;
import com.swcares.scgsi.audit.entity.OrderActMiddle;
import com.swcares.scgsi.audit.entity.OrderAudit;
import com.swcares.scgsi.audit.enums.AuditProcessType;
import com.swcares.scgsi.audit.enums.AuditStatusEnum;
import com.swcares.scgsi.audit.service.OrderAuditService;
import com.swcares.scgsi.audit.vo.*;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.userenum.UserEnum;
import com.swcares.scgsi.dict.dao.SysDictDataDao;
import com.swcares.scgsi.dict.entity.SysDictDataInfo;
import com.swcares.scgsi.message.common.model.form.MessageSendForm;
import com.swcares.scgsi.message.common.msgenum.MessageTypeEnum;
import com.swcares.scgsi.message.service.MessageService;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.util.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.runtime.ProcessInstance;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.text.MessageFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.audit.service.impl <br>
 * Description：审核模块 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月11日 14:56 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class OrderAuditServiceImpl implements OrderAuditService {

    @Autowired
    private ActivitiService activitiService;
    @Autowired
    private ProcessService processService;
    @Resource
    private OrderActMiddleDao orderActMiddleDao;
    @Resource
    private OrderAuditDaoImpl orderAuditDaoImpl;
    @Resource
    private OrderAuditDao orderAuditDao;
    @Resource
    private MessageService messageService;
    @Resource
    private OrderAuditService orderAuditService;

    @Resource
    private RedisService redisService;

    @Autowired
    SysDictDataDao sysDictDataDao;

    //赔偿单审批状态
    public static final String AUDIT_STATUS_RESULT_PREFIX = "AUDIT_STATUS_RESULT";
    //审批状态为驳回
    public static final String AUDIT_STATUS_REJECT = "AUDIT_STATUS_REJECT";
    //赔偿单审批过期时间
    public static final int AUDIT_EXPIRE_DATE = 180;


    /*流程节点 1 发起*/
    private static final String PROCESS_START="1";
    /*流程节点 2 AOC审核*/
    private static final String PROCESS_AOC="2";
    /*流程节点 3 值班审核*/
    private static final String PROCESS_TWO="3";
    /* 处理状态 1已处理 */
    private static final String STATIC_YES="1";

    /*==========审核查询状态===========*/
    /*全部*/
    private static final String ALL="0";
    /*待审核*/
    private static final String NOT_AUDIT="1";
    /*已审核*/
    private static final String AUDIT="2";

    /*===========审核流程=========*/
    /*AOC二级审核流程key*/
    private static final String AOC_SECOND_LEVEL = AuditProcessType.LAUNCH_PROCESS_KEY_AOC2.getNode();
    /*AOC一级审核流程key,AOC发起*/
    private static final String AOC_ONE_LEVEL = AuditProcessType.LAUNCH_PROCESS_KEY_AOC1.getNode();

    /*根据判断赔偿金额进入审核流程（异常行李）*/
    private static final String AMOUNT_LEVEL = AuditProcessType.LAUNCH_PROCESS_KEY_AMOUNT.getNode();


    /*H5消息跳转- 审核结果 不正常航班-跳转至赔偿单详情页*/
    private static final String MOBILE_URL_IRREGULAR = "/passengerCompensation/ListOfCompensation/detailsOfCompensation?orderId={0}&payType={1}";

    /*H5消息跳转- 审核结果 异常行李-跳转至异常行李事故详情页*/
    private static final String MOBILE_URL_ABNORMAL_BAGGAGE= "/examine/accidentDetail?accidentId={0}&payType={1}";

    /*H5消息跳转- 审核结果 旅客超售-跳转至超售事故单详情页*/
    private static final String MOBILE_URL_OVERBOOK = "/examine/accidentDetail?orderId={0}&payType={1}";


    //状态6-驳回
    private static final String ORDER_STATUS_REJECT = "6";


    @Override
    public boolean deployAuditProcess(String delployName, String bpmnFielUrl, String pngFielUrl){
        return activitiService.delployFlow(delployName,bpmnFielUrl,pngFielUrl);
    }

    @SneakyThrows
    @Override
    @Transactional
    public void launchAuditProcess(String userId,String actKey ,String orderId,String handleUser) {
        this.launchAuditProcess(userId,actKey,orderId,handleUser,null,null);

    }
    @Override
    @Transactional
    public void launchAuditProcess(String userId,String actKey ,String orderId,String handleUser,String type,String amount){
        log.info("-------->>>审核-方法【发起审核】参数 ：发起人{}，流程key{}，赔偿单id{}，值班经理{},类型{}，金额{}",userId,actKey,orderId,handleUser,type,amount);
        //校验发起审核参数，并判断是否为重复发起，并推送一级审核消息
        if(this.checkLaunchAuditData(userId,actKey,orderId,handleUser,amount)) return;

        String processKey = actKey;
        if(ObjectUtils.isEmpty(actKey)){
            //判断用户角色 获取审核流程key 字典表维护key用户角色 value 流程key
            processKey = getLaunchProcessKey(userId,type);
        }
        Map<String, Object> data = new HashMap<>();
        data.put("submitter",userId);
        if(ObjectUtils.isNotEmpty(handleUser)){
            handleUser = this.getDecrypHandler(handleUser);
            data.put("handleUser",handleUser);
        }
        if(ObjectUtils.isNotEmpty(amount)){
            data.put("amount",amount);
        }

        log.info("-------->>>审核-方法【发起审核】参数 ：发起人{}，赔偿单id{}，审核流程key{}，流程变量：{}",userId,orderId,processKey,data.toString());
        ProcessInstance processInstance = activitiService.startProcessResult(userId,processKey,true,data);
        log.info("-------->>>审核-方法【发起审核结果】参数 ：发起人{}，赔偿单id{}，ProcessInstance{}",userId,orderId,processInstance);

        if(ObjectUtils.isEmpty(processInstance)){
            log.error("-------->>>审核-【发起审核】，异常! 赔偿单id{}，ProcessInstance{}",orderId,processInstance);
            throw new BusinessException(MessageCode.LAUNCH_PROCESS_ERROR.getCode(),new String[]{orderId});
        }
        log.info("-------->>>审核-方法【发起审核-保存审核业务数据】参数 ：发起人{}，赔偿单id{}，ProcessInstance{}",userId,orderId,processInstance);


        //保存审核记录
        this.saveAuditInfo(orderId,processInstance,userId);

        //推送审核消息（下一级审核者）
        this.pushAuditMessage(processInstance.getProcessInstanceId(),orderId,handleUser);

    }


    /**
     * Title： checkLaunchAuditData <br>
     * Description： 发起审核流程、进行校验。判断是否为重复发起<br>
     * author：傅欣荣 <br>
     * date：2020/6/15 15:48 <br>
     * @param
     * @return
     */
    public boolean checkLaunchAuditData(String userId,String actKey ,String orderId,String handleUser,String amount){
        //判断orderId是否已发起过审核流程
        OrderAuditResult resultT = orderAuditDaoImpl.getTaskIdByOrderId(orderId,userId);
        log.info("-------->>>审核-方法【发起审核-查询用户当前订单的流程任务】参数 ：发起人{}，赔偿单id{}，查询结果：{}",userId,orderId,resultT.toString());
        String taskId = resultT.getTaskId();
        if(ObjectUtils.isNotEmpty(taskId)){
            log.info("-------->>>审核-方法【发起审核-查询用户当前订单的流程任务，有值，用户再次发起流程】参数 ：发起人{}，赔偿单id{}，查询结果：{}",userId,orderId,resultT.toString());
            OrderAuditProcessDto auditDto = getStartAuditParams(userId,orderId,taskId,handleUser,amount);
            handleAuditProcess(auditDto);
            //推送审核消息（下一级审核者）
            this.pushAuditMessage(resultT.getActPinId(),orderId,this.getDecrypHandler(handleUser));
            return true;
        }else if(ObjectUtils.isNotEmpty(resultT.getOrderId()) && ObjectUtils.isEmpty(taskId)){
            log.info("-------->>>审核-方法【发起审核-当前orderId已发起过审核流程，用户不是流程发起人不能发起审核】参数 ：发起人{}，赔偿单id{}，查询结果：{}",userId,orderId,resultT.toString());
            throw new BusinessException(MessageCode.LAUNCH_REPEAT_ERROR.getCode(),new String[]{orderId,userId});
        }
        return false;
    }

    /**
     * Title： getDecrypHandler<br>
     * Description： 解密后审批人数据<br>
     * author：傅欣荣 <br>
     * date：2020/6/26 21:29 <br>
     * @param
     * @return
     */
    private String getDecrypHandler(String handleUser){
        if(ObjectUtils.isNotEmpty(handleUser)){
            String privateKey = UserEnum.PASSWORD_ENCRYPT_KEY.getValue();
            String key = null;
            try {
                key = Base64.encodeBase64String(privateKey.getBytes(CharsetUtil.UTF8));
            } catch (UnsupportedEncodingException e) {
                log.error("发起审批-下一级处理人key加密错误{}",e);
                throw new BusinessException(MessageCode.AUDIT_HANDLEUSER_DECODE_ERROR.getCode());
            }
            return AesEncryptUtil.aesDecrypt(key, handleUser);
        }
        return new String();
    }

    /**
     * Title：pushAuditMessage <br>
     * Description： 发送审核消息:有handleUser值直接发送，没有则从实例中取<br>
     * author：傅欣荣 <br>
     * date：2020/6/15 16:16 <br>
     * @param
     * @return
     */
    public void pushAuditMessage(String processInstanceId ,String orderId ,String handleUser){
        log.info("-------->>>审核-方法【发起审核-发送消息验证】，赔偿单id{}，人员{}，processInstanceId{}",orderId,handleUser,processInstanceId);
        if(StringUtils.isNotBlank(handleUser)){
            log.info("-------->>>审核-方法【发起审核-发送消息给下一处理人】，赔偿单id{}，人员{}",orderId,handleUser);
            this.sendAuditMessage(orderId,handleUser);
        }else {
            AocOndutyUserVo onDutyUser = orderAuditDaoImpl.getAocNodeUserByPinId(processInstanceId);
            log.info("-------------->>>审核-正在执行【推送审核操作消息】流程实例id{},赔偿单id{}，推送给{}",processInstanceId,orderId,onDutyUser.getUserId());
            if(StringUtils.isNotEmpty(onDutyUser.getUserId())){
                orderAuditService.sendAuditMessage(orderId,onDutyUser.getUserId());
            }
        }

    }

    @Transactional
    public void saveAuditInfo(String orderId,ProcessInstance processInstance ,String userId){
        String recordId = handleId();
        orderAuditDaoImpl.updOrderStatus(orderId,AuditStatusEnum.OPINION_START.getStatus());
        this.saveActMiddleInfo(orderId,processInstance.getId(),recordId);
        this.saveAuditDefault(orderId,userId, new Date(),recordId);
    }

    /**
     * 创建发起人-再次发起处理请求参数
     */
    private OrderAuditProcessDto getStartAuditParams(String userId,String orderId,String taskId,String handleUser,String amount){
        OrderAuditProcessDto auditDto = new OrderAuditProcessDto();
        auditDto.setTaskId(taskId);
        auditDto.setOrderId(orderId);
        auditDto.setOpinion(AuditStatusEnum.OPINION_START.getKey());//发起
        auditDto.setProcessNode(PROCESS_START);//1发起
        auditDto.setAuditor(userId);//处理人
        auditDto.setHandleUser(handleUser);
        auditDto.setAmount(amount);
        return auditDto;
    }


    /**
     * Title： <br>
     * Description： 校验审批参数是否合法<br>
     * author：傅欣荣 <br>
     * date：2020/6/15 16:20 <br>
     * @param
     * @return
     */
    private String checkHandleAudit(OrderAuditProcessDto orderAuditPd){
        log.info("赔偿单审核提交数据，orderAuditProcessDto:{}", orderAuditPd);
        if(ObjectUtils.isEmpty(orderAuditPd.getTaskId())){
            throw new BusinessException(MessageCode.TASK_ID_IS_NULL.getCode());
        }
        if(ObjectUtils.isNotEmpty(orderAuditPd.getHandleUser())
                && orderAuditPd.getHandleUser().contains(orderAuditPd.getAuditor())){
            throw new BusinessException(MessageCode.AUDIT_USER_ERROR.getCode(),new String[] {orderAuditPd.getAuditor()});
        }
        if(PROCESS_AOC.equals(orderAuditPd.getProcessNode())
                && AuditStatusEnum.OPINION_AGREE.getKey().equals(orderAuditPd.getOpinion())
                && ObjectUtils.isEmpty(orderAuditPd.getHandleUser())){
            throw new BusinessException(MessageCode.AUDIT_HANDLEUSER_IS_NULL.getCode());
        }

        //查询taskId 是否存在。
        ActRunTaskInfoVo actRunTaskInfoVo = orderAuditDaoImpl.getActInfoByTaskId(orderAuditPd.getTaskId());
        log.info("审批-查询赔偿单号[{}],审批任务[{}],查询结果：[{}]",orderAuditPd.getOrderId(),orderAuditPd.getTaskId(),actRunTaskInfoVo.toString());
        Asserts.notNull(actRunTaskInfoVo.getTaskId(),MessageCode.AUDIT_REPEAT_ERROR.getCode(),
                new String[] {orderAuditPd.getTaskId(),orderAuditPd.getOrderId()});
        Asserts.notNull(actRunTaskInfoVo.getProcessInstancesId(),MessageCode.AUDIT_REPEAT_ERROR.getCode(),
                new String[] {orderAuditPd.getTaskId(),orderAuditPd.getOrderId()});
        return actRunTaskInfoVo.getProcessInstancesId();
    }

    @Override
    @Transactional
    public void handleAuditProcess(OrderAuditProcessDto orderAuditPd){

        String processInstancesId = this.checkHandleAudit(orderAuditPd);
        //根据节点流程变量
        Map<String, Object> data = new HashMap<>();
        data.put("result", orderAuditPd.getOpinion());//任务变量：审批状态 0通过、1不同意 2驳回 3发起
        if(ObjectUtils.isNotEmpty(orderAuditPd.getHandleUser())){
            String handler = this.getDecrypHandler(orderAuditPd.getHandleUser());
            orderAuditPd.setHandleUser(handler);
            data.put("handleUser",handler);//值班人员多个
        }
        if(ObjectUtils.isNotEmpty(orderAuditPd.getAmount())){
            data.put("amount",orderAuditPd.getAmount());
        }
        //驳回存redis
        if(AuditStatusEnum.OPINION_REJECT.getKey().equals(orderAuditPd.getOpinion()))
            redisService.set(AUDIT_STATUS_REJECT+orderAuditPd.getOrderId(),true);


        //********审批处理**************/
        boolean flag = activitiService.complementTask(orderAuditPd.getTaskId(),orderAuditPd.getAuditor(),orderAuditPd.getRemark(),data);
        log.info("赔偿单审核提交,审核节点{},审核状态{},审核人{},任务执行{}",orderAuditPd.getProcessNode()
                ,orderAuditPd.getOpinion(),orderAuditPd.getAuditor(),flag);

        //保存审核数据
        boolean isPush = false;
        String status = "";
        isPush = this.updAuditStatus(orderAuditPd,processInstancesId,status,isPush);

        Object isPushResult = redisService.get(AUDIT_STATUS_RESULT_PREFIX+orderAuditPd.getOrderId());

        //推送处理结果：推送要求： 第一级审核者 驳回。2.终审：通过 3.审核过程中未通过
        if(null != isPushResult && (boolean)isPushResult) {
            log.info("赔偿单审核- 【推送处理结果】，订单号{}", orderAuditPd.getOrderId());
            this.sendAuditResultMessage(orderAuditPd.getOrderId());
            redisService.deleteKey(AUDIT_STATUS_RESULT_PREFIX+orderAuditPd.getOrderId());
        }
        //推送处理结果：推送要求： 第一级审核者 驳回。2.终审：通过 3.审核过程中未通过
        //审核流程未结束，推送审核消息给下一处理人。（审核通过）
        if(isPush) {
            log.info("赔偿单审核-【审核流程未结束，推送审核消息给下一处理人】，订单号{}，处理人{}",
                    orderAuditPd.getOrderId(), orderAuditPd.getHandleUser());
            this.pushAuditMessage(processInstancesId,orderAuditPd.getOrderId(),orderAuditPd.getHandleUser());
        }
        redisService.deleteKey(AUDIT_STATUS_REJECT+orderAuditPd.getOrderId());
    }

    /**
     * Title： updAuditStatus <br>
     * Description： 更新审核对应赔偿单状态，插入审核记录。返回下一审核者（审核消息）推送状态<br>
     * author：傅欣荣 <br>
     * date：2020/6/19 10:54 <br>
     * @param
     * @return
     */
    private boolean updAuditStatus(OrderAuditProcessDto orderAuditPd,String processInstancesId,String status,boolean isPush){
        ActTaskProcdefInfoVo procdef =  orderAuditDaoImpl.getActProcdefInfo(orderAuditPd.getOrderId());
        log.info("赔偿单审核-当前任务流程信息查询，结果{}",procdef.toString());
        //发起
        if(AuditStatusEnum.OPINION_START.getKey().equals(orderAuditPd.getOpinion())){
            status = AuditStatusEnum.OPINION_START.getStatus();
        }
        //不同意
        if(AuditStatusEnum.OPINION_NOT_AGREE.getKey().equals(orderAuditPd.getOpinion())){
            status = AuditStatusEnum.OPINION_NOT_AGREE.getStatus();
        }//驳回
        if(AuditStatusEnum.OPINION_REJECT.getKey().equals(orderAuditPd.getOpinion())){
            status = AuditStatusEnum.OPINION_REJECT.getStatus();
        }
        //审批流程结束推送审批结果；未结束，推送审核消息给下一处理人
        // 例：若分2级审核 1级审核者：同意，赔偿单状态为 审核中 。 2级审核者：同意，赔偿单状态为 通过
        if(AuditStatusEnum.OPINION_AGREE.getKey().equals(orderAuditPd.getOpinion())){

            if(activitiService.isProcessEnd(processInstancesId)){
                status = AuditStatusEnum.OPINION_AGREE.getStatus();
            }else{
                status = AuditStatusEnum.OPINION_AGREE_MIDDLE.getStatus();
                isPush = true;//推送消息给下一个处理人
            }
        }
        String recordId = handleId();
        log.info("赔偿单审核-审核后，更新赔偿单状态，订单号{}，操作记录id{}，状态{},是否给下一级审核人推送{}",
                orderAuditPd.getOrderId(),recordId,status,isPush);
        //更新赔偿单申请表状态
        orderAuditDaoImpl.updOrderStatus(orderAuditPd.getOrderId(),status);
        //更新赔偿单审核状态 【中间表】
        orderAuditDaoImpl.updOrderAuditStatic(orderAuditPd.getOrderId(),recordId);
        //保存审核记录
        this.saveAuditRecord(orderAuditPd,recordId);

        return isPush;
    }

    /**
     * Title： handleId<br>
     * Description： 订单号生成规则<br>
     * author：傅欣荣 <br>
     * date：2020/6/16 14:15 <br>
     * @param
     * @return
     */
    private String handleId() {
        //精确时间到分12位
        String orderId = DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMM);
        //随机5位数
        int random5 = (int) ((Math.random() * 9 + 1) * 100000);
        orderId += String.valueOf(random5);
        orderId += 11;
        return orderId;
    }

    @Override
    public List<OrderAuditRecordVo> findOrderAuditRecord(String orderId){
        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"赔偿单id"});
        return orderAuditDaoImpl.findOrderAuditRecord(orderId);

    }

    @Override
    public QueryResults findAuditProcessByUserId(OrderAuditDataDto orderAuditDataDto){
        log.info("WEB-赔偿单审核列表查询【请求参数】"+orderAuditDataDto.toString());
        Asserts.isNotEmpty(orderAuditDataDto.getUserId(),MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"用户id"});
        Asserts.isNotEmpty(orderAuditDataDto.getType(),MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"审核查询类型type"});
        return orderAuditDaoImpl.findAuditProcess(orderAuditDataDto);
    }


    @Override
    public QueryResults queryOrderAuditList(OrderAuditQueryParams orderAuditQueryParams) {
        log.info("H5-审核列表查询【前端请求参数】{}",orderAuditQueryParams.toString());
        return orderAuditDaoImpl.queryOrderAuditList(orderAuditQueryParams);
    }


    @Override
    public Map<String, Object> getAuditNode(String taskId) {
        Asserts.isNotEmpty(taskId,MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"审核taskId"});
        log.info("审核-查任务当前正在执行的节点，参数{}",taskId);
        if(StringUtils.isBlank(taskId)){
            throw new BusinessException(MessageCode.TASK_ID_IS_NULL.getCode());
        }
        String node = orderAuditDaoImpl.getAuditNode(taskId);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("processNode",node);
        return dataMap;
    }

    @Override
    public Map<String, Object> getIsAocByUserId(String userId){
        log.info("审核-查用户是否在aoc人员，参数{}",userId);
        Asserts.isNotEmpty(userId,MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"用户id"});
        String isAocUser = orderAuditDaoImpl.getIsAocByUserId(userId);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("isAocUser",isAocUser);
        return dataMap;
    }
    /**
     * Title：saveAuditRecord <br>
     * Description： 保存流程处理记录<br>
     * author：傅欣荣 <br>
     * date：2020/3/19 11:38 <br>
     * @param  orderAuditPd
     * @return void
     */
    private void saveAuditRecord(OrderAuditProcessDto orderAuditPd,String recordId ){

        Date createTime = new Date();
        //审核流程跟踪相关
        OrderAudit orderAudit = new OrderAudit();
        orderAudit.setId(recordId);
        orderAudit.setProcessName(orderAuditPd.getProcessNode());//1发起
        orderAudit.setStatus(STATIC_YES);//1已处理
        orderAudit.setAuditor(orderAuditPd.getAuditor());//处理人
        orderAudit.setOpinion(orderAuditPd.getOpinion());
        orderAudit.setOrderId(orderAuditPd.getOrderId());
        orderAudit.setRemark(orderAuditPd.getRemark());
        orderAudit.setAuditTime(createTime);
        orderAudit.setCreateTime(createTime);
        orderAuditDao.save(orderAudit);
    }

    /**
     * Title：saveAuditDefault <br>
     * Description： 保存提交默认审核记录<br>
     * author：傅欣荣 <br>
     * date：2020/3/19 11:36 <br>
     * @param  orderId, userId, createTime
     * @return void
     */
    private void saveAuditDefault(String orderId,String userId,Date createTime,String recordId){
        OrderAudit orderAudit = new OrderAudit();
        orderAudit.setId(recordId);
        orderAudit.setOrderId(orderId);
        orderAudit.setProcessName(PROCESS_START);//1发起
        orderAudit.setStatus(STATIC_YES);//1已处理
        orderAudit.setAuditor(userId);//处理人
        orderAudit.setOpinion(AuditStatusEnum.OPINION_START.getKey());
        orderAudit.setAuditTime(createTime);
        orderAudit.setCreateTime(createTime);
        orderAuditDao.save(orderAudit);
    }

    /**
     * Title：saveActMiddleInfo <br>
     * Description：保存服务单于审核模块中间表<br>
     * author：傅欣荣 <br>
     * date：2020/3/19 11:31 <br>
     * @param  orderId, pinId
     * @return void
     */
    private void saveActMiddleInfo(String orderId ,String pinId,String recordId){
        OrderActMiddle actMiddle = new OrderActMiddle();
        actMiddle.setOrderId(orderId);
        actMiddle.setActPinId(pinId);
        actMiddle.setRecordId(recordId);
        orderActMiddleDao.save(actMiddle);
    }

    @Override
    public List<OrderAuditProgressVo> getAuditProgressInfo(String orderId){
        return orderAuditDaoImpl.getAuditProgressInfo(orderId);
    }

    @Override
    public CompensationProgressVo queryCpsProgressQuery(String orderId) {
        CompensationProgressVo cpVo = orderAuditDaoImpl.queryCpsProgressQuery(orderId);
        cpVo.setIsShowReEdit(verifyIsShowReEdit(orderId,cpVo.getCreateUser(),cpVo.getCpsProgressStatus()));
        return cpVo;
    }

    /**
     * Title： verifyIsShowReEdit<br>
     * Description： 判断是否显示重新编辑按钮<br>
     * author：傅欣荣 <br>
     * date：2020/6/3 14:04 <br>
     * @param
     * @return 1 显示
     */
    @Override
    public String verifyIsShowReEdit(String orderId,String createUser,String status){

        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        ActTaskProcdefInfoVo procdef =  orderAuditDaoImpl.getActProcdefInfo(orderId);

        //订单审核流程流转到发起人
        String taskId = orderAuditService.getUserTaskIdByOrderId(orderId,userId);
        String node = orderAuditDaoImpl.getAuditNode(StringUtils.isBlank(taskId)?"0":taskId);

        if((userId.equals(createUser) || "system".equals(createUser)) && ORDER_STATUS_REJECT.equals(status)
                && StringUtils.isNotBlank(taskId) && PROCESS_START.equals(node)){
            return "1";
        }
        return new String();
    }

    @Override
    public Map<String, Object> getOrderPaxInfo(AuditOrderPaxInfo auditOrderPaxInfo) {
        Map<String, Object> dataMap = new HashMap<>();
        PaxInfosQueryResultVo membersCount = orderAuditDaoImpl.getOrderMembersCount(auditOrderPaxInfo.getOrderId());
        QueryResults paxList = orderAuditDaoImpl.getOrderPaxInfo(auditOrderPaxInfo);
        dataMap.put("membersCount",membersCount.getMembersCount());
        dataMap.put("paxList",paxList);
        return dataMap;
    }

    @Override
    public QueryResults getDeptUserInfo(DeptUserInfoDto deptUserInfoDto) {
        return orderAuditDaoImpl.getDeptUserInfo(deptUserInfoDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendAuditMessage(String orderId,String userId){
        //根据orderId 查询 推送消息数据
        ActSendMessgeVo actSendMessgeVo = orderAuditDaoImpl.getSendMessageInfo(orderId);
        log.info("------------->>>审核-给审核人消息推送-正在向用户：[{}] , 订单{}，查询发送消息{}",userId,orderId,actSendMessgeVo.toString());
        if(StringUtils.isBlank(userId)){
            log.info("------------->>>审核-给审核人消息推送-正在向 订单{}，查询发送审核操作信息异常{}",orderId);
            throw new BusinessException(MessageCode.AUDIT_PUSH_USER_IS_NULL.getCode());
        }
        //组装消息内容
        MessageSendForm sendForm = assembleSendMessage(actSendMessgeVo,userId);

        log.info("------------->>>审核-给审核人消息推送-正在向订单{}，组装后发送消息内容{}，接收人{}",orderId,sendForm.toString(),userId);
        //向下节点审批人
        try {
            messageService.sendMsg(sendForm);
        } catch (Exception e) {
            throw new BusinessException(MessageCode.AUDIT_MESSAGE_ERROR.getCode());
        }

    }

    /**
     * Title：sendAuditResultMessage <br>
     * Description： 发送审核结果消息 <br>
     * author：傅欣荣 <br>
     * date：2020/4/10 14:29 <br>
     * @param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendAuditResultMessage(String orderId){
        //根据orderId 查询 推送消息数据
        ActSendMessgeResultVo actSendMessgeResultVo = orderAuditDaoImpl.getSendMessageResultInfo(orderId);
        log.info("------------->>>审核结果消息推送-正在向订单{}，查询发送审核结果信息{}",orderId,actSendMessgeResultVo.toString());
        String userId = StringUtils.isBlank(actSendMessgeResultVo.getApplyUser())? actSendMessgeResultVo.getCreateId():actSendMessgeResultVo.getApplyUser();
        if(StringUtils.isBlank(userId)){
            log.info("------------->>>审核结果消息推送-正在向 订单{}，查询发送审核结果信息异常{}",orderId);
            throw new BusinessException(MessageCode.AUDIT_PUSH_USER_IS_NULL.getCode());
        }
        /*StringUtils.isNotBlank(status)?status:actSendMessgeResultVo.getAuditResult()
        actSendMessgeResultVo.set*/

        //组装消息内容
        MessageSendForm sendForm = assembleResultSendMessage(actSendMessgeResultVo,userId);
        //向发起人
        try {
            messageService.sendMsg(sendForm);
        } catch (Exception e) {
            throw new BusinessException(MessageCode.AUDIT_MESSAGE_ERROR.getCode());
        }
    }


    /**
     * Title： assembleSendMessage<br>
     * Description： 组装消息内容 向发起人发送审核结果<br>
     * author：傅欣荣 <br>
     * date：2020/4/11 16:59 <br>
     * @param  sendMessage
     * @param  userId 收消息人
     * @return
     */
    public MessageSendForm assembleResultSendMessage(ActSendMessgeResultVo sendMessage,String userId){

        String[] userIds = userId.split(",");
        StringBuffer content=new StringBuffer();
        content.append("<h3>").append("审核结果：").append(sendMessage.getAuditResult()).append(" 备注：").append(sendMessage.getRemarks()).append("</h3>");
        content.append("<p>").append("赔偿类型：").append(sendMessage.getPayTypeStr()).append("</p>");
        content.append("<p>").append("赔偿航班：").append(sendMessage.getFlightNum()).append(" ")
                .append(sendMessage.getFlightDate()).append("</p>");
        content.append("<p>").append("赔偿总金额：").append(sendMessage.getSumMoney()).append("元").append("</p>");

        MessageSendForm sendForm = new MessageSendForm();
        sendForm.setMsgType(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION_AUDIT_RESULT.getType());
        sendForm.setMsgTypeName(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION_AUDIT_RESULT.getTypeName());
        sendForm.setMsgChildType(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION_AUDIT_RESULT.getChildType()+"");
        sendForm.setMsgChildTypeName(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION_AUDIT_RESULT.getChildTypeName());

        sendForm.setPcUrl(MessageFormat.format(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION_AUDIT_RESULT.getPcUrl()
                ,sendMessage.getFlightNum(),sendMessage.getFlightDate()));


        String mobileUrl = "";
        if(DpOrderConstant.STATUS_IRREGULAR.equals(sendMessage.getPayType())){
            mobileUrl = MessageFormat.format(MOBILE_URL_IRREGULAR,sendMessage.getOrderId(),sendMessage.getPayType());
        }
        if(DpOrderConstant.STATUS_ABNORMAL_BAGGAGE.equals(sendMessage.getPayType())){
            mobileUrl = MessageFormat.format(MOBILE_URL_ABNORMAL_BAGGAGE,sendMessage.getAccidentId(),sendMessage.getPayType());
        }
        if(DpOrderConstant.STATUS_OVERBOOK.equals(sendMessage.getPayType())){
            mobileUrl = MessageFormat.format(MOBILE_URL_OVERBOOK,sendMessage.getOrderId(),sendMessage.getPayType());
        }
        sendForm.setMobileUrl(mobileUrl);
        sendForm.setIsAudit(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION_AUDIT_RESULT.getIsAudit());
        sendForm.setMsgDate(new Date());
        sendForm.setMsgTitle("赔偿单审核结果");
        sendForm.setMsgContent(content.toString());
        sendForm.setMsgUser("0");//系统发送
        sendForm.setFlightNo(sendMessage.getFlightNum());
        sendForm.setFlightDate(sendMessage.getFlightDate());
        sendForm.setMsgReplyUser(userIds);
        return sendForm;
    }


    /**
     * Title： assembleSendMessage<br>
     * Description： 组装消息内容 向审核人发起消息<br>
     * author：傅欣荣 <br>
     * date：2020/4/11 16:59 <br>
     * @param  actSendMessgeVo
     * @param  userId 收消息人
     * @return
     */
    public MessageSendForm assembleSendMessage(ActSendMessgeVo actSendMessgeVo,String userId){

        String[] userIds = userId.split(",");
        StringBuffer content=new StringBuffer();
        content.append("<h3>").append(actSendMessgeVo.getFlightDate()).append(" ").append(actSendMessgeVo.getFlightNum())
                .append(" ").append(actSendMessgeVo.getServiceCity()).append(" ").append(actSendMessgeVo.getPromoterName()).append("</h3>");
        content.append("<p>").append(actSendMessgeVo.getPayType()).append(": ").append("补偿人数").append(actSendMessgeVo.getPaxTotalCount()).append("人</p>")
                .append("<p>补偿标准:")
                .append("经济舱:").append(StringUtils.isBlank(actSendMessgeVo.getCpsNum()) ? actSendMessgeVo.getSumMoney() : actSendMessgeVo.getCpsNum()).append("元/人")
                .append(" ，").append("公务舱:").append(StringUtils.isBlank(actSendMessgeVo.getCpsNumClub()) ? actSendMessgeVo.getSumMoney() : actSendMessgeVo.getCpsNumClub()).append("元/人").append("</p>");
        content.append("<p>").append("合计补偿金额：").append(actSendMessgeVo.getSumMoney()).append("元").append("</p>");

        MessageSendForm sendForm = getSendFormCommon(actSendMessgeVo.getOrderId());
        sendForm.setMsgTitle("赔偿单审核");
        sendForm.setMsgContent(content.toString());
        sendForm.setMsgUser("0");//系统发送
        sendForm.setFlightNo(actSendMessgeVo.getFlightNum());
        sendForm.setFlightDate(actSendMessgeVo.getFlightDate());
        sendForm.setMsgReplyUser(userIds);

        return sendForm;
    }

    /**
     * Title： getSendFormCommon<br>
     * Description：公共发送消息实体 <br>
     * author：傅欣荣 <br>
     * date：2020/4/11 17:12 <br>
     * @param
     * @return
     */
    public MessageSendForm getSendFormCommon(String orderId){
        MessageSendForm sendForm = new MessageSendForm();
        sendForm.setMsgType(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getType());
        sendForm.setMsgTypeName(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getTypeName());
        sendForm.setMsgChildType(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getChildType()+"");
        sendForm.setMsgChildTypeName(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getChildTypeName());
        sendForm.setPcUrl(MessageFormat.format(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getPcUrl(),orderId));
        sendForm.setMobileUrl(MessageFormat.format(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getMobileUrl(),orderId));
        sendForm.setIsAudit(MessageTypeEnum.PASSENGERS_FOR_COMPENSATION.getIsAudit());
        sendForm.setMsgDate(new Date());
        return sendForm;
    }

    /**
     * Title： isAocLaunch <br>
     * Description： 【没有根据航站区分，所有调用发起流程时需指定key】是否为AOC部门人员发起 ，返回对应流程key<br>
     * author：傅欣荣 <br>
     * date：2020/4/11 19:18 <br>
     * @param
     * @return
     */
    public String getLaunchProcessKey(String userId,String type){
        //若类型为 异常行李 AMOUNT_LEVEL
        if(DpOrderConstant.STATUS_ABNORMAL_BAGGAGE.equals(type)){
            return AMOUNT_LEVEL;
        }
        //查询用户部门信息并判断是否为aoc人员
        ActAocUserInfoVo userInfo = orderAuditDaoImpl.getAocUserInfo(userId);
        if(StringUtils.isNotBlank(userInfo.getUserId())) return AOC_ONE_LEVEL;
        return AOC_SECOND_LEVEL;
    }

    @Override
    public String getUserTaskIdByOrderId(String orderId, String userId) {
        return orderAuditDaoImpl.getUserTaskIdByOrderId(orderId,userId);
    }



    /**
     * Title：chcekRejectaddHandler <br>
     * Description： 验证是否为驳回状态，并赋值节点处理人（上一级原审批人）<br>
     * author：傅欣荣 <br>
     * date：2020/6/26 14:50 <br>
     * @param
     * @return
     */
    @Override
    public boolean chcekRejectaddHandler(DelegateTask delegateTask) throws Exception {
        log.info("-------------->>>审核-执行节点监听器-正在执行,taskId{},查询赔偿单act信息[{}]",delegateTask.getId());
        OrderActMiddle orderActMiddle = orderActMiddleDao.findByActPinId(delegateTask.getProcessInstanceId());
        if(null != orderActMiddle && StringUtils.isNotEmpty(orderActMiddle.getOrderId())){
            log.info("-------------->>>审核-执行节点监听器-正在执行,taskId{},查询赔偿单act信息结果[{}]",delegateTask.getId(),orderActMiddle.toString());
            Object isReject= redisService.get(AUDIT_STATUS_REJECT+orderActMiddle.getOrderId());
            if(null != isReject && (boolean)isReject){
                log.info("-------------->>>审核-执行节点监听器-正在执行,taskId{},赔偿单处理驳回[{}]",delegateTask.getId());
                redisService.deleteKey(AUDIT_STATUS_REJECT+orderActMiddle.getOrderId());
                //找上一个节点处理人（由于时间问题现在用手动查询上一节点处理人，赋值到当前节点）
                HistoricActivityInstance taskInfo = activitiService.getDestinationTaskInfo(delegateTask.getProcessInstanceId(),"");
                log.info("-------------->>>审核-执行节点监听器-正在执行,taskId{},上一级节点信息[{}]",delegateTask.getId(),taskInfo.toString()+taskInfo.getAssignee());
                if(null != taskInfo && StringUtils.isNotEmpty(taskInfo.getAssignee())){
                    delegateTask.setAssignee(taskInfo.getAssignee());
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * Title： getAuditKeyByServiceCity<br>
     * Description： 获取审核流程key，根据服务航站<br>
     * author：傅欣荣 <br>
     * date：2020/8/27 9:24 <br>
     * @param
     * @return
     */
    public String getAuditKeyByServiceCity(String type,String serviceCity){
        if(DpOrderConstant.STATUS_ABNORMAL_BAGGAGE.equals(type)){
            SysDictDataInfo sysDictDataInfo = sysDictDataDao.findByTypeAndStatusAndValueLike(DpOrderConstant.AUDIT_PKG_KEY,DpOrderConstant.ZERO,serviceCity.toUpperCase());
            if(ObjectUtils.isNotEmpty(sysDictDataInfo)) return sysDictDataInfo.getKey();
        }
        return AuditProcessType.LAUNCH_PROCESS_KEY_AMOUNT.getNode();
    }
}