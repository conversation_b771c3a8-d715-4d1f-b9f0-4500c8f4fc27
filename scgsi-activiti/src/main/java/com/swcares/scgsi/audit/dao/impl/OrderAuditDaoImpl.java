package com.swcares.scgsi.audit.dao.impl;

import com.swcares.scgsi.audit.dto.AuditOrderPaxInfo;
import com.swcares.scgsi.audit.dto.DeptUserInfoDto;
import com.swcares.scgsi.audit.dto.OrderAuditDataDto;
import com.swcares.scgsi.audit.dto.OrderAuditQueryParams;
import com.swcares.scgsi.audit.entity.OrderActMiddle;
import com.swcares.scgsi.audit.vo.*;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.audit.dao.impl <br>
 * Description：赔偿单审核实现dao <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月19日 11:18 <br>
 * @version v1.0 <br>
 */
@Repository
@SuppressWarnings("unchecked")
public class OrderAuditDaoImpl {

    @Resource
    private BaseDAO baseDAO;


    /*==========审核查询状态===========*/
    /*全部*/
    private static final String ALL="0";
    /*待审核*/
    private static final String NOT_AUDIT="1";
    /*已审核*/
    private static final String AUDIT="2";


    /**
     * Title：findOrderAuditRecord <br>
     * Description： 根据服务单id查询审核记录<br>
     * author：傅欣荣 <br>
     * date：2020/3/19 11:25 <br>
     * @param  orderId
     * @return java.util.List<com.swcares.scgsi.audit.vo.OrderAuditRecordVo>
     */
    public List<OrderAuditRecordVo> findOrderAuditRecord(String orderId){
        //根据服务单id查审核流程记录，返回list集合
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("orderId",orderId);
        sql.append(" SELECT  ");
        sql.append(" O.ORDER_ID AS orderId ,get_user_name(O.AUDITOR) AS auditor,get_user_position(O.AUDITOR) job,O.REMARK AS remark,  ");
        sql.append(" TO_CHAR(O.AUDIT_TIME,'YYYY-MM-DD hh24:mi:ss') AS auditTime ,O.OPINION AS opinion,EO.PHOTO AS photo ");
        sql.append(" FROM DP_ORDER_AUDIT O ");
        sql.append(" LEFT JOIN EMPLOYEE EO  ON O.AUDITOR = EO.TUNO ");
        sql.append(" WHERE 1=1 ");
        sql.append(" AND O.ORDER_ID=:orderId ");
        sql.append(" ORDER BY O.AUDIT_TIME DESC");
        return(List<OrderAuditRecordVo>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,OrderAuditRecordVo.class);

    }


    /**
     * Title： findLastAuditRecord<br>
     * Description：根据流程实例id 查询审批记录最后一条<br>
     * author：傅欣荣 <br>
     * date：2020/6/16 13:34 <br>
     * @param
     * @return
     */
    public OrderAuditRecordVo findLastAuditRecord(String processInstanceId){
        //根据服务单id查审核流程记录，返回list集合
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("actPinId",processInstanceId);
        sql.append(" SELECT DM.ORDER_ID  AS orderId , DA.OPINION AS opinion ");
        sql.append(" from DP_ACT_MIDDLE DM ");
        sql.append(" LEFT JOIN DP_ORDER_AUDIT DA ON DM.RECORD_ID = DA.ID                           ");
        sql.append(" WHERE DM.ACT_PIN_ID = :actPinId                                                ");
        List<OrderAuditRecordVo> dataList = (List<OrderAuditRecordVo>)
                baseDAO.findBySQL_comm(sql.toString(),paramsMap,OrderAuditRecordVo.class);
        if(dataList.size() > 0 ){
            return dataList.get(0);
        }
        return new OrderAuditRecordVo();
    }

    /**
     * Title： findAuditProcess<br>
     * Description： WEB 赔偿单审核列表查询sql <br>
     * author：傅欣荣 <br>
     * date：2020/4/21 15:36 <br>
     * @param
     * @return
     */
    public QueryResults findAuditProcess(OrderAuditDataDto orderAuditDataDto){
        StringBuffer sql = findAuditDataByType(orderAuditDataDto.getType());
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("userId",orderAuditDataDto.getUserId());
        if(StringUtils.isNotBlank(orderAuditDataDto.getOrderId())){
            sql.append(" AND orderId LIKE :orderId ");
            paramsMap.put("orderId","%"+orderAuditDataDto.getOrderId()+"%");
        }
        if(StringUtils.isNotBlank(orderAuditDataDto.getFlightNo())){
            sql.append(" AND flightNo = :flightNo");
            paramsMap.put("flightNo",orderAuditDataDto.getFlightNo());
        }
        String startDate = orderAuditDataDto.getStartDate();
        String endDate = orderAuditDataDto.getEndDate();
        if (StringUtils.isNotBlank(endDate) && StringUtils.isBlank(startDate)) {
            paramsMap.put("endDate", endDate);
            sql.append(" AND flightDate <= :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
            endDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND flightDate  BETWEEN :startDate and :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND flightDate  BETWEEN :startDate and :endDate ");
        }
        if(StringUtils.isNotBlank(orderAuditDataDto.getPayType())){
            sql.append(" AND payType =:payType");
            paramsMap.put("payType",orderAuditDataDto.getPayType());
        }
        sql.append(" ORDER BY applyTime DESC");
        return baseDAO.findBySQLPage_comm(sql.toString(),
                orderAuditDataDto.getCurrent(),
                orderAuditDataDto.getPageSize(),
                paramsMap,OrderAuditDataVo.class);
    }




    public StringBuffer findAuditDataByType(String type){
        StringBuffer sql = new StringBuffer();
        //全部
        if(type.equals(ALL)){
            sql.append(" SELECT * FROM (({0}) UNION({1}) ) WHERE 1=1 AND ORDERID IS NOT NULL ");
            return new StringBuffer(MessageFormat.format(sql.toString(),findAuditDataByType(NOT_AUDIT), findAuditDataByType(AUDIT)));
        }
        //待审核
        if(type.equals(NOT_AUDIT)){
            sql.append("SELECT DISTINCT * from (SELECT UACT.TASKID taskId,get_task_node(UACT.TASKID) processNode,OI.ORDER_ID AS orderId,OI.FLIGHT_NO AS flightNo,OI.FLIGHT_DATE AS flightDate,");
            sql.append("OI.CHOICE_SEGMENT AS segment,OI.SUM_MONEY AS totalMoney,OI.PAY_TYPE AS payType,");
            sql.append("OI.STATUS AS status,get_user_name(OI.CREATE_ID) AS createUser, ");
            sql.append("case when OI.CREATE_ID='system' then get_user_name(OI.APPLY_USER) else get_user_name(OI.CREATE_ID) end AS applyUser, ");
            sql.append("case when OI.CREATE_ID='system' then TO_CHAR(OI.APPLY_DATE,'YYYY-MM-DD hh24:mi:ss') else TO_CHAR(OI.CREATE_TIME,'YYYY-MM-DD hh24:mi:ss') end AS applyTime, ");
            sql.append("get_dp_order_receivecount(Oi.ORDER_ID,NULL) AS paxTotalCount");
            sql.append(" ,AM.OPINION auditStatic,'1' isAudit ,1 RN ");
            sql.append(" FROM");
            sql.append(" (SELECT RT.ID_ AS taskId,RT.PROC_INST_ID_ AS actPinId");
            sql.append(" FROM ACT_RU_TASK rt where ASSIGNEE_ = :userId and instr(rt.NAME_,'提交赔偿') != 1");
            sql.append(" UNION");
            sql.append(" SELECT RT.ID_ AS taskId,RT.PROC_INST_ID_ AS actPinId from ACT_RU_TASK rt");
            sql.append(" LEFT JOIN ACT_RU_IDENTITYLINK rk on RT.ID_ = rk.TASK_ID_ and rk.TYPE_ != 'starter'");
            sql.append(" WHERE rk.USER_ID_ = :userId) uact");
            sql.append(" LEFT JOIN DP_ACT_MIDDLE ae on uact.actPinId = ae.ACT_PIN_ID");
            sql.append(" LEFT JOIN DP_ORDER_INFO oi on ae.ORDER_ID = oi.ORDER_ID");
            sql.append(" LEFT JOIN DP_ORDER_AUDIT AM ON AE.RECORD_ID = AM.ID and AM.OPINION !=3 ");
            sql.append("  ORDER BY OI.CREATE_TIME DESC ) WHERE 1=1 and orderId IS NOT NULL ");
        }
        //已审核
        if(type.equals(AUDIT)){
            sql.append("SELECT DISTINCT * from (SELECT NULL AS taskId,NULL AS processNode,OI.ORDER_ID AS orderId,OI.FLIGHT_NO AS flightNo,OI.FLIGHT_DATE AS flightDate,");
            sql.append("OI.CHOICE_SEGMENT AS segment,OI.SUM_MONEY AS totalMoney,OI.PAY_TYPE AS payType,");
            sql.append("OI.STATUS AS status,get_user_name(OI.CREATE_ID) AS createUser, ");
            sql.append("case when OI.CREATE_ID='system' then get_user_name(OI.APPLY_USER) else get_user_name(OI.CREATE_ID) end AS applyUser, ");
            sql.append("case when OI.CREATE_ID='system' then TO_CHAR(OI.APPLY_DATE,'YYYY-MM-DD hh24:mi:ss') else TO_CHAR(OI.CREATE_TIME,'YYYY-MM-DD hh24:mi:ss') end AS applyTime, ");
            sql.append("get_dp_order_receivecount(Oi.ORDER_ID,NULL) AS paxTotalCount");
            sql.append(",ot.OPINION auditStatic,'2' isAudit ");
            sql.append(" ,ROW_NUMBER() OVER(PARTITION BY oi.ORDER_ID ORDER BY OI.CREATE_TIME DESC) rn");
            sql.append(" FROM DP_ORDER_AUDIT ot");
            sql.append(" LEFT JOIN DP_ORDER_INFO oi on ot.ORDER_ID = oi.ORDER_ID  ");
            sql.append(" WHERE ot.OPINION !=3 and   ot.AUDITOR = :userId ");
            sql.append(" AND oi.ORDER_ID is not null");
            sql.append(" ORDER BY OI.CREATE_TIME DESC) WHERE 1=1 AND RN = 1 ");
            sql.append("  AND orderId not IN (SELECT orderId FROM (").append(findAuditDataByType(NOT_AUDIT)).append(" ))");
        }
        return sql;
    }



    /**
     * Title：updOrderAuditStatic <br>
     * Description：更新赔偿单审核状态 <br>
     * author：傅欣荣 <br>
     * date：2020/3/12 14:27 <br>
     * @param
     * @return
     */
    public void updOrderAuditStatic(String orderId,String recordId){
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_ACT_MIDDLE SET RECORD_ID = ? WHERE ORDER_ID = ?");
        baseDAO.batchUpdate(sql.toString(),recordId,orderId);
    }

    /**
     * Title：updOrderStatus <br>
     * Description： 更新赔偿单状态 <br>
     * author：傅欣荣 <br>
     * date：2020/3/12 14:32 <br>
     * @param
     * @return
     */
    public void updOrderStatus(String orderId,String status){
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_ORDER_INFO SET STATUS = ? WHERE ORDER_ID = ?");
        baseDAO.batchUpdate(sql.toString(),status,orderId);
    }

    /**
     * Title：getTaskIdByOrderId <br>
     * Description： 查询赔偿单当前用户的审核任务id<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 13:19 <br>
     * @param  orderId
     * @return java.lang.String
     */

    public OrderAuditResult getTaskIdByOrderId(String orderId,String userId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT rtu.TASKID ,AM.ORDER_ID orderId ,am.ACT_PIN_ID  as actPinId FROM");
        sql.append("  DP_ACT_MIDDLE am");
        sql.append("  LEFT JOIN  (SELECT RT.ID_ AS taskId,RT.PROC_INST_ID_ AS actPinId ");
        sql.append("  FROM ACT_RU_TASK rt where ASSIGNEE_ = :userId ");
        sql.append("  UNION");
        sql.append("  SELECT RT.ID_ AS taskId,RT.PROC_INST_ID_ AS actPinId from ACT_RU_TASK rt");
        sql.append("  LEFT JOIN ACT_RU_IDENTITYLINK rk on RT.ID_ = rk.TASK_ID_");
        sql.append("  WHERE rk.USER_ID_ = :userId ) rtu");
        sql.append("  ON RTU.ACTPINID = AM.ACT_PIN_ID ");
        sql.append("  WHERE AM.ORDER_ID = :orderId ");
        paramsMap.put("userId",userId);
        paramsMap.put("orderId",orderId);
        List<OrderAuditResult> resultList = (List<OrderAuditResult>)
                baseDAO.findBySQL_comm(sql.toString(),paramsMap,OrderAuditResult.class);
        if(resultList.size()>0){
            return resultList.get(0);
        }
        return new OrderAuditResult();
    }

    /**
     * Title：queryOrderAuditList <br>
     * Description： H5-审核列表查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/26 12:25 <br>
     * @param
     * @return
     */
    public QueryResults queryOrderAuditList(OrderAuditQueryParams orderAuditQueryParams){
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        StringBuffer sql = findAuditH5QueryByType(orderAuditQueryParams.getType());
        //用户id 类型必传
        paramsMap.put("userId",orderAuditQueryParams.getUserId());
        String flightNo = orderAuditQueryParams.getFlightNo();
        if(StringUtils.isNotBlank(flightNo)){
            sql.append(" AND flightNo = :flightNo");
            paramsMap.put("flightNo",flightNo);
        }
        String startDate = orderAuditQueryParams.getStartDate();
        String endDate = orderAuditQueryParams.getEndDate();
        if (StringUtils.isNotBlank(endDate) && StringUtils.isBlank(startDate)) {
            paramsMap.put("endDate", endDate);
            sql.append(" AND flightDate <= :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
            endDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND flightDate BETWEEN :startDate and :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND flightDate  BETWEEN :startDate and :endDate ");
        }
        String serviceCity = orderAuditQueryParams.getServiceCity();
        if(StringUtils.isNotBlank(serviceCity)){
            paramsMap.put("serviceCity", serviceCity);
            sql.append(" AND serviceCity = :serviceCity ");
        }
        String status = orderAuditQueryParams.getStatus();
        if(StringUtils.isNotBlank(status)){
            sql.append(" AND status  ");
            sql.append(" in (SELECT REGEXP_SUBSTR('"+status+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+status+"', '[^,]+', 1, level) is not null)");
        }
        sql.append(" ORDER BY applyTime DESC");
        return baseDAO.findBySQLPage_comm(sql.toString(),
                orderAuditQueryParams.getCurrent(),
                orderAuditQueryParams.getPageSize(),
                paramsMap,OrderAuditQuertListVo.class);

    }

    public StringBuffer findAuditH5QueryByType(String type){
        StringBuffer sql = new StringBuffer();
        //全部
        if(type.equals(ALL)){
            sql.append(" SELECT * FROM (({0}) UNION({1}) ) WHERE 1=1 AND ORDERID IS NOT NULL ");
            return new StringBuffer(MessageFormat.format(sql.toString(),findAuditH5QueryByType(NOT_AUDIT)
                    , findAuditH5QueryByType(AUDIT)));
        }
        //待审核
        if(type.equals(NOT_AUDIT)){
            sql.append("SELECT DISTINCT * from (SELECT UACT.TASKID taskId,get_task_node(UACT.TASKID) processNode,OI.ORDER_ID AS orderId, ");
            sql.append("  OI.ACCIDENT_ID accidentId,");
            sql.append(" '旅客赔偿' AS auditType,OI.FLIGHT_NO AS flightNo,OI.FLIGHT_DATE AS flightDate, ");
            sql.append("  OI.PAY_TYPE AS payType,get_user_name(OI.CREATE_ID) AS applyUser, ");
            sql.append("  OI.SERVICE_CITY serviceCity, OI.STATUS status,");
            sql.append("  TO_CHAR(OI.CREATE_TIME,'YYYY-MM-DD hh24:mi:ss') AS applyTime, ");
            sql.append("  get_dp_order_receivecount(Oi.ORDER_ID,NULL) AS paxTotalCount, ");
            sql.append("  OI.SUM_MONEY AS totalMoney ");
            sql.append("  FROM ");
            sql.append("  (SELECT RT.ID_ AS taskId,RT.PROC_INST_ID_ AS actPinId ");
            sql.append("  FROM ACT_RU_TASK rt where rt.ASSIGNEE_ = :userId and instr(rt.NAME_,'提交赔偿') != 1");
            sql.append("  UNION ");
            sql.append("  SELECT RT.ID_ AS taskId,RT.PROC_INST_ID_ AS actPinId from ACT_RU_TASK rt  ");
            sql.append("  LEFT JOIN ACT_RU_IDENTITYLINK rk on RT.ID_ = rk.TASK_ID_ and rk.TYPE_ != 'starter'");
            sql.append("  WHERE rk.USER_ID_ = :userId  ");
            sql.append("  ) uact            ");
            sql.append("  LEFT JOIN DP_ACT_MIDDLE ae on uact.actPinId = ae.ACT_PIN_ID  ");
            sql.append("  LEFT JOIN DP_ORDER_INFO oi on ae.ORDER_ID = oi.ORDER_ID  ");
            sql.append("  LEFT JOIN DP_ORDER_AUDIT ot ON AE.RECORD_ID = ot.ID and ot.OPINION !=3 ");
            sql.append("  ORDER BY OI.CREATE_TIME DESC) WHERE 1=1 and orderId IS NOT NULL ");
        }
        //已审核
        if(type.equals(AUDIT)){
            sql.append("SELECT DISTINCT * from (SELECT NULL AS taskId,NULL AS processNode,OI.ORDER_ID AS orderId,");
            sql.append(" OI.ACCIDENT_ID accidentId,");
            sql.append(" '旅客赔偿' AS auditType,                                    ");
            sql.append("  OI.FLIGHT_NO AS flightNo,OI.FLIGHT_DATE AS flightDate,    ");
            sql.append("  OI.PAY_TYPE AS payType,     ");
            sql.append("  get_user_name(OI.CREATE_ID) AS applyUser,  ");
            sql.append("  OI.SERVICE_CITY serviceCity,OI.STATUS status,");
            sql.append("  TO_CHAR(OI.CREATE_TIME,'YYYY-MM-DD hh24:mi:ss') AS applyTime,");
            sql.append("  get_dp_order_receivecount(Oi.ORDER_ID,NULL) AS paxTotalCount,");
            sql.append("  OI.SUM_MONEY AS totalMoney                                   ");
            sql.append("  FROM DP_ORDER_AUDIT ot                                       ");
            sql.append("  LEFT JOIN DP_ORDER_INFO oi on ot.ORDER_ID = oi.ORDER_ID      ");
            sql.append("  WHERE  ot.OPINION !=3 and  ot.AUDITOR = :userId  ORDER BY OI.CREATE_TIME DESC) WHERE 1=1 and orderId IS NOT NULL ");
            sql.append("   AND orderId not IN (SELECT orderId FROM (").append(findAuditH5QueryByType(NOT_AUDIT)).append(" ))");
        }
        return sql;
    }

    /**
     * Title：getAuditProgressInfo <br>
     * Description： H5-审核进度信息展示查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/26 17:02 <br>
     * @param
     * @return
     */
    public List<OrderAuditProgressVo> getAuditProgressInfo(String orderId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT '1' as processNode ,'发起' AS processName , ");
        sql.append(" case when oi.CREATE_ID = 'system' then get_user_name(oi.APPLY_USER) else get_user_name(oi.CREATE_ID) end as handleUser ,'1' status ");
        sql.append(" FROM DP_ORDER_INFO oi ");
        sql.append(" WHERE oi.order_id =  :orderId ");
        sql.append(" UNION ");
        sql.append(" SELECT '2' as processNode, ");
        /*sql.append(" case  when  ART.id_ is null then DECODE(DT.OPINION,'0','审核通过','1','审核不通过','2','审核驳回')   ");
        sql.append(" ELSE '审核中' END AS processName ,get_user_name(DT.AUDITOR) handleUser,");
        sql.append(" DECODE(DT.AUDITOR,NULL,'0','1') status ");*/
        sql.append(" case  when  ART.id_ is null then DECODE(DT.OPINION,'0','审核通过','1','审核不通过','2','审核驳回') ");
        sql.append(" ELSE '审核中' END AS processName ,                 ");
        sql.append(" CASE when DT.OPINION = 2 and  ART.id_ is not null and ART.NAME_ not LIKE '%提交%' THEN get_user_name(ART.ASSIGNEE_)  ");
        sql.append("  when ART.id_ is not null and  ART.NAME_ LIKE '%提交%' then '' ");
        sql.append(" ELSE get_user_name(DT.AUDITOR) END AS handleUser, ");
        sql.append(" case when ART.NAME_ LIKE '%提交%' then '0' ");
		sql.append(" else DECODE(DT.AUDITOR,NULL,'0','1') END AS status ");

        sql.append(" FROM DP_ORDER_INFO oi ");
        sql.append(" LEFT JOIN DP_ACT_MIDDLE AM ON OI.ORDER_ID = AM.ORDER_ID ");
        sql.append(" LEFT JOIN DP_ORDER_AUDIT DT ON OI.ORDER_ID = DT.ORDER_ID and AM.RECORD_ID = DT.ID  ");
        sql.append(" AND DT.PROCESS_NAME != '1'                 ");
        sql.append(" LEFT JOIN  ACT_RU_TASK ART ON AM.ACT_PIN_ID = ART.PROC_INST_ID_    ");  
        sql.append(" WHERE oi.order_id = :orderId ");
        sql.append(" UNION                                       ");
        sql.append(" SELECT '3' as processNode,'发放' as processName,   ");
        sql.append(" get_user_name(OI.ISS_USER) handleUser,    ");
        sql.append(" DECODE(OI.STATUS,3,'1','0') status  ");
        sql.append(" FROM DP_ORDER_INFO oi    ");
        sql.append(" WHERE oi.order_id = :orderId ");
        paramsMap.put("orderId",orderId);
        return  (List<OrderAuditProgressVo>)
                baseDAO.findBySQL_comm(sql.toString(),paramsMap,OrderAuditProgressVo.class);
    }

    /**
     * Title：queryCpsProgressQuery <br>
     * Description： 赔偿进度-信息查询
     * 赔偿进度状态  - 部门-处理人 <br>
     * author：傅欣荣 <br>
     * date：2020/3/26 19:56 <br>
     * @param
     * @return
     */
    public CompensationProgressVo queryCpsProgressQuery(String orderId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT ");
        sql.append(" OIS.cpsProgressStatus ,OIS.CREATE_ID createUser , ");
        sql.append(" CASE when OIS.cpsProgressStatus ='8' THEN OIS.PAX_NAME ");
        sql.append(" WHEN OIS.cpsProgressStatus = '9' THEN NULL ");
        sql.append(" WHEN OIS.cpsProgressStatus= '3' THEN GET_USER_NAMEANDDEPT(OIS.ISS_USER) ");
        sql.append(" WHEN OIS.cpsProgressStatus= '4' THEN GET_USER_NAMEANDDEPT(OIS.CLOSE_USER) ");
        sql.append(" ELSE GET_USER_NAMEANDDEPT(DT.AUDITOR) END handleUserInfo ");
        sql.append(" FROM ( ");
        sql.append(" SELECT OI.ORDER_ID,OI.ISS_USER,OI.CLOSE_USER,PI.PAX_NAME ,OI.CREATE_ID ,  ");
        sql.append(" decode(PI.SWITCH,'0',(decode(PI.RECEIVE_STATUS,'1','p8',OI.STATUS)),'p9') cpsProgressStatus  ");
        sql.append(" from                                                         ");
        sql.append(" DP_ORDER_INFO OI ");
        sql.append(" LEFT JOIN DP_PAX_INFO PI ON OI.ORDER_ID = PI.ORDER_ID ");
        sql.append(" WHERE OI.ORDER_ID = :orderId ");
        sql.append(" ) OIs ");
        sql.append(" LEFT JOIN DP_ACT_MIDDLE AM ON OIs.ORDER_ID = AM.ORDER_ID ");
        sql.append(" LEFT JOIN DP_ORDER_AUDIT DT ON OIs.ORDER_ID = DT.ORDER_ID and AM.RECORD_ID = DT.ID    ");
        paramsMap.put("orderId",orderId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,CompensationProgressVo.class);
    }

    /**
     * Title： getOrderMembersCount <br>
     * Description： 根据订单号查询成人/儿童/婴儿人数<br>
     * author：傅欣荣 <br>
     * date：2020/4/9 9:37 <br>
     * @param
     * @return
     */
    public PaxInfosQueryResultVo getOrderMembersCount(String orderId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT");
        sql.append(" get_dp_order_receivecount(O.ORDER_ID,NULL) - GET_DP_ORDER_CHILDCOUNT(O.ORDER_ID) - GET_DP_ORDER_BABYCOUNT(O.ORDER_ID) ||");
        sql.append(" '/'||GET_DP_ORDER_CHILDCOUNT(O.ORDER_ID)||");
        sql.append(" '/'||GET_DP_ORDER_BABYCOUNT(O.ORDER_ID) AS membersCount");
        sql.append(" FROM DP_ORDER_INFO O");
        sql.append(" WHERE O.ORDER_ID = :orderId ");
        paramsMap.put("orderId",orderId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,PaxInfosQueryResultVo.class);
    }


    /**
     * Title： getOrderPaxInfo<br>
     * Description：赔偿单审核-旅客列表<br>
     * author：傅欣荣 <br>
     * date：2020/4/7 15:56 <br>
     * @param
     * @return
     */
    public QueryResults getOrderPaxInfo(AuditOrderPaxInfo auditOrderPaxInfo){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT");
        sql.append(" PI.SEGMENT segment,");
        sql.append(" PI.WITH_BABY isInfant,");
        sql.append(" PI.IS_CHILD isChild,");
        sql.append(" PI.PAX_NAME paxName,PI.CURRENT_AMOUNT payMoney,");
        sql.append(" get_dp_order_receivecount(O.ORDER_ID,NULL) - GET_DP_ORDER_CHILDCOUNT(O.ORDER_ID) - GET_DP_ORDER_BABYCOUNT(O.ORDER_ID) ||");
        sql.append(" '/'||GET_DP_ORDER_CHILDCOUNT(O.ORDER_ID)||");
        sql.append(" '/'||GET_DP_ORDER_BABYCOUNT(O.ORDER_ID) AS membersCount");
        sql.append(" FROM DP_ORDER_INFO O");
        sql.append(" LEFT JOIN DP_PAX_INFO PI ON o.ORDER_ID = pi.ORDER_ID");
        sql.append(" WHERE O.ORDER_ID = :orderId ");

        paramsMap.put("orderId",auditOrderPaxInfo.getOrderId());
        String keySearch = auditOrderPaxInfo.getKeySearch();
        String segment = auditOrderPaxInfo.getSegment().replace(" ", "");
        if(StringUtils.isNotBlank(keySearch)){
            paramsMap.put("keySearch", "%" + keySearch + "%");
            sql.append(" AND PI.PAX_NAME LIKE :keySearch ");
        }
        if(StringUtils.isNotBlank(segment)){
            sql.append(" AND replace(PI.SEGMENT ,' ','') ");
            sql.append(" in (SELECT REGEXP_SUBSTR('"+segment+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+segment+"', '[^,]+', 1, level) is not null)");
        }
        sql.append(" ORDER BY NLSSORT(PI.PAX_NAME,'NLS_SORT = SCHINESE_PINYIN_M') ");

        return baseDAO.findBySQLPage_comm(sql.toString(),
                auditOrderPaxInfo.getCurrent(),auditOrderPaxInfo.getPageSize(),
                paramsMap,PaxInfosQueryResultVo.class);

    }

    public QueryResults getDeptUserInfo(DeptUserInfoDto deptUserInfoDto){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();

        /*下方列表为以用户为未读展示所有用户信息，
        按姓名首字母排序展示。需展示用户姓名，部门名称（子部门），职位以及当前值班状态*/
        sql.append(" SELECT  ");
        sql.append(" EO.TUNO userId,EO.PHOTO photo,EO.TU_CNAME userName,EO.TU_SPECIALTY job, ");
        sql.append(" NVL(EO.IS_ON_DUTY,0) isOnDuty,");
        sql.append(" DT.TOSNAME department,EO.TU_ENAME as spell ,substr(EO.TU_ENAME,1,1) as initials   ");
        sql.append(" FROM EMPLOYEE EO LEFT JOIN DEPARTMENT Dt ON EO.TOID = DT.TOID ");
        sql.append(" WHERE 1=1  ");//
        if (StringUtils.isNotBlank(deptUserInfoDto.getKeySearch())) {
            paramsMap.put("keySearch", "%" + deptUserInfoDto.getKeySearch() + "%");
            sql.append(" AND EO.TU_CNAME LIKE:keySearch ");
        }
        //sql.append(" ORDER by substr(EO.TU_ENAME,1,1) ");
        sql.append(" ORDER by EO.TU_ENAME ");

        return baseDAO.findBySQLPage_comm(
                sql.toString(),
                deptUserInfoDto.getCurrent(),
                deptUserInfoDto.getPageSize(),
                paramsMap,
                AuditUserInfoVo.class);
    }

    /**
     * Title： getAocNodeTaskInfo<br>
     * Description： 获取aoc节点上没有执行人的数据<br>
     * author：傅欣荣 <br>
     * date：2020/4/10 10:01 <br>
     * @param
     * @return
     */
    public List<AuditNodeTaskInfoVo> getAuditNodeTaskInfo(String nodeName){
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT RT.ID_ taskId ,RT.NAME_ nodeName ,RK.USER_ID_ handlerUser,");
        sql.append(" RK.TYPE_ nodeType,RT.PROC_INST_ID_ procInstId,AE.ORDER_ID orderId");
        sql.append(" from ACT_RU_TASK RT");
        sql.append(" LEFT JOIN ACT_RU_IDENTITYLINK RK");
        sql.append(" ON RT.ID_ = RK.TASK_ID_");
        sql.append(" LEFT JOIN DP_ACT_MIDDLE AE ON RT.PROC_INST_ID_  = AE.ACT_PIN_ID");
        sql.append(" WHERE RT.NAME_ like '%").append(nodeName).append("%'");
        sql.append(" and RT.ID_ is not null");
        sql.append(" and (RK.ID_ IS NULL OR (RK.TYPE_ = 'candidate'");
        sql.append(" and RK.USER_ID_ IS NULL ))");
        sql.append(" AND RT.ASSIGNEE_ IS NULL");
        sql.append(" AND AE.ORDER_ID  IS NOT NULL");
        return (List<AuditNodeTaskInfoVo>)baseDAO.findBySQL_comm(sql.toString(),new HashMap<>(),AuditNodeTaskInfoVo.class);
    }
    /**
     * Title： getAocNodeUserByPinId<br>
     * Description： 根据流程任务id 查询此流程对应aoc节点 aoc值班处理人数据<br>
     * author：傅欣荣 <br>
     * date：2020/4/17 15:05 <br>
     * @param  procInstId
     * @return
     */
    public AocOndutyUserVo getAocNodeUserByPinId(String procInstId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT to_char(wm_concat(to_char(RK.USER_ID_))) userId ");
        sql.append(" from ACT_RU_TASK RT");
        sql.append(" LEFT JOIN ACT_RU_IDENTITYLINK RK");
        sql.append(" ON RT.ID_ = RK.TASK_ID_");
        sql.append(" LEFT JOIN DP_ACT_MIDDLE AE ON RT.PROC_INST_ID_  = AE.ACT_PIN_ID");
        sql.append(" WHERE 1=1 "); //RT.NAME_ like '%AOC审核%'
        sql.append(" and RT.ID_ is not null");
        sql.append(" and RK.TYPE_ = 'candidate'");
        sql.append(" and RK.USER_ID_ IS not NULL");
        sql.append(" AND RT.PROC_INST_ID_ = :procInstId ");
        paramsMap.put("procInstId",procInstId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,AocOndutyUserVo.class);

    }





    /**
     * Title：getAocOndutyUserInfo <br>
     * Description： 角色下用户值班人员<br>
     * author：傅欣荣 <br>
     * date：2020/4/10 11:00 <br>
     * @param
     * @return
     */
    public AocOndutyUserVo getRoleOndutyUserInfo(String rolName ,Boolean isOnDuty){
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT to_char(wm_concat(EO.TUNO)) userId");
        sql.append(" FROM ROLE ros LEFT JOIN USER_ROLE uro on ros.id = uro.role_id");
        sql.append(" LEFT JOIN EMPLOYEE eo on EO.ID = uro.EMPLOYEE_id ");
        if(isOnDuty) sql.append("  and EO.IS_ON_DUTY = 1");
        sql.append(" where ros.name = '").append(rolName).append("' and ros.FOUNDER = 'superadmin山东地服超级管理员用户'");
        sql.append(" and ros.STATUS = 1 ");
        sql.append(" and EO.TUNO is not null");
        List<AocOndutyUserVo> dataList = (List<AocOndutyUserVo>) baseDAO.findBySQL_comm(sql.toString(),new HashMap<>(),AocOndutyUserVo.class);
        if(dataList.size() > 0){
            return dataList.get(0);
        }
        return new AocOndutyUserVo();
    }



    /**
     * Title：getOneAuditOndutyUserInfo <br>
     * Description： 赔偿金额审核流程（异常行李）一级审核值班人员 异常行李值班主任<br>
     * author：傅欣荣 <br>
     * date：2020/4/10 11:00 <br>
     * @param
     * @return
     */
    public AocOndutyUserVo getOneAuditOndutyUserInfo(String rolName){
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT to_char(wm_concat(EO.TUNO)) userId");
        sql.append(" FROM ROLE ros LEFT JOIN USER_ROLE uro on ros.id = uro.role_id");
        sql.append(" LEFT JOIN EMPLOYEE eo on EO.ID = uro.EMPLOYEE_id and EO.IS_ON_DUTY = 1");
        sql.append(" where ros.name = '").append(rolName).append("' and ros.FOUNDER = 'superadmin山东地服超级管理员用户'");
        sql.append(" and ros.STATUS = 1 ");
        sql.append(" and EO.TUNO is not null");
        List<AocOndutyUserVo> dataList = (List<AocOndutyUserVo>) baseDAO.findBySQL_comm(sql.toString(),new HashMap<>(),AocOndutyUserVo.class);
        if(dataList.size() > 0){
            return dataList.get(0);
        }
        return new AocOndutyUserVo();
    }


    /**
     * Title：getOneAuditOndutyUserInfo <br>
     * Description： 赔偿金额审核流程（异常行李）二级审核 异常行李经理<br>
     * author：傅欣荣 <br>
     * date：2020/4/10 11:00 <br>
     * @param
     * @return
     */
    public AocOndutyUserVo getAuditOndutyUserInfo(String rolName){
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT to_char(wm_concat(EO.TUNO)) userId");
        sql.append(" FROM ROLE ros LEFT JOIN USER_ROLE uro on ros.id = uro.role_id");
        sql.append(" LEFT JOIN EMPLOYEE eo on EO.ID = uro.EMPLOYEE_id");// and EO.IS_ON_DUTY = 1
        sql.append(" where ros.name = '").append(rolName).append("' and ros.FOUNDER = 'superadmin山东地服超级管理员用户' ");
        sql.append(" and ros.STATUS = 1 ");
        sql.append(" and EO.TUNO is not null");
        List<AocOndutyUserVo> dataList = (List<AocOndutyUserVo>) baseDAO.findBySQL_comm(sql.toString(),new HashMap<>(),AocOndutyUserVo.class);
        if(dataList.size() > 0){
            return dataList.get(0);
        }
        return new AocOndutyUserVo();
    }

    /**
     * Title： getAocUserInfo<br>
     * Description：根据用户id 判断是否为aoc,并获取用户信息+部门<br>
     * author：傅欣荣 <br>
     * date：2020/4/11 19:22 <br>
     * @param
     * @return
     */
    public ActAocUserInfoVo getAocUserInfo(String userId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT  EO.TUNO userId,TU_CNAME userName,EO.TU_EXT2 job, DM.TOSNAME department,dM.TOBM tobm  ");
        sql.append(" FROM ROLE ros LEFT JOIN USER_ROLE uro on ros.id = uro.role_id");
        sql.append(" LEFT JOIN EMPLOYEE eo on EO.ID = uro.EMPLOYEE_id");
        sql.append(" LEFT JOIN DEPARTMENT DM ON EO.TOID = DM.TOID");
        sql.append(" where ros.name = 'AOC值班经理角色-系统' and ros.FOUNDER = 'superadmin山东地服超级管理员用户'");
        sql.append(" and ros.STATUS = 1 ");
        sql.append(" and EO.TUNO is not null AND EO.TUNO = :userId ");
        paramsMap.put("userId",userId);
        List<ActAocUserInfoVo> data = (List<ActAocUserInfoVo>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,ActAocUserInfoVo.class);
        if(data.size()>0){
            return data.get(0);
        }
        return new ActAocUserInfoVo();
    }


    /**
     * Title：getAuditNode <br>
     * Description： 查询act 任务节点 是否为aoc审核 2 <br>
     *    1.提交 2-aoc 3值班经理
     * author：傅欣荣 <br>
     * date：2020/4/15 10:40 <br>
     * @param
     * @return
     */
    public String getAuditNode(String taskId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT get_task_node(:taskId) processNode");
        sql.append(" FROM DUAL ");
        paramsMap.put("taskId",taskId);
        List<ActNodeInfo> data = (List<ActNodeInfo>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,ActNodeInfo.class);
        if(data.size() > 0 ){
            return data.get(0).getProcessNode();
        }
        return new String();
    }

    /**
     * Title： getIsAocByUserId<br>
     * Description： 根据用户查询是否为aoc人员 0 存在 1 不存在 <br>
     * author：傅欣荣 <br>
     * date：2020/4/15 12:30 <br>
     * @param
     * @return
     */
    public String getIsAocByUserId(String userId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT get_isAocUser(:userId) isAocUser");
        sql.append(" FROM DUAL ");
        paramsMap.put("userId",userId);
        List<ActNodeInfo> data = (List<ActNodeInfo>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,ActNodeInfo.class);
        if(data.size() > 0 ){
            return data.get(0).getIsAocUser();
        }
        return new String();
    }

    /**
     * Title： getSendMessageInfo<br>
     * Description： 查询审核消息发送的数据 <br>
     * author：傅欣荣 <br>
     * date：2020/4/10 16:18 <br>
     * @param
     * @return
     */
    public ActSendMessgeVo getSendMessageInfo(String orderId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT");
        sql.append(" o.ORDER_ID orderId,o.FLIGHT_NO flightNum,o.FLIGHT_DATE flightDate,O.SERVICE_CITY serviceCity,C.CPS_NUM cpsNum,C2.CPS_NUM cpsNumClub,");
        sql.append(" DECODE(O.PAY_TYPE,'0','不正常航班'，'1','异常行李','2','旅客超售') AS payType,O.SUM_MONEY sumMoney,");
        sql.append(" get_dp_order_receivecount(O.ORDER_ID,NULL) AS paxTotalCount,o.CREATE_ID createId,get_user_name(o.CREATE_ID) promoterName");
        sql.append(" FROM DP_ORDER_INFO o     ");
        sql.append(" LEFT JOIN DP_COMPENSATE_INFO C ON O.ORDER_ID = C.ORDER_ID and c.CLASS_TYPE = 1    ");
        sql.append(" LEFT JOIN DP_COMPENSATE_INFO C2 ON O.ORDER_ID = C2.ORDER_ID and c2.CLASS_TYPE = 2    ");
        sql.append(" WHERE o.order_id= :orderId ");
        paramsMap.put("orderId",orderId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,ActSendMessgeVo.class);
    }

    /**
     * Title： getSendMessageInfo<br>
     * Description： 查询审核结果消息发送的数据 <br>
     * author：傅欣荣 <br>
     * date：2020/4/10 16:18 <br>
     * @param
     * @return
     */
    public ActSendMessgeResultVo getSendMessageResultInfo(String orderId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT o.ORDER_ID orderId,o.FLIGHT_NO flightNum,o.FLIGHT_DATE flightDate,");
        sql.append(" O.PAY_TYPE AS payType , o.ACCIDENT_ID accidentId ,");
        sql.append(" DECODE(O.PAY_TYPE,'0','不正常航班','1','异常行李','2','旅客超售') AS payTypeStr,");
        sql.append(" O.SUM_MONEY sumMoney,o.CREATE_ID createId,");
        sql.append(" o.APPLY_USER applyUser,");
        sql.append(" DECODE(AM.OPINION,'0','通过','1','不同意','2','驳回',NULL) as auditResult,NVL(AM.REMARK,'-') as remarks ");
        sql.append(" FROM DP_ORDER_INFO O");
        sql.append(" LEFT JOIN DP_ACT_MIDDLE AE ON O.ORDER_ID = AE.ORDER_ID    ");
        sql.append(" LEFT JOIN DP_ORDER_AUDIT AM ON AE.RECORD_ID = AM.ID        ");
        sql.append(" WHERE o.order_id= :orderId ");
        paramsMap.put("orderId",orderId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,ActSendMessgeResultVo.class);
    }


    /**
     * Title：getActProcdefInfo <br>
     * Description： 获取服务单对应审核流程信息<br>
     * author：傅欣荣 <br>
     * date：2020/4/11 19:08 <br>
     * @param
     * @return
     */
    public ActTaskProcdefInfoVo getActProcdefInfo(String orderId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT DT.NAME_ deploymentName,PF.KEY_ procdefKey,PF.NAME_ procdefName ");
        sql.append(" FROM DP_ACT_MIDDLE AM               ");
        sql.append(" LEFT JOIN ACT_RU_EXECUTION RE ON AM.ACT_PIN_ID = RE.PROC_INST_ID_  ");
        sql.append(" LEFT JOIN ACT_RE_PROCDEF PF ON RE.PROC_DEF_ID_ = PF.ID_            ");
        sql.append(" LEFT JOIN ACT_RE_DEPLOYMENT DT ON PF.DEPLOYMENT_ID_ = DT.ID_       ");
        sql.append(" WHERE AM.order_id= :orderId                            ");
        paramsMap.put("orderId",orderId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,ActTaskProcdefInfoVo.class);
    }

    /**
     * Title：getOrderActMiddleInfo <br>
     * Description： 根据任务id查询 对应 服务单id<br>
     * author：傅欣荣 <br>
     * date：2020/4/11 20:23 <br>
     * @param
     * @return
     */
    public OrderActMiddle getOrderActMiddleInfo(String taskId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT AM.ID,AM.ACT_PIN_ID ,AM.ORDER_ID,AM.RECORD_ID from");
        sql.append(" ACT_RU_TASK RK LEFT JOIN DP_ACT_MIDDLE AM ON RK.PROC_INST_ID_ = AM.ACT_PIN_ID");
        sql.append(" WHERE RK.ID_ = :taskId ");
        paramsMap.put("taskId",taskId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,OrderActMiddle.class);
    }

    /**
     * Title：getUserTaskIdByOrderId <br>
     * Description： WEB- 审核详情界面- 根据用户id 、赔偿单id查询流程审核任务id<br>
     * author：傅欣荣 <br>
     * date：2020/5/20 14:08 <br>
     * @param
     * @return
     */
    public String getUserTaskIdByOrderId(String orderId, String userId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT");
        sql.append("  ACTS.taskId AS taskId ");
        sql.append(" FROM DP_ACT_MIDDLE O");
        sql.append(" LEFT JOIN ").append(getActUserTaskInfo()).append(" on ACTS.actPinId = O.ACT_PIN_ID ");
        sql.append(" WHERE 1=1");
        sql.append(" AND o.ORDER_ID = :orderId ");
        paramsMap.put("orderId",orderId);
        paramsMap.put("userId",userId);
        List<OrderAuditDataVo> dataList = (List<OrderAuditDataVo>)
                baseDAO.findBySQL_comm(sql.toString(),paramsMap,OrderAuditDataVo.class);
        if(dataList.size() >0) return dataList.get(0).getTaskId();
        return null;
    }


    /**
     * Title： getActUserTaskInfo<br>
     * Description： 查询用户审核任务task，流程id sql<br>
     * author：傅欣荣 <br>
     * date：2020/5/14 10:50 <br>
     * @param
     * @return
     */
    private String getActUserTaskInfo(){
        StringBuffer sql = new StringBuffer();
        sql.append(" (SELECT RT.ID_ AS taskId,RT.PROC_INST_ID_ AS actPinId ");
        sql.append(" FROM ACT_RU_TASK rt where ASSIGNEE_ = :userId ");
        sql.append(" UNION ");
        sql.append(" SELECT RT.ID_ AS taskId,RT.PROC_INST_ID_ AS actPinId from ACT_RU_TASK rt ");
        sql.append(" LEFT JOIN ACT_RU_IDENTITYLINK rk on RT.ID_ = rk.TASK_ID_ ");
        sql.append(" WHERE rk.USER_ID_ = :userId ) ACTS ");
        return sql.toString();
    }


    /**
     * Title：getActInfoByTaskId <br>
     * Description： 根据taskid查任务表数据，任务数据是否存在、<br>
     * author：傅欣荣 <br>
     * date：2020/6/3 17:31 <br>
     * @param
     * @return
     */
    public ActRunTaskInfoVo getActInfoByTaskId(String taskId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT ID_ AS taskId , PROC_INST_ID_ AS processInstancesId from ACT_RU_TASK  where ID_ =  :taskId");
        paramsMap.put("taskId",taskId);
        List<ActRunTaskInfoVo> dataList = (List<ActRunTaskInfoVo>) baseDAO.findBySQL_comm(sql.toString(),paramsMap,ActRunTaskInfoVo.class);
        if (dataList.size() > 0){
            return dataList.get(0);
        }
        return new ActRunTaskInfoVo();
    }
}