package com.swcares.scgsi.audit.vo;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.vo <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 04月07日 16:01 <br>
 * @version v1.0 <br>
 */
@Data
public class PaxInfosQueryResultVo {

    /**
     * 儿童标识
     */
    private String isChild;
    /**
     * 携带婴儿标识
     */
    private String isInfant;

    /**
     * 旅客姓名
     */
    private String paxName;

    /**
     * 航段
     */
    private String segment;

    /**
     * 赔偿金额
     */
    private String payMoney;

    /**
     * 服务单旅客数量 xx成人/xx儿童/xx婴儿
     */
    private String membersCount;
}
