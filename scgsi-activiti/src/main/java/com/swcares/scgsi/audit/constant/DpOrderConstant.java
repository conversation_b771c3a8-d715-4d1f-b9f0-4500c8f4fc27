package com.swcares.scgsi.audit.constant;

/**
 * ClassName：com.swcares.scgsi.audit.constant <br>
 * Description：赔偿单常量类 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 08月27日 11:17 <br>
 * @version v1.0 <br>
 */
public class DpOrderConstant {

    //常量0
    public static final String ZERO="0";

    //赔偿类型 不正常航班 0、
    public static final String STATUS_IRREGULAR="0";
    //赔偿类型 异常行李1、
    public static final String STATUS_ABNORMAL_BAGGAGE = "1";
    //赔偿类型 超售旅客2、
    public static final String STATUS_OVERBOOK = "2";

    //异常行李单审核流程-数据字典类型
    public static final String AUDIT_PKG_KEY="auditPkgKey";

}
