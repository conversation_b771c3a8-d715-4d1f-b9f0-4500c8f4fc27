package com.swcares.scgsi.audit.vo;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.vo <br>
 * Description：H5-审核列表查询返回对象<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月26日 9:40 <br>
 * @version v1.0 <br>
 */
@Data
public class OrderAuditQuertListVo {

    /**
     * 流程任务id 待审核有值
     */
    private String taskId;

    /**
     * 服务单id
     */
    private String orderId;

    /**
     * 事故单id
     */
    private String accidentId;

    /**
     * 审核类型： 默认为旅客赔偿
     */
    private String auditType;

    /**
     * 赔偿类型 不正常航班赔偿0、异常行李1、超售旅客2
     */
    private String payType;

    /**
     * 原航班号
     */
    private String flightNo;

    /**
     * 原航班日期
     */
    private String flightDate;

    /**
     * 服务航站
     */
    private String serviceCity;

    /**
     * 赔偿人数
     */
    private String paxTotalCount;

    /**
     * 赔偿总额
     */
    private String totalMoney;

    /**
     * 发起人（申请人）
     */
    private String applyUser;

    /**
     * 发起时间
     */
    private String applyTime;

    /**
     * 赔偿单状态 0草稿、1审核中、2未生效、3生效、4已关闭5未通过6驳回7待审核 8逾期 选择多个已逗号分隔
     */
    private String status;

    /**
     * 审批节点 1-aoc 2-值班经理
     */
    private String processNode;
}
