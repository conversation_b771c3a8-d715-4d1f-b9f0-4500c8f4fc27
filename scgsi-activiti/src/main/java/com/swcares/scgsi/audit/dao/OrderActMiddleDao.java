package com.swcares.scgsi.audit.dao;

import com.swcares.scgsi.audit.entity.OrderActMiddle;
import com.swcares.scgsi.base.BaseJpaDao;
import org.springframework.data.repository.query.Param;

/**
 * ClassName：com.swcares.scgsi.audit.dao <br>
 * Description：赔偿单与审核流 中间表 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月11日 15:14 <br>
 * @version v1.0 <br>
 */
public interface OrderActMiddleDao extends BaseJpaDao<OrderActMiddle,String> {



    /**
     * Title： findByActPinId <br>
     * Description： 根据流程实例id 查询中间表数据<br>
     * author：傅欣荣 <br>
     * date：2020/6/16 16:34 <br>
     * @param
     * @return
     */
    OrderActMiddle findByActPinId(@Param("ACT_PIN_ID") String actPinId) throws Exception;
}
