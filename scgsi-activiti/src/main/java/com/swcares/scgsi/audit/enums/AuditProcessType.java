package com.swcares.scgsi.audit.enums;

/**
 * ClassName：com.swcares.scgsi.audit.enums <br>
 * Description：审核流程类型 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月11日 11:22 <br>
 * @version v1.0 <br>
 */
public enum AuditProcessType {
    //审核流程二级审核dp_aoc
    LAUNCH_PROCESS_KEY_AOC2("dp_aoc_second_level")
    //审核流程一级审核dp_aoc1
    ,LAUNCH_PROCESS_KEY_AOC1("dp_aoc_one_level")
    //审核流程-根据判断赔偿金额进入不同的审批流程
    ,LAUNCH_PROCESS_KEY_AMOUNT("amount_process");



    AuditProcessType(String node){this.node = node;}
    private String node;//节点
    public String getNode() {
        return this.node;
    }


}
