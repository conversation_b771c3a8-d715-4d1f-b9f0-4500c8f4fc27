package com.swcares.scgsi.audit.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * ClassName：com.swcares.scgsi.audit.entity <br>
 * Description：服务单与审核ACT流程实例id关联表 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月11日 14:08 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "DP_ACT_MIDDLE")
public class OrderActMiddle {

    /**
     * 主键
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;
    /**
     *  服务单id
     */
    @Column(name = "ORDER_ID")
    private String orderId;

    /**
     * 流程实例id
     */
    @Column(name = "ACT_PIN_ID")
    private String actPinId;

    /**
     *
     * 处理审核时更新id
     */
    @Column(name = "RECORD_ID")
    private String recordId;
}
