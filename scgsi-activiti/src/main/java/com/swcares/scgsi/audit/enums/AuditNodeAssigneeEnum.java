package com.swcares.scgsi.audit.enums;

/**
 * ClassName：com.swcares.scgsi.audit.enums <br>
 * Description：审核节点审批人查询。条件： 角色-节点 -值班状态 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月12日 14:56 <br>
 * @version v1.0 <br>
 */
public enum AuditNodeAssigneeEnum {

    AOC_NODE_ASSIGNEE("AOC值班经理角色-系统","AOC审核",true),//AOC审批节点、值班人员查询
    DIRECTOR_NODE_ASSIGNEE("异常行李值班主任","异常行李值班主任",true),//异常行李审核-值班主任，值班人员查询
    MANAGER_NODE_ASSIGNEE("异常行李经理","异常行李经理",false),//异常行李审核-经理，人员查询
    GENERAL_MANAGER_NODE_ASSIGNEE("异常行李总经理","异常行李总经理",false);//异常行李审核-总经理，人员查询


    private String role;

    private String node;

    private Boolean isOnDuty;

    AuditNodeAssigneeEnum(String role, String node,Boolean isOnDuty) {
        this.role = role;
        this.node = node;
        this.isOnDuty = isOnDuty;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getNode() {
        return node;
    }

    public void setNode(String node) {
        this.node = node;
    }

    public Boolean getOnDuty() {
        return isOnDuty;
    }

    public void setOnDuty(Boolean onDuty) {
        isOnDuty = onDuty;
    }
}
