package com.swcares.scgsi.audit.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.audit.entity <br>
 * Description：航延补偿申请单审核表 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月11日 10:19 <br>
 * @version v1.0 <br>
 */
@Data
@Table(name = "DP_ORDER_AUDIT")
@Entity
public class OrderAudit {

    /**
     * 主键
     */
    @Id
    @Column(name = "ID")
   /* @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")*/
    private String id;

    /**
     * 服务单ID
     */
    @Column(name = "ORDER_ID")
    private String orderId;

    /**
     * 流程节点 1流程发起,2AOC,3值班经理
     */
    @Column(name = "PROCESS_NAME")
    private String processName;

    /**
     * 审核备注
     */
    @Column(name = "REMARK")
    private String remark;

    /**
     * 审核状态  0通过、1不同意 2驳回 3.发起
     */
    @Column(name = "OPINION")
    private String opinion;

    /**
     * 状态 0待处理1已处理
     */
    @Column(name = "STATUS")
    private String status;

    /**
     * 审核者
     */
    @Column(name = "AUDITOR")
    private String auditor;

    /**
     * 审核时间
     */
    @Column(name = "AUDIT_TIME")
    private Date auditTime;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;



}
