package com.swcares.scgsi.audit.listener;

import com.swcares.scgsi.api.ActivitiService;
import com.swcares.scgsi.audit.dao.OrderActMiddleDao;
import com.swcares.scgsi.audit.dao.impl.OrderAuditDaoImpl;
import com.swcares.scgsi.audit.entity.OrderActMiddle;
import com.swcares.scgsi.audit.enums.AuditStatusEnum;
import com.swcares.scgsi.audit.service.OrderAuditService;
import com.swcares.scgsi.audit.vo.AocOndutyUserVo;
import com.swcares.scgsi.base.SpringUtil;
import com.swcares.scgsi.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

import static com.swcares.scgsi.audit.service.impl.OrderAuditServiceImpl.*;

/**
 * ClassName：com.swcares.scgsi.listener <br>
 * Description：审核流程中-流程线监听（驳回操作）<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月06日 14:37 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class ActivitiMsgPushListener implements ExecutionListener {


    private static final long serialVersionUID = -760284447071915048L;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateExecution execution) throws Exception {
        OrderAuditService orderAuditService = SpringUtil.getBean(OrderAuditService.class);
        OrderActMiddleDao orderActMiddleDao = SpringUtil.getBean(OrderActMiddleDao.class);
        RedisService redisService = SpringUtil.getBean(RedisService.class);
        log.info("赔偿单审核- 【推送处理结果】，流程实例id{}",execution.getProcessInstanceId());
        OrderActMiddle orderActMiddle = orderActMiddleDao.findByActPinId(execution.getProcessInstanceId());
        if(null == orderActMiddle) return;
        log.info("赔偿单审核- 【推送处理结果】，流程实例id{}，关联赔偿单号{}",execution.getProcessInstanceId(),orderActMiddle.getOrderId());
        /*orderAuditService.sendAuditResultMessage(orderActMiddle.getOrderId());*/
        redisService.set(AUDIT_STATUS_RESULT_PREFIX+orderActMiddle.getOrderId(),true, AUDIT_EXPIRE_DATE);


    }
}