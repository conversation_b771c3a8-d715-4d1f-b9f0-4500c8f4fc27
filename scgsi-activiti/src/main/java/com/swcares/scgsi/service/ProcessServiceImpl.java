package com.swcares.scgsi.service;


import com.swcares.scgsi.api.ProcessService;
import com.swcares.scgsi.vo.QueryTask;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.engine.*;
import org.activiti.engine.HistoryService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.activiti.engine.impl.context.Context;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.activiti.engine.impl.pvm.PvmTransition;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.activiti.image.ProcessDiagramGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 流程服务相关
 * 包含方法：流程图查看（可高亮显示）、任务列表查询、个人任务查询
 */
@Slf4j
@Service
public class ProcessServiceImpl implements ProcessService {

    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private TaskService taskService;

    private static volatile ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();


    @Override
    public byte[] showModelerImg(String deploymentId) throws IOException {
        InputStream in = null;
        OutputStream out = null;
        // 创建仓库服务对象
        RepositoryService repositoryService = processEngine.getRepositoryService();
        // 从仓库中找需要展示的文件
        List<String> names = repositoryService.getDeploymentResourceNames(deploymentId);
        String imageName = null;
        for (String name : names) {
            if (name.indexOf(".png") >= 0) {
                imageName = name;
            }
        }
        if (imageName != null) {
            // 通过部署ID和文件名称得到文件的输入流
            in = repositoryService.getResourceAsStream(deploymentId, imageName);
            byte[] buffer = new byte[in.available()];
            in.read(buffer);
            in.close();
            return buffer;
        } return null;
    }


    @Override
    public byte[] showProcessImg(String processInstanceId,boolean isHighlight) throws IOException {
        InputStream imageStream = null;
        HistoricProcessInstance processInstance = historyService//processEngine.getHistoryService()
                .createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
        ProcessEngineConfiguration proConfig = processEngine.getProcessEngineConfiguration();
        Context.setProcessEngineConfiguration((ProcessEngineConfigurationImpl) proConfig);

        ProcessDiagramGenerator diagramGenerator = proConfig.getProcessDiagramGenerator();
        if(isHighlight) {
            ProcessDefinitionEntity definitionEntity = (ProcessDefinitionEntity) repositoryService.getProcessDefinition(processInstance.getProcessDefinitionId());

            List<HistoricActivityInstance> highLightedActivitList = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).list();
            //高亮环节id集合
            List<String> highLightedActivitis = new ArrayList<String>();
            //高亮线路id集合
            List<String> highLightedFlows = getHighLightedFlows(definitionEntity, highLightedActivitList);

            //判断是否有值，流程是否结束
            // 获取流程当前所在节点
            /*List<Task> t = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
            Task task = t.get(0);
            String excId = task.getExecutionId();
            // 通过当前节点执行id获取执行实体
            ExecutionEntity execution = (ExecutionEntity) runtimeService.createExecutionQuery().executionId(excId).singleResult();
            String activitiId = execution.getActivityId();*/
            for (HistoricActivityInstance tempActivity : highLightedActivitList) {
                //当id一致时，加入list，并退出，解决流程回退后，高亮节点显示不对问题
                String activityId = tempActivity.getActivityId();
               /* if (activitiId.equals(activityId)) {
                    highLightedActivitis.add(activityId);
                    break;
                } else {*/
                    highLightedActivitis.add(activityId);
               /* }*/
            }
            //中文显示的是口口口，设置字体就好了
            imageStream = diagramGenerator.generateDiagram(bpmnModel, "png", highLightedActivitis, highLightedFlows, "宋体", "", null, null, 1.0);
        }else{
            //单独返回流程图，不高亮显示
            imageStream = diagramGenerator.generateDiagram(bpmnModel,"png",
                proConfig.getActivityFontName(),
                proConfig.getLabelFontName(),
                proConfig.getAnnotationFontName(),
                proConfig.getClassLoader());
        }
        byte[] buffer = new byte[imageStream.available()];
        imageStream.read(buffer);
        imageStream.close();
        return buffer;
    }


    private List<String> getHighLightedFlows(ProcessDefinitionEntity processDefinitionEntity,
            List<HistoricActivityInstance> historicActivityInstances) {

        List<String> highFlows = new ArrayList<String>();// 用以保存高亮的线flowId
        for (int i = 0; i < historicActivityInstances.size() - 1; i++) { // 对历史流程节点进行遍历
            ActivityImpl activityImpl = processDefinitionEntity
                    .findActivity(historicActivityInstances.get(i).getActivityId());// 得到节点定义的详细信息
            List<ActivityImpl> sameStartTimeNodes = new ArrayList<ActivityImpl>();// 用以保存后需开始时间相同的节点
            ActivityImpl sameActivityImpl1 = processDefinitionEntity
                    .findActivity(historicActivityInstances.get(i + 1).getActivityId());
            // 将后面第一个节点放在时间相同节点的集合里
            sameStartTimeNodes.add(sameActivityImpl1);
            for (int j = i + 1; j < historicActivityInstances.size() - 1; j++) {
                HistoricActivityInstance activityImpl1 = historicActivityInstances
                        .get(j);// 后续第一个节点
                HistoricActivityInstance activityImpl2 = historicActivityInstances
                        .get(j + 1);// 后续第二个节点
                if (activityImpl1.getStartTime().equals(
                        activityImpl2.getStartTime())) {
                    // 如果第一个节点和第二个节点开始时间相同保存
                    ActivityImpl sameActivityImpl2 = processDefinitionEntity
                            .findActivity(activityImpl2.getActivityId());
                    sameStartTimeNodes.add(sameActivityImpl2);
                } else {
                    // 有不相同跳出循环
                    break;
                }
            }
            List<PvmTransition> pvmTransitions = activityImpl
                    .getOutgoingTransitions();// 取出节点的所有出去的线
            for (PvmTransition pvmTransition : pvmTransitions) {
                // 对所有的线进行遍历
                ActivityImpl pvmActivityImpl = (ActivityImpl) pvmTransition.getDestination();
                // 如果取出的线的目标节点存在时间相同的节点里，保存该线的id，进行高亮显示
                if (sameStartTimeNodes.contains(pvmActivityImpl)) {
                    highFlows.add(pvmTransition.getId());
                }
            }
        }
        return highFlows;
    }




    //============任务查询=======================


    @Override
    public List<QueryTask> findPersonTaskAll() {
        List<Task> list = taskService//processEngine.getTaskService()// 与正在执行的任务管理相关的service
                .createTaskQuery()// 创建任务查询对象
                .orderByTaskCreateTime().asc()// 使用创建时间的升序排列
                .list();// 返回列表
        return getQueryReturnTask(list,null);
    }


    @Override
    public List<QueryTask> findPersonTaskByUser(String assignee) {
        List<Task> list = taskService//processEngine.getTaskService()// 与正在执行的任务管理相关的service
                    .createTaskQuery()// 创建任务查询对象
                    .taskAssignee(assignee)// 指定个人任务查询，指定办理人
                    .orderByTaskCreateTime().asc()// 使用创建时间的升序排列
                    .list();// 返回列表
        return getQueryReturnTask(list,assignee);
    }

    @Override
    public List<QueryTask> findCandidateTaskByUser(String assignee) {
        List<Task> list = taskService
                .createTaskQuery()
                /**查询条件（where部分）*/
                .taskCandidateUser(assignee)
                /**排序*/
                .orderByTaskCreateTime().asc()
                /**返回结果集*/
                .list();
        return getQueryReturnTask(list,assignee);
    }


    @Override
    public List<HistoricTaskInstance> getProcessFinishedByUser(String userId){
         return historyService.createHistoricTaskInstanceQuery()
                .taskAssignee(userId)
                .finished()
                .list();
    }



    @Override
    public List<HistoricActivityInstance> queryHistoricActivitiInstance(String processInstanceId ) {
        return historyService//processEngine.getHistoryService()
                .createHistoricActivityInstanceQuery()
                .processInstanceId(processInstanceId)

                .finished()
                .list();
    }


    @Override
    public boolean isProcessEnd(String processInstanceId) {
        ProcessInstance pi = runtimeService//processEngine.getRuntimeService()//表示正在执行的流程实例和执行对象
                .createProcessInstanceQuery()//创建流程实例查询
                .processInstanceId(processInstanceId)//使用流程实例ID查询
                .singleResult();
        if (pi != null) {
            log.info("流程实例id:"+processInstanceId+" 的任务已经结束");
            return false;
        }
        return true;
    }




    //封装返回对象
    private List<QueryTask> getQueryReturnTask(List<Task> list,String assignee){
        List<QueryTask> voList = new ArrayList<>();
        for (Task task : list) {
            QueryTask tvo = new QueryTask();
            tvo.setCreateTime(task.getCreateTime());
            tvo.setId(task.getId());
            tvo.setExecutionId(task.getExecutionId());
            tvo.setName(task.getName());
            tvo.setProcessDefinitionId(task.getProcessDefinitionId());
            tvo.setProcessInstanceId(task.getProcessInstanceId());
            tvo.setAssignee(task.getAssignee()==null?assignee:task.getAssignee());
            voList.add(tvo);
        }
        return voList;
    }
}
