package com.swcares.scgsi.service;


import com.swcares.scgsi.api.ActivitiService;
import com.swcares.scgsi.config.RejectTaskCMD;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.*;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.RepositoryServiceImpl;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.impl.interceptor.Command;
import org.activiti.engine.impl.persistence.entity.HistoricActivityInstanceEntity;
import org.activiti.engine.impl.persistence.entity.HistoricActivityInstanceEntityManager;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.impl.pvm.PvmTransition;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

/**
 * 部署流程相关
 * 包含方法：通过url读入流程图文件进行部署流程 、 通过zip方式进行部署流程
 *      处理任务
 */
@Slf4j
@Service
public class ActivitiServiceImpl implements ActivitiService {

    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private TaskService taskService;
    /**
     * 部署流程
     */
    //private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
  /*  ProcessEngine processEngine = ProcessEngineConfiguration.createStandaloneInMemProcessEngineConfiguration()
            .setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_FALSE)
            .setJdbcUrl("jdbc:oracle:thin:@************:1521/gsspsc")
            .setJdbcUsername("gsspsc")
            .setJdbcPassword("SDAzhbzpt~")
            .setJdbcDriver("oracle.jdbc.driver.OracleDriver")
            .setAsyncExecutorActivate(false)
            .buildProcessEngine();*/

    @Autowired
    HistoryService historyService ;//= processEngine.getHistoryService();
    @Autowired
    RepositoryService repositoryService ;//= processEngine.getRepositoryService();

    @Autowired
    ManagementService managementService;

    /**
     * 直接读取流程文件进行部署
     * @param delployName 部署流程名称
     * @param bpmnFielUrl 流程文件bpmn路径
     * @param pngFielUrl   流程文件png路径
     * @return boolean true 成功
     */
    @Override
    public boolean delployFlow(String delployName,String bpmnFielUrl,String pngFielUrl){
        Deployment deployment = repositoryService
                .createDeployment()
                .name(delployName)
                .addClasspathResource(bpmnFielUrl)
                .addClasspathResource(pngFielUrl)
                .deploy();
        if (null == deployment) return false;
        return true;
    }


    /**
     * 通过zip格式压缩包来部署流程
     * @param delployName 部署流程名称
     * @param FieldUrl 流程文件zip读取URL
     * @
     * @return
     */
    @Override
    public boolean delployFlowZIP(String delployName,String FieldUrl,boolean isResources){
        //默认从项目配置文件位置resources中读取
        //从绝对路径如F:\Lprocess.zip
        InputStream in = null;
        try {
            if(!isResources){
                ZipFile zf = new ZipFile(FieldUrl);
                in = new BufferedInputStream(new FileInputStream(FieldUrl));
            }else
            in = this.getClass().getClassLoader().getResourceAsStream(FieldUrl);
            Deployment deployment = repositoryService
                    .createDeployment()
                    .name(delployName)	//流程名字
                    .addZipInputStream(new ZipInputStream(in))
                    .deploy();
            if(null == deployment)return false;

        } catch (IOException e) {
            log.error("ActivitiService服务方法：delployFlowZIP。通过zip部署流程异常！！",e);
            return false;
        }
        return true;
    }

    /**
     * 启动一个流程实例
     * @param userId 用户id
     * @param key 通过key启动的流程实例每次都是根据最新版本的流程定义启动的
        isCompleteNext 启动流程时，发起人第一个是否需要自动处理（如启动流程后，第一步节点为申请人提交）
     * @param data 流程变量{该调用流程key，所需的流程变量，可在此阶段传入或分批次传入（调用处理任务方法时传入）}
     */
    @Override
    public boolean startProcess(String userId,String key,boolean isCompleteNext,Map<String, Object> data){
        Authentication.setAuthenticatedUserId(userId);
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(key,data);
        if(null ==  processInstance) return false;
        log.info("用户："+userId +" ,新增key："+key+" ,实例。流程实例ID："+processInstance.getId()
                +",流程部署id："+processInstance.getDeploymentId()
                +"流程定义的ID："+processInstance.getProcessDefinitionId());

        if(isCompleteNext){
            completeUserIdTask(userId,processInstance.getId());
        }
        return true;
    }

    @Override
    public ProcessInstance startProcessResult(String userId,String key,boolean isCompleteNext,Map<String, Object> data){
        Authentication.setAuthenticatedUserId(userId);
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(key,data);
        log.info("用户："+userId +" ,新增key："+key+" ,实例。流程实例ID："+processInstance.getId()
                +",流程部署id："+processInstance.getDeploymentId()
                +"流程定义的ID："+processInstance.getProcessDefinitionId());

        if(isCompleteNext){
            completeUserIdTask(userId,processInstance.getId());
        }
        return processInstance;
    }

    /**
     * 处理发起人的任务
     */
    private void completeUserIdTask(String userId ,String processId){
        //获取当前流程实例，当前申请人的待办任务,并执行当前流程实例的下一步任务
        Task task = null;
        TaskQuery query = taskService.createTaskQuery().taskCandidateOrAssigned(userId).active();
        List<Task> todoList = query.list();
        if(!todoList.isEmpty()){
            for (Task tmp : todoList) {
                if(tmp.getProcessInstanceId().equals(processId)){
                    task = tmp;
                    break;
                }
            }
            taskService.complete(task.getId());
        }
    }

    //+++++++++++++++++++处理任务、驳回（自由跳转、跳转到上一级）+++++++++++++

    /**
     * 流程任务处理，包含：普通流程处理提交、驳回到流程最初(选择历史中最先完成的user task)、驳回到流程上一级（从历史中找到最后完成的activity）
     * @param taskId 当前任务id
     * @param userId 当前任务处理人 可以不传
     * @param isReject 跳转节点。为空：普通流程处理提交操作
     * @param rejectType 1 是回到最初,不传值，默认回到上一级
     * @param description 批注（如审批意见）
     * @param variables 流程任务下一级节点所需的流程变量
     */
    @Override
    public boolean commitProcess(String taskId,String userId,String isReject,String rejectType,String description,
                               Map<String, Object> variables){
        if (variables == null) {
            variables = new HashMap<String, Object>();
        }
        boolean falg = false;
        // 跳转节点为空，默认提交操作
        if (StringUtils.isEmpty(isReject)) {
            falg = complementTask(taskId,userId,description,variables);
        } else {// 流程转向操作
            falg = complementTaskReject(taskId, rejectType,description, variables);
        }
        return falg;
    }



    /**
     * 处理流程任务提交
     * @param taskId 任务id
     * @param  userId 任务审批者
     * @param description 批注（如审批意见）
     * @param variables 流程变量（流程任务下一级节点所需的流程变量，没有就不传。）
     */
    @Override
    public boolean complementTask(String taskId,String userId, String description,Map<String, Object> variables){
        if (variables == null) {
            variables = new HashMap<String, Object>();
        }
        try {
           // TaskService taskService=processEngine.getTaskService();
            // 使用任务id,获取任务对象，获取流程实例id
            Task task=taskService.createTaskQuery().taskId(taskId).singleResult();
            //利用任务对象，获取流程实例id
            String processInstancesId=task.getProcessInstanceId();
            //添加处理批注
            if(StringUtils.isNotBlank(description)) taskService.addComment(taskId,processInstancesId,description);
            //设置当前任务处理的审批人，方便驳回
            if(StringUtils.isNotEmpty(userId)) taskService.setAssignee(taskId,userId);
            //处理任务，传入流程变量
            taskService.complete(taskId,variables);
        }catch (ActivitiException e){
            log.error("activiti服务方法：处理任务提交，出现异常！！！",e);
            return false;
        }
       return true;
    }

    /**
     * 流程驳回- 驳回到流程最初(选择历史中最先完成的user_task)、驳回到流程上一级（从历史中找到最后完成的activity）
     * @param currentTaskID 任务id
     * @param rejectType 1是回到最初,不传值，默认回到上一级{在同一流程中最好不要使用两种驳回方式混合}
     * @param description 批注
     * @param variables 流程变量（流程任务下一级节点所需的流程变量，没有就不传。）
     * @return true 驳回成功 false 失败有异常
     */
    @Override
    public boolean complementTaskReject(String currentTaskID,String rejectType,
                                     String description,Map<String, Object> variables){
        if (variables == null) {
            variables = new HashMap<String, Object>();
        }
        try {
            // region 正在执行的任务实例 historicCurrentTaskInstance 带流程变量，任务变量
            HistoricTaskInstance historicCurrentTaskInstance = historyService
                    .createHistoricTaskInstanceQuery()
                    .taskId(currentTaskID)
                    .includeProcessVariables()
                    .includeTaskLocalVariables()
                    .singleResult();

            // 流程定义ID
            String processDefinitionId = historicCurrentTaskInstance.getProcessDefinitionId();
            // 流程实例ID
            String processInstanceId = historicCurrentTaskInstance.getProcessInstanceId();
            // 流程定义实体
            ProcessDefinitionEntity processDefinition =
                    (ProcessDefinitionEntity) repositoryService.getProcessDefinition(processDefinitionId);

            //获取流程实例
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            //-------------获取目标活动节点----------------------
            String destinationTaskID = this.getDestinationTaskID(processInstance.getId(),rejectType);

            //region 目标任务实例 historicDestinationTaskInstance 带流程变量，任务变量
            HistoricTaskInstance historicDestinationTaskInstance = historyService
                    .createHistoricTaskInstanceQuery()
                    .taskId(destinationTaskID)
                    .includeProcessVariables()
                    .includeTaskLocalVariables()
                    .singleResult();

            //-----------查询历史记录-------------
            // region 历史活动节点实例集合
            List<HistoricActivityInstance> historicActivityInstanceList =
                    historyService
                            .createHistoricActivityInstanceQuery()
                            .processInstanceId(processInstanceId)
                            .orderByHistoricActivityInstanceStartTime()
                            .asc()
                            .list();

            // 获取目标任务的节点信息historicActivityInstance
            ActivityImpl destinationActivity = processDefinition
                    .findActivity(historicDestinationTaskInstance.getTaskDefinitionKey());

            // 定义一个历史活动节点集合，完成任务后要添加的历史活动节点集合
            List<HistoricActivityInstanceEntity> insertHistoricTaskActivityInstanceList = new ArrayList<>();
            // 目标任务编号
            Integer destinationTaskInstanceId = Integer.valueOf(destinationTaskID);

            // 有序 （找出添加的历史活动节点。在目标任务节点前包含目标任务节点）
            for (int i = 0; i < historicActivityInstanceList.size(); i++) {
                HistoricActivityInstance historicActivityInstance = historicActivityInstanceList.get(i);
                // 历史活动节点的任务编号
                Integer historicActivityInstanceTaskId;
                String taskId = historicActivityInstance.getTaskId();
                if (taskId != null) {
                    historicActivityInstanceTaskId = Integer.valueOf(taskId);
                    if (historicActivityInstanceTaskId < destinationTaskInstanceId) {
                        insertHistoricTaskActivityInstanceList.add((HistoricActivityInstanceEntity) historicActivityInstance);
                    }
                    if(historicActivityInstanceTaskId.equals(destinationTaskInstanceId)){
                        ((HistoricActivityInstanceEntity) historicActivityInstance).setEndTime(null);
                        insertHistoricTaskActivityInstanceList.add((HistoricActivityInstanceEntity) historicActivityInstance);
                    }
                } else {
                    if (historicActivityInstance.getActivityType().equals("startEvent")) {
                        insertHistoricTaskActivityInstanceList.add((HistoricActivityInstanceEntity) historicActivityInstance);
                    } else if (historicActivityInstance.getActivityType().equals("exclusiveGateway")) {
                        insertHistoricTaskActivityInstanceList.add((HistoricActivityInstanceEntity) historicActivityInstance);
                    }
                }
            }
            // 获取流程定义的节点信息
            List<ActivityImpl> processDefinitionActivities = processDefinition.getActivities();
            // 用于保存正在执行的任务节点信息
            ActivityImpl currentActivity = null;
            // 用于保存原来的任务节点的出口信息
            PvmTransition pvmTransition = null;
            // 保存原来的流程节点出口信息
            for (ActivityImpl activity : processDefinitionActivities) {
                if (historicCurrentTaskInstance.getTaskDefinitionKey().equals(activity.getId())) {
                    currentActivity = activity;
                    // 备份
                    pvmTransition = activity.getOutgoingTransitions().get(0);
                    // 清空当前任务节点的出口信息
                    activity.getOutgoingTransitions().clear();
                }
            }
            // 执行流程转向processEngine.getManagementService()
            managementService.executeCommand(
                    new RejectTaskCMD(historicDestinationTaskInstance, historicCurrentTaskInstance, destinationActivity));
            // 获取目标任务的流程变量
            Map<String, Object> processVariables = historicDestinationTaskInstance.getProcessVariables();
            //////////////////////////////////////
            // 完成当前任务，任务走向目标任务
            taskService.addComment(currentTaskID,processInstanceId,description);
            processVariables.putAll(variables);
            taskService.complete(currentTaskID, processVariables);

            if (currentActivity != null) {
                // 清空临时转向信息
                currentActivity.getOutgoingTransitions().clear();
            }
            if (currentActivity != null) {
                // 恢复原来的走向
                currentActivity.getOutgoingTransitions().add(pvmTransition);
            }
            //重置流程实例的历史活动节点
            resetHistoricActivityNode(processInstanceId,insertHistoricTaskActivityInstanceList);
        } catch (Exception e) {
            log.error("activiti服务方法：处理任务驳回，出现异常！！！",e);
            return false;
        }
        return true;

    }

    /**
     *  重置流程实例的历史活动节点{避免流程跳转后显示流程节点不对}
     * @param processInstanceId 流程实例ID（删除该id下所有的历史活动节点）
     * @param insertHistoricTaskActivityInstanceList 添加的历史活动节点集合
     */
    private void resetHistoricActivityNode(String processInstanceId,
                List<HistoricActivityInstanceEntity> insertHistoricTaskActivityInstanceList) throws Exception{
        // 删除活动节点 processEngine.getManagementService()
       managementService.executeCommand(
                (Command<List<HistoricActivityInstanceEntity>>) commandContext -> {
                    HistoricActivityInstanceEntityManager historicActivityInstanceEntityManager =
                            commandContext.getHistoricActivityInstanceEntityManager();
                    // 删除所有的历史活动节点
                    historicActivityInstanceEntityManager
                            .deleteHistoricActivityInstancesByProcessInstanceId(processInstanceId);
                    // 提交到数据库
                    commandContext.getDbSqlSession().flush();
                    // 添加历史活动节点的
                    for (HistoricActivityInstanceEntity historicActivityInstance : insertHistoricTaskActivityInstanceList) {
                        historicActivityInstanceEntityManager.insertHistoricActivityInstance(historicActivityInstance);
                    }
                    // 提交到数据库
                    commandContext.getDbSqlSession().flush();
                    return null;
                }
        );
    }

    /**
     * Title：getDestinationTaskID <br>
     * Description： 获得回退的目标节点 taskId<br>
     * author：傅欣荣 <br>
     * date：2020/6/26 13:56 <br>
     * @param processInstanceId
     * @param rejectType
     * @return
     */
    private String getDestinationTaskID(String processInstanceId,String rejectType){

        if(StringUtils.isEmpty(rejectType)){
            //查找上一个user task节点
            List<HistoricActivityInstance> historicActivityInstances = historyService
                    .createHistoricActivityInstanceQuery().activityType("userTask")
                    .processInstanceId(processInstanceId)
                    .finished()
                    .orderByHistoricActivityInstanceEndTime().desc().list();
            //上一节点的taskID
            return  historicActivityInstances.get(0).getTaskId();
        }
        List<HistoricActivityInstance> historicActivityInstances = historyService
                .createHistoricActivityInstanceQuery().activityType("userTask")
                .processInstanceId(processInstanceId)
                .finished()
                .orderByHistoricActivityInstanceEndTime().asc().list();
        //最初一节点的taskID
        return historicActivityInstances.get(0).getTaskId();
    }


    /**
     * Title：getDestinationTaskInfo <br>
     * Description： 获得回退的目标节点 taskId<br>
     * author：傅欣荣 <br>
     * date：2020/6/26 13:56 <br>
     * @param processInstanceId
     * @param rejectType
     * @return
     */
    @Override
    public HistoricActivityInstance getDestinationTaskInfo(String processInstanceId,String rejectType){

        if(StringUtils.isEmpty(rejectType)){
            //查找上一个user task节点
            List<HistoricActivityInstance> historicActivityInstances = historyService
                    .createHistoricActivityInstanceQuery().activityType("userTask")
                    .processInstanceId(processInstanceId)
                    .finished()
                    .orderByHistoricActivityInstanceEndTime().desc().list();
            //上一节点的taskID
            return  historicActivityInstances.get(0);
        }
        List<HistoricActivityInstance> historicActivityInstances = historyService
                .createHistoricActivityInstanceQuery().activityType("userTask")
                .processInstanceId(processInstanceId)
                .finished()
                .orderByHistoricActivityInstanceEndTime().asc().list();
        //最初一节点的taskID
        return historicActivityInstances.get(0);
    }



    //取任务id对应的流程结束节点信息
    public ActivityImpl findActivityInfo(String taskId,String activityId){
        if(StringUtils.isEmpty(taskId) || StringUtils.isEmpty(activityId) ){
            return null;
        }
        TaskEntity task = (TaskEntity) taskService.createTaskQuery().taskId(
        taskId).singleResult();
        // 取得流程定义  
        ProcessDefinitionEntity processDefinition = (ProcessDefinitionEntity) ((RepositoryServiceImpl)
                repositoryService).getDeployedProcessDefinition(task.getProcessDefinitionId());

        if (processDefinition == null) {
            return null;
        }
        // 根据流程定义，获取该流程实例的结束节点  
        if(activityId.toUpperCase().equals("END")){
            for (ActivityImpl activityImpl : processDefinition.getActivities()) {
                List<PvmTransition> pvmTransitionList = activityImpl.getOutgoingTransitions();
                if(pvmTransitionList.isEmpty())return activityImpl;
            }
        }
        return null;
    }


    @Override
    public String getProcessInstancesId(String taskId){
        // 使用任务id,获取任务对象，获取流程实例id
        Task task=taskService.createTaskQuery().taskId(taskId).singleResult();
        //利用任务对象，获取流程实例id
        return task.getProcessInstanceId();
    }

    @Override
    public boolean isProcessEnd(String processInstanceId) {
        ProcessInstance pi = runtimeService//processEngine.getRuntimeService()//表示正在执行的流程实例和执行对象
                .createProcessInstanceQuery()//创建流程实例查询
                .processInstanceId(processInstanceId)//使用流程实例ID查询
                .singleResult();
        if (null != pi) {
            return false;
        }
        log.info("流程实例id:"+processInstanceId+" 的任务已经结束");
        return true;
    }



}
