package com.swcares.scgsi.api;

import com.swcares.scgsi.bean.Authentication;
import com.swcares.scgsi.bean.PayOrder;

import java.util.Map;





/**
 * ClassName：com.swcares.scgsi.api.PayService <br>
 * Description：支付公共接口—对外暴露的所有接口方法 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
public interface PayService<O extends PayOrder>  {


	/*加载配置*/
	PayService setPayConfigStorage(String payConfigStorage);

	/**
	 * Title： authCodeUrl <br>
	 * Description： 微信授权url <br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:29 <br>
	 * @return
	 */
	String authCodeUrl();

	/**
	 * Title： getOpenid <br>
	 * Description： 获取openid <br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:29 <br>
	 * @param code 用户授权后code
	 * @return
	 */
	String getOpenid(String code);



	/**
	 * Title： getRealNameAuthUrl<br>
	 * Description： 实名认证授权url<br>
	 * author：傅欣荣 <br>
	 * date：2020/6/23 13:11 <br>
	 * @param scope 应用授权作用域，
	 *
	 * pay_identity 是  实名验证 - 校验姓名和身份证是否匹配
	 * @return
	 */
	String getRealNameAuthUrl(String scope);
	/**
	 * Title： getPayIdentityToken<br>
	 * Description： 获取实名认证-token<br>
	 * author：傅欣荣 <br>
	 * date：2020/6/23 9:46 <br>
	 * @param
	 * @return
	 */
	String getPayIdentityToken(String code,String openid);

	/**
	 * Title： transfer <br>
	 * Description： 转账 <br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:29 <br>
	 * @param order 转账订单 调用api不同，传入参数对象不同
	 * @return 对应的转账结果
	 */
	  Map<String, Object> transfer(O order);

	/**
	 * Title：transfer <br>
	 * Description： 转账接口-带处理器<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:27 <br>
	 * @param order 转账订单
	 * @param clazz 将微信反馈结果，以对象方式反馈，装载的类
	 * @param <T>
	 * @return
	 */
	<T> T transfer(O order,Class<T> clazz);


	/**
	 * Title：query <br>
	 * Description： 交易查询接口<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:27 <br>
	 * @param tradeNo    支付平台订单号 （微信查询：二选一）
	 * @param outTradeNo 商户单号  不输入则查询该批次全部打款明细；若输入则查询该批次某笔订单
	 * @return 返回查询回来的结果集，支付方原值返回
	 */
	Map<String, Object> query(String tradeNo, String outTradeNo,TransactionType transactionType);

	/**
	 * Title：query <br>
	 * Description： 交易查询接口，带处理器<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:27 <br>
	 * @param tradeNo    支付平台订单号
	 * @param outTradeNo 商户单号
	 * @param clazz      处理器
	 * @param <T>        返回类型
	 * @return 返回查询回来的结果集
	 */
	<T> T query(String tradeNo, String outTradeNo,TransactionType transactionType, Class<T> clazz);


	/**
	 * Title：bankCardQuery <br>
	 * Description： 银行卡信息查询<br>
	 * author：傅欣荣 <br>
	 * date：2020/7/10 16:12 <br>
	 * @param  cardNo 卡号
	 * @return
	 */
	Map<String, Object> bankCardQuery(String cardNo, TransactionType transactionType);

	/**
	 * Title：bankCardQuery <br>
	 * Description： 银行卡信息查询<br>
	 * author：傅欣荣 <br>
	 * date：2020/7/10 16:12 <br>
	 * @param  cardNo 卡号
	 * @return
	 */
	<T> T  bankCardQuery(String cardNo, TransactionType transactionType, Class<T> clazz);


	/**
	 * Title：authentication <br>
	 * Description： 实名认证-请求 <br>
	 * author：傅欣荣 <br>
	 * date：2020/3/3 13:08 <br>
	 * @param
	 * @return
	 */
	<A extends Authentication> Map<String, Object> authentication(A order);

	/**
	 * Title：authentication <br>
	 * Description：实名认证反馈对应处理类<br>
	 * author：傅欣荣 <br>
	 * date：2020/3/3 16:02 <br>
	 * @param  order, clazz
	 * @return T
	 */
	<A extends Authentication,T> T  authentication(A order, Class<T> clazz);


	/**
	 * Title：authenticationQuery <br>
	 * Description： 实名认证-查询<br>
	 * author：傅欣荣 <br>
	 * date：2020/3/3 16:08 <br>
	 * @param  orderId 请求订单号, streamOrderId 流水号
	 * @return java.util.Map<java.lang.String,java.lang.Object>
	 */
	Map<String, Object> authenticationQuery(String orderId,String streamOrderId,TransactionType transactionType);


	/**
	 * Title：authenticationQuery <br>
	 * Description： 实名认证-查询，反馈对应处理类<br>
	 * author：傅欣荣 <br>
	 * date：2020/3/3 16:08 <br>
	 * @param  orderId 请求订单号 , streamOrderId 流水号,clazz 结果接收处理类
	 *                 订单号和流水号任意传一个
	 * @return T 处理类
	 */
	<T> T authenticationQuery(String orderId,String streamOrderId,TransactionType transactionType,Class<T> clazz);


	/**
	 * Title：merchantBalanceQuery <br>
	 * Description： 商户余额查询- 反馈处理类<br>
	 * author：傅欣荣 <br>
	 * date：2020/3/3 17:06 <br>
	 * @param  clazz
	 * @return T
	 */
	<T> T merchantBalanceQuery(Class<T> clazz) ;

}
