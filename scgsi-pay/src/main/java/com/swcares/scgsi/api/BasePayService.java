package com.swcares.scgsi.api;

import com.alibaba.fastjson.JSONObject;
import com.swcares.scgsi.bean.Authentication;
import com.swcares.scgsi.bean.PayOrder;

import java.util.HashMap;
import java.util.Map;


/**
 * ClassName：com.swcares.scgsi.api.BasePayService <br>
 * Description：支付基础服务 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
public abstract class BasePayService<O extends PayOrder>  implements PayService<O> {

	protected String payConfigStorage;

	@Override
	public BasePayService setPayConfigStorage(String payConfigStorage) {
		this.payConfigStorage = payConfigStorage;
		return this;
	}

	public BasePayService() {}

	public BasePayService(String payConfigStorage) {
		this.setPayConfigStorage(payConfigStorage);
	}

	@Override
	public String authCodeUrl() {
		return null;
	}

	@Override
	public String getOpenid(String code) {
		return null;
	}

	@Override
	public Map<String, Object> transfer(O order) {
		return new HashMap<>(0);
	}

	@Override
	public <T> T transfer(O order, Class<T> clazz) {
		return JSONObject.parseObject(JSONObject.toJSONString(transfer(order)), clazz);
	}

	@Override
	public Map<String, Object> bankCardQuery(String cardNo, TransactionType transactionType) {
		return new HashMap<>(0);
	}

	@Override
	public <T> T query(String tradeNo, String outTradeNo,TransactionType transactionType, Class<T> clazz) {
		return JSONObject.parseObject(JSONObject.toJSONString(query(tradeNo,outTradeNo,transactionType)), clazz);
	}

	@Override
	public <T> T bankCardQuery(String cardNo, TransactionType transactionType, Class<T> clazz) {
		return JSONObject.parseObject(JSONObject.toJSONString(bankCardQuery(cardNo,transactionType)), clazz);
	}

	@Override
	public String getRealNameAuthUrl(String scope) {
		return null;
	}

	@Override
	public String getPayIdentityToken(String code, String openid) {
		return null;
	}

	@Override
	public <A extends Authentication> Map<String, Object> authentication(A order) {
		return new HashMap<>(0);
	}

	@Override
	public <A extends Authentication, T> T authentication(A order, Class<T> clazz) {
		return JSONObject.parseObject(JSONObject.toJSONString(authentication(order)), clazz);
	}

	@Override
	public Map<String, Object> authenticationQuery(String orderId, String streamOrderId,TransactionType transactionType) {
		return new HashMap<>(0);
	}

	@Override
	public <T> T authenticationQuery(String orderId, String streamOrderId,TransactionType transactionType, Class<T> clazz) {
		return JSONObject.parseObject(JSONObject.toJSONString(authenticationQuery(orderId,streamOrderId,transactionType)),clazz);
	}

	@Override
	public <T> T merchantBalanceQuery(Class<T> clazz) {
		return null;
	}
}
