package com.swcares.scgsi.util;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

public class Utils {


    /**
     * 元转分
     *
     * @param amount 元的金额
     * @return 分的金额
     */
    public static final int conversionCentAmount(BigDecimal amount) {
        return amount.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
    }

    /**
     * 元,两位小数
     *
     * @param amount 元的金额
     * @return 元的金额 两位小数
     */
    public static final BigDecimal conversionAmount(BigDecimal amount) {
        return amount.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * Title：getNewDate <br>
     * Description： 获取当前系统时间yyyy-MM-dd HH:mm:ss<br>
     * author：傅欣荣 <br>
     * date：2020/3/3 15:43 <br>
     * @param
     * @return
     */
    public static final String getNewDate(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format( new Date());
    }
}
