package com.swcares.scgsi.util.wx;

/**
 * ClassName：com.swcares.scgsi.util.wx <br>
 * Description：微信支付常量类 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月19日 10:32 <br>
 * @version v1.0 <br>
 */
public class WXPayConstants {

    public enum SignType {
        MD5, HMACSHA256
    }

    public static final String FAIL     = "FAIL";
    public static final String SUCCESS  = "SUCCESS";
    public static final String HMACSHA256 = "HMAC-SHA256";
    public static final String MD5 = "MD5";

    public static final String FIELD_SIGN = "sign";
    public static final String FIELD_SIGN_TYPE = "sign_type";
    
    
    
    public static final String APPID = "appid";
    public static final String MCH_ID = "mch_id";
    public static final String NONCE_STR = "nonce_str";

    public static final String SCOPE_PAY_IDENTITY  = "pay_identity";
    
}