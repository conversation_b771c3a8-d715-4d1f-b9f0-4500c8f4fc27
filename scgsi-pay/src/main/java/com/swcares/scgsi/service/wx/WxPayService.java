package com.swcares.scgsi.service.wx;

import com.alibaba.fastjson.JSONObject;
import com.swcares.exception.PayErrorException;
import com.swcares.scgsi.api.BasePayService;
import com.swcares.scgsi.api.TransactionType;
import com.swcares.scgsi.bean.Authentication;
import com.swcares.scgsi.service.wx.bean.WxAuthentication;
import com.swcares.scgsi.service.wx.bean.WxTransferOrder;
import com.swcares.scgsi.util.Utils;
import com.swcares.scgsi.util.wx.HttpClientUtil;
import com.swcares.scgsi.util.wx.WXPayConstants;
import com.swcares.scgsi.util.wx.WXPayUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.springframework.core.io.ClassPathResource;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.security.KeyStore;
import java.util.Map;
import java.util.TreeMap;

/**
 * ClassName：com.swcares.scgsi.service.wx.WxPayService <br>
 * Description：微信-支付处理类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class WxPayService extends BasePayService<WxTransferOrder> {

	protected WxPayConfigStorage wxPayConfigStorage;

	private CloseableHttpClient httpClient;

    private CloseableHttpResponse response;

	public static final String FAILURE = "failure";

	/** 请求超时*/
	private final static int REQ_TIME_OUT = 10000;
	/** 连接超时*/
	private final static int CON_TIME_OUT = 10000;

	/** 设置请求和传输超时时间*/
	private static final RequestConfig requestConfig =
			RequestConfig.custom().setSocketTimeout(REQ_TIME_OUT).setConnectTimeout(CON_TIME_OUT).build();


	public WxPayService(String payConfigStorage) {
		super(payConfigStorage);
	}

	@Override
	public BasePayService setPayConfigStorage(String payConfigStorage) {
		this.payConfigStorage = payConfigStorage;
		wxPayConfigStorage.init(payConfigStorage);
		isOpenConnection();
		return this;
	}

	/**
	 * Title：isOpenConnection <br>
	 * Description： 打开带证书的httpClient<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:37 <br>
	 * @param
	 * @return
	 */
	private void isOpenConnection(){
		if(StringUtils.isNotEmpty(wxPayConfigStorage.certPath)){
			try {
				openConnection();
			} catch (Exception e) {
				log.error("微信—创建http链接失败!{}",e);
				throw new PayErrorException(FAILURE,"微信—创建http链接失败!"+e);
			}
		}
	}

	/**
	 * 创建链接{证书}
	 * @throws Exception
	 */
	private void openConnection() throws Exception {
        KeyStore ks = KeyStore.getInstance("PKCS12");
        ks.load(new ClassPathResource(WxPayConfigStorage.certPath).getInputStream(), WxPayConfigStorage.mchId.toCharArray());
        SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(ks, WxPayConfigStorage.mchId.toCharArray()).build();
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory> create()
                .register("http", PlainConnectionSocketFactory.INSTANCE).register("https", new SSLConnectionSocketFactory(sslcontext)).build();
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        httpClient = HttpClients.custom().setConnectionManager(connManager).setConnectionManagerShared(true).build();
    }

	/**
	 * Title：httpostBeltCert <br>
	 * Description： 发起带证书的请求<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:36 <br>
	 * @param  url 请求地址, data 参数xml
	 * @return java.lang.String
	 */
	private String httpostBeltCert(String url,String data) throws IOException{
		try {
			HttpPost httpost = new HttpPost(url);
			httpost.setConfig(requestConfig);
			httpost.setEntity(new StringEntity(data, "UTF-8"));
			response = httpClient.execute(httpost);
			try {
				HttpEntity entity = response.getEntity();
				String jsonStr = EntityUtils.toString(response.getEntity(), "UTF-8");
				EntityUtils.consume(entity);
				return jsonStr;
			} finally {
				httpost.releaseConnection();
			}
		} finally {
			response.close();
			httpClient.close();
		}
	}

	/**
	 * Title：getPublicParameters <br>
	 * Description： 获取公共参数 <br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:31 <br>
	 * @param
	 * @return java.util.Map<java.lang.String,java.lang.Object>
	 */
	private Map<String, Object> getPublicParameters() {
        Map<String, Object> parameters = new TreeMap<>();
        parameters.put(WXPayConstants.APPID, wxPayConfigStorage.appid);
        parameters.put(WXPayConstants.MCH_ID, wxPayConfigStorage.mchId);
        parameters.put(WXPayConstants.NONCE_STR, WXPayUtil.generateNonceStr());
        return parameters;
    }


	/**
	 * Title：authCodeUrl <br>
	 * Description： 获取微信授权url<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:31 <br>
	 * @param
	 * @return java.lang.String
	 */
	@Override
	public String authCodeUrl() {
    	return new StringBuffer(WxTransactionType.AUTH_CODE.getMethod())
			.append("appid=").append(wxPayConfigStorage.appid)
			.append("&redirect_uri=").append(wxPayConfigStorage.authRedirectuUri)
			.append("&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect").toString();
	}

	/**
	 * Title：getOpenid <br>
	 * Description： 获取openid<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:32 <br>
	 * @param  code 授权后code
	 * @return java.lang.String
	 */
	@Override
	public String getOpenid(String code) {
		String url = new StringBuffer(WxTransactionType.AUTH_OPENID.getMethod())
				.append("appid=").append(wxPayConfigStorage.appid)
				.append("&secret=").append(wxPayConfigStorage.appSecret)
				.append("&code=").append(code).append("&grant_type=authorization_code").toString();
		String result = HttpClientUtil.executeByGET(url);
		log.info("微信获取openid---请求链接【"+url+"】,反馈结果："+result);
		JSONObject ob = JSONObject.parseObject(result);
		String openid = ob.getString("openid");
		if(StringUtils.isBlank(openid)){
			log.error("微信获取openid异常，请求参数{}，返回结果{}",url,result);
			throw new PayErrorException(FAILURE,"微信获取openid异常，请求参数{"+url+"}，返回结果{"+result+"}");
		}
		return openid;
	}


	@Override
	public String getRealNameAuthUrl(String scope){
		return new StringBuffer(WxTransactionType.PAY_IDENTITY_INDEX.getMethod())
				.append("mch_id=").append(wxPayConfigStorage.mchId)
				.append("&appid=").append(wxPayConfigStorage.appid)
				.append("&redirect_uri=").append(wxPayConfigStorage.authRedirectuUri)
				.append("&response_type=code&scope=").append(scope).append("&state=STATE#wechat_redirect").toString();
	}


	/**
	 * Title：getPayIdentityToken <br>
	 * Description： 实名认证-获取token<br>
	 * author：傅欣荣 <br>
	 * date：2020/6/22 18:55 <br>
	 * @param
	 * @return
	 */
	@Override
	public String getPayIdentityToken(String code,String openid){
		String sign = "";
		Map<String, Object> parameters = new TreeMap<>();
		parameters.put("mch_id",wxPayConfigStorage.mchId);
		parameters.put("appid",wxPayConfigStorage.appid);
		parameters.put("openid",openid);
		parameters.put("code",code);
		parameters.put("scope","SCOPE");
		parameters.put("grant_type","authorization_code");
		parameters.put("sign_type","HMAC-SHA256");
		try {
			sign = WXPayUtil.generateSignature(parameters,wxPayConfigStorage.apiKey,WXPayConstants.HMACSHA256.toString());
		} catch (Exception e) {
			log.error("微信请求参数sign签名异常！ {}",e);
			throw new PayErrorException(FAILURE,"微信请求参数sign签名异常"+e);
		}
		parameters.put("sign",sign);
		String url = new StringBuffer(WxTransactionType.PAY_IDENTITY_TOKEN.getMethod())
				.append(WXPayUtil.getMapToString(parameters)).toString();
		String result = HttpClientUtil.executeByGET(url);
		log.info("微信获取实名认证access_token---请求链接【"+url+"】,反馈结果："+result);
		JSONObject ob = JSONObject.parseObject(result);
		String access_token = ob.getString("access_token");
		if(StringUtils.isBlank(access_token) || !ob.getString("retcode").equals("0")){
			log.error("微信获取实名认证access_token异常，请求参数{}，返回结果{}",url,result);
			throw new PayErrorException(FAILURE,"微信获取实名认证access_token异常，请求参数{"+url+"}，返回结果{"+result+"}");
		}
		return access_token;

	}

	@Override
	public <A extends Authentication> Map<String, Object> authentication(A order) {
		WxAuthentication wxOder = (WxAuthentication)order;
		Map<String, Object> parameters = getPublicParameters();
		parameters.put("version","1.0");
		parameters.put("real_name",order.getName());
		parameters.put("cred_id",order.getIdNo());
		parameters.put("cred_type","1");
		parameters.put("openid",wxOder.getOpenid());
		parameters.put("access_token",wxOder.getToken());
		parameters.put("sign_type",WXPayConstants.HMACSHA256);
		parameters.put("sign","");
		return initRequestByType(parameters, (WxTransactionType) wxOder.getTransactionType());
	}



	@Override
	public Map<String, Object> transfer(WxTransferOrder order) {
		Map<String, Object> parameters = getPublicParameters();
		parameters.put("partner_trade_no", order.getOutNo());
		parameters.put("amount", Utils.conversionCentAmount(order.getAmount()));
		parameters.put("desc", order.getRemark());
		parameters.put("spbill_create_ip", wxPayConfigStorage.spbill_create_ip);
		if (null == order.getTransactionType()) {
			log.error("微信转账类型必填:transactionType！ ");
			throw new PayErrorException(FAILURE,"微信转账类型必填:transactionType");
		}
		((WxTransactionType) order.getTransactionType()).setAttribute(parameters, order);
		return initRequestByType(parameters, (WxTransactionType) order.getTransactionType());
	}

	@Override
	public Map<String, Object> query(String tradeNo, String outTradeNo,TransactionType transactionType) {
		Map<String, Object> parameters = getPublicParameters();
		parameters.put("partner_trade_no",outTradeNo);
		return initRequestByType(parameters, (WxTransactionType) transactionType);
	}



	/**
	 * Title：initRequestByType <br>
	 * Description： 发起http请求 <br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:33 <br>
	 * @param  parameters 请求参数,
	 * @param transactionType 请求url，是否需要证书
	 * @return java.util.Map<java.lang.String,java.lang.Object> http响应参数
	 */
	private Map<String, Object> initRequestByType(Map<String, Object> parameters,WxTransactionType transactionType){
    	//将请求参数转换为xml格式
		String data;
		String returnData;
		try {
			data = WXPayUtil.generateSignedXml(parameters, wxPayConfigStorage.apiKey,wxPayConfigStorage.signType);
		} catch (Exception e) {
			log.error("微信请求参数转为xml格式：转换异常！ ",e);
			throw new PayErrorException(FAILURE,"微信请求参数转为xml格式：转换异常！"+e);
		}
		//判断是否需要证书
		try {
			if(transactionType.isNeedCert()){
				returnData = httpostBeltCert(transactionType.getMethod(),data);
			}else{
				returnData = HttpClientUtil.postXML(transactionType.getMethod(),data);
			}
			log.info("赔付-微信端原始返回结果：【{}】", returnData);
		} catch (IOException e) {
			log.error("微信-发起HTTP请求：异常 ！",e);
			throw new PayErrorException(FAILURE,"微信-发起HTTP请求：异常 ！"+e);
		}
		try {
			return WXPayUtil.xmlToMap(returnData);
		} catch (Exception e) {
			log.error("微信—反馈结果转换类型Map：转换异常！ ",e);
			throw new PayErrorException(FAILURE,"微信—反馈结果转换类型Map：转换异常！"+e);
		}
	}


}
