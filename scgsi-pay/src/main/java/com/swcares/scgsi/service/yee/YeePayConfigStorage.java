package com.swcares.scgsi.service.yee;

import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;

/**
 * ClassName：com.swcares.scgsi.service.yee.YeePayConfigStorage <br>
 * Description：易宝支付配置-处理类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
public class YeePayConfigStorage {

    private static Configuration configs;
    public static String merchantNo;

    public static synchronized void init(String filePath) {
        if (configs != null) {
            return;
        }
        try {
            configs = new PropertiesConfiguration(filePath);
        } catch (ConfigurationException e) {
            e.printStackTrace();
        }
        if (configs == null) {
            throw new IllegalStateException("易宝支付读取配置 ：can`t find file by path:"
                    + filePath);
        }
        merchantNo = configs.getString("merchantNo");
    }
}
