package com.swcares.scgsi.service.yee.bean;

import lombok.Data;

@Data
public class YeeTransferResult {

    /**
     * 处理状态
     */
    private String state;

    /**
     * 错误编码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorMsg;

    /**
     * 错误编码 详细
     */
    private String subCode;

    /**
     * 错误描述 详细
     */
    private String subMessage;

    /**
     * 出款订单号
     */
    private String orderId;

    /**
     * 出款批次号
     */
    private String batchNo;

    /**
     * 打款状态码
     */
    private String transferStatusCode;

    /**
     * 是否实时到账
     */
    private String urgency;


}
