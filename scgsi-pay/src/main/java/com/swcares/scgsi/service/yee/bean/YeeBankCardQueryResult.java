package com.swcares.scgsi.service.yee.bean;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.service.yee.bean <br>
 * Description：银行卡bin查询反馈对象 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月02日 15:38 <br>
 * @version v1.0 <br>
 */
@Data
public class YeeBankCardQueryResult {
    /**
     * 处理状态 SUCCESS/FAILURE
     */
    private String state;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 总行代码
     */
    private String bankCode;

    /**
     *银行 ID
     */
    private String bankId;


    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 卡号长度
     */
    private String cardLength;

    /**
     *  卡名
     */
    private String cardName;

    /**
     * 卡类型
     */
    private String cardType;

    /**
     * 返回code
     */
    private String retCode;

    /**
     * 返回信息
     */
    private String retMsg;


    /**
     *
     */
    private String semiCreditState;

    /**
     *
     */
    private String verifyCode;

    /**
     *
     */
    private String verifyLength;


    /*{bankCode=********, bankId=CMBC, bankName=民生银行, bnakId=CMBC, cardLength=16, cardName=民生贷记卡(银联卡),
            cardType=CREDIT, retCode=0000, retMsg=成功, semiCreditState=false, state=SUCCESS,
            verifyCode=421869, verifyLength=6}*/

}
