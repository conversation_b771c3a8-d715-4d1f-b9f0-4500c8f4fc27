package com.swcares.scgsi.service.wx.bean;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.service.wx.bean.WxQueryResult <br>
 * Description：微信企业支付-查询反馈参数 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
@Data
public class WxQueryResult {

    /**
     *  return_code : 返回状态码
     */
    private String return_code;

    /**
     *  return_msg : 返回信息
     */
    private String return_msg;

    /**
     *  result_code : 业务结果
     */
    private String result_code;

    /**
     *  err_code : 错误代码
     */
    private String err_code;

    /**
     *  err_code_des : 错误代码描述
     */
    private String err_code_des;

    /**
     *  partner_trade_no : 商户订单号
     */
    private String partner_trade_no;

    /**
     *  mch_id : 商户号
     */
    private String mch_id;

    /**
     *  appid : 商户appid
     */
    private String appid;

    /**
     *  detail_id : 付款单号
     */
    private String detail_id;

    /**
     *  status : 转账状态
     */
    private String status;

    /**
     *  reason : 失败原因
     */
    private String reason;

    /**
     *  openid : 收款用户openid
     */
    private String openid;

    /**
     *  transfer_name : 收款用户姓名
     */
    private String transfer_name;

    /**
     *  payment_amount : 付款金额
     */
    private String payment_amount;

    /**
     *  transfer_time : 转账时间
     */
    private String transfer_time;

    /**
     *  payment_time : 微信支付成功时间
     */
    private String payment_time;


    /**
     *  desc : 付款描述
     */
    private String desc;
}
