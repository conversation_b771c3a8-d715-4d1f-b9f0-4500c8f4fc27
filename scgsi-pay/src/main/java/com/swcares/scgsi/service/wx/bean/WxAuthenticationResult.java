package com.swcares.scgsi.service.wx.bean;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.service.yee.bean <br>
 * Description：微信实名认证-返回参数 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月03日 13:10 <br>
 * @version v1.0 <br>
 */
@Data
public class WxAuthenticationResult extends WxResult {

    /**
     *  err_code_desc : 错误代码描述
     */
    private String err_code_desc;

    private String nonce_str;

    private String sign;

    //以下字段在return_code 和result_code都为SUCCESS的时候有返回
    private String openid;

    private String access_token;

    /**
     * 有多个结果时用分号”;”连接
     *
     * V_OP_NA:       用户暂未实名认证
     *
     * V_OP_NM_MA:  用户与姓名匹配
     *
     * V_OP_NM_UM:  用户与姓名不匹配
     */
    private String verify_openid;

    /**
     * 当 verify_openid 为 V_OP_NM_MA 时返回:
     *
     * V_NM_ID_MA:   姓名与证件号匹配
     *
     * V_NM_ID_UM:   姓名与证件号不匹配
     */
    private String verify_real_name;

    /**
     * 当 verify_real_name 为 V_NM_ID_MA时返回：
     *
     * YES: 绑定银行卡的实名认证
     *
     * NO: 非绑定银行卡的实名认证
     *
     * ( version 1.1的新增字段， version> = 1.1时返回)
     */
    private String bind_bankcard;
}
