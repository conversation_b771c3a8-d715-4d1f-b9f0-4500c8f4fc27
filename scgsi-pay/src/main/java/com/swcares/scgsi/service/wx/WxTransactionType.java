package com.swcares.scgsi.service.wx;

import com.swcares.scgsi.api.TransactionType;
import com.swcares.scgsi.service.wx.bean.WxTransferOrder;
import org.apache.commons.lang.StringUtils;

import java.util.Map;


/**
 * ClassName：com.swcares.scgsi.service.wx.WxTransactionType <br>
 * Description：微信-交易类型 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
public enum  WxTransactionType implements TransactionType {

    /**
     * 授权url
     */
    AUTH_CODE("https://open.weixin.qq.com/connect/oauth2/authorize?"),
    /**
     * 授权openid
     */
    AUTH_OPENID("https://api.weixin.qq.com/sns/oauth2/access_token?"),
    /**
     * 公众号支付-转账到零钱
     */
    TRANSFERS("https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers"){

        @Override
        public void setAttribute(Map<String, Object> parameters,WxTransferOrder order) {
            String appid = (String) parameters.get("appid");
            String mchid = (String) parameters.get("mch_id");
            parameters.put("mch_appid", appid);
            parameters.put("mchid", mchid);
            parameters.put("openid", order.getPayeeAccount());
            parameters.put("check_name", "NO_CHECK");
            if (StringUtils.isNotEmpty(order.getPayeeName())) {
                parameters.put("check_name", "FORCE_CHECK");
                parameters.put("re_user_name", order.getPayeeName());
            }
            parameters.remove("appid");
            parameters.remove("mch_id");
        }

        @Override
        public boolean isNeedCert() {
            return true;
        }
    },
    /**
     * 公众号支付-转账查询
     */
    TRANSFERS_QUERY("https://api.mch.weixin.qq.com/mmpaymkttransfers/gettransferinfo"){
        @Override
        public boolean isNeedCert() {
            return true;
        }
    },/**
     * 授权支付身份认证code
     */
    PAY_IDENTITY_INDEX("https://payapp.weixin.qq.com/appauth/authindex?"),
    /**
     * 授权支付身份认证code换取token
     */
    PAY_IDENTITY_TOKEN("https://api.mch.weixin.qq.com/appauth/getaccesstoken?"),
    /**
     * 授权支付身份认证 ,实名认证（姓名+ 身份证）
     */
    PAY_IDENTITY_REALNAMEAUTH("https://fraud.mch.weixin.qq.com/secsvc/realnameauth"),;



    WxTransactionType(String method) {
        this.method = method;
    }

    /**
     * 是否直接返回
     * @return 是否直接返回
     */
    public boolean isReturn(){
        return false;
    }
    private String method;

    @Override
    public String getType() {
        return this.name();
    }
    @Override
    public String getMethod() {
        return this.method;
    }

    /**
     * 请求参数
     * @param parameters
     * @param order
     */
    public void setAttribute(Map<String, Object> parameters, WxTransferOrder order) {
    }
    /**
     * 请求是否需要证书
     * @return
     */
    public boolean isNeedCert(){
        return false;
    }
}
