package com.swcares.scgsi.service.yee.bean;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.service.yee.bean <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月03日 15:49 <br>
 * @version v1.0 <br>
 */
@Data
public class YeeAuthenticationResult {

    /**
     * 处理状态 SUCCESS/FAILURE
     */
    private String state;

    /**
     * 认证结果
     * FAILURE：鉴权失败
     * SUCCESS：鉴权成功
     * NOT_AUTH：校验失败等，没进行鉴权
     */
    private String status;

    /**
     * 商户编号，原样返回
     */
    private String merchantNo;

    /**
     * 请求号 原样返回
     */
    private String requestNo;

    /**
     * 认证类型，原样返回
     */
    private String authType;

    /**
     * 易宝流水号，易宝唯一标识
     */
    private String ybOrderId;

    /**
     * 错误码
     */
    private String code;

    /**
     * 错误信息
     */
    private String message;

    private String errorcode;

    private String errormsg;

    private String subCode;

    private String subMessage;
}
