package com.swcares.scgsi.service.yee.bean;

import lombok.Data;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.service.yee.bean <br>
 * Description：查询反馈对象 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月02日 15:38 <br>
 * @version v1.0 <br>
 */
@Data
public class YeeQueryResult {
    /**
     * 处理状态 SUCCESS/FAILURE
     */
    private String state;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 页码
     */
    private String pageNo;

    /**
     * 每页显示条数
     */
    private String pageSize;

    /**
     * 总数
     */
    private String totalCount;

    /**
     * 总页数
     */
    private String totalPageSize;

    /**
     * 扩展信息
     */
    private String extInfos;

    /**
     * 反馈订单信息
     */
    private List<YeeQueryResultOrder> list;

}
