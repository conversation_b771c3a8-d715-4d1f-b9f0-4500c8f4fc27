package com.swcares.scgsi.service.wx;

import com.swcares.scgsi.util.AesEncryptUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * ClassName：com.swcares.scgsi.service.wx.WxPayConfigStorage <br>
 * Description：微信支付配置-处理类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class WxPayConfigStorage {

	private static Configuration configs;

	public static String appid;//合作者id（商户号）

	public static String mchId;//微信支付分配的子商户号

	public static String appSecret;// 服务号的应用密钥

	public static String apiKey;//密钥

	public static String notifyUrl;//异步回调地址

	public static String signType;//签名方式

	public static String inputCharset;//utf-8

	public static  String certPath ;//微信支付证书

	public static  String spbill_create_ip ;//终端IP

	public static  String authRedirectuUri;//授权回调域名

	public static String AES_KEY = "c2Nnc2kjMjAyMHN3Y2FyZXMkc2Nnc2kjMjAyMHN3Y2FyZXM=";


    @Value("${pay.wx.appid}")
	public void setAppid(String appid) {
		this.appid = appid;
	}

	@Value("${pay.wx.appSecret}")
	public void setAppSecret(String appSecret) {
		this.appSecret = appSecret;
	}

	public static synchronized void init(String filePath) {
		if (configs != null) {
			return;
		}
		try {
			configs = new PropertiesConfiguration(filePath);
		} catch (ConfigurationException e) {
			e.printStackTrace();
		}
		if (configs == null) {
			throw new IllegalStateException("微信支付读取配置 ：can`t find file by path:"
					+ filePath);
		}
		/*appid = configs.getString("APP_ID");
		appSecret = configs.getString("APP_SECRET");*/
		mchId = configs.getString("MCH_ID");
		apiKey = configs.getString("API_KEY");
		signType = configs.getString("SIGN_TYPE");
		certPath = configs.getString("CERT_PATH");
		notifyUrl = configs.getString("NOTIFY_URL");
		inputCharset = configs.getString("INPUT_CHARSET");
		spbill_create_ip = configs.getString("SPBILL_CREATE_IP");
		authRedirectuUri = configs.getString("AUTH_REDIRECTU_URL");
		decryptWXInfo();
	}

	private static void decryptWXInfo(){
		log.info("微信支付配置信息解密《前》的数据：appid【{}】，appSecret【{}】，mchId【{}】，apiKey【{}】，signType【{}】，certPath【{}】，spbill_create_ip【{}】，authRedirectuUri【{}】",
				appid, appSecret, mchId, apiKey, signType, certPath, spbill_create_ip, authRedirectuUri);
		appid = AesEncryptUtil.aesDecrypt(AES_KEY, appid);
		appSecret =AesEncryptUtil.aesDecrypt(AES_KEY, appSecret);
		mchId = AesEncryptUtil.aesDecrypt(AES_KEY, mchId);
		apiKey = AesEncryptUtil.aesDecrypt(AES_KEY, apiKey);
		signType = AesEncryptUtil.aesDecrypt(AES_KEY, signType);
		certPath = AesEncryptUtil.aesDecrypt(AES_KEY, certPath);
		spbill_create_ip = AesEncryptUtil.aesDecrypt(AES_KEY, spbill_create_ip);
		authRedirectuUri = AesEncryptUtil.aesDecrypt(AES_KEY, authRedirectuUri);
		log.info("微信支付配置信息解密《后》的数据：appid【{}】，appSecret【{}】，mchId【{}】，apiKey【{}】，signType【{}】，certPath【{}】，spbill_create_ip【{}】，authRedirectuUri【{}】",
				appid, appSecret, mchId, apiKey, signType, certPath, spbill_create_ip, authRedirectuUri);
	}

	public static void main(String[] args) {
		String appids = AesEncryptUtil.aesEncrypt(AES_KEY, "wx65bea544a05708cc");
		String appids1 = AesEncryptUtil.aesEncrypt(AES_KEY, "1aaf68fed9314a1050d05692f46e1022");
		System.out.println(appids);
		System.out.println(appids1);
	}
}
