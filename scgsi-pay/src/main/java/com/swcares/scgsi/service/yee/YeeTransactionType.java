package com.swcares.scgsi.service.yee;

import com.swcares.scgsi.api.TransactionType;
import com.swcares.scgsi.service.yee.bean.YeeTransferOrder;
import com.swcares.scgsi.util.Utils;
import net.sf.json.JSONObject;

import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.service.yee.YeeTransactionType <br>
 * Description：易宝支付-交易类型 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
public enum YeeTransactionType implements TransactionType {

    TRANSFER_SEND("/rest/v1.0/balance/transfer_send"){
        @Override
        public void setAttribute(Map<String, Object> parameters, YeeTransferOrder order) {
            parameters.put("batchNo", order.getBatchNo());
            parameters.put("orderId", order.getOrderId());
            parameters.put("amount", Utils.conversionAmount(order.getAmount()));
            parameters.put("accountName", order.getAccountName());
            parameters.put("accountNumber",order.getAccountNumber());
            parameters.put("desc", order.getDesc());
            //以下为非必填项
            //---------出款到对公账户时，下列银行无需填写：银行列表，其他银行需要填写
            //出款到对私银行卡时，银联卡（卡号62开头）无需填写，非银联卡（卡号非62开头）需要填写。
            parameters.put("bankCode", order.getBankCode());
            parameters.put("bankName", order.getBankName());
            parameters.put("bankBranchName",  order.getBankBranchName());
            parameters.put("provinceCode",  order.getProvinceCode());
            parameters.put("cityCode",  order.getCityCode());
            //-------------------
            parameters.put("product", order.getProduct());
            parameters.put("urgency", order.getUrgency());
            parameters.put("feeType",  order.getFeeType());
            parameters.put("leaveWord", order.getLeaveWord());
            parameters.put("abstractInfo", order.getAbstractInfo());
        }
    },
    /**
     * 转账查询
     */
    TRANSFER_QUERY("/rest/v1.0/balance/transfer_query"),
    /**
     * 实名认证
     */
    AUTHENTICATION_SEND("/rest/v1.0/std/cert/order"){
        String idTypes = "{'身份证':'ID';'护照':'PASSPORT';'警官证':'POLICE';'台胞证':'TAIWAN';'其他证件':'OTHER'" +
                ";'回乡证':'REENTRY';'士兵证':'SOLDIER';'军官证':'OFFICERS';'外国人居留证':'FR_CARD';'临时身份证':'TEMPIDCARD'" +
                ";'户口薄':'RESIDENCEBOOKLET';'港澳居民往来内地通行证':'HM_VISITORPASS'}";

        @Override
        public void setAttribute(Map<String, Object> parameters, YeeTransferOrder order) {
            //根据证件类型，取对应码表,暂时没有引入redis。先这样写着。后期优化从redis中取
            JSONObject json = JSONObject.fromObject(idTypes);
            String idTypeCode = json.getString((String) parameters.get("idCardType"));
            parameters.put("idCardType",idTypeCode);


        }

    },
    /**
     * 实名认证结果查询
     */
    AUTHENTICATION_QUERY("/rest/v1.0/std/cert/orderquery"),
    /**
     * 查询账户余额
     */
    QUERY_CUSTOMER_AMOUNT("/rest/v1.0/balance/query_customer_amount"),
    /**
     * 卡Bin查询
     */
    BANK_CARD_QUERY("/rest/v1.0/bc/bankInterface/getCardBinInfo");

    YeeTransactionType(String method) {
        this.method = method;
    }
    private String method;

    @Override
    public String getType() {
        return this.name();
    }
    @Override
    public String getMethod() {
        return this.method;
    }
    /**
     * 请求参数
     * @param parameters
     * @param order
     */
    public void setAttribute(Map<String, Object> parameters, YeeTransferOrder order){}
    /**
     * 请求是否需要证书
     * @return
     */
    public boolean isNeedCert(){
        return false;
    }
}
