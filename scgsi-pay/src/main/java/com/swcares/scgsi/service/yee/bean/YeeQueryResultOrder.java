package com.swcares.scgsi.service.yee.bean;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.service.yee.bean <br>
 * Description：查询反馈订单信息 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月02日 15:41 <br>
 * @version v1.0 <br>
 */
@Data
public class YeeQueryResultOrder {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 账户名
     */
    private String accountName;

    /**
     * 账号
     */
    private String accountNumber;

    /**
     * 打款状态码
     */
    private String transferStatusCode;

    /**
     * 银行状态码
     */
    private String bankTrxStatusCode;

    /**
     * 银行错误信息 如：账户名有误 银行打款失败时此参数有返回
     */
    private String bankMsg;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 出款金额
     */
    private String amount;

    /**
     * 手续费
     */
    private String fee;

    /**
     * 手续费类型
     */
    private String feeType;

    /**
     * 是否加急
     */
    private String urgency;

    /**
     * 加急类型; MD_URGENCY 秒到 URGENCY 实时 2小时 COMMON 普通 非加急 NEXT_DAY 下一天
     */
    private String urgencyType;
    /**
     * 完成时间(yyyy-MM-dd HH:mm:ss)
     */
    private String finishDate;

    /**
     * 退款金额 拆分订单时使用，为空时不返回
     */
    private String refundAmount;
   /* {errorCode=BAC001, extInfos={"TOTALSRCFEE":0.01,"REFUNDEDCOUNT":1,"TOTALSRCAMOUNT":1.0,"TOTALTARGETAMOUNT":-1.0,"GENERATEBATCHAMOUNT":0.0,"TOTALTARGETFEE":0.0,"GENERATEBATCHCOUNT":0,"REFUNDEDAMOUNT":1.0,"TOTALCOUNT":1},
        list=[{"bankCode":"ABC","batchNo":"****************","amount":1.0,"urgencyType":"MD_URGENCY",
            "orderId":"****************","accountName":"傅欣荣","fee":0.01,"transferStatusCode":"0028",
            "bankName":"中国农业银行","accountNumber":"6228480469817665075","feeType":"SOURCE",
            "bankMsg":"账务异常:系统拦截","urgency":true,"bankTrxStatusCode":"W","finishDate":"2020-03-02 15:20:45",
            "refundAmount":1.00}], pageNo=1, pageSize=100, state=SUCCESS, totalCount=1, totalPageSize=1}*/


}
