package com.swcares.scgsi.service.yee.bean;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.service.yee.bean <br>
 * Description：查询商户余额反馈 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月04日 9:27 <br>
 * @version v1.0 <br>
 */
@Data
public class YeeMerBalanceResult {

    /**
     * api调用处理状态 SUCCESS/FAILURE
     */
    private String state;

    /**
     * 商户编号
     */
    private String customerNumber;

    /**
     * 账户可用余额
     */
    private String accountAmount;

    /**
     * 日结通可用余额
     */
    private String rjtValidAmount;

    /**
     * 代付代发可用余额
     */
    private String wtjsValidAmount;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorMsg;
}
