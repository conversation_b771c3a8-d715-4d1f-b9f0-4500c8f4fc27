package com.swcares.scgsi.service.yee.bean;

import com.swcares.scgsi.api.TransactionType;
import com.swcares.scgsi.bean.PayOrder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 易宝支付-请求参数
 */
@Data
public class YeeTransferOrder extends PayOrder {

    /**
     * 批次号（必填）
     * 数据要求：仅数字
     */
    private String batchNo;

    /**
     * 订单号（必填）
     */
    private String orderId;

    /**
     * 金额（必填）
     * 单位：元，非负浮点数，保留2位小数
     */
    private BigDecimal amount;

    /**
     * 账户名（必填）
     */
    private String accountName;

    /**
     * 卡号（必填）
     */
    private String accountNumber;

    /**
     * 银行编码（必填）
     */
    private String bankCode;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 支行名称
     * 出款到对公账户时，下列银行无需填写：银行列表，其他银行需要填写
     * 出款到对私银行卡时，银联卡（卡号62开头）无需填写，非银联卡（卡号非62开头）需要填写。
     */
    private String bankBranchName;

    /**
     * 省编码
     * 出款到对公账户时，下列银行无需填写：银行列表，其他银行需要填写
     * 出款到对私银行卡时，银联卡（卡号62开头）无需填写，非银联卡（卡号非62开头）需要填写。
     */
    private String provinceCode;

    /**
     * 市编码
     * 出款到对公账户时，下列银行无需填写：银行列表，其他银行需要填写
     * 出款到对私银行卡时，银联卡（卡号62开头）无需填写，非银联卡（卡号非62开头）需要填写。
     */
    private String cityCode;

    /**
     * 额外信息
     * 预留参数，无需传参
     */
    private String abstractInfo;

    /**
     * 业务类型
     * WTJS：代付代发 RJT：日结通 AUTO：自动路由
     * 为空时，默认值：WTJS
     * 备注：为AUTO时优先使用代付代发，若代付代发未开通或可用余额不足，则使用日结通出款
     */
    private String product;

    /**
     * 手续费方式
     * SOURCE：商户承担;TARGET：用户承担
     * 默认值：SOURCE
     */
    private String feeType;

    /**
     * 是否加急出款
     * 0：非加急;1：加急
     * 为空时，默认值：1
     */
    private String urgency;

    /**
     * 描述（必填）
     * 如：航延赔偿金
     */
    private String desc;

    /**
     * 留言
     * 给收款人银行备注，最长30
     */
    private String leaveWord;


    /**
     * 转账类型，支付交易产品类型
     */
    private TransactionType transactionType;

}
