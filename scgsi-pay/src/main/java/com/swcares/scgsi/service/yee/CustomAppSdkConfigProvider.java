package com.swcares.scgsi.service.yee;

import com.yeepay.g3.sdk.yop.config.SDKConfig;
import com.yeepay.g3.sdk.yop.config.provider.BaseCachedAppSdkConfigProvider;
import com.yeepay.g3.sdk.yop.exception.YopClientException;
import com.yeepay.g3.sdk.yop.utils.JsonUtils;
import com.yeepay.shade.org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;

/**
 * ClassName：com.swcares.scgsi.service.yee <br>
 * Description：易宝配置文件读取 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 05月13日 15:42 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class CustomAppSdkConfigProvider extends BaseCachedAppSdkConfigProvider {


    public CustomAppSdkConfigProvider(String defaultAppKey) {
        super(defaultAppKey);
    }

    @Override
    protected SDKConfig loadSDKConfig(String var1) {
        log.info("YEE-【配置文件读取：参数{}】",var1);
        String filePath = "/config/yop_sdk_config_default.json";
        return  this.loadConfig(filePath);
    }

    public static SDKConfig loadConfig(String configFile) {
        InputStream fis = null;

        SDKConfig config;
        try {
            fis = CustomAppSdkConfigProvider.class.getResourceAsStream(configFile);
            config = (SDKConfig)JsonUtils.loadFrom(fis, SDKConfig.class);
        } catch (Exception var11) {
            throw new YopClientException("Errors occurred when loading SDKConfig.", var11);
        } finally {
            if (null != fis) {
                try {
                    fis.close();
                } catch (IOException var10) {
                }
            }

        }

        if (StringUtils.endsWith(config.getServerRoot(), "/")) {
            config.setServerRoot(StringUtils.substring(config.getServerRoot(), 0, -1));
        }

        if (StringUtils.endsWith(config.getYosServerRoot(), "/")) {
            config.setYosServerRoot(StringUtils.substring(config.getYosServerRoot(), 0, -1));
        }

        return config;
    }
}
