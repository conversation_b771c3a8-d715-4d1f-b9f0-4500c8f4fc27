package com.swcares.scgsi.service.yee;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.swcares.exception.PayErrorException;
import com.swcares.scgsi.api.BasePayService;
import com.swcares.scgsi.api.TransactionType;
import com.swcares.scgsi.bean.Authentication;
import com.swcares.scgsi.service.yee.bean.YeeAuthentication;
import com.swcares.scgsi.service.yee.bean.YeeTransferOrder;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.util.Utils;
import com.yeepay.g3.sdk.yop.client.YopRequest;
import com.yeepay.g3.sdk.yop.client.YopResponse;
import com.yeepay.g3.sdk.yop.client.YopRsaClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;



/**
 * ClassName：com.swcares.scgsi.service.yee.YeePayService <br>
 * Description：易宝支付-支付处理类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class YeePayService extends BasePayService<YeeTransferOrder> {


    protected YeePayConfigStorage yeePayConfigStorage;

    public static final String FAILURE = "failure";

    public YeePayService(){}

    public YeePayService(String payConfigStorage) {
        super(payConfigStorage);
    }


    @Override
    public BasePayService setPayConfigStorage(String payConfigStorage) {
        this.payConfigStorage = payConfigStorage;
        yeePayConfigStorage.init(payConfigStorage);
        return this;
    }

    /**
     * Title：getPublicParameters <br>
     * Description： 获取公共参数 <br>
     * author：傅欣荣 <br>
     * date：2020/2/28 17:52 <br>
     * @param
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    private Map<String, Object> getPublicParameters() {
        Map<String, Object> parameters = new TreeMap<>();
        parameters.put("merchantNo",yeePayConfigStorage.merchantNo);
        return parameters;
    }

    @Override
    public Map<String, Object> transfer(YeeTransferOrder order) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        if(null == order.getTransactionType()){
            log.error("YEE交易类型必填:transactionType！ ");
            throw new PayErrorException(FAILURE,"YEE交易类型必填:transactionType");
        }
        ((YeeTransactionType)order.getTransactionType()).setAttribute(parameters,order);

        return yeePayHttp(parameters,order.getTransactionType().getMethod());
    }

    @Override
    public Map<String, Object> query(String tradeNo, String outTradeNo, TransactionType transactionType) {
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("batchNo", tradeNo);
        parameters.put("orderId", outTradeNo);//不输入则查询该批次全部打款明细；若输入则查询该批次某笔订单
        ((YeeTransactionType)transactionType).setAttribute(parameters,null);
        return yeePayHttp(parameters,transactionType.getMethod());
    }


    @Override
    public Map<String, Object> bankCardQuery(String cardNo, TransactionType transactionType){
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("cardNo",cardNo);
        return yeePayHttp(parameters,transactionType.getMethod());
    }


    @Override
    public <A extends Authentication> Map<String, Object> authentication(A order) {

        if(null == order.getTransactionType()){
            log.error("YEE交易类型必填:transactionType！ ");
            throw new PayErrorException(FAILURE,"YEE交易类型必填:transactionType");
        }
        Map<String, Object> parameters = getPublicParameters();
        YeeAuthentication yee = (YeeAuthentication)order;
        parameters.put("authType","FastRealNameVerify");
        parameters.put("requestNo",yee.getOrderId());
        parameters.put("userName",yee.getName());
        parameters.put("idCardNo",yee.getIdNo());
        parameters.put("bankCardNo",yee.getBankCardNo());
        parameters.put("requestTime", Utils.getNewDate());
        parameters.put("mobilePhone",yee.getMobilePhone());
        if(StringUtils.isNotBlank(yee.getIdType())){
            parameters.put("idCardType",yee.getIdType());
            ((YeeTransactionType)yee.getTransactionType()).setAttribute(parameters,null);
        }
        log.info("YEE-【实名认证请求参数】:{}",parameters);
        return yeePayHttp(parameters,yee.getTransactionType().getMethod());
    }

    @Override
    public Map<String, Object> authenticationQuery(String orderId, String streamOrderId, TransactionType transactionType) {
        Map<String, Object> parameters = getPublicParameters();
        parameters.put("requestNo",orderId);
        parameters.put("ybOrderId",streamOrderId);
        return yeePayHttp(parameters,transactionType.getMethod());
    }

    @Override
    public <T> T merchantBalanceQuery(Class<T> clazz) {
        Map<String, Object> result = yeePayHttp(null,YeeTransactionType.QUERY_CUSTOMER_AMOUNT.getMethod());
        return JSONObject.parseObject(JSONObject.toJSONString(result),clazz);
    }


    //------------------以下为私有处理方法--------------------------------------

    /**
     * Title：yeePayHttp <br>
     * Description： 使用sdk发起请求 <br>
     * author：傅欣荣 <br>
     * date：2020/2/28 17:52 <br>
     * @param  parameters 请求参数, url 请求地址
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    private Map<String,Object> yeePayHttp(Map<String, Object> parameters,String url){

        Map<String, Object> result = new HashMap<String, Object>();
        YopRequest yoprequest = new YopRequest();
        if(null != parameters){
            Set<Map.Entry<String, Object>> entry = parameters.entrySet();
            for (Map.Entry<String, Object> s : entry) {
                yoprequest.addParam(s.getKey(), s.getValue());
            }
        }
        YopResponse yopresponse = null;
        try {
            yopresponse = YopRsaClient.post(url, yoprequest);
        } catch (IOException e) {
            log.error("易宝-发起HTTP请求异常。 参数[{}],异常[{}]",yoprequest.getParams(),e);
            throw new PayErrorException(FAILURE,"易宝-发起HTTP请求异常。 参数[{}],异常[{}] "+yoprequest.getParams()+e);
        }
        log.info("请求YO：参数[{}]，结果[{}]，结果stringResult[{}]" ,yoprequest.getParams(),yopresponse.toString(),yopresponse.getStringResult());
        if ("FAILURE".equals(yopresponse.getState())) {//支付为FAILURE时反馈错误信息
            if (yopresponse.getError() != null) {
                result.put("state",yopresponse.getState());
                result.put("errorcode", yopresponse.getError().getCode());
                result.put("errormsg", yopresponse.getError().getMessage());
                result.put("subCode", yopresponse.getError().getSubCode());
                result.put("subMessage", yopresponse.getError().getSubMessage());
            }
            return result;
        }
        //成功则进行相关处理
        if (yopresponse.getStringResult() != null) {
            result = parseResponse(yopresponse.getStringResult());
            result.put("state",yopresponse.getState());
        }
        return result;
    }

    /**
     * Title：parseResponse <br>
     * Description：response转换成json格式<br>
     * author：傅欣荣 <br>
     * date：2020/2/28 17:53 <br>
     * @param  response 参数
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    public static Map<String, Object> parseResponse(String response) {

        Map<String, Object> jsonMap = new HashMap<>();
        jsonMap = JSON.parseObject(response,
                new TypeReference<TreeMap<String, Object>>() {
                });
        return jsonMap;
    }

    /**
     * Title：handlePayOrderId <br>
     * Description： 支付订单号生成<br>
     * author：傅欣荣 <br>】
     * date：2020/4/22 15:45 <br>
     * @param
     * @return
     */
    public String handlePayOrderId() {
        //精确时间到分12位
        String orderId = DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMM);
        //随机5位数
        int random5 = (int) ((Math.random() * 9 + 1) * 10000);
        orderId += String.valueOf(random5);
        return orderId;
    }

}
