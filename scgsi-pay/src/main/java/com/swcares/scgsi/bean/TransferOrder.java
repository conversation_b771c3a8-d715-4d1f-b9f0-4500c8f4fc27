package com.swcares.scgsi.bean;

import com.swcares.scgsi.api.TransactionType;
import lombok.Data;

import java.math.BigDecimal;



/**
 * ClassName：com.swcares.scgsi.bean.TransferOrder <br>
 * Description：转账订单 ————暂时废弃 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
@Data
public class TransferOrder extends PayOrder{

	/**
	 * 转账批次订单单号
	 */
	private String batchNo;

	/**
	 * 转账订单单号
	 */
	private String outNo;

	/**
	 * 收款方账户, 用户openid,卡号等等
	 */
	private String  payeeAccount ;
	/**
	 * 转账金额
	 */
	private BigDecimal amount;

	/**
	 * @Fields openBank : 开户银行
	 */
	private String openBank;

	/**
	 * 收款用户姓名， 非必填，如果填写将强制验证收款人姓名
	 */
	private String payeeName;

	/**
	 * 付款人名称
	 */
	private String payerName;
	/**
	 * 备注（付款备注）
	 */
	private String remark;
	/**
	 * 转账类型，支付交易产品类型
	 */
	private TransactionType transactionType;
	/**
	 * 操作者ip，根据支付平台所需进行设置
	 */
	private String ip;





}
