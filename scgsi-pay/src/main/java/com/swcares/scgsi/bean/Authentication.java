package com.swcares.scgsi.bean;

import com.swcares.scgsi.api.TransactionType;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

/**
 * ClassName：com.swcares.scgsi.service.yee.bean <br>
 * Description：实名认证 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月03日 13:10 <br>
 * @version v1.0 <br>
 */
@Data
public class Authentication extends PayOrder{
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 证件号
     */
    private String idNo;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 交易类型，支付交易产品类型
     */
    private TransactionType transactionType;
}
