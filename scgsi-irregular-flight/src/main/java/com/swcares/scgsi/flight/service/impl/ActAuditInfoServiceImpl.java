package com.swcares.scgsi.flight.service.impl;

import com.alibaba.fastjson.JSON;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.constant.SmsConstant;
import com.swcares.scgsi.flight.dao.ActApplyAuditInfoDao;
import com.swcares.scgsi.flight.dao.impl.ActAuditInfoDaoImpl;
import com.swcares.scgsi.flight.dao.impl.ActConfigInfoDaoImpl;
import com.swcares.scgsi.flight.dto.ActAuditParamDto;
import com.swcares.scgsi.flight.dto.ActQueryParamDto;
import com.swcares.scgsi.flight.dto.ActUserAuditParamDto;
import com.swcares.scgsi.flight.entity.ActApplyAuditInfo;
import com.swcares.scgsi.flight.service.ActAuditInfoService;
import com.swcares.scgsi.flight.vo.ActAuditInfoVo;
import com.swcares.scgsi.flight.vo.ActAuditPaxInfoVo;
import com.swcares.scgsi.flight.vo.ActAuditRecordVo;
import com.swcares.scgsi.sms.api.SmsService;
import com.swcares.scgsi.sms.entity.SmsTemplate;
import com.swcares.scgsi.sms.form.SmsTemplateForm;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.service.impl <br>
 * Description：代领审核serviceImpl <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月16日 10:30 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class ActAuditInfoServiceImpl implements ActAuditInfoService {
    @Resource
    private ActApplyAuditInfoDao actApplyAuditInfoDao;

    @Resource
    private ActAuditInfoDaoImpl actAuditInfoDao;

    @Resource
    private ActConfigInfoDaoImpl actConfigInfoDaoImpl;

    @Resource
    private SmsService smsService;

    @Override
    public QueryResults getAuditInfoPage(ActQueryParamDto actQueryParamDto) {
        log.info("-----------分页查询代领审核列表--查询参数[{}]",actQueryParamDto.toString());
        QueryResults qr = actAuditInfoDao.getAuditInfoPage(actQueryParamDto);
        List<ActAuditInfoVo> list = (List<ActAuditInfoVo>) qr.getList();
        if(ObjectUtils.isNotEmpty(list)){
            list.forEach(e->{
                e.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, e.getTelephone()));            });
        }
        return qr;

    }

    @Override
    public List<ActAuditPaxInfoVo> getActApplyPaxInfo(String applyCode) {
        ActAuditPaxInfoVo actApplyPaxInfoVo = actAuditInfoDao.getApplyInfoByApplyCode(applyCode);
        actApplyPaxInfoVo.setActRole("0");
        actApplyPaxInfoVo.setIdType("身份证");
        // 2.获取代领旅客信息
        String receiveStatus = "0";
        if ("1".equals(actApplyPaxInfoVo.getPayStatus())) {
            receiveStatus = "1";
        }
        List<ActAuditPaxInfoVo> showActUserDtoList = actAuditInfoDao.getActPaxInfoByApplyCode(applyCode, receiveStatus);
        showActUserDtoList.add(0, actApplyPaxInfoVo);
        showActUserDtoList.forEach(e->{
            e.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, e.getTelephone()));
        });
        return showActUserDtoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveActAuditInfo(ActAuditParamDto actAuditParamDto) {
        List<ActUserAuditParamDto> auditInfoList = actAuditParamDto.getAuditInfo();
        //处理审核状态,有不通过的状态，主审核单状态为不通过
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser = (String) authentication.getPrincipal();
        String auditOpinion = "1";
        Date auditTime = new Date();
        for (ActUserAuditParamDto actUserAuditParam : auditInfoList) {
            if ("2".equals(actUserAuditParam.getAuditOpinion())) {
                auditOpinion = actUserAuditParam.getAuditOpinion();
                break;
            }
        }
        for (ActUserAuditParamDto actUserAuditParam : auditInfoList) {
            if ("0".equals(actUserAuditParam.getActRole())) {
                //申领人,更新申领人信息
                actAuditInfoDao.updateApplyOrderInfo(actUserAuditParam.getApplyCode(), actUserAuditParam.getAuditOpinion(), auditOpinion);
                //保存审核信息
                ActApplyAuditInfo applyOrderAudit = new ActApplyAuditInfo();
                applyOrderAudit.setApplyCode(actUserAuditParam.getApplyCode());
                applyOrderAudit.setStatus(auditOpinion);
                applyOrderAudit.setAuditor(currentUser);
                applyOrderAudit.setAuditTime(auditTime);

                //拒绝原因
                if (StringUtils.isNotBlank(actUserAuditParam.getRefuse())) {
                    applyOrderAudit.setAuditOpinion(actUserAuditParam.getRefuse());
                }
                //备注
                if (StringUtils.isNotBlank(actUserAuditParam.getRemark())) {
                    applyOrderAudit.setRemark(actUserAuditParam.getRemark());
                }
                if ("1".equals(actUserAuditParam.getAuditOpinion())) {
                    applyOrderAudit.setRemark("");
                    applyOrderAudit.setAuditOpinion("");
                }
                actApplyAuditInfoDao.save(applyOrderAudit);
            } else {
                //更新被代领人审核信息
                if ("1".equals(actUserAuditParam.getAuditOpinion())) {
                    actUserAuditParam.setRemark("");
                    actUserAuditParam.setRefuse("");
                }
                if ("2".equals(actUserAuditParam.getAuditOpinion())) {
                    //拒绝之后恢复申领资格
                    log.info("WEB代领审核不通过恢复旅客领取资格,旅客ID:" + actUserAuditParam.getPaxId());
                   // actAuditInfoDao.updateDpPaxInfoStatus(actUserAuditParam.getPaxId(), "0");
                }
                actAuditInfoDao
                        .updatePaxAuditInfo(actUserAuditParam, currentUser, DateUtils.parseDateToStr(auditTime, DateUtils.YYYY_MM_DD_HH_MM_SS));
            }
        }
    }

    @Override
    public String getLastAuditTime(String applyCode) {
        return actAuditInfoDao.getLastAuditTime(applyCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetActAuditInfo(String applyCode) {
        //1.更新申领主表信息
        actAuditInfoDao.updateApplyOrder(applyCode);
        //2.保存审核信息
        ActApplyAuditInfo applyOrderAudit = new ActApplyAuditInfo();
        applyOrderAudit.setApplyCode(applyCode);
        applyOrderAudit.setStatus("0");
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser = (String) authentication.getPrincipal();
        applyOrderAudit.setAuditor(currentUser);
        applyOrderAudit.setAuditTime(new Date());
        actApplyAuditInfoDao.save(applyOrderAudit);
        //3.更新被代领旅客信息
        actAuditInfoDao.resetActAuditPaxInfo(applyCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void quickPay(String applyCode) {
        //1.更新申领主表快速支付标识
        actAuditInfoDao.quickPay(applyCode);
        //插入操作快速支付审核记录
        ActApplyAuditInfo applyOrderAudit = new ActApplyAuditInfo();
        applyOrderAudit.setApplyCode(applyCode);
        applyOrderAudit.setStatus("3");
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser = (String) authentication.getPrincipal();
        applyOrderAudit.setAuditor(currentUser);
        applyOrderAudit.setAuditTime(new Date());
        actApplyAuditInfoDao.save(applyOrderAudit);
    }

    @Override
    public List<ActAuditRecordVo> getActAuditRecordByApplyCode(String applyCode) {
        return actAuditInfoDao.getActAuditRecordByApplyCode(applyCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateActOrder() {
        List<Map<String, Object>> actOrderList = actAuditInfoDao.getOverTimeActOrder();
        if (actOrderList.size() > 0) {
            //获取缓冲时间
            String defaultTime = actConfigInfoDaoImpl.getDefaultBufferTime();
            if (StringUtils.isNotBlank(defaultTime)) {
                //判断是否超过缓冲时间
                for (Map<String, Object> map : actOrderList) {
                    Date auditDate = (Date) map.get("AUDITTIME");
                    String applyCode = (String) map.get("APPLYCODE");
                    long time = Integer.valueOf(defaultTime) * 60 * 1000;
                    Date expirationDate =
                            new Date(auditDate.getTime() + time);
                    if (expirationDate.compareTo(new Date()) <= 0) {
                        actAuditInfoDao.quickPay(applyCode);
                    }
                }
            } else {
                //未设置缓冲时间,立即更新订单状态
                for (Map<String, Object> map : actOrderList) {
                    String applyCode = (String) map.get("APPLYCODE");
                    actAuditInfoDao.quickPay(applyCode);
                }
            }
        }
    }

    @Override
    public void sendMessageToPax() {
        List<Map<String, Object>> sendList = actAuditInfoDao.getOverTimeActRefuseOrder();
        if (sendList.size() > 0) {
            //审核不通过给申领人发送短信通知
            log.info("代领审核不通过短信通知旅客:" + JSON.toJSONString(sendList));
            for (Map<String, Object> map : sendList) {
                String telephone = map.get("TELEPHONE").toString();
                String applyCode = map.get("APPLYCODE").toString();
                String flightNo = map.get("FLIGHTNO").toString();
                String flightDate = map.get("FLIGHTDATE").toString();
                List<Map<String, Object>> paxIdList = actAuditInfoDao.getApplyPaxIdByApplyCode(applyCode);
                log.info("代领审核不通过更新旅客领取状态:" + JSON.toJSONString(paxIdList));
                for (Map<String, Object> map1 : paxIdList) {
                    log.info("代领审核不通过申领单号:" + applyCode + "旅客ID:" + map1.get("PAXID").toString());
                    actAuditInfoDao.updateDpPaxInfoStatus(map1.get("PAXID").toString(), "0");
                }
                SmsTemplateForm smsTemplateForm = new SmsTemplateForm();
                smsTemplateForm.setTemplateCode(SmsConstant.SUBSTITUTE_AUDIT);
                SmsTemplate smsTemplate = smsService.queryTemplateByType(smsTemplateForm);
                if (null != smsTemplate) {
                    String sendContent = smsTemplate.getTemplateContent();
                    //调用短信
                    smsService.smsSend(AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_KEY,telephone), sendContent, flightNo, flightDate, SmsConstant.SUBSTITUTE_AUDIT, "system");
                } else {
                    log.info("代领审核不通过短信通知:未找到短信模板");
                }
                actAuditInfoDao.sendMessage(applyCode);
            }

        }
    }

    @Override
    public ActAuditPaxInfoVo getApplyInfoByApplyCode(String applyCode) {
        ActAuditPaxInfoVo vo = actAuditInfoDao.getApplyInfoByApplyCode(applyCode);
        vo.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, vo.getTelephone()));
        return vo;
    }
}