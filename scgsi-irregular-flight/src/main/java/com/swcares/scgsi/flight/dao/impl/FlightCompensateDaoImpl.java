package com.swcares.scgsi.flight.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.flight.dto.FlightQueryParamDto;
import com.swcares.scgsi.flight.dto.PaxInfoQueryParamDto;
import com.swcares.scgsi.flight.enums.OrderStatusEnum;
import com.swcares.scgsi.flight.vo.*;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

/**
 * ClassName：com.swcares.scgsi.flight.dao.impl <br>
 * Description：不正常航班自定义实现sqldao <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月19日 10:54 <br>
 * @version v1.0 <br>
 */
@Repository
public class FlightCompensateDaoImpl {
    @Resource
    private BaseDAO baseDAO;
    @Resource
    private UserUtil userUtil;

    /**
     * Title：getFlightInfoPage <br>
     * Description： 分页查询赔偿单管理列表<br>
     * author：王建文 <br>
     * date：2020-3-19 11:02 <br>
     *
     * @param flightQueryParamDto 参数接收
     * @return
     */
    public QueryResults getFlightInfoPage(FlightQueryParamDto flightQueryParamDto) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT P.flightNo,P.flightDate,P.segment, get_dp_order_status_count (NULL,P.flightDate,P.flightNo) AS applyCount, ");
        sql.append(" get_dp_order_status_count ('3',P.flightDate,P.flightNo) AS effectCount, ");
        sql.append(" get_dp_order_status_count ('1',P.flightDate,P.flightNo) AS auditCount, ");
        sql.append(" get_dp_order_status_count ('4',P.flightDate,P.flightNo) AS closeCount ");
        sql.append(" FROM ( ");
        sql.append(" SELECT F.FLIGHT_NO AS flightNo,F.FLIGHT_DATE AS flightDate,REPLACE(F.SEGMENT,' ', '') AS SEGMENT ");
        sql.append(" FROM DP_FLIGHT_INFO F ");
        sql.append(" LEFT JOIN DP_ORDER_INFO DOI ON DOI.FLIGHT_NO = F.FLIGHT_NO ");
        sql.append(" AND DOI.FLIGHT_DATE = F.FLIGHT_DATE ");
        sql.append(" LEFT JOIN DP_PAX_INFO DPI ON DPI.ORDER_ID=DOI.ORDER_ID ");
        sql.append(" WHERE 1 = 1 ");
        String flightNo = flightQueryParamDto.getFlightNo();
        if (StringUtils.isNotBlank(flightNo)) {
            paramsMap.put("flightNo", flightNo);
            sql.append(" AND F.FLIGHT_NO= :flightNo ");
        }
        String payType=flightQueryParamDto.getPayType();
        if (StringUtils.isNotBlank(payType)) {
            paramsMap.put("payType", payType);
            sql.append(" AND DOI.PAY_TYPE= :payType ");
        }
        String startDate = flightQueryParamDto.getStartDate();
        String endDate = flightQueryParamDto.getEndDate();
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND F.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }
        if (StringUtils.isNotBlank(endDate) && StringUtils.isBlank(startDate)) {
            paramsMap.put("endDate", endDate);
            sql.append(" AND F.FLIGHT_DATE <= :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
            endDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND F.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }
        String orgCityAirp = flightQueryParamDto.getOrgCityAirp();
        if (StringUtils.isNotBlank(orgCityAirp)) {
            paramsMap.put("orgCityAirp", orgCityAirp);
            sql.append(" AND DPI.ORG_CITY_AIRP= :orgCityAirp ");
        }
        String dstCityAirp = flightQueryParamDto.getDstCityAirp();
        if (StringUtils.isNotBlank(dstCityAirp)) {
            paramsMap.put("dstCityAirp", dstCityAirp);
            sql.append(" AND DPI.DST_CITY_AIRP= :dstCityAirp ");
        }
        String status = flightQueryParamDto.getStatus();
        if (StringUtils.isNotBlank(status)) {
            Authentication authentication = AuthenticationUtil.getAuthentication();
            String myOwn=(String) authentication.getPrincipal();
            paramsMap.put("myOwn", myOwn);
            if("0".equals(status)){
                sql.append(" AND DOI.STATUS='0' ");
                String condition = " AND ( " +
                        "    ( " +
                        "        (SELECT COUNT(1) " +
                        "         FROM ROLE_CITY_CODE rcc " +
                        "         WHERE rcc.CODE IN ( " +
                        "             SELECT cc.AIRPORT_3CODE " +
                        "             FROM CITY_CODE cc " +
                        "             WHERE cc.CITY_CH_NAME = DOI.SERVICE_CITY) " +
                        "        ) = 1 AND DOI.CREATE_ID= 'system' " +
                        "        AND EXISTS ( " +
                        "            SELECT 1 " +
                        "            FROM USER_ROLE ur " +
                        "            WHERE ur.EMPLOYEE_ID = ( " +
                        "                SELECT e2.id " +
                        "                FROM EMPLOYEE e2 " +
                        "                WHERE e2.TUNO = :myOwn) " +
                        "            AND ur.ROLE_ID = '051wr27d-36h9-4027-b4z8-3q53f8c3wrhz' " +
                        "        ) " +
                        "    ) " +
                        "    OR " +
                        "    ( " +
                        "            INSTR(DOI.ALL_SEGMENT, ( " +
                        "            SELECT cc2.CITY_CH_NAME " +
                        "            FROM CITY_CODE cc2 " +
                        "            WHERE cc2.AIRPORT_3CODE = ( " +
                        "                SELECT e3.CITY_3CODE " +
                        "                FROM EMPLOYEE e3 " +
                        "                WHERE e3.TUNO = :myOwn) " +
                        "        ), 1, 1) > 0 AND DOI.CREATE_ID= 'system' " +
                        "    ) " +
                        "    OR F.CREATE_ID = :myOwn " +
                        ")";
                sql.append(condition);
            }
            if("1".equals(status)){
                sql.append(" AND DOI.STATUS !='0' ");
                sql.append(" AND (F.CREATE_ID= :myOwn or DOI.APPLY_USER = :myOwn) ");
            }
        }
        sql.append(" GROUP BY F.FLIGHT_NO,F.FLIGHT_DATE,REPLACE(F.SEGMENT,' ', '') ) P ");
        sql.append(" ORDER BY P.flightDate DESC ");
        return baseDAO.findBySQLPage_comm(
                sql.toString(),
                flightQueryParamDto.getCurrent(),
                flightQueryParamDto.getPageSize(),
                paramsMap,
                FlightInfoVo.class);
    }

    /**
     * Title：getOrderInfoByFlightINoAndFlightDate <br>
     * Description： 根据航班日期航班号获取下面的赔付单集合信息<br>
     * author：王建文 <br>
     * date：2020-3-19 11:04 <br>
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @return
     */
    public List<ServiceOrderInfoVo> getOrderInfoByFlightINoAndFlightDate(String flightNo, String flightDate,String payType,String status) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT DISTINCT(O.ORDER_ID) AS orderId,O.STATUS AS status,O.PAY_TYPE AS payType, ");
        sql.append(" O.CHOICE_SEGMENT AS choiceSegment, ");
        sql.append(" get_dp_order_receivecount(O.ORDER_ID,1)||'/'||get_dp_order_receivecount(O.ORDER_ID,NULL) AS executeCount, ");
        sql.append(" NVL(get_dp_order_receivemoney(O.ORDER_ID,1),0) ||'/'||O.SUM_MONEY executeMoney, ");
        sql.append(" get_dp_order_pax_freezecount(O.ORDER_ID) AS freezeCount, ");
        sql.append(" get_user_name(O.APPLY_USER) AS applyUser,TO_CHAR(O.APPLY_DATE,'YYYY-MM-DD hh24:mi:ss') AS applyTime,O.CREATE_ID AS createId, O.APPLY_USER AS applyId, ");
        sql.append(" get_user_name(O.CREATE_ID) AS createUser,TO_CHAR(O.CREATE_TIME,'YYYY-MM-DD hh24:mi:ss') AS createDate,O.AUTO_CREATE AS autoCreate, ");
        sql.append(" get_user_name(O.CLOSE_USER)  AS closeUser,TO_CHAR(O.CLOSE_TIME,'YYYY-MM-DD hh24:mi:ss') AS closeTime  ");
        sql.append(" FROM DP_ORDER_INFO O ");
        paramsMap.put("flightNo", flightNo);
        paramsMap.put("flightDate", flightDate);
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String myOwn=(String) authentication.getPrincipal();
        sql.append(" WHERE O.FLIGHT_NO =:flightNo ");
        sql.append(" AND O.FLIGHT_DATE=:flightDate ");
        List<String> payTypeLIst=userUtil.findBussiTypeByEmpId(myOwn);
        if(payTypeLIst.size()>0){
            String queryType1="";
            for(String payType1:payTypeLIst){
                queryType1+=payType1+",";
            }
            sql.append(" AND O.PAY_TYPE IN ");
            sql.append("  (SELECT REGEXP_SUBSTR('"+queryType1+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+queryType1+"', '[^,]+', 1, level) is not null) ");
        }
        if (StringUtils.isNotBlank(status)) {
            paramsMap.put("myOwn", myOwn);
            if("0".equals(status)){
                sql.append(" AND O.STATUS='0' ");
                //sql.append(" AND O.CREATE_ID= :myOwn ");
                String condition = " AND ( " +
                        "    ( " +
                        "        (SELECT COUNT(1) " +
                        "         FROM ROLE_CITY_CODE rcc " +
                        "         WHERE rcc.CODE IN ( " +
                        "             SELECT cc.AIRPORT_3CODE " +
                        "             FROM CITY_CODE cc " +
                        "             WHERE cc.CITY_CH_NAME = O.SERVICE_CITY) " +
                        "        ) = 1 AND O.CREATE_ID= 'system' " +
                        "        AND EXISTS ( " +
                        "            SELECT 1 " +
                        "            FROM USER_ROLE ur " +
                        "            WHERE ur.EMPLOYEE_ID = ( " +
                        "                SELECT e2.id " +
                        "                FROM EMPLOYEE e2 " +
                        "                WHERE e2.TUNO = :myOwn) " +
                        "            AND ur.ROLE_ID = '051wr27d-36h9-4027-b4z8-3q53f8c3wrhz' " +
                        "        ) " +
                        "    ) " +
                        "    OR " +
                        "    ( " +
                        "           INSTR(O.ALL_SEGMENT, ( " +
                        "            SELECT cc2.CITY_CH_NAME " +
                        "            FROM CITY_CODE cc2 " +
                        "            WHERE cc2.AIRPORT_3CODE = ( " +
                        "                SELECT e3.CITY_3CODE " +
                        "                FROM EMPLOYEE e3 " +
                        "                WHERE e3.TUNO = :myOwn) " +
                        "        ), 1, 1) > 0 AND O.CREATE_ID= 'system' " +
                        "    ) " +
                        "    OR O.create_id = :myOwn " +
                        ")";
                sql.append(condition);
            }
            if("1".equals(status)){
                sql.append(" AND O.STATUS !='0' ");
                // 这里要修改，发起的修改了字段，兼容历史数据需要
                sql.append(" AND (O.CREATE_ID= :myOwn or O.APPLY_USER = :myOwn) ");
            }
        }
        sql.append(" ORDER BY applyTime DESC ");
        return (List<ServiceOrderInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, ServiceOrderInfoVo.class);
    }

    public List<ServiceOrderInfoVo> getOrderInfoByFlightINoAndFlightDate(String orderId,String status) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT DISTINCT(O.ORDER_ID) AS orderId,O.STATUS AS status,O.PAY_TYPE AS payType, ");
        sql.append(" O.CHOICE_SEGMENT AS choiceSegment, ");
        sql.append(" get_dp_order_receivecount(O.ORDER_ID,1)||'/'||get_dp_order_receivecount(O.ORDER_ID,NULL) AS executeCount, ");
        sql.append(" NVL(get_dp_order_receivemoney(O.ORDER_ID,1),0) ||'/'||O.SUM_MONEY executeMoney, ");
        sql.append(" get_dp_order_pax_freezecount(O.ORDER_ID) AS freezeCount, ");
        sql.append(" get_user_name(O.APPLY_USER) AS applyUser,TO_CHAR(O.APPLY_DATE,'YYYY-MM-DD hh24:mi:ss') AS applyTime,O.CREATE_ID AS createId, ");
        sql.append(" get_user_name(O.CREATE_ID) AS createUser,TO_CHAR(O.CREATE_TIME,'YYYY-MM-DD hh24:mi:ss') AS createDate,O.AUTO_CREATE AS autoCreate, ");
        sql.append(" get_user_name(O.CLOSE_USER)  AS closeUser,TO_CHAR(O.CLOSE_TIME,'YYYY-MM-DD hh24:mi:ss') AS closeTime  ");
        sql.append(" FROM DP_ORDER_INFO O ");
        paramsMap.put("orderId", orderId);
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String myOwn=(String) authentication.getPrincipal();
        sql.append(" WHERE O.ORDER_ID =:orderId ");
        List<String> payTypeLIst=userUtil.findBussiTypeByEmpId(myOwn);
        if(payTypeLIst.size()>0){
            String queryType1="";
            for(String payType1:payTypeLIst){
                queryType1+=payType1+",";
            }
            sql.append(" AND O.PAY_TYPE IN ");
            sql.append("  (SELECT REGEXP_SUBSTR('"+queryType1+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+queryType1+"', '[^,]+', 1, level) is not null) ");
        }
        if (StringUtils.isNotBlank(status)) {
            paramsMap.put("myOwn", myOwn);
            if("0".equals(status)){
                sql.append(" AND O.STATUS='0' ");
                //sql.append(" AND O.CREATE_ID= :myOwn ");
                String condition = " AND ( " +
                        "    ( " +
                        "        (SELECT COUNT(1) " +
                        "         FROM ROLE_CITY_CODE rcc " +
                        "         WHERE rcc.CODE IN ( " +
                        "             SELECT cc.AIRPORT_3CODE " +
                        "             FROM CITY_CODE cc " +
                        "             WHERE cc.CITY_CH_NAME = O.SERVICE_CITY) " +
                        "        ) = 1 AND O.CREATE_ID= 'system' " +
                        "        AND EXISTS ( " +
                        "            SELECT 1 " +
                        "            FROM USER_ROLE ur " +
                        "            WHERE ur.EMPLOYEE_ID = ( " +
                        "                SELECT e2.id " +
                        "                FROM EMPLOYEE e2 " +
                        "                WHERE e2.TUNO = :myOwn) " +
                        "            AND ur.ROLE_ID = '051wr27d-36h9-4027-b4z8-3q53f8c3wrhz' " +
                        "        ) " +
                        "    ) " +
                        "    OR " +
                        "    ( " +
                        "            INSTR(O.ALL_SEGMENT, ( " +
                        "            SELECT cc2.CITY_CH_NAME " +
                        "            FROM CITY_CODE cc2 " +
                        "            WHERE cc2.AIRPORT_3CODE = ( " +
                        "                SELECT e3.CITY_3CODE " +
                        "                FROM EMPLOYEE e3 " +
                        "                WHERE e3.TUNO = :myOwn) " +
                        "        ), 1, 1) > 0 AND O.CREATE_ID= 'system' " +
                        "    ) " +
                        "    OR O.create_id = :myOwn " +
                        ")";
                sql.append(condition);
            }
            if("1".equals(status)){
                sql.append(" AND O.STATUS !='0' ");
                // 这里要修改，发起的修改了字段，兼容历史数据需要
                sql.append(" AND (O.CREATE_ID= :myOwn or O.APPLY_USER = :myOwn) ");
            }
        }
        sql.append(" ORDER BY applyTime DESC ");
        return (List<ServiceOrderInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, ServiceOrderInfoVo.class);
    }

    public List<ServiceOrderInfoVo> getEditPermission(String orderId,String status) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT DISTINCT(O.ORDER_ID) AS orderId,O.STATUS AS status,O.PAY_TYPE AS payType, ");
        sql.append(" O.CHOICE_SEGMENT AS choiceSegment, ");
        sql.append(" get_dp_order_receivecount(O.ORDER_ID,1)||'/'||get_dp_order_receivecount(O.ORDER_ID,NULL) AS executeCount, ");
        sql.append(" NVL(get_dp_order_receivemoney(O.ORDER_ID,1),0) ||'/'||O.SUM_MONEY executeMoney, ");
        sql.append(" get_dp_order_pax_freezecount(O.ORDER_ID) AS freezeCount, ");
        sql.append(" get_user_name(O.APPLY_USER) AS applyUser,TO_CHAR(O.APPLY_DATE,'YYYY-MM-DD hh24:mi:ss') AS applyTime,O.CREATE_ID AS createId, ");
        sql.append(" get_user_name(O.CREATE_ID) AS createUser,TO_CHAR(O.CREATE_TIME,'YYYY-MM-DD hh24:mi:ss') AS createDate,O.AUTO_CREATE AS autoCreate, ");
        sql.append(" get_user_name(O.CLOSE_USER)  AS closeUser,TO_CHAR(O.CLOSE_TIME,'YYYY-MM-DD hh24:mi:ss') AS closeTime  ");
        sql.append(" FROM DP_ORDER_INFO O ");
        paramsMap.put("orderId", orderId);
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String myOwn=(String) authentication.getPrincipal();
        sql.append(" WHERE O.ORDER_ID =:orderId ");
        List<String> payTypeLIst=userUtil.findBussiTypeByEmpId(myOwn);
        if(payTypeLIst.size()>0){
            String queryType1="";
            for(String payType1:payTypeLIst){
                queryType1+=payType1+",";
            }
            sql.append(" AND O.PAY_TYPE IN ");
            sql.append("  (SELECT REGEXP_SUBSTR('"+queryType1+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+queryType1+"', '[^,]+', 1, level) is not null) ");
        }
        if (StringUtils.isNotBlank(status)) {
            paramsMap.put("myOwn", myOwn);
            if("0".equals(status)){
                sql.append(" AND O.STATUS='0' ");
                //sql.append(" AND O.CREATE_ID= :myOwn ");
                String condition = " AND ( " +
                        "    ( " +
                        "        (SELECT COUNT(1) " +
                        "         FROM ROLE_CITY_CODE rcc " +
                        "         WHERE rcc.CODE IN ( " +
                        "             SELECT cc.AIRPORT_3CODE " +
                        "             FROM CITY_CODE cc " +
                        "             WHERE cc.CITY_CH_NAME = O.SERVICE_CITY) " +
                        "        ) = 1 AND O.CREATE_ID= 'system' " +
                        "        AND EXISTS ( " +
                        "            SELECT 1 " +
                        "            FROM USER_ROLE ur " +
                        "            WHERE ur.EMPLOYEE_ID = ( " +
                        "                SELECT e2.id " +
                        "                FROM EMPLOYEE e2 " +
                        "                WHERE e2.TUNO = :myOwn) " +
                        "            AND ur.ROLE_ID = '051wr27d-36h9-4027-b4z8-3q53f8c3wrhz' " +
                        "        ) " +
                        "    ) " +
                        "    OR " +
                        "    ( " +
                        "        (SELECT COUNT(1) " +
                        "         FROM ROLE_CITY_CODE rcc " +
                        "         WHERE rcc.CODE IN ( " +
                        "             SELECT cc.AIRPORT_3CODE " +
                        "             FROM CITY_CODE cc " +
                        "             WHERE cc.CITY_CH_NAME = O.SERVICE_CITY) " +
                        "        ) = 0 AND O.CREATE_ID= 'system' AND " +
                        "            INSTR(O.ALL_SEGMENT, ( " +
                        "            SELECT cc2.CITY_CH_NAME " +
                        "            FROM CITY_CODE cc2 " +
                        "            WHERE cc2.AIRPORT_3CODE = ( " +
                        "                SELECT e3.CITY_3CODE " +
                        "                FROM EMPLOYEE e3 " +
                        "                WHERE e3.TUNO = :myOwn) " +
                        "        ), 1, 1) > 0 AND O.CREATE_ID= 'system' " +
                        "    ) " +
                        "    OR O.create_id = :myOwn " +
                        ")";
                sql.append(condition);
            }
            if("1".equals(status)){
                sql.append(" AND O.STATUS !='0' ");
                // 这里要修改，发起的修改了字段，兼容历史数据需要
                sql.append(" AND (O.CREATE_ID= :myOwn or O.APPLY_USER = :myOwn) ");
            }
        }
        sql.append(" ORDER BY applyTime DESC ");
        return (List<ServiceOrderInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, ServiceOrderInfoVo.class);
    }

    /**
     * Title：getOrderDetailByOrderId <br>
     * Description： 根据赔付单号获取赔付单详情<br>
     * author：王建文 <br>
     * date：2020-3-19 11:08 <br>
     *
     * @param orderId
     * @return
     */
    public OrderDetailInfoVo getOrderDetailByOrderId(String orderId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT O.ORDER_ID AS orderId,O.PAY_TYPE AS payType,NVL(O.ACCIDENT_ID,' ') AS accidentId, ");
        sql.append(" O.SERVICE_CITY AS serviceSegment,O.FLIGHT_NO AS flightNo,O.FLIGHT_DATE AS flightDate, ");
        sql.append(" F.PLANE_CODE AS planeCode,NVL(F.ETD,' ') AS etd,NVL(F.STD,' ') AS std,F.LATE_REASON AS lateReason,F.AC_TYPE AS acType, ");
        sql.append(" F.FLIGHT_ID AS flightId, ");
        sql.append(" F.DELAY_TIME AS delayTime,O.REMARK AS remark,get_dp_order_receivecount(O.ORDER_ID,NULL) AS personTotalCount,");
        sql.append(" O.SUM_MONEY AS totalMoney,O.CHOICE_SEGMENT AS choiceSegment,OI.PRICE_SPREAD AS tktPriceDiff, ");
        sql.append(
                " get_dp_order_receivecount(O.ORDER_ID,NULL)-GET_DP_ORDER_CHILDCOUNT(O.ORDER_ID)-GET_DP_ORDER_BABYCOUNT(O.ORDER_ID)||'/'||GET_DP_ORDER_CHILDCOUNT(O.ORDER_ID)||'/'||GET_DP_ORDER_BABYCOUNT(O.ORDER_ID) AS membersCount ");
        sql.append(" FROM DP_ORDER_INFO O LEFT JOIN DP_FLIGHT_INFO F ON  ");
        sql.append(" F.ORDER_ID=O.ORDER_ID   ");
        sql.append(" LEFT JOIN DP_OVER_INFO OI ON OI.ORDER_ID=O.ORDER_ID ");
        sql.append(" WHERE 1=1 ");
        paramsMap.put("orderId", orderId);
        sql.append(" AND O.ORDER_ID =:orderId ");
        return baseDAO.findOneBySql(sql.toString(), paramsMap, OrderDetailInfoVo.class);
    }

    /**
     * Title：getCompensateInfo <br>
     * Description：不正常航班根据订单号舱位类型获取赔付标准信息<br>
     * author：王建文 <br>
     * date：2020-3-19 11:11 <br>
     *
     * @param orderId   赔付单号
     * @param classType 舱位类型
     * @return
     */
    public CompensateInfoVo getCompensateInfo(String orderId, String classType) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("orderId", orderId);
        sql.append(" SELECT C.CPS_NUM AS cpsNum,C.CHILD_STD AS childStd,C.BABY_STD AS babyStd ");
        sql.append(" FROM DP_COMPENSATE_INFO C ");
        sql.append(" WHERE 1=1 ");
        sql.append(" AND C.ORDER_ID=:orderId ");
        if(StringUtils.isNotBlank(classType)){
            paramsMap.put("classType", classType);
            sql.append(" AND C.CLASS_TYPE=:classType ");
        }else{
            sql.append(" AND C.CLASS_TYPE IS NULL ");
        }
        List<CompensateInfoVo> compensateInfoVoList=(List<CompensateInfoVo>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,CompensateInfoVo.class);
        if(compensateInfoVoList.size()>0){
            return compensateInfoVoList.get(0);
        }
        return new CompensateInfoVo();
    }

    /**
     * Title：getPaxInfoList <br>
     * Description： 获取赔付单旅客列表<br>
     * author：王建文 <br>
     * date：2020-3-19 11:15 <br>
     *
     * @param paxInfoQueryParamDto 参数接收
     * @return
     */
    public List<PaxInfoVo> getPaxInfoList(PaxInfoQueryParamDto paxInfoQueryParamDto) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" select P.PAX_ID AS paxId,P.PAX_NAME AS paxName,P.ID_TYPE AS idType,P.PNR AS pnr,P.WITH_BABY AS isInfant, ");
        sql.append(" P.TELEPHONE AS telephone,");
        sql.append(" P.ID_NO AS idNo,P.SEGMENT AS segment,P.CURRENT_AMOUNT AS currentAmount, ");
        sql.append(" P.RECEIVE_STATUS AS receiveStatus,TO_CHAR(P.SWITCH) AS switchOff,P.PAX_STATUS AS paxStatus, ");
        sql.append(" p.MAIN_CLASS AS mainClass,P.SUB_CLASS AS subClass,P.TKT_NO AS tktNo, ");
        sql.append(" P.TKT_ISSUE_DATE AS tktDate, ");
        sql.append("P.IS_CHILD AS isChild, ");
        sql.append(" get_dp_pax_paystatus(P.PAX_ID,P.ORDER_ID) AS payStatus, ");
        sql.append(" get_dp_pax_payfailcontent(P.PAX_ID,P.ORDER_ID) AS payFailRemark, ");
        sql.append(" P.ORG_CITY_AIRP AS orgCityAirp,P.DST_CITY_AIRP AS dstCityAirp, ");
        sql.append(" P.SEX AS sex,NVL(p.BABY_PAX_NAME,' ') AS babyName,O.FLIGHT_NO AS flightNo, ");
        sql.append(" o.FLIGHT_DATE AS flightDate,get_dp_pax_paycount(P.PAX_ID,O.FLIGHT_NO,O.FLIGHT_DATE) AS payCount, ");
        sql.append(" get_dp_pax_paymoney(P.PAX_ID,O.FLIGHT_NO,O.FLIGHT_DATE) AS payMoney ");
        sql.append(" from DP_PAX_INFO P  ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ON O.ORDER_ID=P.ORDER_ID ");
        sql.append(" where 1=1 ");
        paramsMap.put("orderId", paxInfoQueryParamDto.getOrderId());
        sql.append(" AND P.ORDER_ID =:orderId ");
        String keySearch = paxInfoQueryParamDto.getKeySearch();
        if (StringUtils.isNotBlank(keySearch)) {
            paramsMap.put("keySearch", "%" + keySearch + "%");
            sql.append(" AND (P.PAX_NAME LIKE:keySearch OR P.ID_NO LIKE:keySearch OR P.TKT_NO LIKE:keySearch) ");
        }
        String receiveStatus = paxInfoQueryParamDto.getReceiveStatus();
        if (StringUtils.isNotBlank(receiveStatus)) {
            paramsMap.put("receiveStatus", receiveStatus);
            sql.append(" AND P.RECEIVE_STATUS =:receiveStatus ");
        }
        String switchOff = paxInfoQueryParamDto.getSwitchOff();
        if (StringUtils.isNotBlank(switchOff)) {
            paramsMap.put("switchOff", switchOff);
            sql.append(" AND P.SWITCH =:switchOff ");
        }
        return (List<PaxInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, PaxInfoVo.class);
    }
    public QueryResults getPaxInfoPage(PaxInfoQueryParamDto paxInfoQueryParamDto) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" select P.PAX_ID AS paxId,P.PAX_NAME AS paxName,P.ID_TYPE AS idType,P.PNR AS pnr,P.WITH_BABY AS isInfant, ");
        sql.append(" P.TELEPHONE AS telephone,");
        sql.append(" P.ID_NO AS idNo,P.SEGMENT AS segment,P.CURRENT_AMOUNT AS currentAmount, ");
        sql.append(" P.RECEIVE_STATUS AS receiveStatus,TO_CHAR(P.SWITCH) AS switchOff,P.PAX_STATUS AS paxStatus, ");
        sql.append(" p.MAIN_CLASS AS mainClass,P.SUB_CLASS AS subClass,P.TKT_NO AS tktNo, ");
        sql.append(" P.TKT_ISSUE_DATE AS tktDate, ");
        sql.append(" P.IS_CHILD AS isChild, ");
        sql.append(" get_dp_pax_paystatus(P.PAX_ID,P.ORDER_ID) AS payStatus, ");
        sql.append(" get_dp_pax_payfailcontent(P.PAX_ID,P.ORDER_ID) AS payFailRemark, ");
        sql.append(" P.ORG_CITY_AIRP AS orgCityAirp,P.DST_CITY_AIRP AS dstCityAirp, ");
        sql.append(" P.SEX AS sex,NVL(p.BABY_PAX_NAME,' ') AS babyName,O.FLIGHT_NO AS flightNo, ");
        sql.append(" o.FLIGHT_DATE AS flightDate,get_dp_pax_paycount(P.PAX_ID,O.FLIGHT_NO,O.FLIGHT_DATE) AS payCount, ");
        sql.append(" get_dp_pax_paymoney(P.PAX_ID,O.FLIGHT_NO,O.FLIGHT_DATE) AS payMoney ");
        sql.append(" from DP_PAX_INFO P  ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ON O.ORDER_ID=P.ORDER_ID ");
        sql.append(" where 1=1 ");
        paramsMap.put("orderId", paxInfoQueryParamDto.getOrderId());
        sql.append(" AND P.ORDER_ID =:orderId ");
        String keySearch = paxInfoQueryParamDto.getKeySearch();
        if (StringUtils.isNotBlank(keySearch)) {
            paramsMap.put("keySearch", "%" + keySearch + "%");
            sql.append(" AND (P.PAX_NAME LIKE:keySearch OR P.ID_NO LIKE:keySearch OR P.TKT_NO LIKE:keySearch) ");
        }
        String receiveStatus = paxInfoQueryParamDto.getReceiveStatus();
        if (StringUtils.isNotBlank(receiveStatus)) {
            paramsMap.put("receiveStatus", receiveStatus);
            sql.append(" AND P.RECEIVE_STATUS =:receiveStatus ");
        }
        String switchOff = paxInfoQueryParamDto.getSwitchOff();
        if (StringUtils.isNotBlank(switchOff)) {
            paramsMap.put("switchOff", switchOff);
            sql.append(" AND P.SWITCH =:switchOff ");
        }
        return baseDAO.findBySQLPage_comm(sql.toString(),paxInfoQueryParamDto.getCurrent(),paxInfoQueryParamDto.getPageSize(), paramsMap, PaxInfoVo.class);
    }

    /**
     * Title：getUnPayApplyOrder <br>
     * Description： 赔偿单关闭获取未领取申领单进行冻结操作<br>
     * author：王建文 <br>
     * date：2020-3-10 10:42 <br>
     *
     * @param orderId 赔偿单Id
     * @return List
     */
    public List<Map<String, Object>> getUnPayApplyOrder(String paxIds,String orderId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("orderId", orderId);
        sql.append(" SELECT DAP.APPLY_CODE AS applyCode,DAP.PAX_ID AS paxId,DPI.PAX_NAME AS paxName,DAO.APPLY_STATUS AS applyStatus,DAO.STATUS AS status ");
        sql.append(" FROM DP_APPLY_PAX DAP LEFT JOIN DP_PAX_INFO DPI ON  ");
        sql.append(" DPI.PAX_ID=DAP.PAX_ID LEFT JOIN DP_APPLY_ORDER DAO ON ");
        sql.append(" DAO.APPLY_CODE=DAP.APPLY_CODE ");
        sql.append(" WHERE DAO.PAY_STATUS!=1 AND :orderId ");
        sql.append(" in (SELECT REGEXP_SUBSTR(DAP.ORDER_ID,'[^,]+', 1, LEVEL) FROM DUAL ");
        sql.append(" connect by regexp_substr(DAP.ORDER_ID, '[^,]+', 1, level) is not null) ");
        if(StringUtils.isNotBlank(paxIds)){
            sql.append(" AND DAP.PAX_ID IN ");
            sql.append("  (SELECT REGEXP_SUBSTR('"+paxIds+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+paxIds+"', '[^,]+', 1, level) is not null) ");
        }
        //执行sql
        return (List<Map<String, Object>>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, null);
    }

    /**
     * Title：updateOrderStatusByOrderIdAndStatus <br>
     * Description： 更新赔付单状态<br>
     * author：王建文 <br>
     * date：2020-3-10 11:10 <br>
     *
     * @param orderId 赔付单号
     * @param status  状态
     * @return
     */
    public void updateOrderStatusByOrderIdAndStatus(String orderId, String status) {
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_ORDER_INFO SET ");
        sql.append(" STATUS=? ");
        //关闭状态执行更新关闭人，关闭时间
        Authentication authentication =AuthenticationUtil.getAuthentication();
        String currentUser =(String) authentication.getPrincipal();
        if (status.equals(OrderStatusEnum.CLOSE.getKey())) {
            Date closeTime = new Date();
            sql.append(" ,CLOSE_TIME=?, ");
            sql.append(" CLOSE_USER=? ");
            sql.append(" WHERE ORDER_ID=? ");
            baseDAO.batchUpdate(sql.toString(), status, closeTime, currentUser, orderId);
        } else {
            sql.append(" ,ISS_USER=? ");
            sql.append(" WHERE ORDER_ID=? ");
            baseDAO.batchUpdate(sql.toString(), status,currentUser, orderId);
        }
    }

    /**
     * Title：freezeApplyOrder <br>
     * Description： 冻结申领主表<br>
     * author：王建文 <br>
     * date：2020-3-10 11:26 <br>
     *
     * @param applyCode 申领单号
     * @return
     */
    public void freezeApplyOrder(String applyCode,String status) {
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_APPLY_ORDER SET ");
        sql.append(" APPLY_STATUS=? ");
        sql.append(" where APPLY_CODE=? ");
        baseDAO.batchUpdate(sql.toString(),status, applyCode);
    }

    /**
     * Title：freezeApplyPax <br>
     * Description：冻结申领旅客<br>
     * author：王建文 <br>
     * date：2020-3-10 11:34 <br>
     *
     * @param applyCode 申领单号
     * @param paxId     旅客id
     * @return
     */
    public void freezeApplyPax(String applyCode, String paxId,String status,String remark) {
        StringBuffer sql = new StringBuffer();
        List<Object> paramters = new ArrayList<>();
        sql.append(" UPDATE DP_APPLY_PAX SET ");
        if("4".equals(status)){
            sql.append(" FREEZE_REMARK =? ");
            paramters.add(remark);
        }
        if("3".equals(status)){
            sql.append(" FAILURE_REMARK =? ");
            paramters.add(remark);
        }
        if("0".equals(status)){
            sql.append(" STATUS='0', ");
            sql.append(" FREEZE_REMARK =? ");
            paramters.add(remark);
        }
        sql.append(" where APPLY_CODE =? ");
        sql.append(" AND PAX_ID =? ");
        paramters.add(applyCode);
        paramters.add(paxId);
        baseDAO.batchUpdate(sql.toString(),paramters);
    }

    /**
     * Title：updatePaxStatus <br>
     * Description：激活关闭旅客领取资格<br>
     * author：王建文 <br>
     * date：2020-3-19 11:27 <br>
     *
     * @param paxIds 旅客数组
     * @param status 状态
     * @return
     */
    public void updatePaxStatus(String[] paxIds, String status,String orderId) {
        for (String paxId : paxIds) {
            StringBuffer sql = new StringBuffer();
            sql.append(" UPDATE DP_PAX_INFO SET ");
            sql.append(" SWITCH=? ");
            sql.append(" WHERE PAX_ID=? AND ORDER_ID=?");
            baseDAO.batchUpdate(sql.toString(), status,paxId, orderId);
        }
    }

    /**
     * Title：getCity3CodeByCityName <br>
     * Description： 根据城市名称获取对应三字码<br>
     * author：王建文 <br>
     * date：2020-3-23 11:13 <br>
     *
     * @param cityName 根据城市名称获取对应的三字码
     * @return
     */
    public CityCodeVo getCityCodeInfoByCityName(String cityName) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("cityName", cityName);
        sql.append(" SELECT AIRPORT_3CODE AS airport3code, ");
        sql.append(" AIRPORT_4CODE AS airport4code ");
        sql.append(" FROM CITY_CODE  ");
        sql.append(" WHERE CITY_CH_NAME=:cityName ");
        return baseDAO.findOneBySql(sql.toString(),paramsMap,CityCodeVo.class);
    }

    /**
     * Title： getCityCodeInfoByCityCode3<br>
     * Description： 根据城市名称获取对应三字码 、四字码<br>
     * author：傅欣荣 <br>
     * date：2020/4/13 21:22 <br>
     * @param
     * @return
     */
    public CityCodeVo getCityCodeInfoByCityCode3(String cityCode) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("cityCode", cityCode);
        sql.append(" SELECT AIRPORT_3CODE AS airport3code, ");
        sql.append(" CITY_CH_NAME AS cityChName,");
        sql.append(" AIRPORT_4CODE AS airport4code ");
        sql.append(" FROM CITY_CODE  ");
        sql.append(" WHERE AIRPORT_3CODE=:cityCode ");
        return baseDAO.findOneBySql(sql.toString(),paramsMap,CityCodeVo.class);
    }
    /**
     * Title：getSelectCityInfo <br>
     * Description： 获取航站下拉选项<br>
     * author：王建文 <br>
     * date：2020-3-25 14:22 <br>
     * @param
     * @return
     */
    public List<Map<String,Object>> getSelectCityInfo() {
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT AIRPORT_3CODE AS city3code, ");
        sql.append(" CITY_CH_NAME AS cityName ");
        sql.append(" FROM CITY_CODE  ");
        return (List<Map<String,Object>>) baseDAO.findBySQL_comm(sql.toString(),new HashMap<>(),null);
    }
    public Map<String,Object> getPaxPayCountAndPayMoney(String paxId,String flightNo,String flightDate) {
        StringBuffer sql = new StringBuffer();
        Map<String,Object> paramMap=new HashMap<>();
        paramMap.put("paxId",paxId);
        sql.append(" select ");
        sql.append(" get_dp_pax_paycount(:paxId,'"+flightNo+"','"+flightDate+"') AS payCount, ");
        sql.append(" NVL(get_dp_pax_paymoney('"+paxId+"','"+flightNo+"','"+flightDate+"'),'0') AS payMoney ");
        sql.append(" from DP_PAX_INFO P  ");
        sql.append(" where 1=1 ");
        sql.append(" and  rownum=1 ");
        List<Map<String,Object>> dataMapList=(List<Map<String,Object>>)baseDAO.findBySQL_comm(sql.toString(),paramMap, null);
        if(dataMapList.size()>0){
            return dataMapList.get(0);
        }
        return new HashMap<>();
    }
    /**
     * Title：getPaxOrderInfo <br>
     * Description： 获取旅客赔偿次数详情<br>
     * author：王建文 <br>
     * date：2020-3-23 17:28 <br>
     * @param  paxId 旅客id
     * @param  flightNo 航班号
     * @param  flightDate 航班日期
     * @return
     */
    public List<Map<String,Object>> getPaxOrderInfo(String paxId,String flightNo,String flightDate){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("paxId", paxId);
        paramsMap.put("flightNo", flightNo);
        paramsMap.put("flightDate", flightDate);
        sql.append(" SELECT DISTINCT(O.ORDER_ID) AS orderId,O.PAY_TYPE AS payType,P.PAX_NAME AS paxName, ");
        sql.append(" P.IS_CHILD AS isChild,P.BABY_PAX_NAME AS babyName, P.WITH_BABY isInfant,");
        sql.append(" O.STATUS AS status,P.CURRENT_AMOUNT AS payMoney, ");
        sql.append(" P.SWITCH AS switchOff,p.RECEIVE_STATUS AS receiveStatus, ");
        sql.append(" get_user_name(O.CREATE_ID) AS applyUser,O.CREATE_TIME AS applyTime ");
        sql.append(" FROM DP_PAX_INFO P ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ");
        sql.append(" ON O.ORDER_ID=P.ORDER_ID ");
        sql.append(" WHERE ((O.STATUS = '3' AND P.SWITCH ='0') OR  P .RECEIVE_STATUS = '1') AND  P.PAX_ID=:paxId ");
        sql.append(" AND O.FLIGHT_NO=:flightNo ");
        sql.append(" AND O.FLIGHT_DATE=:flightDate ");
        return (List<Map<String,Object>>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,null);
    }
    public List<Map<String,Object>> getPaxAllOrderInfo(String paxId,String flightNo,String flightDate){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("paxId", paxId);
        paramsMap.put("flightNo", flightNo);
        paramsMap.put("flightDate", flightDate);
        sql.append(" SELECT DISTINCT(O.ORDER_ID) AS orderId,O.PAY_TYPE AS payType,P.PAX_NAME AS paxName, ");
        sql.append(" P.IS_CHILD AS isChild,P.BABY_PAX_NAME AS babyName, ");
        sql.append(" O.STATUS AS status,P.CURRENT_AMOUNT AS payMoney, ");
        sql.append(" P.SWITCH AS switchOff,p.RECEIVE_STATUS AS receiveStatus, ");
        sql.append(" get_user_name(O.CREATE_ID) AS applyUser,O.CREATE_TIME AS applyTime ");
        sql.append(" FROM DP_PAX_INFO P ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ");
        sql.append(" ON O.ORDER_ID=P.ORDER_ID ");
        //sql.append(" WHERE (O.STATUS='1' OR O.STATUS='2' OR O.STATUS='3' OR O.STATUS='7' OR p.RECEIVE_STATUS='1')  ");
        sql.append(" WHERE O.STATUS!='0'   ");
        sql.append(" AND P.PAX_ID=:paxId ") ;
        sql.append(" AND O.FLIGHT_NO=:flightNo ");
        sql.append(" AND O.FLIGHT_DATE=:flightDate ");
        return (List<Map<String,Object>>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,null);
    }
    /**
     * Title：deleteOrderInfo <br>
     * Description： 删除赔付单<br>
     * author：王建文 <br>
     * date：2020-3-26 9:34 <br>
     * @param  orderId 赔付单号
     * @return
     */
    public void deleteOrderInfo(String orderId) {
        //1.删除航班信息
        StringBuffer flightSql = new StringBuffer();
        flightSql.append(" DELETE FROM DP_FLIGHT_INFO ");
        flightSql.append(" where ORDER_ID=? ");
        baseDAO.batchUpdate(flightSql.toString(), orderId);
        //2.删除赔付单信息
        StringBuffer orderSql = new StringBuffer();
        orderSql.append(" DELETE FROM DP_ORDER_INFO ");
        orderSql.append(" where ORDER_ID=? ");
        baseDAO.batchUpdate(orderSql.toString(), orderId);
        //3.删除赔付标准
        StringBuffer compensateSql = new StringBuffer();
        compensateSql.append(" DELETE FROM DP_COMPENSATE_INFO ");
        compensateSql.append(" where ORDER_ID=? ");
        baseDAO.batchUpdate(compensateSql.toString(), orderId);
        //4.删除旅客信息
        StringBuffer paxSql = new StringBuffer();
        paxSql.append(" DELETE FROM DP_PAX_INFO ");
        paxSql.append(" where ORDER_ID=? ");
        baseDAO.batchUpdate(paxSql.toString(), orderId);
    }
    public List<Map<String,Object>> getPkgOrderInfo(String orderId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT O.ORDER_ID AS orderId,O.SUM_MONEY AS payMoney, ");
        sql.append(" O.STATUS AS status,GET_USER_NAME(O.CREATE_ID) AS createUser,O.CREATE_ID AS createId,P.RECEIVE_STATUS AS receiveStatus ");
        sql.append(" FROM DP_ORDER_INFO O ");
        sql.append(" LEFT JOIN DP_PAX_INFO P ON P.ORDER_ID=O.ORDER_ID ");
        sql.append(" where  1=1 ");
        sql.append(" AND O.ORDER_ID ");
        sql.append(" in (SELECT REGEXP_SUBSTR('"+orderId+"','[^,]+', 1, LEVEL) FROM DUAL ");
        sql.append(" connect by regexp_substr('"+orderId+"', '[^,]+', 1, level) is not null) ");
        return (List<Map<String,Object>>)baseDAO.findBySQL_comm(sql.toString(),new HashMap<>(), null);
    }
    /**
     * Title：expireOrderInfo <br>
     * Description： 逾期赔付单修改状态<br>
     * author：王建文 <br>
     * date：2020-11-18 10:40 <br>
     * @param
     * @return
     */
    public void expireOrderInfo(){
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_ORDER_INFO SET STATUS='8' ");
        sql.append(" WHERE ORDER_ID IN ");
        sql.append(" (SELECT o.ORDER_ID FROM DP_ORDER_INFO O ");
        sql.append(" WHERE 1=1 AND TO_CHAR(add_months(TO_DATE(O.FLIGHT_DATE, 'YYYY-MM-DD'),12*1),'YYYY-MM-DD') < TO_CHAR(SYSDATE,'YYYY-MM-DD')) ");
        baseDAO.batchUpdate(sql.toString(),new ArrayList<>());
    }
    /**
     * Title：getOrderIssueUserAndLastAuditor <br>
     * Description： 获取赔付单发放人和终审人<br>
     * author：王建文 <br>
     * date：2021-1-7 10:45 <br>
     * @param  orderId 赔付单号
     * @return
     */
    public Map<String,Object> getOrderIssueUserAndLastAuditor(String orderId) {
        StringBuffer sql = new StringBuffer();
        Map<String,Object> paramMap=new HashMap<>();
        paramMap.put("orderId",orderId);
        sql.append(" SELECT get_user_name(GET_DP_ORDER_LATEST_AUDITOR(O.ORDER_ID)) AS auditor, ");
        sql.append(" get_user_name(O.ISS_USER) AS grantUser ");
        sql.append(" FROM DP_ORDER_INFO O ");
        sql.append(" where 1=1 AND O.ORDER_ID=:orderId ");
        List<Map<String,Object>> dataMapList=(List<Map<String,Object>>)baseDAO.findBySQL_comm(sql.toString(),paramMap, null);
        if(dataMapList.size()>0){
            return dataMapList.get(0);
        }
        return new HashMap<>();
    }
}