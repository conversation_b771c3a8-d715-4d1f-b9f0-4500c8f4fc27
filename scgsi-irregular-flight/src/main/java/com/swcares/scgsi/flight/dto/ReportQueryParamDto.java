package com.swcares.scgsi.flight.dto;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.dto <br>
 * Description：报表查询参数接收 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月30日 15:49 <br>
 * @version v1.0 <br>
 */
@Data
public class ReportQueryParamDto {
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 赔付类型
     */
    private String payType;

    /**
     * D国内I国际R地区
     */
    private String areaType;

    /**
     * 航班起始日期
     */
    private String flightStartDate;
    /**
     * 航班结束日期
     */
    private String flightEndDate;
    /**
     * 支付状态
     */
    private String payStatus;
    /**
     * 支持旅客姓名、票号、支付单号搜索
     */
    private String keySearch;

    /**
     * 领取状态
     */
    private String receiveStatus;
    /**
     * 领取渠道
     */
    private String receiveChannel;
    /**
     * 支付方式
     */
    private String receiveWay;
    /**
     * 服务航站
     */
    private String serviceCity;
    /**
     * 起始航站
     */
    private String orgCity;
    /**
     * 到达航站
     */
    private String dstCity;
    /**
     * 支付起始时间
     */
    private String payStartDate;
    /**
     * 支付结束时间
     */
    private String payEndDate;
    /**
     * 赔付单状态
     */
    private String orderStatus;
    /**
     * 是否标记
     */
    private String isFlag;
    /**
     * 机型
     */
    private String acType;
    /**
     * 证件号
     */
    private String idNo;
    /**
     * 申领人手机号
     */
    private String telephone;
    /**
     * 代领旅客审核状态
     */
    private String actStatus;
    /**
     * 当前页数，默认为第一页
     **/
    private int current = 1;

    /** 每页显示记录数，默认为10条 **/
    private int pageSize = 10;
}