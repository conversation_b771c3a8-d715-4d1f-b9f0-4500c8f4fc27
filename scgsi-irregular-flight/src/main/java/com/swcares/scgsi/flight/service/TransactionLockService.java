package com.swcares.scgsi.flight.service;


import com.swcares.scgsi.flight.dto.ApplyInfoDto;
import com.swcares.scgsi.flight.dto.ApplyParamDto;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/
 * Title：TransactionLockService
 * Package：com.swcares.eupsi.flight.service
 * Description：事务提交申领单
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2022-05-18
 */
public interface TransactionLockService {

    /**
     * Title：saveNormalApplyInfo <br>
     * Description： 普通申领提交<br>
     * author：董晨 <br>
     * date：2022-5-12 <br>
     *
     * @param applyParamDto 前端参数接收
     */
    void saveNormalApplyInfo(ApplyParamDto applyParamDto);

    /**
     * Title：saveActApplyInfo <br>
     * Description： 代领申领提交<br>
     * author：董晨 <br>
     * date：2022-5-12 <br>
     *
     * @param applyParamDto 前端参数接收
     */
    void saveActApplyInfo(ApplyParamDto applyParamDto);

    /**
     * Title：saveCashApplyInfo <br>
     * Description： 现金申领提交<br>
     * author：董晨 <br>
     * date：2022-5-12 <br>
     *
     * @param applyInfoDto 前端参数接收
     */
    void saveCashApplyInfo(ApplyInfoDto applyInfoDto);

}
