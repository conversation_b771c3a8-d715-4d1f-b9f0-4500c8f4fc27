package com.swcares.scgsi.flight.vo;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：服务单详情展示层 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * ate 2020年 03月06日 9:51 <br>
 * @version v1.0 <br>
 */
@Data
public class OrderDetailInfoVo {
    /**
     * 服务单id
     */
    private String orderId;
    /**
     *赔偿类型 不正常航班赔偿0、异常行李1、超售旅客2
     */
    private String payType;
    /**
     * 事故单ID
     */
    private String accidentId;
    /**
     * 服务航站
     */
    private String serviceSegment;
    /**
     * 所选航段
     */
    private String  choiceSegment;
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 飞机号
     */
    private String planeCode;
    /**
     * 预计起飞时间
     */
    private String etd;
    /**
     * 计划起飞时间
     */
    private String std;
    /**
     * 延误原因
     */
    private String lateReason;
    /**
     * 延误时长
     */
    private String delayTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 赔偿人数
     */
    private String personTotalCount;
    /**
     * 总赔偿金额
     */
    private int totalMoney;
    /**
     * 成/童/婴 人数显示
     */
    private String membersCount;

    /**
     * 客票差价
     */
    private String tktPriceDiff;
    /**
     * 机型
     */
    private String acType;
    /**
     * 航班id
     */
    private String flightId;
}