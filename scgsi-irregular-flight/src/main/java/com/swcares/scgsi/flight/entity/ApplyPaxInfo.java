package com.swcares.scgsi.flight.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * ClassName：com.swcares.scgsi.flight.entity <br>
 * Description：申领旅客信息 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月11日 14:04 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "DP_APPLY_PAX")
@Data
public class ApplyPaxInfo {
    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    /**
     * 旅客id
     */
    @Column(name = "PAX_ID")
    private String paxId;

    /**
     * 申领单号
     */
    @Column(name = "APPLY_CODE")
    private String applyCode;

    /**
     * 审核状态(0待审核、1已通过、2未通过)
     */
    @Column(name = "STATUS")
    private String status;

    /**
     * 拒绝原因
     */
    @Column(name = "AUDIT_REMARK")
    private String auditRemark;

    /**
     * 其他原因
     */
    @Column(name = "REMARK")
    private String remark;

    /**
     * 图片地址
     */
    @Column(name = "IMG_URL")
    private String imgUrl;

    /**
     * 审核人
     */
    @Column(name = "AUDIT_USER")
    private String auditUser;

    /**
     * 审核时间
     */
    @Column(name = "AUDIT_TIME")
    private String auditTime;

    /**
     * 是否代领标识0本人1代领旅客
     */
    @Column(name = "ACTING_ROLE")
    private String actingRole;

    /**
     * 赔付单号
     */
    @Column(name = "ORDER_ID")
    private String orderId;
}