package com.swcares.scgsi.flight.vo;

import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：赔偿单管理---赔偿单列表展示层 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月05日 13:41 <br>
 * @version v1.0 <br>
 */
@Data
public class ServiceOrderInfoVo {
    /**
     * 状态
     */
    private String status;
    /**
     * 赔偿单号
     */
    private String orderId;
    /**
     * 赔偿类型 不正常航班赔偿0、异常行李1、超售旅客2、
     */
    private String payType;
    /**
     * 赔偿航段
     */
    private String choiceSegment;
    /**
     * 执行人数
     */
    private String executeCount;
    /**
     * 执行金额金额
     */
    private String executeMoney;
    /**
     * 冻结人数
     */
    private int freezeCount;
    /**
     * 申请人
     */
    private String applyUser;
    /**
     * 申请时间
     */
    private String applyTime;
    /**
     * 关闭人
     */
    private String closeUser;
    /**
     * 关闭时间
     */
    private String closeTime;

    /**
     * 创建人
     */
    private String createId;

    /**
     * 针对审核: 是否展示重新编辑按钮  当前登录人为发起人| 状态为驳回 、审核流程流转到发起人 1显示
     *
     */
    private String isShowReEdit;

    private Boolean autoCreate;

    private String createUser;

    private Date createDate;

    private String applyId;
}