package com.swcares.scgsi.flight.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.flight.entity.ApplyOrderInfo;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.dao.impl <br>
 * Description：申领单表查询 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 04月08日 19:56 <br>
 * @version v1.0 <br>
 */
@Repository
public class ApplyOrderQuartzDaoImpl {
    @Resource
    private BaseDAO baseDAO;


    /**
     * Title： findUnpaidOrderInfo <br>
     * Description： 查询待支付订单集合 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 20:01 <br>
     * @param
     * @return
     */
    public List<ApplyOrderInfo> findUnpaidOrderInfo(){
        StringBuffer sql = new StringBuffer();
      /*  sql.append(" SELECT distinct ");
        sql.append(" O.ID , O.APPLY_USER ,O.TELEPHONE ,O.TRANS_AMOUNT ,  ");
        sql.append(" O.PAY_STATUS ,O.GET_MONEY_ACCOUNT ,O.GET_MONEY_WAY ,  ");
        sql.append(" O.ID_NO ,O.OPEN_BANK_NAME ,O.APPLY_CODE ,O.PAY_DATE,");
        sql.append(" O.BANK_CODE ,O.QUICK_PAY              ");
        sql.append(" FROM DP_APPLY_ORDER O  ");
        sql.append(" LEFT JOIN DP_APPLY_PAX apx on o.APPLY_CODE = APX.APPLY_CODE ");
        sql.append(" LEFT JOIN DP_ORDER_INFO oi on  oi.order_id in ");
        sql.append(" (SELECT REGEXP_SUBSTR(apx.order_id,'[^,]+', 1, LEVEL) FROM DUAL ");
        sql.append(" connect by regexp_substr(apx.order_id, '[^,]+', 1, level) is not null) ");
        sql.append(" WHERE ");
        sql.append(" (O.APPLY_WAY = 0 OR (O.APPLY_WAY = 1 AND O.APPLY_STATUS = 1 AND O.QUICK_PAY = 1))");
        sql.append(" AND O.GET_MONEY_WAY <> 2 ");
        sql.append(" AND oi.order_id IS NOT NULL AND ((O.PAY_STATUS in (0,3)) OR ");
        sql.append(" (O.PAY_STATUS = 2  AND to_char(O.CREATE_TIME, 'yyyy-mm-dd') >= to_char(trunc(sysdate)-1, 'yyyy-mm-dd') )) ");
*/
        sql.append(" SELECT distinct ");
        sql.append(" O.ID , O.APPLY_USER ,O.TELEPHONE ,O.TRANS_AMOUNT ,  ");
        sql.append(" O.PAY_STATUS ,O.GET_MONEY_ACCOUNT ,O.GET_MONEY_WAY ,  ");
        sql.append(" O.ID_NO ,O.OPEN_BANK_NAME ,O.APPLY_CODE ,O.PAY_DATE,");
        sql.append(" O.BANK_CODE ,O.QUICK_PAY              ");
        sql.append(" FROM DP_APPLY_ORDER O  ");
        sql.append(" WHERE ");
        sql.append(" (O.APPLY_WAY = 0 OR (O.APPLY_WAY = 1 AND O.APPLY_STATUS = 1 AND O.QUICK_PAY = 1))");
        sql.append(" AND O.GET_MONEY_WAY <> 2 ");
        sql.append(" AND ((O.PAY_STATUS in (0,3))) ");
        // sql.append(" (O.PAY_STATUS = 2  AND to_char(O.CREATE_TIME, 'yyyy-mm-dd') >= to_char(trunc(sysdate)-1, 'yyyy-mm-dd') )) ");

        return (List<ApplyOrderInfo>)baseDAO.findBySQL_comm(sql.toString(),null,ApplyOrderInfo.class);
    }

    /**
     * Title：updApplyOrderStatus <br>
     * Description： 更新申领单支付状态 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 20:02 <br>
     * @param applyCode   支付订单号
     * @param payStatus   支付状态
     * @param receiveTime  领取时间
     * @return
     */
    public int updApplyOrderStatus(String applyCode,String payStatus,String payDate,String receiveTime){
        StringBuffer sql = new StringBuffer();
        sql.append("UPDATE DP_APPLY_ORDER SET PAY_STATUS =? ,PAY_DATE = TO_DATE(?, 'yyyy-mm-dd hh24:mi:ss'),RECEIVE_TIME = TO_DATE(?, 'yyyy-mm-dd hh24:mi:ss') WHERE APPLY_CODE =? ");
        return baseDAO.batchUpdate(sql.toString(),payStatus,payDate,receiveTime,applyCode);
    }

    /**
     * Title：updApplyOrderStatus <br>
     * Description： 更新申领单旅客支付状态 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 20:02 <br>
     * @param paxId orderId
     * @param payStatus   支付状态
     * @param receiveTime  领取时间
     * @return
     */
    public int updOrderPaxStatus(String paxId,String orderId, String payStatus, String receiveTime,boolean isReceiveTime){
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_PAX_INFO set RECEIVE_STATUS = ?");
        sql.append(" ,RECEIVE_TIME = TO_DATE(?, 'yyyy-mm-dd hh24:mi:ss') ");
        sql.append("  WHERE pax_id = ?");
        sql.append("  AND ORDER_ID ");
        sql.append(" in (SELECT REGEXP_SUBSTR(?,'[^,]+', 1, LEVEL) FROM DUAL ");
        sql.append(" connect by regexp_substr(?, '[^,]+', 1, level) is not null) ");

        return baseDAO.batchUpdate(sql.toString(),payStatus,receiveTime,paxId,orderId,orderId);

    }
}
