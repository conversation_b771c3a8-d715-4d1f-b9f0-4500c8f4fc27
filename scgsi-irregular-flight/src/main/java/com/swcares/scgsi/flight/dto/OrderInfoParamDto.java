package com.swcares.scgsi.flight.dto;

import com.swcares.scgsi.flight.entity.CompensatesInfo;
import com.swcares.scgsi.flight.entity.PassengerInfo;
import lombok.Data;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.dto <br>
 * Description：接收前端申领信息参数封装实体 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月11日 15:45 <br>
 * @version v1.0 <br>
 */
@Data
public class OrderInfoParamDto {
    /**
     * 航班基本信息
     */
    ServiceOrderInfoDto orderInfo;

    /**
     * 赔付信息
     */
    List<CompensatesInfo> compensateInfoList;
    /**
     * 旅客信息
     */
    List<PassengerInfo> paxInfoList;
}