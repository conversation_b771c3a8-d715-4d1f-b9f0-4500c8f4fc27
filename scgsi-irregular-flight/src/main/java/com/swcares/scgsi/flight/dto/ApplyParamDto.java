package com.swcares.scgsi.flight.dto;

import com.swcares.scgsi.flight.entity.ApplyPaxInfo;
import lombok.Data;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.dto <br>
 * Description：接收前端申领信息参数封装实体 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月11日 15:45 <br>
 * @version v1.0 <br>
 */
@Data
public class ApplyParamDto {
    /**
     * 申领信息
     */
    ApplyInfoDto applyInfo;

    /**
     * 查询参数
     */
    ApplyQueryDto queryParam;

    /**
     * 申领旅客信息
     */
    List<ApplyPaxInfoDto> paxInfo;
}