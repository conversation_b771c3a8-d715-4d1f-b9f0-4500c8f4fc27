package com.swcares.scgsi.flight.service;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.form.ContentTraceForm;
import com.swcares.scgsi.entity.FocFlightInfo;
import com.swcares.scgsi.flight.dto.FlightQueryParamDto;
import com.swcares.scgsi.flight.dto.OrderInfoParamDto;
import com.swcares.scgsi.flight.dto.PaxInfoQueryParamDto;
import com.swcares.scgsi.flight.dto.ServiceOrderInfoDto;
import com.swcares.scgsi.flight.vo.CompensateInfoVo;
import com.swcares.scgsi.flight.vo.PaxInfoVo;
import com.swcares.scgsi.flight.vo.ServiceOrderInfoVo;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.service <br>
 * Description：不正常航班补偿 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月03日 13:23 <br>
 * @version v1.0 <br>
 */
public interface FlightCompensateService {
    /**
     * Title： saveOrderInfo<br>
     * Description： 保存服务单消息<br>
     * author：王建文 <br>
     * date：2020-3-3 15:41 <br>
     *
     * @param orderInfoParamDto
     * @return
     */
    public String saveOrderInfo(OrderInfoParamDto orderInfoParamDto);

    /**
     * Title：getFlightInfoPage <br>
     * Description： 补偿单管理列表航班信息查询<br>
     * author：王建文 <br>
     * date：2020-3-4 15:54 <br>
     *
     * @param flightQueryParamDto
     * @return QueryResults
     */
    public QueryResults getFlightInfoPage(FlightQueryParamDto flightQueryParamDto);

    /**
     * Title：getOrderInfoByFlightINoAndFlightDate <br>
     * Description：查询出航班号航班日期下面所有的赔付单 <br>
     * author：王建文 <br>
     * date：2020-3-5 14:32 <br>
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @return List<ServiceOrderInfoVo>
     */
    public List<ServiceOrderInfoVo> getOrderInfoByFlightINoAndFlightDate(String flightNo, String flightDate,String payType,String status);

    /**
     * Title：getOrderDetailInfoByOrderId <br>
     * Description： 根据赔偿单id获取赔付详情<br>
     * author：王建文 <br>
     * date：2020-3-6 10:23 <br>
     *
     * @param orderId 服务单
     * @return Map
     */
    public Map<String, Object> getOrderDetailInfoByOrderId(String orderId);

    /**
     * Title：getCompensateInfo <br>
     * Description： 获取赔偿标准信息<br>
     * author：王建文 <br>
     * date：2020-3-6 10:49 <br>
     *
     * @param orderId   赔付单id
     * @param classType
     * @return CompensateInfoVo
     */
    public CompensateInfoVo getCompensateInfo(String orderId, String classType);

    /**
     * Title： getPaxInfoList<br>
     * Description： 赔偿单管理获取旅客列表信息<br>
     * author：王建文 <br>
     * date：2020-3-6 16:13 <br>
     *
     * @param paxInfoQueryParamDto
     * @return List<PaxInfoVo>
     */
    public QueryResults getPaxInfoList(PaxInfoQueryParamDto paxInfoQueryParamDto);
    /**
     * Title：updateOrderStatus <br>
     * Description： 更新赔付单状态，关闭时逻辑判断申领旅客状态<br>
     * author：王建文 <br>
     * date：2020-3-6 16:55 <br>
     * @param  orderId 赔付单id
     * @param  status 状态3确认发放4关闭
     * @return
     */
    public void updateOrderStatus(String orderId,String status);
    /**
     * Title：updatePaxStatus <br>
     * Description：关闭激活旅客领取资格<br>
     * author：王建文 <br>
     * date：2020-3-10 13:51 <br>
     * @param  paxIds 旅客id
     * @param  status 领取资格0恢复1取消
     * @param  orderId 服务单单id
     * @return
     */
    public void updatePaxStatus(String[] paxIds,String status,String orderId);
    /**
     * Title：getPaxOrderInfo <br>
     * Description： 获取旅客赔偿次数详情<br>
     * author：王建文 <br>
     * date：2020-3-23 17:28 <br>
     * @param  paxId 旅客id
     * @param  flightNo 航班号
     * @param  flightDate 航班日期
     * @return
     */
    public List<Map<String,Object>> getPaxOrderInfo(String paxId,String flightNo,String flightDate);
    
    /**
     * Title：getSelectCityInfoByInitial <br>
     * Description：获取航站并按照3字码首字母分组 <br>
     * author：王磊 <br>
     * date：2020年4月17日 下午1:15:40 <br>
     * @return <br>
     */
    public Map<String, List<Map<String, Object>>> getSelectCityInfoByInitial();
    /**
     * Title：sendMessageToPaxByPayType <br>
     * Description： 赔付单确认发放短信通知<br>
     * author：王建文 <br>
     * date：2020-4-22 15:17 <br>
     * @param  orderId 赔付单
     * @param  payType 赔付类型 不正常航班赔偿0、异常行李1、超售旅客2、
     * @return
     */
    public void sendMessageToPaxByPayType(String orderId,String payType);

    List<FocFlightInfo> getFocFlightInfos(String flightNo, String flightDate, String choiceSegment);

    Map<String, Object> getReturnFlightPaxInfo(String flightNo, String flightDate, String choiceSegment, ContentTraceForm form, String selectSegment);

    public Object getEditPermission(String orderId);
    
}