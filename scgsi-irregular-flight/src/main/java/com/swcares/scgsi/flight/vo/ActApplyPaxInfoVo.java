package com.swcares.scgsi.flight.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：代领详情旅客信息展示<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月13日 17:21 <br>
 * @version v1.0 <br>
 */
@Data
@EncryptionClassz
public class ActApplyPaxInfoVo {
    /**
     * 旅客姓名
     */
    private String paxName;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号
     */
    @Encryption
    private String idNo;
    /**
     * 性别C儿童M男F女
     */
    private String sex;
    /**
     * 婴儿名字
     */
    private String babyName;
    /**
     * 图片地址
     */
    private String imgUrl;
    /**
     * 赔偿金额
     */
    private String currentAmount;
    /**
     * 拒绝原因
     */
    private String refuseRemark;
    /**
     * 备注
     */
    private String remark;
    /**
     * 旅客id
     */
    private String paxId;
    /**
     * 儿童标识
     */
    private String isChild;

    /**
     * 携带婴儿标识
     */
    private String isInfant;
    /**
     * 票号
     */
    private String tktNo;
}