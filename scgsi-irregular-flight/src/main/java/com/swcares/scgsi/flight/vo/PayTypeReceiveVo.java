package com.swcares.scgsi.flight.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.security.PrivateKey;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：第一部分- 各业务赔偿金额数据<br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 09月07日 9:51 <br>
 * @version v1.0 <br>
 */
@Data
public class PayTypeReceiveVo {

    private String key;

    private String title;

    private List<TypeAmountVo> dataArray;

    @Data
    public static class TypeAmountVo{

        //0,'不正常航班赔偿',1,'异常行李',2,'超售旅客'
        private String title;

        //已领取金额（元）
        private String received;

        //应发放金额（元）
        private String payable;
        //已领取率
        private String receivingRate;

        //日期类型 ： 当日 、 近7天 、本月、本年
        @JsonIgnore
        private String dateType;
        @JsonIgnore
        private String key;
        @JsonIgnore
        private String payType;

    }

}
