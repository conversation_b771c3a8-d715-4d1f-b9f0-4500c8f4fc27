package com.swcares.scgsi.flight.service.impl;

import com.swcares.scgsi.flight.dao.ApplyPaxDao;
import com.swcares.scgsi.flight.dao.impl.ApplyOrderQuartzDaoImpl;
import com.swcares.scgsi.flight.entity.ApplyOrderInfo;
import com.swcares.scgsi.flight.entity.ApplyPaxInfo;
import com.swcares.scgsi.flight.service.ApplyOrderQuartzService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.service.impl <br>
 * Description：申领单定时任务 - 处理<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 04月08日 20:40 <br>
 * @version v1.0 <br>
 */
@Service
public class ApplyOrderQuartzServiceImpl implements ApplyOrderQuartzService {

    @Resource
    private ApplyOrderQuartzDaoImpl applyOrderQuartzDaoImpl;
    @Resource
    private ApplyPaxDao applyPaxDao;


    @Override
    public List<ApplyOrderInfo> findUnpaidOrderInfo() {
        return applyOrderQuartzDaoImpl.findUnpaidOrderInfo();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updApplyOrderStatus(String applyCode, String payStatus,String payDate, String receiveTime) {
       return applyOrderQuartzDaoImpl.updApplyOrderStatus(applyCode,payStatus,payDate,receiveTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updOrderPaxStatus(String applyCode, String payStatus, String receiveTime,boolean isReceiveTime) {
        int updateCount = 0;
        List<ApplyPaxInfo> data = applyPaxDao.findByApplyCode(applyCode);
        if(data.size()>0){
            for (ApplyPaxInfo paxInfo:data) {
                int temp = applyOrderQuartzDaoImpl.updOrderPaxStatus(paxInfo.getPaxId(),paxInfo.getOrderId(),payStatus,receiveTime,isReceiveTime);
                updateCount+=temp;
            }
        }
        return updateCount;
    }
}
