package com.swcares.scgsi.flight.entity;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.modules.entity <br>
 * Description：转账记录表 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 04月07日 18:47 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "DP_AO_TRANS_RECORD")
@Data
@EncryptionClassz
public class AoTransRecord {

    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    /**
     * 申领单号
     */
    @Column(name = "APPLY_CODE")
    private String applyCode;

    /**
     * 转账金额
     */
    @Column(name = "TRANS_AMOUNT")
    private String transAmount;

    /**
     * 转账账号
     */
    @Encryption
    @Column(name = "APPLY_ACCOUNT")
    private String applyAccount;

    /**
     * 转账手续费
     */
    @Column(name = "FEE_AMOUNT")
    private String feeAmount;

    /**
     * 支付方式 0微信，1银联,2现金
     */
    @Column(name = "PAY_TYPE")
    private String payType;

    /**
     * 支付状态(0未支付,1已支付,2支付失败,3支付处理中)
     */
    @Column(name = "PAY_STATUS")
    private String payStatus;

    /**
     * 转账发起时间
     */
    @Column(name = "PAY_START_TIME")
    private Date payStartTime;

    /**
     * 转账返回时间
     */
    @Column(name = "PAY_RETURN_TIME")
    private Date payReturnTime;


    /**
     * 转账返回的流水号
     */
    @Column(name = "RETURN_ORDER_NO")
    private String returnOrderNo;

    /**
     * 转账返回的CODE
     */
    @Column(name = "TRANS_CODE")
    private String transCode;

    /**
     * 转账返回CODE描述
     */
    @Column(name = "TRANS_MSG")
    private String transMsg;

    /**
     * 转账返回子CODE
     */
    @Column(name = "TRANS_SUB_CODE")
    private String transSubCode;

    /**
     * 转账返回子CODE描述
     */
    @Column(name = "TRANS_SUB_MSG")
    private String transSubMsg;

    /**
     * 错误code
     */
    @Column(name = "ERR_CODE")
    private String errCode;

    /**
     * 错误code描述
     */
    @Column(name = "ERR_CODE_DES")
    private String errCodeDes;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

}

