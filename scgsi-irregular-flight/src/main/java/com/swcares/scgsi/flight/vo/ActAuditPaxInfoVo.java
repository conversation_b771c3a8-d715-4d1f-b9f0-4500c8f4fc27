package com.swcares.scgsi.flight.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：代领旅客显示VO <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月16日 14:05 <br>
 * @version v1.0 <br>
 */
@Data
@EncryptionClassz
public class ActAuditPaxInfoVo {
    /**
     * 申领单号
     */
    private String applyCode;
    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 旅客id
     */
    private String paxId;

    /**
     * 旅客姓名
     */
    private String paxName;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号
     */
    @Encryption
    private String idNo;
    /**
     * 联系电话
     */
    @Encryption
    private String telephone;
    /**
     * 状态审核结果 (0待审核、1已通过、2未通过)
     */
    private String status;
    /**
     * 赔偿金额
     */
    private String payMoney;
    /**
     * 审核信息
     */
    private String auditRemark;
    /**
     * 角色0申领人1代领旅客
     */
    private String actRole;
    /**
     * 支付状态
     */
    private String payStatus;
    /**
     * 票号
     */
    private String tktNo;
    /**
     * 终审人
     */
    private String lastAuditor;
    /**
     * 终审时间
     */
    private String lastAuditTime;

    /**
     * 航段
     */
    private String segment;

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班日期
     */
    private String flightDate;
    /**
     *
     */
    private String freezeRemark;

    /**
     *
     */
    private String failureRemark;
    private String applyStatus;
}