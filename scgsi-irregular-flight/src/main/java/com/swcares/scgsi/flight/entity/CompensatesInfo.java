package com.swcares.scgsi.flight.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.flight.entity <br>
 * Description：赔付标准实体 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月03日 11:19 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "DP_COMPENSATE_INFO")
@Data
@NoArgsConstructor
public class CompensatesInfo {

    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    /**
     * 赔偿金额
     */
    @Column(name = "CPS_NUM")
    private int cpsNum;

    /**
     * 儿童赔偿标准
     */
    @Column(name = "CHILD_STD")
    private int childStd;

    /**
     * 婴儿赔偿标准
     */
    @Column(name = "BABY_STD")
    private int babyStd;

    /**
     * 舱位类型(1-经济舱，2-公务舱)
     */
    @Column(name = "CLASS_TYPE")
    private String classType;

    /**
     * 赔付单ID
     */
    @Column(name = "ORDER_ID")
    private String orderId;

    /**
     * 事故单号当前仅针对异常行李、超售旅客
     */
    @Column(name = "ACCIDENT_ID")
    private int accidentId;

    /**
     * 创建人
     */
    @Column(name = "CREATE_ID")
    private String createId;

    /**
     * 创建日期
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    // 构造函数
    public CompensatesInfo(String classType, int cpsNum, int childStd, int babyStd) {
        this.classType = classType;
        this.cpsNum = cpsNum;
        this.childStd = childStd;
        this.babyStd = babyStd;
    }
}