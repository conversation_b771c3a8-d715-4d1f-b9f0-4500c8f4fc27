package com.swcares.scgsi.flight.dao;

import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.flight.entity.OrdersInfo;
import org.springframework.data.repository.query.Param;

/**
 * ClassName：com.swcares.scgsi.flight.dao <br>
 * Description：赔付单单信息dao <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月03日 13:16 <br>
 * @version v1.0 <br>
 */
public interface OrdersInfoDao extends BaseJpaDao<OrdersInfo,String> {

    OrdersInfo findByOrderId(@Param("orderId") String orderId);
}
