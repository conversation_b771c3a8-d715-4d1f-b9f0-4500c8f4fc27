package com.swcares.scgsi.flight.service;

import com.swcares.scgsi.flight.vo.PayTypeReceiveVo;
import com.swcares.scgsi.flight.vo.ReceiveWayAnalysisVo;
import com.swcares.scgsi.flight.vo.ServiceCityAccidentVo;
import com.swcares.scgsi.flight.vo.ServiceCityAnalysisVo;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.service <br>
 * Description：首页数据分析统计<br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 09月07日 9:37 <br>
 * @version v1.0 <br>
 */
public interface DataAnalysisService {




    /**
     * Title：getPayTypeReceiveData <br>
     * Description： 当天、近7日、本月、本年 各业务类型下领取金额统计<br>
     * author：傅欣荣 <br>
     * date：2021/9/7 18:23 <br>
     * @param
     * @return
     */
    List<PayTypeReceiveVo> getPayTypeReceiveData();


    /**
     * Title：getReceiveHannelData <br>
     * Description： 本年01月-当月 领取渠道金额统计<br>
     * author：傅欣荣 <br>
     * date：2021/9/7 18:23 <br>
     * @param
     * @return
     */
    List<ReceiveWayAnalysisVo> getReceiveChannelData();




    /**
     * Title： getServiceCityClaimRank<br>
     * Description： 航站已领取金额排名前五<br>
     * author：傅欣荣 <br>
     * date：2021/9/8 9:38 <br>
     * @param
     * @return
     */
    List<ServiceCityAnalysisVo> getServiceCityAmountRank();

    /**
     * Title：getServiceCityPeopleNumRank <br>
     * Description： 服务航站已领取人数排名，前五<br>
     * author：傅欣荣 <br>
     * date：2021/9/8 9:39 <br>
     * @param
     * @return
     */
    List<ServiceCityAnalysisVo> getServiceCityPeopleNumRank();


    /**
     * Title： getServiceCityAccidentNumRank<br>
     * Description： 航站各事故单类型，数量统计.排名前10<br>
     * author：傅欣荣 <br>
     * date：2021/9/8 10:27 <br>
     * @param
     * @return
     */
    List<ServiceCityAccidentVo> getServiceCityAccidentNumRank();

    /**
     * Title： getServiceCityTrunkNumRank<br>
     * Description： 各航站-行李箱数量统计。排名前10<br>
     * author：傅欣荣 <br>
     * date：2021/9/8 10:27 <br>
     * @param
     * @return
     */
    List<ServiceCityAnalysisVo> getServiceCityTrunkNumRank();


    /**
     * Title：getAnalysisStatisticsData <br>
     * Description： 数据统计接口<br>
     * author：傅欣荣 <br>
     * date：2021/9/8 16:34 <br>
     * @param
     * @return
     */
    Map<String,Object> getAnalysisStatisticsData();
}
