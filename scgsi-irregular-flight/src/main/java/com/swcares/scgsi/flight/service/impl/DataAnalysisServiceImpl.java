package com.swcares.scgsi.flight.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.swcares.scgsi.flight.dao.impl.DataAnalysisDaoImpl;
import com.swcares.scgsi.flight.enums.DateTypeEnum;
import com.swcares.scgsi.flight.enums.PayTypeEnum;
import com.swcares.scgsi.flight.enums.ReceiveWayEnum;
import com.swcares.scgsi.flight.service.DataAnalysisService;
import com.swcares.scgsi.flight.vo.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateUtil.parse;
import static org.apache.commons.lang3.time.DateFormatUtils.format;

/**
 * ClassName：com.swcares.scgsi.flight.service.impl <br>
 * Description：首页数据分析统计图表 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 09月07日 9:41 <br>
 * @version v1.0 <br>
 */
@Service
public class DataAnalysisServiceImpl implements DataAnalysisService {

    @Resource
    DataAnalysisDaoImpl dataAnalysisDao;

    @Override
    public List<PayTypeReceiveVo> getPayTypeReceiveData() {
        List<PayTypeReceiveVo> data = Lists.newArrayList();

        List<PayTypeReceiveVo.TypeAmountVo> receiveVoList =  dataAnalysisDao.getPayTypeReceiveByDate();

        Map<String,List<PayTypeReceiveVo.TypeAmountVo>> dataMap = receiveVoList.stream().collect(Collectors.groupingBy(f->f.getKey()+"_"+f.getDateType()));

        //判断本日、近7日、本月、本年 类型是否完整、不存在的补全数据
        if(dataMap.size()<4){
            if(!dataMap.containsKey(DateTypeEnum.SYSDATE.getKey()+"_"+DateTypeEnum.SYSDATE.getValue())){
                dataMap.put(DateTypeEnum.SYSDATE.getKey()+"_"+DateTypeEnum.SYSDATE.getValue(),Lists.newArrayList() );
            }
            if(!dataMap.containsKey(DateTypeEnum.LAST_7_DAYS.getKey()+"_"+DateTypeEnum.LAST_7_DAYS.getValue())){
                dataMap.put(DateTypeEnum.LAST_7_DAYS.getKey()+"_"+DateTypeEnum.LAST_7_DAYS.getValue(),Lists.newArrayList());
            }
            if(!dataMap.containsKey(DateTypeEnum.MONTHS.getKey()+"_"+DateTypeEnum.MONTHS.getValue())){
                dataMap.put(DateTypeEnum.MONTHS.getKey()+"_"+DateTypeEnum.MONTHS.getValue(),Lists.newArrayList());
            }
            if(!dataMap.containsKey(DateTypeEnum.YEAR.getKey()+"_"+DateTypeEnum.YEAR.getValue())){
                dataMap.put(DateTypeEnum.YEAR.getKey()+"_"+DateTypeEnum.YEAR.getValue(),Lists.newArrayList());
            }
        }

        dataMap.forEach((key, value) -> {
            List<String> keys = Arrays.asList(key.split("_"));
            PayTypeReceiveVo typeReceiveVo = new PayTypeReceiveVo();
            typeReceiveVo.setKey(keys.get(0));
            typeReceiveVo.setTitle(keys.get(1));

            if(value.size()<3){
                //根据paytype（3种类型）分组
                Map<String,List<PayTypeReceiveVo.TypeAmountVo>> typeMap = value.stream().collect(Collectors.groupingBy(PayTypeReceiveVo.TypeAmountVo::getPayType));
                if(!typeMap.containsKey(PayTypeEnum.FLIGHT.getKey())){
                    value.add(editPayTypeReceiveVo(PayTypeEnum.FLIGHT));
                }
                if(!typeMap.containsKey(PayTypeEnum.OVER_BOOKING.getKey())){
                    value.add(editPayTypeReceiveVo(PayTypeEnum.OVER_BOOKING));
                }
                if(!typeMap.containsKey(PayTypeEnum.PKG.getKey())){
                    value.add(editPayTypeReceiveVo(PayTypeEnum.PKG));
                }
            }
            value.sort(Comparator.comparing(PayTypeReceiveVo.TypeAmountVo::getPayType));
            typeReceiveVo.setDataArray(value);
            data.add(typeReceiveVo);
        }); data.sort(Comparator.comparing(PayTypeReceiveVo::getKey));
        return data;
    }

    @Override
    public List<ReceiveWayAnalysisVo> getReceiveChannelData() {
        List<ReceiveWayAnalysisVo> dataList = Lists.newArrayList();

        //默认加载当年01月至当月的月份
        Map<String, String> monthMap = this.getMonthBetweenDates(this.getCurrYearFirst(), DateUtil.yesterday().toDateStr());

        //各领取渠道每个月已领取金额统计。
        List<ReceiveWayAmountVo> receiveWayAmountVoList = dataAnalysisDao.getReceiveChannelByDate();

        //根据领取渠道分组
        Map<String,List<ReceiveWayAmountVo>> dataMap = receiveWayAmountVoList.stream().collect(Collectors.groupingBy(ReceiveWayAmountVo::getReceiveWay));

        //领取渠道
        if(dataMap.size()<3){
            if(!dataMap.containsKey(ReceiveWayEnum.WEIXIN.getValue())){
                dataMap.put(ReceiveWayEnum.WEIXIN.getValue(),Lists.newArrayList() );
            }
            if(!dataMap.containsKey(ReceiveWayEnum.UNIONPAY.getValue())){
                dataMap.put(ReceiveWayEnum.UNIONPAY.getValue(),Lists.newArrayList() );
            }
            if(!dataMap.containsKey(ReceiveWayEnum.CASH.getValue())){
                dataMap.put(ReceiveWayEnum.CASH.getValue(),Lists.newArrayList() );
            }
        }

        dataMap.forEach((key, value) ->{
            ReceiveWayAnalysisVo analysisVo = new ReceiveWayAnalysisVo();
            analysisVo.setName(key);
            analysisVo.setMonthMap(monthMap);
            //判断月份数量是否正确，不正确进行处理。
            if(analysisVo.getMonthMap().size() != value.size()){
                value.stream().forEach(data -> {
                    if(analysisVo.getMonthMap().containsKey(data.getYearMonths())){//匹配更新覆盖值
                        analysisVo.getMonthMap().put(data.getYearMonths(),data.getSumMoney());
                    }
                });
             //将月份对应value转换为list值
             analysisVo.setData(monthMap.values().stream().collect(Collectors.toList()));
            }else {
                value.sort(Comparator.comparing(ReceiveWayAmountVo::getYearMonths));//排序避免数据错乱
                analysisVo.setData(value.stream().map(ReceiveWayAmountVo::getSumMoney).collect(Collectors.toList()));
            }
            dataList.add(analysisVo);
        });



        return dataList;
    }


    @Override
    public List<ServiceCityAnalysisVo> getServiceCityAmountRank() {

        return dataAnalysisDao.getServiceCityClaimAmount();
    }

    @Override
    public List<ServiceCityAnalysisVo> getServiceCityPeopleNumRank() {
        return dataAnalysisDao.getServiceCityPeopleNum();
    }

    @Override
    public List<ServiceCityAccidentVo> getServiceCityAccidentNumRank() {
        return dataAnalysisDao.getServiceCityAccidentNum();
    }

    @Override
    public List<ServiceCityAnalysisVo> getServiceCityTrunkNumRank() {
        return dataAnalysisDao.getServiceCityTrunkNum();
    }

    @Override
    public Map<String, Object> getAnalysisStatisticsData() {
        Map<String,Object> data = new LinkedHashMap<>();
        data.put("monthClaim",this.getReceiveChannelData());//1-12月支付方式金额统计
        data.put("sc_received",this.getServiceCityAmountRank());//航站-已领取金额排名前五
        data.put("sc_peopleNum",this.getServiceCityPeopleNumRank());//航站-已领取人数排名前五
        data.put("sc_accident",this.getServiceCityAccidentNumRank());//航站-各业务类型，数量统计.排名前10
        data.put("sc_runk",this.getServiceCityTrunkNumRank());//航站-行李箱数量统计。排名前10
        return data;
    }


    private PayTypeReceiveVo.TypeAmountVo editPayTypeReceiveVo(PayTypeEnum typeEnum){
        PayTypeReceiveVo.TypeAmountVo typeAmountVo = new PayTypeReceiveVo.TypeAmountVo();
        typeAmountVo.setTitle(PayTypeEnum.FLIGHT.getKey().equals(typeEnum.getKey())?typeEnum.getValue().substring(0,5):typeEnum.getValue());
        typeAmountVo.setPayable("0");
        typeAmountVo.setReceived("0");
        typeAmountVo.setPayType(typeEnum.getKey());
        typeAmountVo.setReceivingRate("-%");
        return typeAmountVo;
    }


    /**
     * 获取某个时间段内所有月份
     * @param minDate
     * @param maxDate
     * @return
     * @throws ParseException
     */
    public static Map<String,String> getMonthBetweenDates(String minDate, String maxDate){
        Map<String,String> result = new LinkedHashMap<>();
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();
        min.setTime(parse(minDate,"yyyy-MM"));
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);
        max.setTime(parse(maxDate,"yyyy-MM"));
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);

        Calendar curr = min;
        while (curr.before(max)) {
            result.put(format(curr.getTime(),"yyyy-MM"),"0");
            //result.add(format(curr.getTime(),"yyyy-MM"));
            curr.add(Calendar.MONTH, 1);
        }
        return result;
    }

    /**
     * 获取当前年的第一天
     * @return
     */
    public static String getCurrYearFirst(){
        Calendar currCal=Calendar.getInstance();
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR,currCal.get(Calendar.YEAR));
        Date time = calendar.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(time);
    }

}
