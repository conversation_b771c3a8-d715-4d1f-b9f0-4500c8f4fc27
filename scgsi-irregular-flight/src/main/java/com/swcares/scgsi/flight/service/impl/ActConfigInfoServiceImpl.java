package com.swcares.scgsi.flight.service.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.flight.dao.ActConfigInfoDao;
import com.swcares.scgsi.flight.dao.impl.ActConfigInfoDaoImpl;
import com.swcares.scgsi.flight.entity.ActConfigInfo;
import com.swcares.scgsi.flight.service.ActConfigInfoService;
import com.swcares.scgsi.flight.vo.ActConfigInfoVo;
import com.swcares.scgsi.util.AuthenticationUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.service.impl <br>
 * Description：代领审核配置serviceImpl <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月15日 10:37 <br>
 * @version v1.0 <br>
 */
@Service
public class ActConfigInfoServiceImpl implements ActConfigInfoService {
    @Resource
    private ActConfigInfoDao actConfigInfoDao;

    @Resource
    private BaseDAO baseDAO;

    @Resource
    private ActConfigInfoDaoImpl actConfigInfoDaoImpl;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateActConfigInfo(String type, String content, String id) {
        if (StringUtils.isNotBlank(id)) {
            updateConfigInfo(type, content, id);
        } else {
            if ("0".equals(type)) {
                updateConfigInfo(type, content, id);
            } else {
                //新增
                Authentication authentication = AuthenticationUtil.getAuthentication();
                String createUser = (String) authentication.getPrincipal();
                ActConfigInfo actConfigInfo = new ActConfigInfo();
                actConfigInfo.setContent(content);
                actConfigInfo.setType(type);
                actConfigInfo.setCreateTime(new Date());
                actConfigInfo.setCreateUser(createUser);
                actConfigInfo.setStatus("0");
                actConfigInfoDao.save(actConfigInfo);
            }
        }
    }

    private void updateConfigInfo(String type, String content, String id) {
        //更新已存在的缓冲时间
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_CONFIG_INFO SET ");
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String updateUser = (String) authentication.getPrincipal();
        Date updateTime = new Date();
        if ("0".equals(type)) {
            sql.append(" STATUS='1', ");
        } else {
            sql.append(" CONTENT=?, ");
        }
        sql.append(" UPDATE_USER=?, ");
        sql.append(" UPDATE_TIME=? ");
        if (("1".equals(type) || "2".equals(type)) && StringUtils.isNotBlank(id)) {
            sql.append(" where STATUS=0 AND TYPE=? AND ID=? ");
            baseDAO.batchUpdate(sql.toString(), content, updateUser, updateTime, type, id);
        } else {
            sql.append(" where STATUS=0 AND TYPE=? ");
            baseDAO.batchUpdate(sql.toString(), updateUser, updateTime, type);
            ActConfigInfo actConfigInfo = new ActConfigInfo();
            actConfigInfo.setContent(content);
            actConfigInfo.setType(type);
            actConfigInfo.setCreateTime(updateTime);
            actConfigInfo.setCreateUser(updateUser);
            actConfigInfo.setStatus("0");
            actConfigInfoDao.save(actConfigInfo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRefuseInfo(String[] ids) {
        actConfigInfoDaoImpl.deleteRefuseInfo(ids);
    }

    @Override
    public String getDefaultBufferTime() {
        return actConfigInfoDaoImpl.getDefaultBufferTime();
    }

    @Override
    public List<ActConfigInfoVo> getRefuseInfo(String type) {
        return actConfigInfoDaoImpl.getRefuseInfo(type);
    }
}