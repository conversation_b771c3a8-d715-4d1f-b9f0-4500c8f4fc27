package com.swcares.scgsi.flight.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.flight.dto.ReportQueryParamDto;
import com.swcares.scgsi.flight.vo.ReportInfoVo;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

/**
 * ClassName：com.swcares.scgsi.flight.dao.impl <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月30日 15:54 <br>
 * @version v1.0 <br>
 */
@Repository
public class ReportDaoImpl {
    @Resource
    private BaseDAO baseDAO;
    @Resource
    private UserUtil userUtil;
    /**
     * Title：getReportDataPage <br>
     * Description：航延报表分页查询<br>
     * author：王建文 <br>
     * date：2020-3-30 15:50 <br>
     *
     * @param reportQueryParamDto 查询参数接收
     * @return
     */
    public QueryResults getReportDataPage(ReportQueryParamDto reportQueryParamDto) {
        Map<String,Object> map=getSql(reportQueryParamDto);
        return baseDAO.findBySQLPage_comm(
                map.get("sql").toString(),
                reportQueryParamDto.getCurrent(),
                reportQueryParamDto.getPageSize(),
                (Map<String,Object>)map.get("paramsMap"),
                ReportInfoVo.class);
    }
    private Map<String,Object> getSql(ReportQueryParamDto reportQueryParamDto){
        Map<String, Object> data = new HashMap<String, Object>();
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        //采用您建议的更简洁方案，直接在内层查询中处理
        sql.append(" SELECT P.* FROM ( ");
        sql.append(" SELECT DISTINCT(DPI.ID) AS id,O.FLIGHT_NO AS flightNo, ");
        sql.append(" O.FLIGHT_DATE AS flightDate,DPI.ORG_CITY_AIRP AS orgCity, ");
        sql.append(" DPI.DST_CITY_AIRP AS dstCity,O.SERVICE_CITY AS serviceCity, ");
        sql.append(" F.SEGMENT AS segment, ");
        sql.append(" F.AC_TYPE AS acType,DECODE(O.PAY_TYPE,'0','不正常航班','1','异常行李','2','旅客超售') AS payType, ");
        sql.append(" F.LATE_REASON AS lateReson,F.DELAY_TIME AS delayTime,O.ORDER_ID AS orderId,DPI.PAX_ID AS paxId, ");
        sql.append(" DECODE(O.STATUS,'2','通过','3','生效','4','关闭') AS orderStatus, ");
        sql.append(" DPI.PAX_NAME AS paxName,DPI.TKT_NO AS tktNo,DPI.CURRENT_AMOUNT AS totalPay, ");
        sql.append(" (DPI.CURRENT_AMOUNT-NVL(OI.PRICE_SPREAD,'0')) AS payMoney,OI.PRICE_SPREAD AS tktPriceDiff, ");
        sql.append(" DECODE(DPI.RECEIVE_STATUS,'0','待领取','1','已领取','2','处理中','3','已逾期') AS receiveStatus, ");
        sql.append(" DECODE (DPI.RECEIVE_CHANNEL,'0','','1',DECODE (DAO.APPLY_STATUS,'0','待审核','1','通过','2','未通过','3','关闭','4','冻结'),'2','') AS actStatus, ");
        sql.append(" DECODE(DPI.RECEIVE_CHANNEL,'0','普通','1','代领','2','现金') AS receiveChannel, ");
        sql.append(" DECODE(DPI.RECEIVE_WAY,'0','微信','1','银联','2','现金') AS receiveWay, ");
        sql.append(" DECODE (DPI.RECEIVE_WAY,'0','','1',DAO.APPLY_USER,'2','') AS accountName, ");
        sql.append(" DECODE (DPI.RECEIVE_WAY,'0','','1',DAO.OPEN_BANK_NAME,'2','') AS openBankName, ");
        sql.append(" DECODE (DPI.RECEIVE_WAY,'2','',DAO.GET_MONEY_ACCOUNT) AS getAccount, ");
        sql.append(" DPI.ID_NO AS idNo,DAO.TELEPHONE AS telephone, ");
        sql.append(" O.REMARK AS remark, ");
        sql.append(" TO_CHAR(DAO.PAY_DATE,'YYYY-MM-DD hh24:mi:ss') AS payDate, ");
        sql.append(" TO_CHAR(add_months(TO_DATE(O.FLIGHT_DATE, 'YYYY-MM-DD'),12*1),'YYYY-MM-DD hh24:mi:ss') AS expireTime, ");
        sql.append(" E.TU_CNAME AS applyUser,DPI.IS_FLAG AS isFlag, ");
        sql.append(" DPI.RECEIVE_STATUS AS receiveOrder, DAO.PAY_STATUS, ");
        sql.append(" ROW_NUMBER() OVER(partition by DPI.PAX_ID,DPI.ORDER_ID ORDER by DAO.CREATE_TIME DESC) AS exitCount ");
        sql.append(" FROM DP_PAX_INFO DPI ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ");
        sql.append(" ON O.ORDER_ID=DPI.ORDER_ID ");
        sql.append(" LEFT JOIN DP_FLIGHT_INFO F ON F.ORDER_ID=O.ORDER_ID ");
        sql.append(" LEFT JOIN DP_OVER_INFO OI ON OI.ORDER_ID=O.ORDER_ID ");
        sql.append(" LEFT JOIN DP_APPLY_PAX DAP ON DAP.PAX_ID=DPI.PAX_ID ");
        sql.append(" AND DPI.ORDER_ID in ");
        sql.append(" (SELECT REGEXP_SUBSTR(DAP.ORDER_ID,'[^,]+', 1, LEVEL) FROM DUAL ");
        sql.append(" connect by regexp_substr(DAP.ORDER_ID, '[^,]+', 1, level) is not null) ");
        sql.append(" LEFT JOIN DP_APPLY_ORDER DAO ON DAO.APPLY_CODE=DAP.APPLY_CODE ");
        sql.append(" LEFT JOIN EMPLOYEE E ON E.TUNO=O.CREATE_ID ");
        sql.append(" WHERE 1=1 AND ");
        sql.append(" (O.STATUS='3' OR O.STATUS='2' OR DPI.RECEIVE_STATUS='1')  ");
        String flightNo=reportQueryParamDto.getFlightNo();
        if(StringUtils.isNotBlank(flightNo)){
            paramsMap.put("flightNo",flightNo);
            sql.append(" AND O.FLIGHT_NO=:flightNo ");
        }
        String payType=reportQueryParamDto.getPayType();
        if(StringUtils.isNotBlank(payType)){
            paramsMap.put("payType",payType);
            sql.append(" AND O.PAY_TYPE=:payType ");
        }
        String idNo=reportQueryParamDto.getIdNo();
        if(StringUtils.isNotBlank(idNo)){
            idNo= AesEncryptUtil.aesEncryptScgsi(idNo);
            paramsMap.put("idNo",idNo);
            sql.append(" AND DPI.ID_NO=:idNo ");
        }
        String telephone=reportQueryParamDto.getTelephone();
        if(StringUtils.isNotBlank(telephone)){
            telephone= AesEncryptUtil.aesEncryptScgsi(telephone);
            paramsMap.put("telephone",telephone);
            sql.append(" AND DAO.TELEPHONE=:telephone ");
        }
        String flightStartDate = reportQueryParamDto.getFlightStartDate();
        String flightEndDate = reportQueryParamDto.getFlightEndDate();
        if (StringUtils.isNotBlank(flightStartDate) && StringUtils.isNotBlank(flightEndDate)) {
            paramsMap.put("flightEndDate", flightEndDate);
            paramsMap.put("flightStartDate", flightStartDate);
            sql.append(" AND O.FLIGHT_DATE  BETWEEN :flightStartDate and :flightEndDate ");
        }
        if (StringUtils.isNotBlank(flightEndDate) && StringUtils.isBlank(flightStartDate)) {
            paramsMap.put("flightEndDate", flightEndDate);
            sql.append(" AND O.FLIGHT_DATE <= :flightEndDate ");
        }
        if (StringUtils.isNotBlank(flightStartDate) && StringUtils.isBlank(flightEndDate)) {
            flightEndDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
            paramsMap.put("flightEndDate", flightEndDate);
            paramsMap.put("flightStartDate", flightStartDate);
            sql.append(" AND O.FLIGHT_DATE  BETWEEN :flightStartDate and :flightEndDate ");
        }
        if (StringUtils.isBlank(flightStartDate) && StringUtils.isBlank(flightEndDate)) {
            String currentDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.DAY_OF_MONTH, -6);
            paramsMap.put("flightEndDate",currentDate );
            paramsMap.put("flightStartDate", DateUtils.parseDateToStr(calendar.getTime(),DateUtils.YYYY_MM_DD));
            sql.append(" AND O.FLIGHT_DATE  BETWEEN :flightStartDate and :flightEndDate ");
        }
        String payStartDate = reportQueryParamDto.getPayStartDate();
        String payEndDate = reportQueryParamDto.getPayEndDate();
        if (StringUtils.isNotBlank(payStartDate) && StringUtils.isNotBlank(payEndDate)) {
            paramsMap.put("payStartDate", payStartDate);
            paramsMap.put("payEndDate", payEndDate);
            sql.append(" AND TO_CHAR(DAO.PAY_DATE,'YYYY-MM-DD')  BETWEEN :payStartDate and :payEndDate ");
        }

        String orgCity = reportQueryParamDto.getOrgCity();
        if (StringUtils.isNotBlank(orgCity)) {
            paramsMap.put("orgCity", orgCity);
            sql.append(" AND DPI.ORG_CITY_AIRP= :orgCity ");
        }
        String dstCity =reportQueryParamDto.getDstCity();
        if (StringUtils.isNotBlank(dstCity)) {
            paramsMap.put("dstCity", dstCity);
            sql.append(" AND DPI.DST_CITY_AIRP= :dstCity ");
        }

        String keySearch=reportQueryParamDto.getKeySearch();
        if(StringUtils.isNotBlank(keySearch)){
            paramsMap.put("keySearch", "%" + keySearch + "%");
            sql.append(" AND (DPI.PAX_NAME LIKE:keySearch OR DPI.TKT_NO LIKE:keySearch OR O.ORDER_ID LIKE:keySearch) ");
        }
        String receiveStatus=reportQueryParamDto.getReceiveStatus();
        if (StringUtils.isNotBlank(receiveStatus)) {
            paramsMap.put("receiveStatus", receiveStatus);
            sql.append(" AND DPI.RECEIVE_STATUS= :receiveStatus ");
        }
        String receiveChannel=reportQueryParamDto.getReceiveChannel();
        if (StringUtils.isNotBlank(receiveChannel)) {
            paramsMap.put("receiveChannel", receiveChannel);
            sql.append(" AND DPI.RECEIVE_CHANNEL= :receiveChannel ");
        }
        String receiveWay=reportQueryParamDto.getReceiveWay();
        if (StringUtils.isNotBlank(receiveWay)) {
            paramsMap.put("receiveWay", receiveWay);
            sql.append(" AND DPI.RECEIVE_WAY= :receiveWay ");
        }
        String serviceCity=reportQueryParamDto.getServiceCity();
        if (StringUtils.isNotBlank(serviceCity)) {
            sql.append(" AND O.SERVICE_CITY = get_city_name('"+serviceCity+"') ");
        }
        String orderStatus=reportQueryParamDto.getOrderStatus();
        if (StringUtils.isNotBlank(orderStatus)) {
            paramsMap.put("orderStatus", orderStatus);
            sql.append(" AND O.STATUS= :orderStatus ");
        }
        String isFlag=reportQueryParamDto.getIsFlag();
        if (StringUtils.isNotBlank(isFlag)) {
            paramsMap.put("isFlag", isFlag);
            sql.append(" AND DPI.IS_FLAG= :isFlag ");
        }
        String acType=reportQueryParamDto.getAcType();
        if (StringUtils.isNotBlank(acType)) {
            paramsMap.put("acType", acType);
            sql.append(" AND F.AC_TYPE= :acType ");
        }
        String actStatus=reportQueryParamDto.getActStatus();
        if(StringUtils.isNotBlank(actStatus)){
            paramsMap.put("actStatus", actStatus);
            sql.append(" AND DAO.STATUS= :actStatus ");
        }
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String myOwn=(String) authentication.getPrincipal();
        List<String> payTypeLIst=userUtil.findBussiTypeByEmpId(myOwn);
        if(payTypeLIst.size()>0){
            String queryPayType="";
            for(String payType1:payTypeLIst){
                queryPayType+=payType1+",";
            }
            sql.append(" AND O.PAY_TYPE IN ");
            sql.append("  (SELECT REGEXP_SUBSTR('"+queryPayType+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+queryPayType+"', '[^,]+', 1, level) is not null) ");
        }
        sql.append(" order by flightDate DESC, receiveOrder DESC,payDate ASC ");
        //子查询包
        sql.append(" ) P WHERE P.exitCount=1 ");
        //支付状态 - 现在使用finalPayStatus字段
        String payStatus=reportQueryParamDto.getPayStatus();
        if (StringUtils.isNotBlank(payStatus)) {
            paramsMap.put("payStatus", payStatus);
            sql.append(" AND P.finalPayStatus = :payStatus ");
        }

        data.put("sql",sql);
        data.put("paramsMap",paramsMap);
        return data;
    }
    /**
     * Title：getReportDataPage <br>
     * Description：航延报表分页查询<br>
     * author：王建文 <br>
     * date：2020-3-30 15:50 <br>
     *
     * @param reportQueryParamDto 查询参数接收
     * @return
     */
    public List<ReportInfoVo> getExportData(ReportQueryParamDto reportQueryParamDto) {
        Map<String,Object> map=getSql(reportQueryParamDto);
      return (List<ReportInfoVo>)baseDAO.findBySQL_comm(map.get("sql").toString(), (Map<String,Object>)map.get("paramsMap"),ReportInfoVo.class);
    }
    /**
     * Title：updateFlag <br>
     * Description： 标记<br>
     * author：王建文 <br>
     * date：2020-3-31 10:19 <br>
     * @param  ids 主键id
     * @param  status 状态
     * @return
     */
    public void updateFlag(String[] ids,String status){
        for(String id:ids){
            StringBuffer sql = new StringBuffer();
            sql.append(" UPDATE DP_PAX_INFO SET ");
            sql.append(" IS_FLAG=? ");
            sql.append(" where ID=?");
            baseDAO.batchUpdate(sql.toString(),status,id);
        }
    }
    /**
     * 此方法已废弃，相关数据现在直接在主查询中获取
     * @deprecated 使用优化后的主查询替代
     */
    @Deprecated
    public Map<String,Object> handSpecialReportFiled(String orgCity,String dstCity,String paxId,String orderId){
        // 返回空Map，因为数据现在直接在主查询中获取
        return new HashMap<>();
    }
}