package com.swcares.scgsi.flight.service;

import com.swcares.scgsi.flight.vo.ActMessageVo;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.service <br>
 * Description：代领审核消息 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 04月13日 19:53 <br>
 * @version v1.0 <br>
 */
public interface ActMessageService {
    /**
     * Title：getActMessageInfo <br>
     * Description：获取需要发送的代领审核信息<br>
     * author：王建文 <br>
     * date：2020-4-13 19:56 <br>
     * @param
     * @return
     */
    List<ActMessageVo> getActMessageInfo();
    /**
     * Title：updateActSendStatus <br>
     * Description：标记已发送消息<br>
     * author：王建文 <br>
     * date：2020-4-9 11:14 <br>
     * @param  applyCode 事故单号
     * @return
     */
    public void updateActSendStatus(String applyCode);
}
