package com.swcares.scgsi.flight.dao.impl;

import cn.hutool.core.util.StrUtil;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.flight.vo.PayTypeReceiveVo;
import com.swcares.scgsi.flight.vo.ReceiveWayAmountVo;
import com.swcares.scgsi.flight.vo.ServiceCityAccidentVo;
import com.swcares.scgsi.flight.vo.ServiceCityAnalysisVo;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.overbook.dao.impl <br>
 * Description：首页数据分析 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 09月07日 10:17 <br>
 * @version v1.0 <br>
 */
@Repository
public class DataAnalysisDaoImpl {

    @Resource
    BaseDAO baseDAO;

    private String PAYTYPE_RECEIVE_SQL=
            "   SELECT OI.PAY_TYPE payType,DECODE(OI.PAY_TYPE,0,'不正常航班',1,'异常行李',2,'旅客超售','其他') title , " +
            "   nvl(sum(decode(oi.STATUS,3,oi.SUM_MONEY,0)),0)  payable,nvl(SUM(PI.CURRENT_AMOUNTS),0) received ," +
            "   decode(nvl(sum(decode(oi.STATUS,3,oi.SUM_MONEY,0)),0),0,'-'," +
            "   ROUND(nvl(SUM(PI.CURRENT_AMOUNTS),0)/nvl(sum(decode(oi.STATUS,3,oi.SUM_MONEY,0)),0),2)*100) || '%' receivingRate" +
             ",{} dateType,{} key" +

            "   FROM DP_ORDER_INFO  OI " +
            "   LEFT JOIN (SELECT ORDER_ID,SUM(CURRENT_AMOUNT) CURRENT_AMOUNTS FROM DP_PAX_INFO WHERE RECEIVE_STATUS = 1 GROUP BY ORDER_ID)\n" +
            "   PI ON OI.ORDER_ID = PI.ORDER_ID " +
            "   WHERE 1=1 " +
            "   {} " +
            "   GROUP BY OI.PAY_TYPE";

    //当天
    private String SYSDATE_SQL = " AND to_char(oi.create_time,'YYYY-mm-dd') =to_char(SYSDATE,'yyyy-mm-dd') ";
    //近7天 ，n 至 n-7
    private String LAST_7_DAYS_SQL="and to_char(oi.create_time,'YYYY-mm-dd') BETWEEN to_char(TRUNC(SYSDATE-6),'yyyy-mm-dd')\n" +
            "and to_char(SYSDATE,'yyyy-mm-dd')";
    //本月
    private String MONTHS_SQL=" and to_char(oi.create_time,'YYYY-mm') = to_char(SYSDATE,'YYYY-mm') ";
    //本年
    private String YEAR_SQL=" and to_char(oi.create_time,'YYYY') = to_char(SYSDATE,'YYYY') ";

    /**
     * Title： getPayTypeReceiveByDate <br>
     * Description： 各日期类型筛选下-各业务类型的已领取金额/应发放金额<br>
     * author：傅欣荣 <br>
     * date：2021/9/7 10:21 <br>
     * @param
     * @return
     */
    public List<PayTypeReceiveVo.TypeAmountVo> getPayTypeReceiveByDate(){
        StringBuffer sql = new StringBuffer();
        sql.append(StrUtil.format(PAYTYPE_RECEIVE_SQL,"'本日'","1",SYSDATE_SQL)).append(" UNION ALL ");
        sql.append(StrUtil.format(PAYTYPE_RECEIVE_SQL,"'近7日'","2",LAST_7_DAYS_SQL)).append(" UNION ALL ");
        sql.append(StrUtil.format(PAYTYPE_RECEIVE_SQL,"'本月'","3",MONTHS_SQL)).append(" UNION ALL ");
        sql.append(StrUtil.format(PAYTYPE_RECEIVE_SQL,"'本年'","4",YEAR_SQL));
        return (List<PayTypeReceiveVo.TypeAmountVo>)baseDAO.findBySQL_comm(sql.toString(),null,PayTypeReceiveVo.TypeAmountVo.class);
    }

    /**
     * Title： getReceiveHannelByDate <br>
     * Description： 本年度-每月、各支付方式的已领取金额<br>
     * author：傅欣荣 <br>
     * date：2021/9/7 10:21 <br>
     * @param
     * @return
     */
    public List<ReceiveWayAmountVo> getReceiveChannelByDate(){
        //统计本年所有月、已领取状态下各支付方式的金额
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT decode(RECEIVE_WAY,0,'微信',1,'银联',2,'现金') receiveWay, nvl(SUM(CURRENT_AMOUNT),0) sumMoney,TO_CHAR(RECEIVE_TIME,'YYYY-mm')AS yearMonths ");
        sql.append(" FROM DP_PAX_INFO");
        sql.append(" WHERE TO_CHAR(RECEIVE_TIME,'YYYY') = TO_CHAR(SYSDATE,'YYYY') AND RECEIVE_STATUS = 1");
        sql.append(" GROUP BY RECEIVE_WAY,TO_CHAR(RECEIVE_TIME,'YYYY-mm') ORDER BY RECEIVE_WAY,to_char(RECEIVE_TIME,'YYYY-mm')");

        return (List<ReceiveWayAmountVo>)baseDAO.findBySQL_comm(sql.toString(),null,ReceiveWayAmountVo.class);
    }

    /**
     * Title：getServiceCityClaimAmount <br>
     * Description： 根据航站统计已领取金额 ，金额排名取前五。 时间范围；本年1月 到 当天<br>
     * author：傅欣荣 <br>
     * date：2021/9/8 9:45 <br>
     * @param
     * @return
     */
    public List<ServiceCityAnalysisVo> getServiceCityClaimAmount(){
        //统计当年01月至当天 各航站已领取金额，根据金额排名，取前五
        StringBuffer sql = new StringBuffer();
        sql.append(" WITH DATA AS(SELECT oi.SERVICE_CITY name ,nvl(sum(pi.current_amount),0) value FROM DP_PAX_INFO pi");
        sql.append("         LEFT JOIN DP_ORDER_INFO  oi  on oi.order_id = pi.order_id");
        sql.append("         WHERE   pi.receive_status = 1 and  oi.create_time  BETWEEN  TO_DATE(TO_CHAR(sysdate, 'yyyy')|| '-01','yyyy-MM') and sysdate");
        sql.append("         GROUP BY oi.SERVICE_CITY");
        sql.append("         ORDER BY sum(pi.current_amount) DESC");
        sql.append(" ) ");
        sql.append(" SELECT * FROM DATA WHERE ROWNUM <=10");
        return (List<ServiceCityAnalysisVo>)baseDAO.findBySQL_comm(sql.toString(),null,ServiceCityAnalysisVo.class);

    }

    /**
     * Title：getServiceCityPeopleNum <br>
     * Description： 根据航站统计已领取人数 ，人数排名取前五。 时间范围；本年1月 到 当天<br>
     * author：傅欣荣 <br>
     * date：2021/9/8 9:45 <br>
     * @param
     * @return
     */
    public List<ServiceCityAnalysisVo> getServiceCityPeopleNum(){
        //统计当年01月至当天 各航站已领取金额，根据金额排名，取前五
        StringBuffer sql = new StringBuffer();
        sql.append(" WITH DATA AS(SELECT oi.SERVICE_CITY name ,count(pi.pax_id) value FROM DP_PAX_INFO pi");
        sql.append("         LEFT JOIN DP_ORDER_INFO  oi  on oi.order_id = pi.order_id");
        sql.append("         WHERE   pi.receive_status = 1 and  oi.create_time  BETWEEN  TO_DATE(TO_CHAR(sysdate, 'yyyy')|| '-01','yyyy-MM') and sysdate");
        sql.append("         GROUP BY oi.SERVICE_CITY");
        sql.append("         ORDER BY count(pi.pax_id) DESC");
        sql.append(" ) ");
        sql.append(" SELECT * FROM DATA WHERE ROWNUM <=10");
        return (List<ServiceCityAnalysisVo>)baseDAO.findBySQL_comm(sql.toString(),null,ServiceCityAnalysisVo.class);
    }

    /**
     * Title： getServiceCityAccidentNum <br>
     * Description： 航站各事故单类型，数量统计.排名前10<br>
     * author：傅欣荣 <br>
     * date：2021/9/8 10:32 <br>
     * @param
     * @return
     */
    public List<ServiceCityAccidentVo> getServiceCityAccidentNum(){
        StringBuffer sql = new StringBuffer();
        sql.append(" WITH DATA AS( ");
        sql.append("   select oi.SERVICE_CITY serviceCity,decode(count(decode(oi.PAY_TYPE,0,1,null)) ,0,'',count(decode(oi.PAY_TYPE,0,1,null)))  flightNum,");
        sql.append("   decode(count(decode(oi.PAY_TYPE,1,1,null)),0,'',count(decode(oi.PAY_TYPE,1,1,null))) pkgNum, ");
        sql.append("    decode(count(decode(oi.PAY_TYPE,2,1,null)),0,'', count(decode(oi.PAY_TYPE,2,1,null))) overBookingNum");
        sql.append("   from DP_ORDER_INFO oi where oi.STATUS = 3");
        sql.append("   and  oi.create_time  BETWEEN  TO_DATE(TO_CHAR(sysdate, 'yyyy')|| '-01','yyyy-MM') and sysdate");
        sql.append("   GROUP BY oi.SERVICE_CITY");
        sql.append("   ORDER BY count(oi.PAY_TYPE) desc");
        sql.append(" ) SELECT * FROM DATA WHERE ROWNUM <=10           ");
        return (List<ServiceCityAccidentVo>)baseDAO.findBySQL_comm(sql.toString(),null,ServiceCityAccidentVo.class);

    }

    /**
     * Title： getServiceCityTrunkNum <br>
     * Description： 统计航站-领取行李箱的数量，排名前10（已结案）<br>
     * author：傅欣荣 <br>
     * date：2021/9/8 10:32 <br>
     * @param
     * @return
     */
    public List<ServiceCityAnalysisVo> getServiceCityTrunkNum(){
        StringBuffer sql = new StringBuffer();
        sql.append(" WITH DATA AS( ");
        sql.append(" SELECT ct.CITY_CH_NAME name, count(pio.PKG_SIZE) value ");
        sql.append(" FROM DP_PKG_INFO pio");

        sql.append(" LEFT JOIN CITY_CODE ct ");
        sql.append(" on (regexp_like(pio.SERVICE_CITY, '[a-zA-Z]') and upper(pio.SERVICE_CITY) = CT.AIRPORT_3CODE) ");
        sql.append(" or pio.SERVICE_CITY = ct.CITY_CH_NAME ");

        sql.append("  WHERE pio.STATUS = 1 AND  pio.create_time  BETWEEN  TO_DATE(TO_CHAR(sysdate, 'yyyy')|| '-01','yyyy-MM') and sysdate");
        sql.append(" GROUP BY ct.CITY_CH_NAME ");
        sql.append(" ORDER BY count(pio.PKG_SIZE) desc");
        sql.append(" ) SELECT * FROM DATA WHERE ROWNUM <=10");
        return (List<ServiceCityAnalysisVo>)baseDAO.findBySQL_comm(sql.toString(),null,ServiceCityAnalysisVo.class);

    }
}
