package com.swcares.scgsi.flight.dao.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.entity.Resources;
import com.swcares.scgsi.flight.dto.ApplyQueryDto;
import com.swcares.scgsi.flight.dto.ApplyValidateCodeDto;
import com.swcares.scgsi.flight.entity.AoTransRecord;
import com.swcares.scgsi.flight.entity.ApplyOrderInfo;
import com.swcares.scgsi.flight.entity.ApplyPaxInfo;
import com.swcares.scgsi.flight.vo.*;
import com.swcares.scgsi.util.AesEncryptUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.dao.impl <br>
 * Description：旅客领取 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月19日 11:30 <br>
 * @version v1.0 <br>
 */
@Repository
public class PaxReceiveDaoImpl {
    @Resource
    private BaseDAO baseDAO;

    /**
     * Title：getAuthInfo <br>
     * Description：获取旅客领取信息<br>
     * author：王建文 <br>
     * date：2020-3-19 11:32 <br>
     *
     * @param
     * @return
     */
    public List<AuthInfoVo> getAuthInfo(String idNo, String flightNo, String flightDate) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("flightNo", flightNo);
        paramsMap.put("flightDate", flightDate);
        sql.append(" SELECT DISTINCT (P.ORDER_ID) AS orderId, ");
        sql.append(" P.PAX_ID AS paxId,p.ID_NO AS idNo,p.TKT_NO as tktNo, ");
        sql.append(" O.FLIGHT_NO AS flightNo,O.FLIGHT_DATE AS flightDate, ");
        sql.append(" p.PAX_NAME as paxName,p.SEGMENT as segment,p.ID_TYPE AS idType, ");
        sql.append(" P.IS_CHILD as isChild,P.WITH_BABY isInfant, ");
        sql.append(" p.TELEPHONE AS telephone,P.BABY_PAX_NAME AS babyName, ");
        sql.append(" p.SEX as sex,o.PAY_TYPE as payType,P.CURRENT_AMOUNT AS currentAmount, ");
        sql.append(" GET_DP_PAX_RECEIVEFAILCOUNT(P.PAX_ID) AS receiveCount, ");
        sql.append(" (SELECT DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE='paxReceiveFail') AS limitCount ");
        sql.append(" FROM DP_PAX_INFO P ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ON O.ORDER_ID=P.ORDER_ID ");
        sql.append(" WHERE 1=1 ");
        sql.append(" AND O.STATUS='3' AND P.RECEIVE_STATUS='0' AND P.SWITCH='0' ");
        sql.append(" AND O.FLIGHT_NO=:flightNo ");
        sql.append(" AND O.FLIGHT_DATE=:flightDate ");
        sql.append(" AND P.ID_NO ");
        sql.append(" IN (SELECT REGEXP_SUBSTR('"+idNo+"','[^,]+', 1, LEVEL) FROM DUAL ");
        sql.append(" connect by regexp_substr('"+idNo+"', '[^,]+', 1, level) is not null) ");
        return (List<AuthInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, AuthInfoVo.class);
    }

    /**
     * Title：updatePaxReceiveStatus <br>
     * Description： 申领更新旅客领取状态领取方式<br>
     * author：王建文 <br>
     * date：2020-3-12 10:31 <br>
     *
     * @param getMoneyWay, receiveChanel, receiveStatus, paxId
     * @return void
     */
    public void updatePaxReceiveStatus(String getMoneyWay, String receiveChanel, String receiveStatus, String paxId,String orderId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_PAX_INFO SET ");
        sql.append(" RECEIVE_WAY=?, ");
        sql.append(" RECEIVE_CHANNEL=?,");
        sql.append(" RECEIVE_STATUS=? ");
        sql.append(" WHERE PAX_ID IN").append(baseDAO.assembleSqlIn(paxId));
        sql.append(" AND ORDER_ID ");
        sql.append(" IN (SELECT REGEXP_SUBSTR('"+orderId+"','[^,]+', 1, LEVEL) FROM DUAL ");
        sql.append(" connect by regexp_substr('"+orderId+"', '[^,]+', 1, level) is not null) ");
        baseDAO.batchUpdate(sql.toString(), getMoneyWay, receiveChanel, receiveStatus);
    }

    public void updatePaxReceiveStatusBak(String getMoneyWay, String receiveChanel, String receiveStatus, String paxId,String orderId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_PAX_INFO SET ");
        sql.append(" RECEIVE_WAY=?, ");
        sql.append(" RECEIVE_CHANNEL=?,");
        sql.append(" RECEIVE_STATUS=? ");
        sql.append(" WHERE PAX_ID IN").append(baseDAO.assembleSqlIn(paxId));
        sql.append(" AND ORDER_ID ");
        sql.append(" IN (SELECT REGEXP_SUBSTR('"+orderId+"','[^,]+', 1, LEVEL) FROM DUAL ");
        sql.append(" connect by regexp_substr('"+orderId+"', '[^,]+', 1, level) is not null) ");
        baseDAO.batchUpdate(sql.toString(), getMoneyWay, receiveChanel, receiveStatus);
    }

    public List<DpPaxInfoVo> findDpPaxInfo(String paxId, String orderId){
        Map<String,Object> params = new HashMap<>();
        params.put("paxId",paxId);
        params.put("orderId",orderId);
        return (List<DpPaxInfoVo>) baseDAO.findBySQL_comm("SELECT * FROM dp_pax_info WHERE order_id =:orderId and pax_id =:paxId",params, DpPaxInfoVo.class);
    }

    /**
     * Title：selectPaxApplyInfo <br>
     * Description：数据库查询旅客申领信息<br>
     * author：王建文 <br>
     * date：2020-3-12 15:17 <br>
     *
     * @param
     * @return
     */
    public List<ApplyPaxInfoVo> selectPaxApplyInfo(ApplyQueryDto applyQueryDto) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT * FROM   ( ");
        sql.append(" SELECT DISTINCT (P.ORDER_ID) AS orderId,P.PAX_ID AS paxId, ");
        sql.append(" P.ID_NO AS idNo,p.TKT_NO AS tktNo,p.RECEIVE_STATUS AS receiveStatus,get_dp_pax_cash_receivecount(P.PAX_ID) AS receiveCount, ");
        sql.append(" O.FLIGHT_NO AS flightNo,O.FLIGHT_DATE AS flightDate,P.PAX_NAME AS paxName, ");
        sql.append(" p.SEGMENT AS segment,p.ID_TYPE AS idType,p.TELEPHONE AS telephone, ");
        sql.append(" NVL(p.WITH_BABY,' ') AS babyName,p.CURRENT_AMOUNT AS currentAmount,P.SWITCH AS switchOff, ");
        sql.append(" P.SUB_CLASS AS subClass,P.SEX AS sex,P.IS_CHILD AS isChild, P.WITH_BABY isInfant ,");
        sql.append(" GET_DP_PAX_RECEIVEFAILCOUNT(P.PAX_ID) AS applyCount, ");
        sql.append(" (SELECT DATA_VALUE FROM SYS_DICT WHERE DATA_TYPE='paxReceiveFail') AS limitCount ");
        sql.append(" FROM DP_PAX_INFO P ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ON O.ORDER_ID=P.ORDER_ID ");
        sql.append(" WHERE (O.STATUS='3' OR p.RECEIVE_STATUS='1')  ");
        String paxName = applyQueryDto.getPaxName();
        if (StringUtils.isNotBlank(paxName)) {
            paramsMap.put("paxName",  "%" +paxName+ "%");
            sql.append(" AND P.PAX_NAME LIKE:paxName ");
        }
        String idNo = applyQueryDto.getIdNo();
        if (StringUtils.isNotBlank(idNo)) {
            paramsMap.put("idNo", AesEncryptUtil.aesEncryptScgsi(applyQueryDto.getIdNo()));
            sql.append(" AND P.ID_NO=:idNo ");
        }
        String flightNo = applyQueryDto.getFlightNo();
        if (StringUtils.isNotBlank(flightNo)) {
            paramsMap.put("flightNo", flightNo);
            sql.append(" AND O.FLIGHT_NO=:flightNo ");
        }
        String flightDate = applyQueryDto.getFlightDate();
        if (StringUtils.isNotBlank(flightDate)) {
            paramsMap.put("flightDate", flightDate);
            sql.append(" AND O.FLIGHT_DATE=:flightDate ");
        }
        String paxType = applyQueryDto.getPaxType();
        if (StringUtils.isNotBlank(paxType) && "C".equals(paxType)) {
            sql.append(" AND P.IS_CHILD IS NOT NULL ");
        }
        if (StringUtils.isNotBlank(paxType) && "B".equals(paxType)) {
            sql.append(" AND P.WITH_BABY IS NOT NULL ");
        }
        if (StringUtils.isNotBlank(paxType) && "A".equals(paxType)) {
            sql.append(" AND P.WITH_BABY IS NULL AND P.IS_CHILD IS NULL ");
        }
        String segment = applyQueryDto.getSegment();
        if (StringUtils.isNotBlank(segment)) {
            sql.append(" AND REPLACE(P.SEGMENT, ' ', '')  ");
            sql.append(" in (SELECT REGEXP_SUBSTR('"+segment+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+segment+"', '[^,]+', 1, level) is not null) ");
        }
        sql.append(" ) P1  ");
        sql.append(" WHERE to_number(P1.APPLYCOUNT)<to_number(P1.limitCount) OR P1.SWITCHOFF=1 ");
        return (List<ApplyPaxInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, ApplyPaxInfoVo.class);
    }

    /**
     * Title：getApplyRecordInfo <br>
     * Description： 根据旅客id查看本人申领记录<br>
     * author：王建文 <br>
     * date：2020-3-12 16:50 <br>
     *
     * @param paxId 旅客id
     * @return
     */
    public List<ApplyRecordInfoVo> getApplyRecordInfo(String paxId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("paxId", paxId);
        sql.append(" SELECT DISTINCT(DAP.APPLY_CODE) AS applyCode,DAO.APPLY_USER AS applyUser, ");
        sql.append(" DAO.ID_NO AS idNo,DAO.TELEPHONE AS telephone,DAO.TRANS_AMOUNT AS transAmount, ");
        sql.append(" DECODE(DAO.GET_MONEY_WAY,'0','微信','1','银联','2','现金')AS receiveWay , ");
        sql.append(" DECODE(DAO.APPLY_WAY,'0','普通','1','代领','2','现金')AS receiveChannel, ");
        sql.append(" DAO.GET_MONEY_ACCOUNT AS getAccount,DAO.QUICK_PAY AS quickPay, ");
        sql.append(" DAO.APPLY_STATUS AS status,DAO.PAY_STATUS AS payStatus,DAO.IMG_URL AS imgUrl, ");
        sql.append(" TO_CHAR(DAO.CREATE_TIME,'YYYY-MM-DD hh24:mi') AS applyTime, ");
        sql.append(" TO_CHAR(DAO.RECEIVE_TIME,'YYYY-MM-DD hh24:mi') AS receiveTime, ");
        sql.append(" get_dp_actOrder_latest_opinion(DAP.APPLY_CODE) AS auditRemark ,");
        sql.append(" DAR.ERR_CODE_DES transMsg");
        sql.append(" FROM DP_APPLY_PAX DAP ");
        sql.append(" LEFT JOIN DP_APPLY_ORDER DAO ON DAO.APPLY_CODE=DAP.APPLY_CODE ");
        sql.append(" LEFT JOIN DP_PAX_INFO DPI ON DPI.PAX_ID=DAP.PAX_ID ");
        sql.append(" LEFT JOIN (select ROW_NUMBER() OVER(PARTITION BY APPLY_CODE ORDER BY CREATE_TIME DESC) num ,APPLY_CODE,TRANS_MSG,ERR_CODE_DES  ");
        sql.append(" FROM DP_AO_TRANS_RECORD ) DAR ON num = 1 and DAO.APPLY_CODE=DAR.APPLY_CODE   ");
        sql.append(" where DAP.PAX_ID=:paxId ");
        return (List<ApplyRecordInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, ApplyRecordInfoVo.class);
    }

    /**
     * Title： getActApplyInfo<br>
     * Description：旅客领取代领记录查询<br>
     * author：王建文 <br>
     * date：2020-3-13 11:07 <br>
     *
     * @param applyQueryDto 查询参数
     * @return
     */
    public List<ActApplyInfoVo> getActApplyInfo(ApplyQueryDto applyQueryDto) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("idNo", AesEncryptUtil.aesEncryptScgsi(applyQueryDto.getIdNo()));

        sql.append(" SELECT DISTINCT(DAO.APPLY_CODE) AS applyCode,DAO.TRANS_AMOUNT AS transAmount, ");
        sql.append(" DAO.APPLY_CUST_NUM AS applyCustNum,DOI.FLIGHT_NO AS flightNo, ");
        sql.append(" DOI.FLIGHT_DATE AS flightDate,DFI.SEGMENT AS segment,DAO.APPLY_STATUS AS status,DAO.IMG_URL AS imgUrl, ");
        sql.append(" DECODE(DAO.GET_MONEY_WAY,'0','微信','1','银联','2','现金')AS receiveWay , ");
        sql.append(" DECODE(DAO.APPLY_WAY,'0','本人','1','代领')AS receiveChannel,DAO.QUICK_PAY AS quickPay, ");
        sql.append(" DAO.PAY_STATUS payStatus ");
        sql.append(" FROM DP_APPLY_ORDER DAO  ");
        sql.append(" LEFT JOIN DP_APPLY_PAX DAP ON DAP.APPLY_CODE=DAO.APPLY_CODE ");
        sql.append(" LEFT JOIN DP_PAX_INFO DPI ON DPI.PAX_ID=DAP.PAX_ID ");
        sql.append(" LEFT JOIN DP_ORDER_INFO DOI ON DOI.ORDER_ID=DPI.ORDER_ID ");
        sql.append(" LEFT JOIN DP_FLIGHT_INFO DFI ON DOI.FLIGHT_NO=DFI.FLIGHT_NO  ");
        sql.append(" AND DOI.FLIGHT_DATE=DFI.FLIGHT_DATE ");
        sql.append(" WHERE DAO.APPLY_WAY='1' ");
        sql.append(" AND DOI.FLIGHT_DATE IS NOT NULL AND DFI.FLIGHT_NO IS NOT NULL ");
        sql.append(" AND DAO.ID_NO=:idNo ");
        if (StringUtils.isNotBlank(applyQueryDto.getPaxName())) {
            paramsMap.put("paxName", applyQueryDto.getPaxName());
            sql.append(" AND DAO.APPLY_USER=:paxName ");
        }

        String flightNo = applyQueryDto.getFlightNo();
        if (StringUtils.isNotBlank(flightNo)) {
            paramsMap.put("flightNo", flightNo);
            sql.append(" AND DFI.FLIGHT_NO=:flightNo ");
        }
        String flightDate = applyQueryDto.getFlightDate();
        if (StringUtils.isNotBlank(flightDate)) {
            paramsMap.put("flightDate", flightDate);
            sql.append(" AND DFI.FLIGHT_DATE=:flightDate ");
        }
        sql.append(" order by flightDate DESC ");
        return (List<ActApplyInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, ActApplyInfoVo.class);
    }

    /**
     * Title：getApplyInfoByApplyCode <br>
     * Description：根据申领单号获取申领信息<br>
     * author：王建文 <br>
     * date：2020-3-13 15:23 <br>
     *
     * @param applyCode 申领单号
     * @return ApplyRecordInfoVo
     */
    public ApplyRecordInfoVo getApplyInfoByApplyCode(String applyCode) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("applyCode", applyCode);
        sql.append(" SELECT DAO.APPLY_USER AS applyUser,DAO.TELEPHONE AS telephone, ");
        sql.append(" DAO.ID_NO AS idNo,DAO.TRANS_AMOUNT AS transAmount,DAO.IMG_URL AS imgUrl, ");
        sql.append(" DECODE(DAO.APPLY_WAY,'0','本人','1','代领')AS receiveChannel, ");
        sql.append(" DAO.QUICK_PAY AS quickPay, ");
        sql.append(" decode(DAO.GET_MONEY_WAY,'0','微信','1','银联','2','现金') AS receiveWay,DAO.GET_MONEY_ACCOUNT AS getAccount, ");
        sql.append(" DAO.APPLY_STATUS AS status,DAO.PAY_STATUS AS payStatus, ");
        sql.append(" TO_CHAR(DAO.CREATE_TIME,'YYYY-MM-DD hh24:mi') AS applyTime, ");
        sql.append(" TO_CHAR(DAO.RECEIVE_TIME,'YYYY-MM-DD hh24:mi') AS receiveTime, ");
        sql.append(" get_dp_actOrder_latest_opinion(" + applyCode + ") AS auditRemark ,");
        sql.append(" DAR.ERR_CODE_DES transMsg");
        sql.append(" FROM DP_APPLY_ORDER DAO ");
        sql.append(" LEFT JOIN (select ROW_NUMBER() OVER(PARTITION BY APPLY_CODE ORDER BY CREATE_TIME DESC) num ,APPLY_CODE,TRANS_MSG,ERR_CODE_DES  ");
        sql.append(" FROM DP_AO_TRANS_RECORD ) DAR ON num = 1 and DAO.APPLY_CODE=DAR.APPLY_CODE   ");
        sql.append(" WHERE DAO.APPLY_CODE=:applyCode ");
        return baseDAO.findOneBySql(sql.toString(), paramsMap, ApplyRecordInfoVo.class);
    }

    /**
     * Title：getActApplyPaxInfoByApplyCode <br>
     * Description： 根据申领单号查询申领旅客信息<br>
     * author：王建文 <br>
     * date：2020-3-19 11:39 <br>
     *
     * @param applyCode 申领单号
     * @return
     */
    public List<ActApplyPaxInfoVo> getActApplyPaxInfoByApplyCode(String applyCode) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("applyCode", applyCode);
        sql.append(" SELECT  ");
        sql.append(" get_dp_act_pax_paymoney(DAP.PAX_ID,DAP.ORDER_ID) AS currentAmount,DAP.PAX_ID AS paxId,DAP.IMG_URL AS imgUrl, ");
        sql.append(" CONCAT(DAP.AUDIT_REMARK ,nvl2(DAP.REMARK,';'||DAP.REMARK,DAP.REMARK)) AS refuseRemark,");
        sql.append(" DAP.REMARK AS remark, ");
        sql.append(" (SELECT P.PAX_NAME FROM  DP_PAX_INFO P WHERE P.PAX_ID=DAP.PAX_ID AND ROWNUM=1) AS paxName ");
        sql.append(" FROM DP_APPLY_PAX DAP ");
        sql.append(" WHERE DAP.APPLY_CODE=:applyCode ");
        return (List<ActApplyPaxInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, ActApplyPaxInfoVo.class);
    }
    public ActApplyPaxInfoVo getPaxInfo(String paxId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("paxId", paxId);
        sql.append(" SELECT DPI.PAX_NAME AS paxName,DPI.ID_NO AS idNo,DPI.ID_TYPE AS idType, ");
        sql.append(" DPI.SEX AS sex,DPI.WITH_BABY AS babyName,DPI.IS_CHILD AS isChild,DPI.WITH_BABY isInfant,DPI.TKT_NO AS tktNo, ");
        sql.append(" DPI.PAX_ID AS paxId ");
        sql.append(" FROM DP_PAX_INFO DPI ");
        sql.append(" WHERE DPI.PAX_ID =:paxId AND ROWNUM=1");
        sql.append(" ORDER BY DPI.CREATE_TIME DESC ");
        return  (ActApplyPaxInfoVo)baseDAO.findBySQL_comm(sql.toString(), paramsMap, ActApplyPaxInfoVo.class).get(0);
    }
    /**
     * Title：getPaxWeiXinResource <br>
     * Description： 旅客微信公众号领取请求资源查询<br>
     * author：王建文 <br>
     * date：2020-4-26 13:22 <br>
     * @param
     * @return
     */
    public List<Resources> getPaxWeiXinResource(){
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT W.RESOURCE_URL, ");
        sql.append(" W.CONTAIN_URL, ");
        sql.append(" W.RESOURCE_CODE ");
        sql.append(" FROM WEIXIN_RESOURCE W ");
        sql.append(" WHERE W.STATUS=0 ");
        return (List<Resources>)baseDAO.findBySQL_comm(sql.toString(),new HashMap<>(),Resources.class);
    }

    public List<String> findByPaxIdAndOrderId(String paxId, String orderIds){
        String sql = " SELECT apply_code from dp_apply_pax where pax_Id =:paxId and instr(order_id,:orderId ) != 0 ";
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("paxId", paxId);
        paramsMap.put("orderId", orderIds);
        List<ApplyPaxInfo> bySQL_comm = (List<ApplyPaxInfo>) baseDAO.findBySQL_comm(sql, paramsMap, ApplyPaxInfo.class);
        if(ObjectUtils.isNotEmpty(bySQL_comm) && bySQL_comm.size()>0){
            List<String> list = new ArrayList<>();
            bySQL_comm.forEach(d->{list.add(d.getApplyCode());});
            return list;
        }
        return null;
    }

    public List<AoTransRecord> findByApplyCode(List<String> applyCodes){
        String applyCode = baseDAO.assembleSqlIn( String.join(",", applyCodes));
        String sql = " SELECT ID  ,PAY_ORDER_ID   ,APPLY_CODE   ,TRANS_AMOUNT,RETURN_ORDER_NO," +
                " TRANS_CODE   ,TRANS_MSG   ,TRANS_SUB_CODE ,TRANS_SUB_MSG  ,PAY_TYPE       ," +
                " PAY_STATUS     ,PAY_RETURN_TIME,PAY_START_TIME ,ERR_CODE  ," +
                " ERR_CODE_DES   ,APPLY_ACCOUNT,FEE_AMOUNT   ,CREATE_TIME  " +
                " from DP_AO_TRANS_RECORD where apply_code in " + applyCode;

        return (List<AoTransRecord>) baseDAO.findBySQL_comm(sql, null, AoTransRecord.class);
    }


    /***
     * @title validateApplyInfo
     * @description 申领记录查询-校验输入手机号，是否申领时预留的手机号
     * <AUTHOR>
     * @date 2024/11/4 11:47
     * @param dto
     * @return java.lang.Integer
     */
    public Integer validateApplyInfoByApplyRecord(ApplyValidateCodeDto dto){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT  ");
        sql.append(" ap.*  ");
        sql.append(" from DP_APPLY_ORDER ap  ");
        sql.append(" left join DP_APPLY_PAX dap on ap.APPLY_CODE = dap.APPLY_CODE  ");
        sql.append(" left join DP_ORDER_INFO doi  on DoI.ORDER_ID in  ");
        sql.append(" (SELECT REGEXP_SUBSTR(DAP.ORDER_ID,'[^,]+', 1, LEVEL) FROM DUAL  ");
        sql.append(" connect by regexp_substr(DAP.ORDER_ID, '[^,]+', 1, level) is not null)  ");
        sql.append(" where 1=1 ");
        sql.append(" and ap.TELEPHONE = :telephone ");
        sql.append(" and doi.FLIGHT_NO  =:flightNo");
        sql.append(" and doi.FLIGHT_DATE =:flightDate ");
        sql.append(" and ap.ID_NO =:idNo ");
        paramsMap.put("telephone",AesEncryptUtil.aesEncryptScgsi(AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, dto.getTelephone())));
        paramsMap.put("flightNo",dto.getFlightNo());
        paramsMap.put("flightDate",dto.getFlightDate());
        paramsMap.put("idNo",AesEncryptUtil.aesEncryptScgsi(dto.getIdNo()));
        List<ApplyOrderInfo> bySQL_comm = (List<ApplyOrderInfo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, ApplyOrderInfo.class);
        return bySQL_comm.size();
    }

}