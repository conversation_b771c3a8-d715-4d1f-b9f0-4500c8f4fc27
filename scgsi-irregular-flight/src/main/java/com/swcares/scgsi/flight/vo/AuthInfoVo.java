package com.swcares.scgsi.flight.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：旅客申领查询领取信息接收数据库返回对象 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月10日 15:21 <br>
 * @version v1.0 <br>
 */
@Data
@EncryptionClassz
public class AuthInfoVo{
    /**
     * 旅客ID
     */
    private String paxId;
    /**
     * 旅客姓名
     */
    private String paxName;
    /**
     * 联系电话
     */
    @Encryption
    private String telephone;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号
     */
    @Encryption
    private String idNo;
    /**
     * 航段
     */
    private String segment;
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 性别C儿童M男F女
     */
    private String sex;
    /**
     * 婴儿名字
     */
    private String babyName;
    /**
     * 旅客总赔偿金额
     */
    private String payMoney;
    /**
     * 赔偿单号
     */
    private String orderIds;
    /**
     * 服务单id
     */
    private String orderId;
    /**
     *赔偿类型 不正常航班赔偿0、异常行李1、超售旅客2
     */
    private String payType;
    /**
     * 服务单对应的赔偿金额
     */
    private int currentAmount;
    /**
     * 票号
     */
    private String tktNo;
    /**
     * 计划起飞时间
     */
    private String std;
    /**
     * 计划到达
     */
    private String sta;
    /**
     * 儿童标识
     */
    private String isChild;
    /**
     * 携带婴儿标识
     */
    private String isInfant;

    /**
     * 领取次数
     */
    private int receiveCount;
    /**
     * 领取次数限制
     */
    private int limitCount;

}