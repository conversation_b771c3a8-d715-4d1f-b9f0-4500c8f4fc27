package com.swcares.scgsi.flight.vo;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：代领记录查询VO <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月13日 10:59 <br>
 * @version v1.0 <br>
 */
@Data
public class ActApplyInfoVo {
    /**
     * 申领单号
     */
    private String applyCode;
    /**
     * 申领金额
     */
    private String transAmount;
    /**
     * 代领人数
     */
    private String applyCustNum;
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 航段
     */
    private String segment;
    /**
     * 审核状态(0待审核、1已通过、2未通过)
     */
    private String status;

    /**
     * 支付状态(0未支付,1支付)
     */
    private String payStatus;
    /**
     * 计划起飞时间
     */
    private String std;
    /**
     * 计划到达时间
     */
    private String sta;
    /**
     * 未领取金额
     */
    private String unreceivedMoney;
    private String receiveWay;
    private String receiveChannel;
    private String imgUrl;

    /**
     * 快速支付标识
     */
    private String quickPay;
}