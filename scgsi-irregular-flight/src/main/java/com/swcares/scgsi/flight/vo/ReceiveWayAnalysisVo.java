package com.swcares.scgsi.flight.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：领取渠道 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 09月07日 18:50 <br>
 * @version v1.0 <br>
 */
@Data
public class ReceiveWayAnalysisVo {

    private String name;

    private String type="line";

    private String stack="总量";

    private List<String> data;

    @JsonIgnore
    private Map<String, String> monthMap;
}
