package com.swcares.scgsi.flight.service.impl;

import com.alibaba.fastjson.JSON;
import com.swcares.scgsi.base.SpringUtil;
import com.swcares.scgsi.entity.Resources;
import com.swcares.scgsi.flight.dao.impl.PaxReceiveDaoImpl;
import com.swcares.scgsi.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.service <br>
 * Description：旅客微信公众号领取菜单资源处理 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 04月26日 13:09 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Component
public class PaxWeiResourceService {

    //微信公众号资源key
    public static final String PAX_WEIXIN_RESOURCE="weixin:pax:openidResource:";

    /**
     * Title：initPaxWeiXinResource <br>
     * Description：微信公众号领取请求资源初始化<br>
     * author：王建文 <br>
     * date：2020-4-26 13:30 <br>
     * @param openId String
     */
    public void setPaxWeChatResource(String openId) {
        RedisService redisService = SpringUtil.getBean(RedisService.class);
        PaxReceiveDaoImpl paxReceiveDao = SpringUtil.getBean(PaxReceiveDaoImpl.class);
        String openidResource = redisService.get(PAX_WEIXIN_RESOURCE + openId) == null ? "" : (String) redisService.get(PAX_WEIXIN_RESOURCE + openId);
        log.info("微信公众号领取请求资源初始化: openId:[{}} ,openidResource:[{}]",openId,openidResource);
//        if (StringUtils.isBlank(openidResource)) {
            List<Resources> openidResourceList = paxReceiveDao.getPaxWeiXinResource();
            log.info("微信公众号领取请求资源初始化: openId:[{}} ,openidResourceList:[{}]",openId,JSON.toJSONString(openidResourceList));
            if (openidResourceList.size() > 0) {
                redisService.set(PAX_WEIXIN_RESOURCE + openId, JSON.toJSONString(openidResourceList));
            }
//        }
    }
}