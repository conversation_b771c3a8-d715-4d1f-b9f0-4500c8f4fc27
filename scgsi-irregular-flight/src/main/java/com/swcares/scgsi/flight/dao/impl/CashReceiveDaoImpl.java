package com.swcares.scgsi.flight.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.flight.vo.*;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.dao.impl <br>
 * Description：现金领取自定义sql<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月19日 11:43 <br>
 * @version v1.0 <br>
 */
@Repository
public class CashReceiveDaoImpl {
    @Resource
    private BaseDAO baseDAO;

    /**
     * Title：getDelayFlightInfo <br>
     * Description： 根据航班号航班日期查看航班信息<br>
     * author：王建文 <br>
     * date：2020-3-17 15:12 <br>
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @return
     */
    public List<CashFlightInfoVo> getDelayFlightInfo(String flightNo, String flightDate) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("flightNo", flightNo);
        paramsMap.put("flightDate", flightDate);
        sql.append(" SELECT F.FLIGHT_NO AS flightNo,F.FLIGHT_DATE AS flightDate, ");
        sql.append(" F.FLIGHT_ID AS flightId,F.SEGMENT AS segment,F.STA AS sta, ");
        sql.append(" F.STD AS std,get_dp_flight_receivedmoney(F.FLIGHT_NO,F.FLIGHT_DATE) AS receivedMoney, ");
        sql.append(" get_dp_flight_totalmoney(F.FLIGHT_NO,F.FLIGHT_DATE) AS totalMoney,F.AC_TYPE AS acType ");
        sql.append(" FROM DP_FLIGHT_INFO F ");
        sql.append(" LEFT JOIN DP_ORDER_INFO DOI  ");
        sql.append(" ON DOI.FLIGHT_NO=F.FLIGHT_NO AND F.FLIGHT_DATE=DOI.FLIGHT_DATE ");
        sql.append(" WHERE 1=1 ");
        sql.append(" AND F.FLIGHT_NO=:flightNo ");
        sql.append(" AND F.FLIGHT_DATE=:flightDate ");
        return (List<CashFlightInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, CashFlightInfoVo.class);
    }

    /**
     * Title：getCashOrderInfo <br>
     * Description： 根据航班号航班日期查看下面的赔付单<br>
     * author：王建文 <br>
     * date：2020-3-17 14:12 <br>
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @return
     */
    public List<CashOrderInfoVo> getCashOrderInfo(String flightNo, String flightDate) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("flightNo", flightNo);
        paramsMap.put("flightDate", flightDate);
        sql.append(" SELECT DISTINCT(O.ORDER_ID) AS orderId,O.PAY_TYPE AS payType, ");
        sql.append(" o.CHOICE_SEGMENT AS createSegment, ");
        sql.append(" get_dp_order_receivecount(O.ORDER_ID,1)||'/'|| ");
        sql.append(" get_dp_order_receivecount(O.ORDER_ID,NULL) ");
        sql.append(" AS receiveInfo, ");
        sql.append(" get_dp_order_receivecount(O.ORDER_ID,NULL)-GET_DP_ORDER_CHILDCOUNT(O.ORDER_ID)-GET_DP_ORDER_BABYCOUNT(O.ORDER_ID)||'/'||GET_DP_ORDER_CHILDCOUNT(O.ORDER_ID)||'/' ");
        sql.append(" ||GET_DP_ORDER_BABYCOUNT(O.ORDER_ID) AS membersCount ");
        sql.append(" FROM DP_ORDER_INFO O ");
        sql.append(" WHERE O.STATUS='3'  ");
        sql.append(" AND O.FLIGHT_NO=:flightNo ");
        sql.append(" AND O.FLIGHT_DATE=:flightDate ");
        return (List<CashOrderInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, CashOrderInfoVo.class);
    }

    /**
     * Title：getOrderDetail <br>
     * Description： 现金领取根据赔付单号获取赔付详情<br>
     * author：王建文 <br>
     * date：2020-3-19 11:50 <br>
     *
     * @param orderId 赔付单号
     * @return
     */
    public CashOrderDetailVo getOrderDetail(String orderId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("orderId", orderId);
        sql.append(" SELECT DISTINCT(O.ORDER_ID) AS orderId,O.PAY_TYPE AS payType, ");
        sql.append(" o.CHOICE_SEGMENT AS createSegment,O.FLIGHT_NO AS flightNo, ");
        sql.append(" O.FLIGHT_DATE AS flightDate,get_user_name(O.CREATE_ID) AS applyUser, ");
        sql.append(" TO_CHAR(O.CREATE_TIME,'YYYY-MM-DD hh24:mi:ss') AS applyTime ");
        sql.append(" FROM DP_ORDER_INFO O ");
        sql.append(" WHERE O.ORDER_ID=:orderId ");
        return baseDAO.findOneBySql(sql.toString(), paramsMap, CashOrderDetailVo.class);
    }
    public CashOrderDetailVo getOrderLastAuditor(String orderId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("orderId", orderId);
        sql.append(" SELECT get_user_name(O.AUDITOR) AS lastAuditor,TO_CHAR(O.AUDIT_TIME,'YYYY-MM-DD hh24:mi:ss') AS auditTime ");
        sql.append(" FROM DP_ACT_MIDDLE DAM ");
        sql.append(" LEFT JOIN DP_ORDER_AUDIT O ON O.ORDER_ID=DAM.ORDER_ID ");
        sql.append(" AND O.ID=DAM.RECORD_ID  ");
        sql.append(" WHERE DAM.ORDER_ID=:orderId ");
        return baseDAO.findOneBySql(sql.toString(), paramsMap, CashOrderDetailVo.class);
    }

    /**
     * Title：getFlightSegment <br>
     * Description： 根据航班号航班日期获取赔付单下面旅客所有航段<br>
     * author：王建文 <br>
     * date：2020-3-17 19:29 <br>
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @return
     */
    public List<FlightInfoVo> getFlightSegment(String flightNo, String flightDate) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("flightNo", flightNo);
        paramsMap.put("flightDate", flightDate);
        sql.append(" SELECT DISTINCT(REPLACE(DPI.SEGMENT, ' ', '')) AS segment ");
        sql.append(" FROM DP_PAX_INFO DPI ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ON O.ORDER_ID=DPI.ORDER_ID ");
        sql.append(" WHERE O.FLIGHT_NO=:flightNo  ");
        sql.append(" AND O.FLIGHT_DATE=:flightDate ");
        return (List<FlightInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, FlightInfoVo.class);

    }

    /**
     * Title：getPaxServiceOrder <br>
     * Description： 获取旅客赔偿单列表<br>
     * author：王建文 <br>
     * date：2020-3-18 9:52 <br>
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @param paxId      旅客ID
     * @return
     */
    public List<CashServiceOrderVo> getPaxServiceOrder(String flightNo, String flightDate, String paxId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("flightNo", flightNo);
        paramsMap.put("flightDate", flightDate);
        paramsMap.put("paxId", paxId);
        sql.append(" SELECT DISTINCT(O.ORDER_ID) AS orderId, ");
        sql.append(" O.FLIGHT_NO AS flightNo,O.FLIGHT_DATE AS flightDate, ");
        sql.append(" P.SEGMENT AS segment,O.STATUS AS status, ");
        sql.append(" O.PAY_TYPE AS payType,P.SWITCH AS switchOff, ");
        sql.append(" get_user_name(O.CREATE_ID) AS createUser,P.CURRENT_AMOUNT AS payMoney,P.RECEIVE_STATUS AS receiveStatus ");
        sql.append(" FROM DP_ORDER_INFO O ");
        sql.append(" LEFT JOIN DP_PAX_INFO P ON P.ORDER_ID=O.ORDER_ID ");
        sql.append(" WHERE ((O.STATUS = '3' AND P.SWITCH ='0') OR  P .RECEIVE_STATUS = '1') ");
        sql.append(" AND P.PAX_ID=:paxId ");
        sql.append(" AND O.FLIGHT_NO=:flightNo ");
        sql.append(" AND O.FLIGHT_DATE=:flightDate ");
        return (List<CashServiceOrderVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, CashServiceOrderVo.class);
    }
}