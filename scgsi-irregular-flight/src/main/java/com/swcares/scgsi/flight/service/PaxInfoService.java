package com.swcares.scgsi.flight.service;

import com.swcares.scgsi.flight.dto.ApplyParamDto;
import com.swcares.scgsi.flight.dto.ApplyQueryDto;
import com.swcares.scgsi.flight.dto.ApplyValidateCodeDto;
import com.swcares.scgsi.flight.vo.ActApplyInfoVo;
import com.swcares.scgsi.flight.vo.ApplyPaxInfoVo;
import com.swcares.scgsi.flight.vo.AuthReceiveInfoVo;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.service <br>
 * Description：旅客领取相关处理service <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月10日 14:58 <br>
 * @version v1.0 <br>
 */
public interface PaxInfoService {
    /**
     * Title：getPaxAuthInfo <br>
     * Description： 旅客申领返回申领信息<br>
     * author：王建文 <br>
     * date：2020-3-10 15:26 <br>
     *
     * @param idNo       证件号
     * @param flightNo   航班号
     * @param flightDate 日期
     * @return
     */
    public Map<String, Object> getPaxAuthInfo(String idNo, String flightNo, String flightDate);

    /**
     * Title：saveActApplyInfo <br>
     * Description： 保存申领--代领信息<br>
     * author：王建文 <br>
     * date：2020-3-12 10:29 <br>
     *
     * @param applyParamDto 申领信息
     * @return
     */
    public void saveActApplyInfo(ApplyParamDto applyParamDto);

    /**
     * Title：getPaxApplyInfo <br>
     * Description：旅客申领记录查询<br>
     * author：王建文 <br>
     * date：2020-3-12 15:15 <br>
     *
     * @param applyQueryDto 查询参数 证件号，姓名必填
     * @return
     */
    public List<Map<String, Object>> getPaxApplyInfo(ApplyQueryDto applyQueryDto);

    /**
     * @title getPaxApplyInfoValidateCode
     * @description 旅客申领记录查询-需要校验手机短信验证码
     * <AUTHOR>
     * @date 2025/1/14 16:00
     * @param applyQueryDto
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     */
    public List<Map<String, Object>> getPaxApplyInfoValidateCode(ApplyQueryDto applyQueryDto);
    /**
     * Title：getApplyRecordInfo <br>
     * Description： 根据旅客id查看本人申领记录<br>
     * author：王建文 <br>
     * date：2020-3-12 16:50 <br>
     *
     * @param paxId 旅客id
     * @return
     */
    public Map<String, Object> getApplyRecordInfo(String paxId);

    /**
     * Title： getActApplyInfo<br>
     * Description：旅客领取代领记录查询<br>
     * author：王建文 <br>
     * date：2020-3-13 11:07 <br>
     *
     * @param applyQueryDto 查询参数
     * @return
     */
    public List<ActApplyInfoVo> getActApplyInfo(ApplyQueryDto applyQueryDto);

    /**
     * Title：getActApplyInfoByApplyCode <br>
     * Description：代领领取详情查看<br>
     * author：王建文 <br>
     * date：2020-3-13 15:15 <br>
     *
     * @param applyCode 申领单号
     * @return
     */
    public Map<String, Object> getActApplyInfoByApplyCode(String applyCode, String flightNo, String flightDate);

    /**
     * Title：getReceiveRandomCode <br>
     * Description： 旅客微信公众号领取获取验证码<br>
     * author：王建文 <br>
     * date：2020-5-19 9:55 <br>
     *
     * @param telephone 手机号
     */
    public void getReceiveRandomCode(String telephone, String codeKey, String captcha);

    /***
     * @title getReceiveRandomCode
     * @description 旅客微信公众号领取获取验证码
     * <AUTHOR>
     * @date 2024/10/28 11:27
     * @param dto
     * @return void
     */
    public void getReceiveRandomCode(ApplyValidateCodeDto dto);


    /**
     * Title：validateAuthCode <br>
     * Description： 微信公众号验证旅客验证码是否输入正确<br>
     * author：王建文 <br>
     * date：2020-5-19 10:09 <br>
     * @param  telephone 手机号
     * @param  randomCode 验证码
     * @return
     */
    public void validateAuthCode(String telephone, String randomCode);

    
    /*** 
     * @title validateAuthCode
     * @description 旅客端验证旅客输入的手机验证码，图形验证码
     * <AUTHOR>
     * @date 2024/10/28 9:55
     * @param dto
     * @return void
     */
    public void validateAuthCode(ApplyValidateCodeDto dto);
    
    /**
     * Title：saveNormalApplyInfo <br>
     * Description：旅客领取微信公众号保存普通申领信息<br>
     * author：王建文 <br>
     * date：2020-5-19 10:20 <br>
     * @param  applyParamDto 参数接收
     * @return
     */
    public void saveNormalApplyInfo(ApplyParamDto applyParamDto);
    /**
     * Title： <br>
     * Description： TODO(用一句话描述该方法做什么)<br>
     * author：王建文 <br>
     * date：2020-5-19 10:55 <br>
     * @param
     * @return
     */
    public List<AuthReceiveInfoVo> getPaxInfoData(String idNo, String flightNo, String flightDate);
}
