package com.swcares.scgsi.flight.service.impl;

import com.swcares.scgsi.common.model.form.FlightInfoListForm;
import com.swcares.scgsi.common.model.view.OriginalSegmentView;
import com.swcares.scgsi.flight.dao.impl.ActAuditInfoDaoImpl;
import com.swcares.scgsi.flight.service.ActMessageService;
import com.swcares.scgsi.flight.vo.ActMessageVo;
import com.swcares.scgsi.service.impl.FlightInfoServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.service.impl <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 04月13日 19:57 <br>
 * @version v1.0 <br>
 */
@Service
public class ActMessageServiceImpl implements ActMessageService {
    @Resource
    private ActAuditInfoDaoImpl actAuditInfoDao;
    @Resource
    private FlightInfoServiceImpl flightInfoService;
    @Override
    public List<ActMessageVo> getActMessageInfo() {
        List<ActMessageVo> applyOrderInfoList=actAuditInfoDao.getActOrderMessage();
        List<ActMessageVo> actMessageVoList=new ArrayList<>();
        if(applyOrderInfoList.size()>0){
            for( ActMessageVo actMessageVo:applyOrderInfoList){
                //获取航班信息
                Map<String,Object> flightMap=actAuditInfoDao.getActOrderMessageFlightNoAndFlightDate(actMessageVo.getApplyCode()).get(0);
                FlightInfoListForm form1=new FlightInfoListForm();
                String flightNo=flightMap.get("FLIGHTNO").toString();
                String flightDate=flightMap.get("FLIGHTDATE").toString();
                form1.setFlightNum(flightNo);
                form1.setFlightDate(flightDate.replaceAll("-","/"));
                OriginalSegmentView originalSegementView=flightInfoService.getOriginalSegment(form1);
                actMessageVo.setFlightNo(flightNo);
                actMessageVo.setFlightDate(flightDate);
                actMessageVo.setSegment(originalSegementView.getSegment());
                actMessageVoList.add(actMessageVo);
            }
        }
        return actMessageVoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateActSendStatus(String applyCode) {
        actAuditInfoDao.updateActSendStatus(applyCode);
    }
}