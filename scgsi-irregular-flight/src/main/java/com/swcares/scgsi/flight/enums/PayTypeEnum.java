package com.swcares.scgsi.flight.enums;

/**
 * ClassName：com.swcares.scgsi.flight.enums <br>
 * Description：赔付类型枚举 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月06日 12:01 <br>
 * @version v1.0 <br>
 */
public enum PayTypeEnum {
    FLIGHT("0", "不正常航班赔偿"),
    PKG("1", "异常行李"),
    OVER_BOOKING("2", "旅客超售");

    private PayTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static PayTypeEnum build(String key) {
        return build(key, true);
    }

    public static PayTypeEnum build(String key, boolean throwEx) {
        PayTypeEnum typeEnum = null;
        for (PayTypeEnum element : PayTypeEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + PayTypeEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
