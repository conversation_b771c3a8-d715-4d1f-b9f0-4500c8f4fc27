package com.swcares.scgsi.flight.enums;

/**
 * ClassName：com.swcares.scgsi.flight.enums <br>
 * Description：赔付类型枚举 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月06日 12:01 <br>
 * @version v1.0 <br>
 */
public enum PaxStatusEnum {
    PT("PT", "出票"),
    NA("NA", "未值机"),
    AC("AC", "值机"),
    XR("XR", "值机取消"),
    CL("CL", "订座取消"),
    SB("SB", "候补"),
    DL("DL", "拉下");

    private PaxStatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static PaxStatusEnum build(String key) {
        return build(key, true);
    }

    public static PaxStatusEnum build(String key, boolean throwEx) {
        PaxStatusEnum typeEnum = null;
        for (PaxStatusEnum element : PaxStatusEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + PaxStatusEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
