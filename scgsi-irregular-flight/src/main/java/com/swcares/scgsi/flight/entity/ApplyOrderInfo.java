package com.swcares.scgsi.flight.entity;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.flight.entity <br>
 * Description：申领单信息入库 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月11日 13:36 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "DP_APPLY_ORDER")
@Data
@EncryptionClassz
public class ApplyOrderInfo {
    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;
    /**
     * 申领人
     */
    @Column(name = "APPLY_USER")
    private String applyUser;

    /**
     * 联系电话
     */
    @Column(name = "TELEPHONE")
    @Encryption
    private String telephone;

    /**
     * 申领赔偿金额
     */
    @Column(name = "TRANS_AMOUNT")
    private String transAmount;

    /**
     * 申领人数(普通渠道默认1,代领渠道根据人数计算)
     */
    @Column(name = "APPLY_CUST_NUM")
    private String applyCustNum;

    /**
     * 是否代领(本人0，代领1)
     */
    @Column(name = "APPLY_WAY")
    private String applyWay;

    /**
     * 审核状态(0待审核、1已通过、2未通过,3冻结)
     */
    @Column(name = "STATUS")
    private String status;
    /**
     * 支付状态(0未支付,1已支付,2支付失败3处理中)
     */
    @Column(name = "PAY_STATUS")
    private String payStatus;
    /**
     * 领取账号
     */
    @Column(name = "GET_MONEY_ACCOUNT")
    @Encryption
    private String getMoneyAccount;
    /**
     * 领取方式(0微信，1银联,2现金)
     */
    @Column(name = "GET_MONEY_WAY")
    private String getMoneyWay;
    /**
     * 申领单号
     */
    @Column(name = "APPLY_CODE")
    private String applyCode;
    /**
     * 审核时间
     */
    @Column(name = "AUDIT_TIME")
    private Date auditTime;
    /**
     * 证件号
     */
    @Column(name = "ID_NO")
    @Encryption
    private String idNo;
    /**
     * 支付时间
     */
    @Column(name = "PAY_DATE")
    private Date payDate;
    /**
     * 代领人图片路径
     */
    @Column(name = "IMG_URL")
    private String imgUrl;
    /**
     * 开户行
     */
    @Column(name = "OPEN_BANK_NAME")
    private String openBankName;

    /**
     * 银行编码
     */
    @Column(name = "BANK_CODE")
    private String bankCode;

    /**
     * 1标识为快速支付0默认状态
     */
    @Column(name = "QUICK_PAY")
    private String quickPay;

    /**
     * 收款时间
     */
    @Column(name = "RECEIVE_TIME")
    private Date receiveTime;

    /**
     * 创建日期
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 申领单状态
     */
    @Column(name = "APPLY_STATUS")
    private String applyStatus;
}