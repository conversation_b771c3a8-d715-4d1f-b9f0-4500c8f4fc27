package com.swcares.scgsi.flight.service;

import com.swcares.scgsi.flight.entity.ApplyOrderInfo;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.service <br>
 * Description：申领单-服务 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 04月08日 20:34 <br>
 * @version v1.0 <br>
 */
public interface ApplyOrderQuartzService {


    /**
     * Title： findUnpaidOrderInfo <br>
     * Description： 查询待支付订单集合 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 20:01 <br>
     * @param
     * @return
     */
    List<ApplyOrderInfo> findUnpaidOrderInfo();

    /**
     * Title：updApplyOrderStatus <br>
     * Description： 更新申领单支付状态 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 20:02 <br>
     * @param applyCode   支付订单号
     * @param payStatus   支付状态
     * @param receiveTime  领取时间
     * @return
     */
    int updApplyOrderStatus(String applyCode,String payStatus,String payDate,String receiveTime);


    /**
     * Title：updApplyOrderStatus <br>
     * Description： 更新申领单旅客支付状态 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 20:02 <br>
     * @param applyCode   支付订单号
     * @param payStatus   支付状态
     * @param receiveTime  领取时间
     * @return
     */
    int updOrderPaxStatus(String applyCode, String payStatus, String receiveTime,boolean isReceiveTime);
}
