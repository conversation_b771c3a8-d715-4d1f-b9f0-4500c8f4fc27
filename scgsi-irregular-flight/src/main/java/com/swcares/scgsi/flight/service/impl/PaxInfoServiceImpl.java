package com.swcares.scgsi.flight.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.aop.EncryptFieldAop;
import com.swcares.scgsi.common.model.form.ContentIdxForm;
import com.swcares.scgsi.common.model.form.FlightInfoListForm;
import com.swcares.scgsi.common.model.view.OriginalSegmentView;
import com.swcares.scgsi.common.userenum.UserEnum;
import com.swcares.scgsi.fileuploadanddownload.UploadAndDownload;
import com.swcares.scgsi.flight.dao.ApplyOrderDao;
import com.swcares.scgsi.flight.dao.ApplyPaxDao;
import com.swcares.scgsi.flight.dao.PassengerInfoDao;
import com.swcares.scgsi.flight.dao.impl.CashReceiveDaoImpl;
import com.swcares.scgsi.flight.dao.impl.FlightCompensateDaoImpl;
import com.swcares.scgsi.flight.dao.impl.PaxReceiveDaoImpl;
import com.swcares.scgsi.flight.dto.*;
import com.swcares.scgsi.flight.entity.AoTransRecord;
import com.swcares.scgsi.flight.entity.ApplyOrderInfo;
import com.swcares.scgsi.flight.entity.ApplyPaxInfo;
import com.swcares.scgsi.flight.entity.PassengerInfo;
import com.swcares.scgsi.flight.service.FlightCompensateService;
import com.swcares.scgsi.flight.service.PaxInfoService;
import com.swcares.scgsi.flight.vo.*;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.service.TraceService;
import com.swcares.scgsi.service.impl.FlightInfoServiceImpl;
import com.swcares.scgsi.service.yee.YeePayService;
import com.swcares.scgsi.service.yee.YeeTransactionType;
import com.swcares.scgsi.service.yee.bean.YeeAuthentication;
import com.swcares.scgsi.service.yee.bean.YeeAuthenticationResult;
import com.swcares.scgsi.sms.api.SmsService;
import com.swcares.scgsi.sms.entity.SmsSendResult;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.util.SeedIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.scgsi.flight.service.impl <br>
 * Description：旅客领取相关service<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月10日 15:29 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class PaxInfoServiceImpl implements PaxInfoService {

    @PostConstruct
    public void initValidateSuccessPayment() {
        redisService.set(VALIDATE_SUCCESS_PAYMENT, VALIDATE_SUCCESS_PAYMENT);
    }

    //公务舱标识
    private static final String B_CLASS_TYPE = "2";

    private final String PAY_STATUS_PAID = "1";

    //旅客验证码
    public static final String PAX_TELEPHONE_RANDOMCODE = "weixin:validate_code:";
    private final static String  MESSAGE_TYPE_PREFIX="您正在使用山航微信公众号领取补偿，验证码:";
    private final static String  MESSAGE_TYPE_SUFFIX="，仅限本次领取使用，请5分钟内输入，并确认是本人操作，提供给他人可能导致您航班信息泄露，此验证码请勿在任何短信或邮件链接的页面中输入!谨防被骗，95369是山航唯一客服号码。";
    //旅客手机验证码+图形验证码校验结果key
    public static final String APPLY_VALIDATE_CODE_RESULT = "weixin:validate_code_result:";

    //申领短信有效期秒
    private static final int SMS_EXPIRE_DATE = 300;

    //银联支付方式
    private static final String UNION_PAY = "1";

    private static final String SUCCESS = "SUCCESS";

    //是否开启 检验当前旅客在一个赔偿单号下是否有重复的转账记录
    private static final String VALIDATE_SUCCESS_PAYMENT = "VALIDATE_SUCCESS_PAYMENT";


    @Resource
    private ApplyOrderDao applyOrderDao;

    @Resource
    private ApplyPaxDao applyPaxDao;

    @Resource
    private FlightCompensateService flightCompensateService;

    @Resource
    private RedisService redisService;

    @Resource
    private PaxReceiveDaoImpl paxReceiveDao;
    @Resource
    PassengerInfoDao passengerInfoDao;
    @Resource
    private SeedIdUtil seedIdUtil;

    @Resource
    private TraceService traceService;

    @Resource
    private CashReceiveDaoImpl cashReceiveDao;

    @Resource
    private FlightInfoServiceImpl flightInfoService;

    @Resource
    private UploadAndDownload uploadAndDownload;

    @Resource
    private SmsService smsService;

    @Resource
    private YeePayService yeePayService;
    @Resource
    private FlightCompensateDaoImpl flightCompensateDao;
    @Override
    public Map<String, Object> getPaxAuthInfo(String idNo, String flightNo, String flightDate) {
        log.info("【旅客微信公众号领取】---旅客登陆验证-请求参数: 证件号：" + idNo + " ，航班号：" + flightNo + ",航班日期：" + flightDate);
        Map<String, Object> dataMap = new HashMap<>(16);
        String[] idNos = idNo.split(",");

        List<AuthReceiveInfoVo> authReceiveInfoVoList = getPaxInfo(idNo, flightNo, flightDate);
        //校验是否存在旅客数据
        if (authReceiveInfoVoList.size() <= 0) {
            throw new BusinessException(MessageCode.AUTH_PAX_FAIL.getCode());
        }
        //校验领取次数限制
        StringBuffer paxLimitReceiveSb=new StringBuffer();
        for(AuthReceiveInfoVo authReceiveInfoVo:authReceiveInfoVoList){
            authReceiveInfoVo.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, authReceiveInfoVo.getTelephone()));
            if(authReceiveInfoVo.getReceiveCount()>=authReceiveInfoVo.getLimitCount()){
                paxLimitReceiveSb.append(authReceiveInfoVo.getPaxName());
                paxLimitReceiveSb.append(":"+authReceiveInfoVo.getIdNo1()).append(";");
                //冻结旅客
                String[] orderIds=authReceiveInfoVo.getOrderId().split(",");
                for(String orderId:orderIds){
                    flightCompensateService.updatePaxStatus(new String[]{authReceiveInfoVo.getPaxId()}, "1", orderId);
                }

            }
        }
        if(paxLimitReceiveSb.length()>0){
            throw new BusinessException(MessageCode.PAX_RECEIVE_LIMIT.getCode(),new String[]{paxLimitReceiveSb.toString()});
        }
        if (idNos.length != authReceiveInfoVoList.size()) {
            throw new BusinessException(MessageCode.PAX_ACT_RESUBMIT.getCode());
        }
        if (authReceiveInfoVoList.size() > 0) {
            dataMap.put("paxInfo", authReceiveInfoVoList);
            FlightInfoListForm flightInfoListForm = new FlightInfoListForm();
            flightInfoListForm.setFlightNum(flightNo);
            flightInfoListForm.setFlightDate(flightDate.replaceAll("-", "/"));
            OriginalSegmentView originalSegmentView = flightInfoService.getOriginalSegment(flightInfoListForm);
            Map<String, Object> flightInfo = new HashMap<>(16);
            flightInfo.put("segment", originalSegmentView.getSegment());
            flightInfo.put("flightNo", flightNo);
            flightInfo.put("flightDate", flightDate);
            flightInfo.put("std", originalSegmentView.getStd());
            flightInfo.put("sta", originalSegmentView.getSta());
            dataMap.put("flightInfo", flightInfo);
        } else {
            throw new BusinessException(MessageCode.AUTH_PAX_FAIL.getCode());
        }
        return dataMap;
    }

    /**
     * Title：saveApplyOrder <br>
     * Description：普通申领---保存申领单信息<br>
     * author：王建文 <br>
     * date：2020-3-11 14:43 <br>
     *
     * @param applyInfoDto 申领信息
     * @param applyCode    申领单号
     * @return
     */
    private void saveApplyOrder(ApplyInfoDto applyInfoDto, String applyCode, String paxCount, String applyWay, String status) {
        ApplyOrderInfo applyOrderInfo = new ApplyOrderInfo();
        BeanUtils.copyProperties(applyInfoDto, applyOrderInfo);
        applyOrderInfo.setApplyCode(applyCode);
        applyOrderInfo.setApplyWay(applyWay);
        applyOrderInfo.setStatus(status);
        applyOrderInfo.setApplyStatus(status);
        applyOrderInfo.setAuditTime(new Date());
        applyOrderInfo.setCreateTime(new Date());
        applyOrderInfo.setPayStatus("0");
        applyOrderInfo.setQuickPay("0");
        if (StringUtils.isNotBlank(applyOrderInfo.getImgUrl())) {
            try {
                String imgUrl = uploadAndDownload.saveImg(applyOrderInfo.getImgUrl());
                applyOrderInfo.setImgUrl(imgUrl);
            } catch (Exception e) {
                throw new BusinessException(MessageCode.PAX_IMG_FAIL.getCode());
            }

        }
        applyOrderInfo.setApplyCustNum(paxCount);
        EncryptFieldAop.manualEncrypt(applyOrderInfo);
        applyOrderDao.save(applyOrderInfo);
    }

    /**
     * Title：saveApplyPax <br>
     * Description：普通申领--保存旅客信息<br>
     * author：王建文 <br>
     * date：2020-3-11 14:56 <br>
     *
     * @param applyInfoDto 申领信息
     * @param applyCode    申领单号
     * @return
     */
    private void saveApplyPax(ApplyInfoDto applyInfoDto, String applyCode) {
        ApplyPaxInfo applyPaxInfo = new ApplyPaxInfo();
        applyPaxInfo.setApplyCode(applyCode);
        applyPaxInfo.setPaxId(applyInfoDto.getPaxId());
        applyPaxInfo.setStatus("1");
        applyPaxInfo.setActingRole("0");
        applyPaxInfo.setOrderId(applyInfoDto.getOrderId());
        applyPaxDao.save(applyPaxInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveActApplyInfo(ApplyParamDto applyParamDto) {
        // 校验当前赔偿单下的旅客是否有领取成功的记录
        if(VALIDATE_SUCCESS_PAYMENT.equals(redisService.get(VALIDATE_SUCCESS_PAYMENT))){
            validateRecordsUnderCompensationOrder(applyParamDto);
        }
        //1.判断是否重复点击
        ApplyQueryDto applyQueryDto = applyParamDto.getQueryParam();
        String flightNo = applyQueryDto.getFlightNo();
        String flightDate = applyQueryDto.getFlightDate();
        String[] idNos = applyQueryDto.getIdNo().split(",");

        List<AuthReceiveInfoVo> authReceiveInfoVoList = getPaxInfo(applyQueryDto.getIdNo(), flightNo, flightDate);
        if (authReceiveInfoVoList.size() <= 0 || idNos.length != authReceiveInfoVoList.size()) {
            throw new BusinessException(MessageCode.PAX_ACT_RESUBMIT.getCode());
        }
        ApplyInfoDto applyInfoDto = applyParamDto.getApplyInfo();
        if(!getPaxPayMoney(applyQueryDto).equals(applyInfoDto.getTransAmount())){
            throw new BusinessException(MessageCode.PAX_ACT_RESUBMIT.getCode());
        }
        applyInfoDto.setTelephone(AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, applyInfoDto.getTelephone()));
        //判断支付方式银联开启实名认证
        if (UNION_PAY.equals(applyInfoDto.getGetMoneyWay())) {
            //调用实名认证方法
            String result=authUnionPay(applyInfoDto);
            if (!SUCCESS.equals(result)) {
                throw new BusinessException(MessageCode.PAX_UNIONPAY_AUTH_FAIL.getCode(),new String[]{result});
            }
        }
        //重新计算旅客领取金额设值
        int transAmount = 0;
        String payMoney = getPaxPayMoney(applyQueryDto);
        transAmount += Integer.valueOf(payMoney);
        applyInfoDto.setTransAmount(String.valueOf(transAmount));
        List<ApplyPaxInfoDto> paxInfoList = applyParamDto.getPaxInfo();
        int paxCount = 0;
        //申领人数+携带婴儿数量
        for (ApplyPaxInfoDto pax : paxInfoList) {
            //成人携带婴儿
            if (StringUtils.isBlank(pax.getIsChild()) && StringUtils.isNotBlank(pax.getIsInfant())) {
                paxCount++;
            }
        }
        String applyCode = seedIdUtil.getId();
        //1.保存申领单信息
        saveApplyOrder(applyInfoDto, applyCode, String.valueOf(paxInfoList.size() + paxCount), "1", "0");
        log.info("【代人领取】代人领取申领单入库，申领单号【{}】", applyCode);
        //保存代领旅客信息
        List<ApplyPaxInfo> savePaxInfoList = new ArrayList<>();
        ApplyPaxInfo applyPaxInfo = null;
        for (ApplyPaxInfoDto paxInfo : paxInfoList) {
            applyPaxInfo = new ApplyPaxInfo();
            paxInfo.setStatus("0");
            paxInfo.setActingRole("1");
            paxInfo.setApplyCode(applyCode);
            if (StringUtils.isNotBlank(paxInfo.getImgUrl())) {
                try {
                    String paxImg = uploadAndDownload.saveImg(paxInfo.getImgUrl());
                    paxInfo.setImgUrl(paxImg);
                } catch (Exception e) {
                    throw new BusinessException(MessageCode.PAX_IMG_FAIL.getCode());
                }
            }
            BeanUtils.copyProperties(paxInfo, applyPaxInfo);
            savePaxInfoList.add(applyPaxInfo);
            //更新旅客领取状态
            paxReceiveDao.updatePaxReceiveStatusBak(applyInfoDto.getGetMoneyWay(), "1", "2", paxInfo.getPaxId(), paxInfo.getOrderId());
            log.info("【代人领取】代人领取修改赔偿单上的旅客信息，申领单号【{}】,赔偿单号【{}】, 申领人姓名【{}】, 当前代领人旅客pax_id【{}】,旅客账号（openid或者银行卡号）【{}】", applyCode,
                    paxInfo.getOrderId(), applyInfoDto.getApplyUser(), paxInfo.getPaxId(), applyInfoDto.getGetMoneyAccount());
        }
        List<ApplyPaxInfo> resultPaxInfoList = new ArrayList<>();
        for (ApplyPaxInfo paxInfo : savePaxInfoList) {
            List<String> paxIdList = Arrays.asList(paxInfo.getPaxId().split(","));
            List<String> orderIdList = Arrays.asList(paxInfo.getOrderId().split(","));
            if (paxIdList.size()!= orderIdList.size()){
                throw new BusinessException(MessageCode.PAX_NORMAL_SUBMIT.getCode());
            }
            for (int i = 0; i < paxIdList.size(); i++) {
                ApplyPaxInfo api = new ApplyPaxInfo();
                BeanUtils.copyProperties(paxInfo, api);
                api.setPaxId(paxIdList.get(i));
                api.setOrderId(orderIdList.get(i));
                List<DpPaxInfoVo> dpPaxInfo = paxReceiveDao.findDpPaxInfo(paxIdList.get(i), orderIdList.get(i));
                if (dpPaxInfo != null && dpPaxInfo.size() != 1) {
                    throw new BusinessException(MessageCode.PAX_NORMAL_SUBMIT.getCode());
                }
                resultPaxInfoList.add(api);
            }
        }
        log.info("【代人领取】代人领取初始集合:{} \n 代人领取转换后集合:{}", JSONUtil.toJsonStr(savePaxInfoList),JSONUtil.toJsonStr(resultPaxInfoList));
        applyPaxDao.saveAll(resultPaxInfoList);
    }

    /**
     * @title isApplyValidateCodePass
     * @description 申领记录查询-判断是否已通过短信验证码
     * <AUTHOR>
     * @date 2024/10/31 10:02
     * @param applyQueryDto
     * @return void
     */
    private void isApplyValidateCodePass(ApplyQueryDto applyQueryDto){
        //判断是否已验证过短信
        ApplyValidateCodeDto validateCodeDto = new ApplyValidateCodeDto();
        BeanUtils.copyProperties(applyQueryDto,validateCodeDto);
        String validateCodeResultKey = getApplyValidateCodeResultKey(validateCodeDto);
        Object validateResult = redisService.get(validateCodeResultKey);
        log.info("申领记录查询，是否已验证过手机验证码key:" + validateCodeResultKey+",验证结果："+ validateResult);
        if(validateResult == null || !(Boolean) validateResult){
            throw new BusinessException(MessageCode.PAX_TELEPHONE_VALIDATE_FAIL.getCode());
        }
    }

    @Override
    public List<Map<String, Object>> getPaxApplyInfoValidateCode(ApplyQueryDto applyQueryDto) {
        //判断是否已验证过短信
        isApplyValidateCodePass(applyQueryDto);
        return getPaxApplyInfo(applyQueryDto);
    }

    /**
     * @title getPaxApplyInfo
     * @description 【现金协助】 和 【旅客端-领取查询】都在调这个方法
     * <AUTHOR>
     * @date 2025/1/14 15:59
     * @param applyQueryDto
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     */
    @Override
    public List<Map<String, Object>> getPaxApplyInfo(ApplyQueryDto applyQueryDto) {

        List<Map<String, Object>> showPaxList = new ArrayList<>();
        List<ApplyPaxInfoVo> applyPaxInfoVoList = paxReceiveDao.selectPaxApplyInfo(applyQueryDto);
        if (applyPaxInfoVoList.size() > 0) {
            // 以旅客id分组计算金额，同一航班赔偿多次
            List<String> paxIdsList = new ArrayList<>();
            for (ApplyPaxInfoVo paxInfoVo : applyPaxInfoVoList) {
                paxIdsList.add(paxInfoVo.getPaxId());
            }
            // 去掉重复的旅客id
            HashSet hPaxIds = new HashSet(paxIdsList);
            paxIdsList.clear();
            paxIdsList.addAll(hPaxIds);
            Map<String, List<ApplyPaxInfoVo>> newDataMap = new HashMap<>();
            for (int i = 0; i < paxIdsList.size(); i++) {
                List<ApplyPaxInfoVo> paxInfoDtoList1 = new ArrayList<>();
                for (ApplyPaxInfoVo dpPaxInfoDto : applyPaxInfoVoList) {
                    if (paxIdsList.get(i).equals(dpPaxInfoDto.getPaxId())) {
                        paxInfoDtoList1.add(dpPaxInfoDto);
                    }
                }
                newDataMap.put(paxIdsList.get(i), paxInfoDtoList1);
            }
            // 重新显示新的列表数据
            List<ApplyPaxInfoVo> showPaxInfoList = new ArrayList<>();
            for (String key : newDataMap.keySet()) {
                List<ApplyPaxInfoVo> paxInfoDtoList2 = newDataMap.get(key);
                if (paxInfoDtoList2.size() == 1) {
                    ApplyPaxInfoVo paxInfoDto = paxInfoDtoList2.get(0);
                    if (StringUtils.isNotBlank(paxInfoDto.getBabyName()) && StringUtils.isBlank(paxInfoDto.getIsChild())) {
                        // 计算婴儿赔偿金额
                        String classType = "1";
                        // 公务舱
                        if (Arrays.asList(B_CLASS_TYPE).contains(paxInfoDto.getSubClass())) {
                            classType = "2";
                        }
                        CompensateInfoVo compensateInfo = flightCompensateService.getCompensateInfo(paxInfoDto.getOrderId(), classType);
                        int cspNum = compensateInfo.getCpsNum();
                        int babyStd = compensateInfo.getBabyStd();
                        Double babyPayCount = Double.valueOf(cspNum) * (Double.valueOf(babyStd) / 100);
                        paxInfoDto.setBabyPay(new DecimalFormat("#").format(babyPayCount));
                    }
                    // 计算未领取金额
                    if ("0".equals(paxInfoDto.getReceiveStatus()) && "0".equals(paxInfoDto.getSwitchOff())) {
                        paxInfoDto.setUnreceivedMoney(String.valueOf(paxInfoDto.getCurrentAmount()));
                    }
                    if ("1".equals(paxInfoDto.getReceiveStatus())) {
                        paxInfoDto.setReceivedMoney(String.valueOf(paxInfoDto.getCurrentAmount()));
                    }
                    if (null == paxInfoDto.getUnreceivedMoney()) {
                        paxInfoDto.setUnreceivedMoney("0");
                    }
                    if ("0".equals(paxInfoDto.getSwitchOff())) {
                        paxInfoDto.setTotalAmount(String.valueOf(paxInfoDto.getCurrentAmount()));
                    } else {
                        paxInfoDto.setTotalAmount("0");
                    }
                    showPaxInfoList.add(paxInfoDto);
                }
                if (paxInfoDtoList2.size() > 1) {
                    ApplyPaxInfoVo paxInfo = paxInfoDtoList2.get(0);
                    int totalAmount = 0;
                    int babyPayCount = 0;
                    int unreceivedMoney = 0;
                    int receivedMoney = 0;
                    // 计算未领取金额和赔付单号
                    String orderIds = "";
                    for (ApplyPaxInfoVo paxInfoDto1 : paxInfoDtoList2) {
                        if ("0".equals(paxInfoDto1.getSwitchOff())) {
                            totalAmount += Integer.valueOf(paxInfoDto1.getCurrentAmount());
                        }
                        if ("0".equals(paxInfoDto1.getReceiveStatus()) && "0".equals(paxInfoDto1.getSwitchOff())) {
                            unreceivedMoney += Integer.valueOf(paxInfoDto1.getCurrentAmount());
                            orderIds += paxInfoDto1.getOrderId() + ",";
                        }

                        if ("1".equals(paxInfoDto1.getReceiveStatus())) {
                            receivedMoney += Integer.valueOf(paxInfoDto1.getCurrentAmount());
                        }
                        if (StringUtils.isNotBlank(paxInfoDto1.getBabyName()) && StringUtils.isBlank(paxInfoDto1.getIsChild()) &&
                                "0".equals(paxInfoDto1.getSwitchOff())) {
                            // 计算婴儿赔偿金额
                            // 计算婴儿赔偿金额
                            String classType = "1";
                            // 公务舱
                            if (Arrays.asList(B_CLASS_TYPE).contains(paxInfoDto1.getSubClass())) {
                                classType = "2";
                            }
                            CompensateInfoVo compensateInfo = flightCompensateService.getCompensateInfo(paxInfoDto1.getOrderId(), classType);
                            int cspNum = compensateInfo.getCpsNum();
                            int babyStd = compensateInfo.getBabyStd();
                            Double babyPay = Double.valueOf(cspNum) * (Double.valueOf(babyStd) / 100);
                            babyPayCount += babyPay;
                        }
                    }
                    if (StringUtils.isNotBlank(orderIds)) {
                        paxInfo.setOrderId(orderIds);
                    }
                    paxInfo.setUnreceivedMoney(String.valueOf(unreceivedMoney));
                    paxInfo.setBabyPay(new DecimalFormat("#").format(babyPayCount));
                    paxInfo.setTotalAmount(String.valueOf(totalAmount));
                    paxInfo.setReceivedMoney(String.valueOf(receivedMoney));
                    showPaxInfoList.add(paxInfo);
                }
            }
            //处理旅客数据显示字段
            ApplyPaxInfoVo applyPaxInfoVo = showPaxInfoList.get(0);
            if (StringUtils.isBlank(applyQueryDto.getFlightNo()) || StringUtils.isBlank(applyQueryDto.getFlightDate())) {
                applyQueryDto.setFlightNo(applyPaxInfoVo.getFlightNo());
                applyQueryDto.setFlightDate(applyPaxInfoVo.getFlightDate());
            }
            CashFlightInfoVo cashFlightInfoVo = cashReceiveDao.getDelayFlightInfo(applyQueryDto.getFlightNo(), applyQueryDto.getFlightDate()).get(0);
            Map<String, Object> flightInfoMap = new HashMap<>();
            flightInfoMap.put("flightNo", cashFlightInfoVo.getFlightNo());
            flightInfoMap.put("flightDate", cashFlightInfoVo.getFlightDate());
            flightInfoMap.put("segment", cashFlightInfoVo.getSegment());
            flightInfoMap.put("sta", cashFlightInfoVo.getSta());
            flightInfoMap.put("std", cashFlightInfoVo.getStd());
            flightInfoMap.put("acType", cashFlightInfoVo.getAcType());
            for (ApplyPaxInfoVo paxInfoVo : showPaxInfoList) {
                Map<String, Object> map = new HashMap<>();
                map.put("paxId", paxInfoVo.getPaxId());
                map.put("paxName", paxInfoVo.getPaxName());
                map.put("idNo", paxInfoVo.getIdNo());
                map.put("idType", paxInfoVo.getIdType());
                map.put("tktNo", paxInfoVo.getTktNo());
                map.put("segment", paxInfoVo.getSegment());
                if(StringUtils.isNotEmpty(paxInfoVo.getTelephone())){
                    paxInfoVo.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, paxInfoVo.getTelephone()));
                }
                map.put("telephone", paxInfoVo.getTelephone());
                map.put("totalAmount", paxInfoVo.getTotalAmount());
                map.put("unreceivedMoney", paxInfoVo.getUnreceivedMoney());
                map.put("sex", paxInfoVo.getSex());
                map.put("babyName", paxInfoVo.getBabyName());
                map.put("isInfant", paxInfoVo.getIsInfant());
                map.put("orderId", paxInfoVo.getOrderId());
                map.put("isChild", paxInfoVo.getIsChild());
                map.put("receiveCount", paxInfoVo.getReceiveCount());
                map.put("flightInfo", flightInfoMap);
                map.put(
                        "orderInfo",
                        cashReceiveDao
                                .getPaxServiceOrder(cashFlightInfoVo.getFlightNo(), cashFlightInfoVo.getFlightDate(), paxInfoVo.getPaxId()));
                showPaxList.add(map);
            }
        }
        return showPaxList;
    }

    @Override
    public Map<String, Object> getApplyRecordInfo(String paxId) {
        log.info("---------------根据旅客id获取领取信息----请求参数[{}]",paxId);
        Map<String, Object> dataMap = new HashMap<>();
        if(StringUtils.isBlank(paxId)){
            return dataMap;
        }
        //1.根据旅客id获取旅客信息
        ContentIdxForm contentIdxForm = new ContentIdxForm(paxId);
        Object object = traceService.getPsgByIdx(contentIdxForm);
        PaxParseDto parseObject = JSON.parseObject(object.toString(), PaxParseDto.class);
        PaxInfoParseVo paxInfoParseVo = parseObject.getDataList().get(0);
        PassengerInfo paxInfo = new PassengerInfo();
        paxInfo.setPaxId(paxInfoParseVo.getIdx());
        paxInfo.setPaxName(paxInfoParseVo.getPsgName());
        paxInfo.setIdType(paxInfoParseVo.getIdType());
        paxInfo.setIdNo(paxInfoParseVo.getIdNum());
        paxInfo.setTktNo(paxInfoParseVo.getEtNum());
        paxInfo.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, paxInfoParseVo.getPhone()));
        paxInfo.setIsChild(paxInfoParseVo.getIsChild());
        paxInfo.setBabyName(paxInfoParseVo.getIsInfant());
        paxInfo.setIsInfant(paxInfoParseVo.getIsInfant());
        int totalMoney = 0;
        int unreceivedMoney = 0;
        int receivedMoney = 0;
        List<Map<String, Object>> paxOrderList = getPaxOrderInfo(paxId, paxInfoParseVo.getFlightNum(), paxInfoParseVo.getFlightDate());
        for (Map<String, Object> map : paxOrderList) {
            totalMoney += Integer.valueOf(map.get("totalMoney").toString());
            if (null != map.get("receivedMoney")) {
                receivedMoney += Integer.valueOf(map.get("receivedMoney").toString());
            }
            if (null != map.get("unReceivedMoney")) {
                unreceivedMoney += Integer.valueOf(map.get("unReceivedMoney").toString());
            }
        }
        List<ApplyRecordInfoVo> applyRecordInfoVoList = paxReceiveDao.getApplyRecordInfo(paxId);
        List<ApplyRecordInfoVo> showApplyRecordInfoVoList = new ArrayList<>();
        if (applyRecordInfoVoList.size() > 0) {
            for (ApplyRecordInfoVo applyRecordInfoVo : applyRecordInfoVoList) {
                if ("代领".equals(applyRecordInfoVo.getReceiveChannel()) && "1".equals(applyRecordInfoVo.getStatus()) &&
                        StringUtils.isNotBlank(applyRecordInfoVo.getQuickPay()) && !"1".equals(applyRecordInfoVo.getQuickPay())) {
                    applyRecordInfoVo.setStatus("0");
                }
                //审核旅客信息
                List<ActApplyPaxInfoVo> actApplyPaxInfoVoList = paxReceiveDao.getActApplyPaxInfoByApplyCode(applyRecordInfoVo.getApplyCode());
                //判断如果未通过展示拒绝原因
                if ("2".equals(applyRecordInfoVo.getStatus())) {
                    //处理拒绝原因
                    StringBuffer refuseSb = new StringBuffer();
                    for (ActApplyPaxInfoVo actApplyPaxInfoVo : actApplyPaxInfoVoList) {
                        if (StringUtils.isNotBlank(actApplyPaxInfoVo.getRefuseRemark())) {
                            ActApplyPaxInfoVo paxInfo1 = paxReceiveDao.getPaxInfo(actApplyPaxInfoVo.getPaxId());
                            refuseSb.append(paxInfo1.getPaxName()).append(":").append(actApplyPaxInfoVo.getRefuseRemark()).append(";");
                        }
                    }
                    if (StringUtils.isNotBlank(applyRecordInfoVo.getAuditRemark())) {
                        applyRecordInfoVo.setAuditRemark(
                                applyRecordInfoVo.getApplyUser() + ":" + applyRecordInfoVo.getAuditRemark() + ";" + refuseSb.toString());
                    } else {
                        applyRecordInfoVo.setAuditRemark(refuseSb.toString());
                    }

                }
                //处理图片
//                String imgUrl = applyRecordInfoVo.getImgUrl() + ",";
//                for (ActApplyPaxInfoVo actApplyPaxInfoVo : actApplyPaxInfoVoList) {
//                    if (StringUtils.isNotBlank(actApplyPaxInfoVo.getImgUrl())) {
//                        imgUrl += actApplyPaxInfoVo.getImgUrl() + ",";
//                    }
//                }
                applyRecordInfoVo.setImgUrl(applyRecordInfoVo.getImgUrl());
                applyRecordInfoVo.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, applyRecordInfoVo.getTelephone()));
                showApplyRecordInfoVoList.add(applyRecordInfoVo);
            }
        }
        dataMap.put("totalMoney", totalMoney);
        dataMap.put("unreceivedMoney", unreceivedMoney);
        dataMap.put("receivedMoney", receivedMoney);
        dataMap.put("applyInfo", showApplyRecordInfoVoList);
        dataMap.put("flightInfo", getFlightInfo(paxInfoParseVo.getFlightNum(), paxInfoParseVo.getFlightDate()));
        dataMap.put("paxInfo", paxInfo);
        dataMap.put("orderList", getPaxOrderInfo(paxId, paxInfoParseVo.getFlightNum(), paxInfoParseVo.getFlightDate()));
        return dataMap;
    }

    /**
     * Title：getFlightInfo <br>
     * Description：根据航班号航班日期获取航班信息<br>
     * author：王建文 <br>
     * date：2020-3-27 18:21 <br>
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @return
     */
    private Map<String, Object> getFlightInfo(String flightNo, String flightDate) {
        Map<String, Object> flightInfo = new HashMap<>();
        FlightInfoListForm form = new FlightInfoListForm();
        form.setFlightNum(flightNo);
        form.setFlightDate(flightDate.replaceAll("-", "/"));
        OriginalSegmentView originalSegmentView = flightInfoService.getOriginalSegment(form);
        flightInfo.put("segment", originalSegmentView.getSegment().replaceAll(" ", ""));
        flightInfo.put("std", originalSegmentView.getStd());
        flightInfo.put("sta", originalSegmentView.getSta());
        flightInfo.put("flightNo", flightNo);
        flightInfo.put("flightDate", originalSegmentView.getFlightDate().replaceAll("/", "-"));
        return flightInfo;
    }

    /**
     * Title：getPaxOrderInfo <br>
     * Description：获取旅客赔付单<br>
     * author：王建文 <br>
     * date：2020-3-27 17:13 <br>
     *
     * @param paxId      旅客id
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @return
     */
    private List<Map<String, Object>> getPaxOrderInfo(String paxId, String flightNo, String flightDate) {

        List<Map<String, Object>> showOrderList = new ArrayList<>();
        List<Map<String, Object>> orderList = flightCompensateDao.getPaxOrderInfo(paxId, flightNo, flightDate);
        for (Map<String, Object> map : orderList) {
            int receivedMoney = 0;
            int totalMoney = 0;
            Map<String, Object> data = new HashMap<>();
            data.put("payType", map.get("PAYTYPE"));
            totalMoney += Integer.valueOf(map.get("PAYMONEY").toString());
            if ("1".equals(map.get("RECEIVESTATUS"))) {
                receivedMoney += Integer.valueOf(map.get("PAYMONEY").toString());
            }
            if ("0".equals(map.get("RECEIVESTATUS"))) {
                data.put("unReceivedMoney", map.get("PAYMONEY"));
            }
            if ("2".equals(map.get("RECEIVESTATUS"))) {
                data.put("unReceivedMoney", "0");
            }
            data.put("receivedMoney", receivedMoney);
            data.put("totalMoney", totalMoney);
            data.put("paxName", map.get("PAXNAME"));
            data.put("isChild", map.get("ISCHILD"));
            data.put("isInfant", map.get("ISINFANT"));
            data.put("babyName", map.get("BABYNAME"));
            showOrderList.add(data);
        }
        return showOrderList;
    }

    @Override
    public List<ActApplyInfoVo> getActApplyInfo(ApplyQueryDto applyQueryDto) {
        //判断是否已验证过短信
        isApplyValidateCodePass(applyQueryDto);


        List<ActApplyInfoVo> showActApplyInfoVoList = new ArrayList<>();
        List<ActApplyInfoVo> actApplyInfoVoList = paxReceiveDao.getActApplyInfo(applyQueryDto);
        for (ActApplyInfoVo actApplyInfoVo : actApplyInfoVoList) {
            if ("1".equals(actApplyInfoVo.getPayStatus())) {
                actApplyInfoVo.setUnreceivedMoney("0");
            } else {
                actApplyInfoVo.setUnreceivedMoney(actApplyInfoVo.getTransAmount());
            }
            if ("代领".equals(actApplyInfoVo.getReceiveChannel()) && "1".equals(actApplyInfoVo.getStatus()) &&
                    StringUtils.isNotBlank(actApplyInfoVo.getQuickPay()) && !"1".equals(actApplyInfoVo.getQuickPay())) {
                actApplyInfoVo.setStatus("0");
            }
            showActApplyInfoVoList.add(actApplyInfoVo);
        }
        return showActApplyInfoVoList;
    }

    @Override
    public Map<String, Object> getActApplyInfoByApplyCode(String applyCode, String flightNo, String flightDate) {
        Map<String, Object> dataMap = new HashMap<>();
        //1.获取申领信息
        ApplyRecordInfoVo applyRecordInfoVo = paxReceiveDao.getApplyInfoByApplyCode(applyCode);
        if(ObjectUtils.isNotEmpty(applyRecordInfoVo) && StringUtils.isNotEmpty(applyRecordInfoVo.getTelephone())){
            applyRecordInfoVo.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, applyRecordInfoVo.getTelephone()));
        }
        if ("代领".equals(applyRecordInfoVo.getReceiveChannel()) && "1".equals(applyRecordInfoVo.getStatus()) &&
                StringUtils.isNotBlank(applyRecordInfoVo.getQuickPay()) && !"1".equals(applyRecordInfoVo.getQuickPay())) {
            applyRecordInfoVo.setStatus("0");
        }
        //审核旅客信息
        List<ActApplyPaxInfoVo> actApplyPaxInfoVoList = paxReceiveDao.getActApplyPaxInfoByApplyCode(applyCode);
        //判断如果未通过展示拒绝原因
        if ("2".equals(applyRecordInfoVo.getStatus())) {
            //处理拒绝原因
            StringBuffer refuseSb = new StringBuffer();
            for (ActApplyPaxInfoVo actApplyPaxInfoVo : actApplyPaxInfoVoList) {
                        if(StringUtils.isNotBlank(actApplyPaxInfoVo.getRefuseRemark())){
                            refuseSb.append(actApplyPaxInfoVo.getPaxName()).append(actApplyPaxInfoVo.getRefuseRemark()).append(";");
                        }
            }
            if(StringUtils.isNotBlank(applyRecordInfoVo.getAuditRemark())){
                refuseSb.append(applyRecordInfoVo.getApplyUser() + applyRecordInfoVo.getAuditRemark() + ";");
            }
            if(refuseSb.length()>0){
                applyRecordInfoVo.setAuditRemark(refuseSb.toString());
            }

        }
        List<Map<String, Object>> showPaxOrderInfoList = new ArrayList<>();
        int totalReceivedMoney = 0;
        int totalUnreceivedMoney = 0;
        for (ActApplyPaxInfoVo actApplyPaxInfoVo : actApplyPaxInfoVoList) {
            List<Map<String, Object>> paxOrderInfoList = getPaxOrderInfo(actApplyPaxInfoVo.getPaxId(), flightNo, flightDate);
            for (Map<String, Object> map : paxOrderInfoList) {
                totalReceivedMoney += Integer.valueOf(map.get("receivedMoney").toString());
                if (null == map.get("unReceivedMoney")) {
                    totalUnreceivedMoney += 0;
                } else {
                    totalUnreceivedMoney += Integer.valueOf(map.get("unReceivedMoney").toString());
                }

            }
            showPaxOrderInfoList.addAll(paxOrderInfoList);
        }
        //处理同一旅客多赔付单显示问题
        List<Map<String, Object>> paxOrderList = new ArrayList<>();
        List<String> paxList = new ArrayList<>();
        for (Map<String, Object> map : showPaxOrderInfoList) {
            paxList.add(map.get("paxName").toString());
        }
        //去掉重复的行李号
        HashSet hPaxList = new HashSet(paxList);
        paxList.clear();
        paxList.addAll(hPaxList);
        for (int i = 0; i < paxList.size(); i++) {
            Map<String, Object> dataMap1 = new HashMap<>();
            List<Map<String, Object>> orderList = new ArrayList<>();
            int totalMoney = 0;
            for (int j = 0; j < showPaxOrderInfoList.size(); j++) {
                Map orderMap = (Map<String, Object>) showPaxOrderInfoList.get(j);
                if (paxList.get(i).equals(orderMap.get("paxName").toString())) {
                    totalMoney += Integer.valueOf(orderMap.get("totalMoney").toString());
                    orderList.add(orderMap);
                }
            }
            dataMap1.put("paxName", paxList.get(i));
            dataMap1.put("orderList", orderList);
            dataMap1.put("totalMoney", totalMoney);
            dataMap1.put("babyName", orderList.get(0).get("babyName"));
            dataMap1.put("isChild", orderList.get(0).get("isChild"));
            paxOrderList.add(dataMap1);
        }
        List<ActApplyPaxInfoVo> paxInfoList = new ArrayList<>();
        for (ActApplyPaxInfoVo actApplyPaxInfoVo : actApplyPaxInfoVoList) {
            ActApplyPaxInfoVo paxInfo = paxReceiveDao.getPaxInfo(actApplyPaxInfoVo.getPaxId());
            actApplyPaxInfoVo.setBabyName(paxInfo.getBabyName());
            actApplyPaxInfoVo.setPaxName(paxInfo.getPaxName());
            actApplyPaxInfoVo.setIdType(paxInfo.getIdType());
            actApplyPaxInfoVo.setIsChild(paxInfo.getIsChild());
            actApplyPaxInfoVo.setIsInfant(paxInfo.getIsInfant());
            actApplyPaxInfoVo.setSex(paxInfo.getSex());
            actApplyPaxInfoVo.setIdNo(paxInfo.getIdNo());
            actApplyPaxInfoVo.setTktNo(paxInfo.getTktNo());
            paxInfoList.add(actApplyPaxInfoVo);
        }
        List<ApplyRecordInfoVo> applyRecordInfoVoList = new ArrayList<>();
        applyRecordInfoVoList.add(applyRecordInfoVo);
        dataMap.put("totalMoney", applyRecordInfoVo.getTransAmount());
        dataMap.put("unreceivedMoney",totalUnreceivedMoney );
        dataMap.put("receivedMoney", totalReceivedMoney);
        dataMap.put("orderList", paxOrderList);
        dataMap.put("flightInfo", getFlightInfo(flightNo, flightDate));
        dataMap.put("applyInfo", applyRecordInfoVoList);
        dataMap.put("paxInfo", paxInfoList);
        return dataMap;
    }

    //身份证前三后四脱敏
    public static String idEncrypt(String id) {
        if (StringUtils.isEmpty(id) || (id.length() < 8)) {
            return id;
        }
        return id.replaceAll("(?<=\\w{3})\\w(?=\\w{4})", "*");
    }

    /**
     * Title：getPaxInfo <br>
     * Description： 获取旅客领取信息<br>
     * author：王建文 <br>
     * date：2020-5-19 9:41 <br>
     *
     * @param idNo       证件号
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @return
     */
    private List<AuthReceiveInfoVo> getPaxInfo(String idNo, String flightNo, String flightDate) {
        String[] paxIdNos = idNo.split(",");
        Set<String> set = new HashSet<String>(Arrays.asList(paxIdNos));
        set.toArray(new String[set.size()]);
        List<AuthReceiveInfoVo> authReceiveInfoVoList = new ArrayList<>();
        for (String paxIdNo : paxIdNos) {
            paxIdNo=AesEncryptUtil.aesEncryptScgsi(paxIdNo);
            List<AuthInfoVo> authInfoVoList = paxReceiveDao.getAuthInfo(paxIdNo, flightNo, flightDate);
            int dataSize = authInfoVoList.size();
            if (dataSize > 0) {
                if (dataSize == 1) {
                    AuthReceiveInfoVo authReceiveInfoVo = new AuthReceiveInfoVo();
                    AuthInfoVo authInfoVo = authInfoVoList.get(0);
                    BeanUtils.copyProperties(authInfoVo, authReceiveInfoVo);
                    authReceiveInfoVo.setOrderId(authInfoVo.getOrderId());
                    authReceiveInfoVo.setPayMoney(String.valueOf(authInfoVo.getCurrentAmount()));
                    authReceiveInfoVo.setIdNo(idEncrypt(authInfoVo.getIdNo()));
                    authReceiveInfoVo.setIdNo1(authInfoVo.getIdNo());
                    authReceiveInfoVo.setIsInfant(authInfoVo.getIsInfant());
                    authReceiveInfoVoList.add(authReceiveInfoVo);
                    List<AuthPaxOrderInfoVo> authPaxOrderInfoVoList = new ArrayList<>();
                    AuthPaxOrderInfoVo authPaxOrderInfoVo = new AuthPaxOrderInfoVo();
                    BeanUtils.copyProperties(authInfoVo, authPaxOrderInfoVo);
                    authPaxOrderInfoVoList.add(authPaxOrderInfoVo);
                    authReceiveInfoVo.setOrderInfo(authPaxOrderInfoVoList);
                }
                if (dataSize > 1) {
                    AuthInfoVo authInfoVo = authInfoVoList.get(0);
                    AuthReceiveInfoVo authReceiveInfoVo = new AuthReceiveInfoVo();
                    BeanUtils.copyProperties(authInfoVo, authReceiveInfoVo);
                    authReceiveInfoVo.setIdNo(idEncrypt(authInfoVo.getIdNo()));
                    authReceiveInfoVo.setIdNo1(authInfoVo.getIdNo());
                    authReceiveInfoVo.setIsInfant(authInfoVo.getIsInfant());
                    //处理订单号和赔偿金额,赔付单信息
                    int currentCount = 0;
                    List<String> orderList = new ArrayList<>();
                    List<AuthPaxOrderInfoVo> authPaxOrderInfoVoList = new ArrayList<>();
                    Set<String> ticketNumSet = new HashSet<>();
                    for (AuthInfoVo authInfoVo1 : authInfoVoList) {
                        currentCount += Integer.valueOf(authInfoVo1.getCurrentAmount());
                        orderList.add(authInfoVo1.getOrderId());
                        AuthPaxOrderInfoVo authPaxOrderInfoVo = new AuthPaxOrderInfoVo();
                        BeanUtils.copyProperties(authInfoVo1, authPaxOrderInfoVo);
                        authPaxOrderInfoVo.setPaxId(authInfoVo1.getPaxId());
                        authPaxOrderInfoVoList.add(authPaxOrderInfoVo);
                        authReceiveInfoVo.setTktNo(authInfoVo1.getTktNo());
                        ticketNumSet.add(authInfoVo1.getTktNo());
                    }
                    authReceiveInfoVo.setTktNo(String.join(",", ticketNumSet));
                    authReceiveInfoVo.setOrderId(String.join(",",orderList));
                    authReceiveInfoVo.setPayMoney(String.valueOf(currentCount));
                    authReceiveInfoVo.setOrderInfo(authPaxOrderInfoVoList);
                    authReceiveInfoVoList.add(authReceiveInfoVo);
                }
            }
        }
        log.info("【旅客微信公众号领取-方法getPaxInfo】--获取旅客领取信息-请求参数: 证件号：" + idNo + " ，航班号：" + flightNo + ",航班日期：" + flightDate
                + "\n 查询结果：" + JSON.toJSONString(authReceiveInfoVoList));
        return authReceiveInfoVoList;
    }

    /**
     * Title：authUnionPay <br>
     * Description：申领银联信息验证<br>
     * author：王建文 <br>
     * date：2020-4-28 10:16 <br>
     *
     * @param applyInfoDto 申领信息
     * @return
     */
    public String authUnionPay(ApplyInfoDto applyInfoDto) {
        YeeAuthentication yeeAuthentication = new YeeAuthentication();
        String orderId = yeePayService.handlePayOrderId();
        yeeAuthentication.setOrderId(orderId);
        yeeAuthentication.setTransactionType(YeeTransactionType.AUTHENTICATION_SEND);
        yeeAuthentication.setMobilePhone(applyInfoDto.getTelephone());
        yeeAuthentication.setBankCardNo(applyInfoDto.getGetMoneyAccount());
        yeeAuthentication.setName(applyInfoDto.getApplyUser());
        yeeAuthentication.setIdNo(applyInfoDto.getIdNo());
        YeeAuthenticationResult yeeAuthenticationResult = yeePayService.authentication(yeeAuthentication, YeeAuthenticationResult.class);
        log.info("yee-【前端传入实名认证请求参数：】{},-实名认证结果：{}", yeeAuthentication.toString(), yeeAuthenticationResult.toString());
        if (SUCCESS.equals(yeeAuthenticationResult.getStatus()) && SUCCESS.equals(yeeAuthenticationResult.getState())) {
            return SUCCESS;
        }
        return yeeAuthenticationResult.getMessage();
    }

    @Override
    public void getReceiveRandomCode(String telephone, String codeKey, String captcha) {
        telephone = AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, telephone);
        checkCaptcha(codeKey, captcha);
        String hasSendRandom = (String) redisService.get(PAX_TELEPHONE_RANDOMCODE + telephone);
        if (StringUtils.isNotBlank(hasSendRandom)) {
            throw new BusinessException(MessageCode.PAX_RANDOMCODE_IS_SEND.getCode());
        } else {
            //准备发送的验证码4位随机整数
            int random4 = (int) ((Math.random() * 9 + 1) * 1000);
            //调用短信发送接口，发送成功存redis
            SmsSendResult smsSendResult = smsService.smsSendResultObj(telephone, MESSAGE_TYPE_PREFIX+String.valueOf(random4)+MESSAGE_TYPE_SUFFIX);
            if (null == smsSendResult) {
                throw new BusinessException(MessageCode.PAX_RANDOMCODE_FAIL.getCode());
            } else {
                if ("0".equals(smsSendResult.getCode())) {
                    //存入redis,有效期5分钟默认
                    redisService.set(PAX_TELEPHONE_RANDOMCODE + telephone, String.valueOf(random4), SMS_EXPIRE_DATE);
                } else {
                    throw new BusinessException(MessageCode.PAX_RANDOMCODE_FAIL.getCode());
                }
            }
        }
    }


    private void checkCaptcha(String codeKey, String captcha){
        String tempCaptcha = (String) redisService.get(codeKey + UserEnum.REDIS_VERIFY_CODE.getValue());
        captcha = AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, captcha);
        if(StringUtils.isEmpty(captcha) || !captcha.equalsIgnoreCase(tempCaptcha)){
            log.error("旅客领取时对手机号进行校验,codeKey【{}】,captcha【{}】,redis中captcha【{}】",
                    codeKey, captcha, tempCaptcha);
            throw new BusinessException(MessageCode.SYS_CODE_ERROR.getCode());
        }
    }

    @Override
    public void validateAuthCode(String telephone, String randomCode) {
        randomCode = AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, randomCode);
        telephone = AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, telephone);
        String hasSendRandom = (String) redisService.get(PAX_TELEPHONE_RANDOMCODE + telephone);
        if (StringUtils.isBlank(hasSendRandom)) {
            throw new BusinessException(MessageCode.PAX_RANDOMCODE_EXPIRE.getCode());
        } else {
            if (!randomCode.equals(hasSendRandom)) {
                throw new BusinessException(MessageCode.PAX_RANDOMCODE_INPUT_FAIL.getCode());
            }
        }
    }

    @Override
    public void getReceiveRandomCode(ApplyValidateCodeDto dto) {
        //初始化校验手机验证码标识，为false未校验
        String validateCodeResultKey = getApplyValidateCodeResultKey(dto);
        redisService.set(validateCodeResultKey,false);

        Integer integer = paxReceiveDao.validateApplyInfoByApplyRecord(dto);
        if(integer == null  || integer ==0){
            throw new BusinessException(MessageCode.PAX_APPLY_TELEPHONE_VALIDATE_FAIL.getCode());
        }

        this.getReceiveRandomCode(dto.getTelephone(),dto.getCodeKey(),dto.getCaptcha());

    }

    @Override
    public void validateAuthCode(ApplyValidateCodeDto dto) {
        //验证图形验证码
        checkCaptcha(dto.getCodeKey(), dto.getCaptcha());
        String telephone = AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, dto.getTelephone());
        String randomCode = AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, dto.getRandomCode());
        String hasSendRandom = (String) redisService.get(PAX_TELEPHONE_RANDOMCODE + telephone);
        //校验手机短信验证码
        if (StringUtils.isBlank(hasSendRandom)) {
            throw new BusinessException(MessageCode.PAX_RANDOMCODE_EXPIRE.getCode());
        } else {
            if (!randomCode.equals(hasSendRandom)) {
                throw new BusinessException(MessageCode.PAX_RANDOMCODE_INPUT_FAIL.getCode());
            }
        }
        String validateCodeResultKey = getApplyValidateCodeResultKey(dto);
        //redis存校验成功标识，是否校验成功,有效期3小时
        redisService.set(validateCodeResultKey,true,60 * 60);
    }

    private String getApplyValidateCodeResultKey(ApplyValidateCodeDto dto){
//        标识组成由：航班号+航班日期+手机号+用户id
        String telephone = AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, dto.getTelephone());
        Authentication authentication = AuthenticationUtil.getAuthentication();
        Object principal = authentication.getCredentials();
        String userId = String.valueOf(principal);
        return APPLY_VALIDATE_CODE_RESULT+dto.getFlightNo()+dto.getFlightDate()+telephone+userId;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveNormalApplyInfo(ApplyParamDto applyParamDto) {
        // 校验当前赔偿单下的旅客是否有领取成功的记录
        if(VALIDATE_SUCCESS_PAYMENT.equals(redisService.get(VALIDATE_SUCCESS_PAYMENT))){
            validateRecordsUnderCompensationOrder(applyParamDto);
        }

        ApplyQueryDto applyQueryDto = applyParamDto.getQueryParam();
        List<AuthReceiveInfoVo> authReceiveInfoVoList = getPaxInfo(
                applyQueryDto.getIdNo(),
                applyQueryDto.getFlightNo(),
                applyQueryDto.getFlightDate());
        log.info("【本人领取】验证重复提交:" + applyQueryDto.getIdNo() + applyQueryDto.getFlightNo() + applyQueryDto.getFlightDate());
        if (authReceiveInfoVoList.size() <= 0) {
            throw new BusinessException(MessageCode.PAX_NORMAL_SUBMIT.getCode());
        } else {
            ApplyInfoDto applyInfoDto = applyParamDto.getApplyInfo();
            applyInfoDto.setTelephone(AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, applyInfoDto.getTelephone()));
            //判断支付方式银联开启实名认证
            if (UNION_PAY.equals(applyInfoDto.getGetMoneyWay())) {
                //调用实名认证方法
                log.info("【本人领取】银联实名认证:" + JSON.toJSONString(applyInfoDto));
                applyInfoDto.setIdNo(applyQueryDto.getIdNo());
                //调用实名认证方法
                String result=authUnionPay(applyInfoDto);
                if (!SUCCESS.equals(result)) {
                    throw new BusinessException(MessageCode.PAX_UNIONPAY_AUTH_FAIL.getCode(),new String[]{result});
                }
            }
            //重新计算旅客领取金额设值
            String payMoney = getPaxPayMoney(applyQueryDto);
            if(!payMoney.equals(applyInfoDto.getTransAmount())){
                log.info("【本人领取】后台重新计划金额异常，前端提交金额【{}】，重新计划金额【{}】", applyInfoDto.getTransAmount(), payMoney);
                throw new BusinessException(MessageCode.PAX_ACT_RESUBMIT.getCode());
            }
            applyInfoDto.setTransAmount(payMoney);
            String applyCode = seedIdUtil.getId();
            //1.保存申领单信息
            log.info("【本人领取】开始保存申领单信息:" + JSON.toJSONString(applyInfoDto) + "， 申领单号：" + applyCode);
            applyInfoDto.setIdNo(applyQueryDto.getIdNo());
            saveApplyOrder(applyInfoDto, applyCode, "1", "0", "1");
            log.info("【本人领取】保存申领单信息结束， 申领单号：" + applyCode);

            List<ApplyPaxInfoDto> paxInfoList = applyParamDto.getPaxInfo();
            List<ApplyPaxInfo> savePaxInfoList = new ArrayList<>();
            for (ApplyPaxInfo paxInfo : paxInfoList) {
                ApplyPaxInfo applyPaxInfo = new ApplyPaxInfo();

                applyPaxInfo.setApplyCode(applyCode);
                applyPaxInfo.setPaxId(paxInfo.getPaxId());
                applyPaxInfo.setStatus("1");
                applyPaxInfo.setActingRole("0");
                applyPaxInfo.setOrderId(paxInfo.getOrderId());
                savePaxInfoList.add(applyPaxInfo);
                // 更新旅客领取状态
                log.info("【本人领取】更新旅客领取状态和领取方式:旅客paxId【{}】,补偿单号【{}】", paxInfo.getPaxId(), applyInfoDto.getOrderId());
                paxReceiveDao.updatePaxReceiveStatusBak(applyInfoDto.getGetMoneyWay(), "0", "2",
                        paxInfo.getPaxId(), applyInfoDto.getOrderId());
                log.info("【本人领取】更新旅客领取状态和领取方式结束");
                log.info("【本人领取】保存提交信息结束:" + JSON.toJSONString(applyInfoDto));
            }
            log.info("【本人领取】保存申领旅客信息:" + JSON.toJSONString(savePaxInfoList));
            applyPaxDao.saveAll(savePaxInfoList);
            log.info("【本人领取】保存申领旅客信息结束");
        }
    }

    /**
     * Title：getPaxPayMoney <br>
     * Description：获取旅客对应航班日期的赔付金额<br>
     * author：王建文 <br>
     * date：2020-5-18 17:00 <br>
     *
     * @param applyQueryDto 参数
     * @return
     */
    private String getPaxPayMoney(ApplyQueryDto applyQueryDto) {
        String idNos=applyQueryDto.getIdNo();
        String []idsNos1=idNos.split(",");
        String aesEncryptIdNos="";
        for(String idNO:idsNos1){
            idNO=AesEncryptUtil.aesEncryptScgsi(idNO);
            aesEncryptIdNos+=idNO+",";
        }
        int payMoney = 0;
        List<AuthInfoVo> authInfoVoList = paxReceiveDao
                .getAuthInfo(aesEncryptIdNos, applyQueryDto.getFlightNo(), applyQueryDto.getFlightDate());
        int dataSize = authInfoVoList.size();
        if (dataSize > 0) {
            for (AuthInfoVo authInfoVo1 : authInfoVoList) {
                payMoney += Integer.valueOf(authInfoVo1.getCurrentAmount());
            }
        }
        return String.valueOf(payMoney);
    }

    @Override
    public List<AuthReceiveInfoVo> getPaxInfoData(String idNo, String flightNo, String flightDate) {
        return getPaxInfo(idNo, flightNo, flightDate);
    }

    /**
     * @title PaxInfoServiceImpl.java
     * @description 校验当前赔偿单下的旅客是否有领取成功的记录(该方法可以一个sql搞定，写为多个方法是为了打印日志，查看当前数据库状态，有利于问题排查和定位)
     * <AUTHOR>
     * @date 2024/4/22 16:41
     * @param applyParamDto
     * @return void
     */
    public void validateRecordsUnderCompensationOrder(ApplyParamDto applyParamDto){
        // 拿到请求参数的里面的赔偿单ID，旅客paxID  去查询支付记录，如果有有（存在）则抛出异常

        /*
         * 获取到paxId和orderId，此处要分几种情况
         * 1、代领：A给BC代领，paxInfo集合里面会有三个paxId,orderId,此时应该用申领人的paxId来确定查询条件的paxId，orderId则应该包含这三个的in查询，
         * 2、本人领取：A只有一个补偿单，这里面分为补偿单里面只有一个A或者一人多座，这两种情况orderId只有一个，paxId也只有一个
         * 3、本人领取：A有5个补偿单，一次领取，这个时候paxId只有一个，orderId有多个，此种情况和情况一一样的逻辑
         * 4、代领：A给BC代领，有5个补偿单，orderId取哪个值，和逻辑1一样
         */

        //循环申领的旅客列表，查询是否已存在申领记录且已有支付成功的情况
        for(ApplyPaxInfoDto d:applyParamDto.getPaxInfo()){
            String paxId = d.getPaxId();
            //查询申领单号
            List<String> applyCodes = paxReceiveDao.findByPaxIdAndOrderId(paxId, d.getOrderId());
            log.info("创建申领单打款之前校验-旅客【{}】创建申领单前，查询该旅客paxId:【{}】在赔偿单id:【{}】下已有申领单号:【{}】", applyParamDto.getApplyInfo().getApplyUser() + applyParamDto.getApplyInfo().getIdNo(),
                    paxId, d.getOrderId(), JSONUtil.toJsonStr(applyCodes));

            if(ObjectUtils.isEmpty(applyCodes)){
                continue;
            }
            // 通过获取到的申领单号查询转账记录表
            List<AoTransRecord> records = paxReceiveDao.findByApplyCode(applyCodes);
            log.info("创建申领单打款之前校验-旅客【{}】创建申领单前，查询该旅客paxId:【{}】在赔偿单id:【{}】下已有申领单号:【{}】，通过这些申领单号查询支付转账记录表的数据为【{}】", applyParamDto.getApplyInfo().getApplyUser() + applyParamDto.getApplyInfo().getIdNo(),
                    paxId, d.getOrderId(), JSONUtil.toJsonStr(applyCodes), JSONUtil.toJsonStr(records));

            // 使用Stream API来计算支付状态为“已支付”的记录数量
            long paidCount = records.stream()
                    .filter(r -> PAY_STATUS_PAID.equals(r.getPayStatus()))
                    .count();

            // 检查数量是否大于0，并抛出异常
            if (paidCount > 0) {
                throw new BusinessException(MessageCode.PAYMENT_SUCCESS_RECORD_EXISTS_FOR_CLAIM.getCode());
            }
        }

    }
}