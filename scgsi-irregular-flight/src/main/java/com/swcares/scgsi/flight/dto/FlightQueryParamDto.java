package com.swcares.scgsi.flight.dto;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.dto <br>
 * Description：不正常航班查询接收前端参数实体 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月04日 15:47 <br>
 * @version v1.0 <br>
 */
@Data
public class FlightQueryParamDto {
    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 赔付类型 不正常航班赔偿0、异常行李1、超售旅客2、
     */
    private String payType;

    /**
     * 航班开始日期
     */
    private String startDate;
    /**
     * 航班结束日期
     */
    private String endDate;
    /**
     * 出发航站
     */
    private String orgCityAirp;

    /**
     * 到达航站
     */
    private String dstCityAirp;
    /**
     * 赔付单状态
     */
    private String status;
    /** 当前页数，默认为第一页 **/
    private int current = 1;

    /** 每页显示记录数，默认为10条 **/
    private int pageSize = 10;
}