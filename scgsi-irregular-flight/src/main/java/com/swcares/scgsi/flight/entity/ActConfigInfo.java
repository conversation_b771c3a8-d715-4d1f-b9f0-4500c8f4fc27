package com.swcares.scgsi.flight.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.flight.entity <br>
 * Description：代领审核配置信息 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月15日 10:24 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "DP_CONFIG_INFO")
@Data
public class ActConfigInfo {
    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    /**
     * 类型0缓冲时间1拒绝原因2行李箱尺寸配置
     */
    @Column(name = "TYPE")
    private String type;

    /**
     * 内容
     */
    @Column(name = "CONTENT")
    private String content;

    /**
     * 状态0激活1禁用
     */
    @Column(name = "STATUS")
    private String status;
    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    private String createUser;

    /**
     * 创建日期
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;
    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
}