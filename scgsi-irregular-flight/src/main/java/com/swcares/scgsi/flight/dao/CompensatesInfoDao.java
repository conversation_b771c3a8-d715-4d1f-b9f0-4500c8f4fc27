package com.swcares.scgsi.flight.dao;

import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.flight.entity.CompensatesInfo;

/**
 * ClassName：com.swcares.scgsi.flight.dao <br>
 * Description：赔付标准 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月03日 13:15 <br>
 * @version v1.0 <br>
 */
public interface CompensatesInfoDao extends BaseJpaDao<CompensatesInfo, String> {
    /**
     * Title：getCompensateInfoByOrderId <br>
     * Description： 根据赔付单id获取赔付标准信息<br>
     * author：王建文 <br>
     * date：2020-3-6 11:26 <br>
     * @param  orderId 赔付单号
     * @return CompensateInfo
     */
    CompensatesInfo getCompensateInfoByOrderId(String orderId);
}
