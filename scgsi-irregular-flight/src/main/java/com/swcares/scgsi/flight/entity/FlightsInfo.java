package com.swcares.scgsi.flight.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.flight.entity <br>
 * Description：保存航班信息实体 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月03日 11:04 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "DP_FLIGHT_INFO")
@Data
public class FlightsInfo {

    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    /**
     * 航班ID
     */
    @Column(name = "FLIGHT_ID")
    private String flightId;

    /**
     * 赔付单ID
     */
    @Column(name = "ORDER_ID")
    private String orderId;

    /**
     * 航班号
     */
    @Column(name = "FLIGHT_NO")
    private String flightNo;

    /**
     * 航班日期
     */
    @Column(name = "FLIGHT_DATE")
    private String flightDate;

    /**
     * 航段
     */
    @Column(name = "SEGMENT")
    private String segment;

    /**
     * 飞机号
     */
    @Column(name = "PLANE_CODE")
    private String planeCode;

    /**
     * 机型
     */
    @Column(name = "AC_TYPE")
    private String acType;

    /**
     * 预计起飞时间
     */
    @Column(name = "ETD")
    private String etd;

    /**
     * 计划起飞时间
     */
    @Column(name = "STD")
    private String std;

    /**
     * 计划到达时间
     */
    @Column(name = "STA")
    private String sta;

    /**
     * 延误原因
     */
    @Column(name = "LATE_REASON")
    private String lateReason;

    /**
     * 延误时长
     */
    @Column(name = "DELAY_TIME")
    private String delayTime;

    /**
     * 创建人
     */
    @Column(name = "CREATE_ID")
    private String createId;

    /**
     * 创建日期
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;
}