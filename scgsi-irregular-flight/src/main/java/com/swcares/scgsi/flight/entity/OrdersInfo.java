package com.swcares.scgsi.flight.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.flight.entity <br>
 * Description：赔付单信息 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月03日 11:26 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "DP_ORDER_INFO")
@Data
public class OrdersInfo {

    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;
    /**
     * 赔付单ID
     */
    @Column(name = "ORDER_ID")
    private String orderId;

    /**
     * 事故单号当前仅针对异常行李、超售旅客
     */
    @Column(name = "ACCIDENT_ID")
    private String accidentId;
    /**
     * 赔偿类型 不正常航班赔偿0、异常行李1、超售旅客2、
     */
    @Column(name = "PAY_TYPE")
    private int  payType;
    /**
     * 航班号
     */
    @Column(name = "FLIGHT_NO")
    private String flightNo;

    /**
     * 航班日期
     */
    @Column(name = "FLIGHT_DATE")
    private String flightDate;
    /**
     * 服务航站
     */
    @Column(name = "SERVICE_CITY")
    private String serviceCity;
    /**
     * 所选航段
     */
    @Column(name = "CHOICE_SEGMENT")
    private String  choiceSegment;

    @Column(name = "ALL_SEGMENT")
    private String  allSegment;
    /**
     * 备注
     */
    @Column(name = "REMARK")
    private String remark;
    /**
     * 服务单状态(0草稿、1审核中、2通过、3生效、4关闭,5未通过)
     */
    @Column(name = "STATUS")
    private String  status;
    /**
     * 航班ID
     */
    @Column(name = "FLIGHT_ID")
    private String flightId;
    /**
     * 有效期
     */
    @Column(name = "EXPIRY_DATE")
    private Date expiryDate;
    /**
     * 合计金额
     */
    @Column(name = "SUM_MONEY")
    private String  sumMoney;
    /**
     * 创建人(现有的申请人)
     */
    @Column(name = "CREATE_ID")
    private String createId;

    /**
     * 创建日期（现有的申请时间）
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;
    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
    /**
     * 关闭人
     */
    @Column(name = "CLOSE_USER")
    private String closeUser;

    /**
     * 关闭
     */
    @Column(name = "CLOSE_TIME")
    private Date closeTime;

    @Column(name = "AUTO_CREATE")
    private Boolean autoCreate;

    @Column(name = "APPLY_USER")
    private String applyUser;

    @Column(name = "APPLY_DATE")
    private Date applyDate;
}