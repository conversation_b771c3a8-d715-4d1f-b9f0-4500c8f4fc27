package com.swcares.scgsi.flight.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：申领记录旅客信息 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月12日 15:05 <br>
 * @version v1.0 <br>
 */
@Data
@EncryptionClassz
public class ApplyPaxInfoVo {
    /**
     * 旅客ID
     */
    private String paxId;
    /**
     * 旅客姓名
     */
    private String paxName;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号
     */
    @Encryption
    private String idNo;
    /**
     * 赔偿单号
     */
    private String orderId;
    /**
     * 航段
     */
    private String segment;
    /**
     * 当前赔付单对应领取金额
     */
    private int currentAmount;
    /**
     * 领取状态(0未领取,1已领取)
     */
    private String receiveStatus;
    /**
     * 子舱位
     */
    private String subClass;
    /**
     * 票号
     */
    private String tktNo;
    /**
     * 性别C儿童M男F女
     */
    private String sex;
    /**
     * 婴儿名字
     */
    private String babyName;
    /**
     * 联系电话
     */
    @Encryption
    private String telephone;
    /**
     * 婴儿赔偿金额
     */
    private String babyPay;
    /**
     * 总赔偿金额
     */
    private String totalAmount;
    /**
     * 已领金额
     */
    private String receivedMoney;
    /**
     * 未领取金额
     */
    private String unreceivedMoney;
    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 儿童标识
     */
    private String isChild;

    /**
     * 携带婴儿标识
     */
    private String isInfant;

    /**
     * 冻结标识1冻结
     */
    private String switchOff;

    /**
     * 领取次数
     */
    private String receiveCount;
}