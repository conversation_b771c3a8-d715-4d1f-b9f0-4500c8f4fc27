package com.swcares.scgsi.flight.service;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.flight.dto.ActAuditParamDto;
import com.swcares.scgsi.flight.dto.ActQueryParamDto;
import com.swcares.scgsi.flight.vo.ActAuditPaxInfoVo;
import com.swcares.scgsi.flight.vo.ActAuditRecordVo;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.service <br>
 * Description：代领审核service <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月16日 10:24 <br>
 * @version v1.0 <br>
 */
public interface ActAuditInfoService {
    /**
     * Title：getAuditInfoPage <br>
     * Description： 分页查询代领审核列表<br>
     * author：王建文 <br>
     * date：2020-3-16 10:25 <br>
     *
     * @param actQueryParamDto 参数接收
     * @return
     */
    public QueryResults getAuditInfoPage(ActQueryParamDto actQueryParamDto);

    /**
     * Title：getActApplyPaxInfo <br>
     * Description：获取代领旅客信息<br>
     * author：王建文 <br>
     * date：2020-3-16 14:15 <br>
     *
     * @param applyCode 申领单号
     * @return
     */
    public List<ActAuditPaxInfoVo> getActApplyPaxInfo(String applyCode);

    /**
     * Title：saveActAuditInfo <br>
     * Description： 保存代领审核信息<br>
     * author：王建文 <br>
     * date：2020-3-16 16:10 <br>
     *
     * @param actAuditParamDto 审核信息参数
     * @return
     */
    public void saveActAuditInfo(ActAuditParamDto actAuditParamDto);

    /**
     * Title：getLastAuditTime <br>
     * Description： 获取最新的审核时间<br>
     * author：王建文 <br>
     * date：2020-3-17 9:33 <br>
     *
     * @param applyCode 审核单
     * @return
     */
    public String getLastAuditTime(String applyCode);

    /**
     * Title：resetActAuditInfo <br>
     * Description： 代领审核单撤回<br>
     * author：王建文 <br>
     * date：2020-3-17 10:29 <br>
     *
     * @param applyCode 申领单号
     * @return
     */
    public void resetActAuditInfo(String applyCode);

    /**
     * Title：quickPay <br>
     * Description： 代领审核快速支付<br>
     * author：王建文 <br>
     * date：2020-3-17 11:11 <br>
     *
     * @param applyCode 申领单号
     * @return
     */
    public void quickPay(String applyCode);
    /**
     * Title：getActAuditRecordByApplyCode <br>
     * Description： 根据申领单号查询审核记录<br>
     * author：王建文 <br>
     * date：2020-3-17 14:23 <br>
     * @param  applyCode 申领单号
     * @return
     */
    public List<ActAuditRecordVo> getActAuditRecordByApplyCode(String applyCode);
    /**
     *
     * Title：updateActOrder <br>
     * Description：更新审核通过超过缓冲时间未支付的订单 <br>
     * author：王建文 <br>
     * date：2020年4月15日 下午3:02:38 <br>
     */
    public void updateActOrder();
    /**
     * Title：sendMessageToPax <br>
     * Description： 代领审核未通过短信通知用户<br>
     * author：王建文 <br>
     * date：2020-4-27 14:27 <br>
     * @param
     * @return
     */
    public void sendMessageToPax();
    /**
     * Title：getApplyInfoByApplyCode <br>
     * Description： 根据申领单号获取申领信息<br>
     * author：王建文 <br>
     * date：2020-5-8 16:53 <br>
     * @param  applyCode 申领单号
     * @return
     */
    public ActAuditPaxInfoVo getApplyInfoByApplyCode(String applyCode);
}
