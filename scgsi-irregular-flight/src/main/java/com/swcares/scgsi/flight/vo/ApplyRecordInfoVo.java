package com.swcares.scgsi.flight.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：申领记录查询 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月12日 16:42 <br>
 * @version v1.0 <br>
 */
@Data
@EncryptionClassz
public class ApplyRecordInfoVo {
    /**
     * 申领人
     */
    private String applyUser;

    /**
     * 申领证件号
     */
    @Encryption
    private String idNo;

    /**
     * 申领手机号
     */
    @Encryption
    private String telephone;

    /**
     * 申领金额
     */
    private String transAmount;

    /**
     * 领取方式（0微信，1银联，2现金）
     */
    private String receiveWay;

    /**
     * 领取账户
     */
    @Encryption
    private String getAccount;

    /**
     * 审核状态(0待审核、1已通过、2未通过)
     */
    private String status;

    /**
     * 支付状态(0未支付,1支付)
     */
    private String payStatus;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 到账时间
     */
    private String receiveTime;

    /**
     * 拒绝原因
     */
    private String auditRemark;

    /**
     * 领取方式
     */
    private String receiveChannel;

    /**
     * 快速支付标识
     */
    private String quickPay;
    private String applyCode;

    /**
     * 支付失败原因
     */
    private String transMsg;
}