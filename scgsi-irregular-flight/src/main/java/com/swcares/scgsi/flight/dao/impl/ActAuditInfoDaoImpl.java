package com.swcares.scgsi.flight.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.flight.dto.ActQueryParamDto;
import com.swcares.scgsi.flight.dto.ActUserAuditParamDto;
import com.swcares.scgsi.flight.vo.*;
import com.swcares.scgsi.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.dao.impl <br>
 * Description：代领审核自定义sql <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月19日 13:32 <br>
 * @version v1.0 <br>
 */
@Repository
public class ActAuditInfoDaoImpl {
    @Resource
    private BaseDAO baseDAO;

    /**
     * Title：getAuditInfoPage <br>
     * Description： 分页查询代领审核列表<br>
     * author：王建文 <br>
     * date：2020-3-16 10:25 <br>
     *
     * @param actQueryParamDto 参数接收
     * @return
     */
    public QueryResults getAuditInfoPage(ActQueryParamDto actQueryParamDto) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT DISTINCT(DAO.APPLY_CODE) AS applyCode,DAO.STATUS AS status,DAO.PAY_STATUS AS payStatus,DAO.APPLY_STATUS AS applyStatus, ");
        sql.append(" TO_CHAR(DAO.CREATE_TIME,'YYYY-MM-DD hh24:mi:ss') AS createTime,DAO.APPLY_USER AS applyUser, ");
        sql.append(" DAO.APPLY_CUST_NUM AS applyCustNum,DAO.TRANS_AMOUNT AS transAmount,DAO.TELEPHONE AS telephone, ");
        sql.append(" O.FLIGHT_NO AS flightNo,O.FLIGHT_DATE AS flightDate, ");
        sql.append(" DAO.GET_MONEY_WAY AS getMoneyWay,DAO.GET_MONEY_ACCOUNT AS getMoneyAccount, ");
        sql.append(" TO_CHAR(DAO.RECEIVE_TIME,'YYYY-MM-DD hh24:mi:ss') AS receiveTime,DAO.OPEN_BANK_NAME AS openBank,DAO.QUICK_PAY AS quickPay ");
        sql.append(" FROM DP_APPLY_ORDER DAO ");
        sql.append(" LEFT JOIN DP_APPLY_PAX DAP ON DAP.APPLY_CODE=DAO.APPLY_CODE ");
        sql.append(" LEFT JOIN DP_PAX_INFO DPI ON DPI.PAX_ID=DAP.PAX_ID ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ON O.ORDER_ID=DPI.ORDER_ID ");
        sql.append(" WHERE DAO.APPLY_WAY=1 ");
        String startDate = actQueryParamDto.getStartDate();
        String endDate = actQueryParamDto.getEndDate();
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND O.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }
        String applyCode = actQueryParamDto.getApplyCode();
        if (StringUtils.isNotBlank(applyCode)) {
            paramsMap.put("applyCode", applyCode);
            sql.append(" AND DAO.APPLY_CODE= :applyCode ");
        }
        String paxName=actQueryParamDto.getPaxName();
        if (StringUtils.isNotBlank(paxName)) {
            paramsMap.put("paxName", "%"+paxName+"%");
            sql.append(" and DPI.PAX_NAME like :paxName");
        }
        String flightNo = actQueryParamDto.getFlightNo();
        if (StringUtils.isNotBlank(flightNo)) {
            paramsMap.put("flightNo", flightNo);
            sql.append(" AND O.FLIGHT_NO= :flightNo ");
        }
        String queryType = actQueryParamDto.getQueryType();
        if (StringUtils.isNotBlank(queryType)) {
            if ("1".equals(queryType)) {
                sql.append(" AND DAO.STATUS !='0' ");
            }
            if ("0".equals(queryType)) {
                sql.append(" AND DAO.STATUS ='0' AND (DAO.APPLY_STATUS!='3' AND DAO.APPLY_STATUS!='4') ");
            }

        }
        if (StringUtils.isNotBlank(endDate) && StringUtils.isBlank(startDate)) {
            paramsMap.put("endDate", endDate);
            sql.append(" AND O.FLIGHT_DATE <= :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
            endDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND O.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }

        String applyStartDate = actQueryParamDto.getApplyStartDate();
        String applyEndDate = actQueryParamDto.getApplyEndDate();
        if (StringUtils.isNotBlank(applyEndDate) && StringUtils.isBlank(applyStartDate)) {
            paramsMap.put("applyEndDate", applyEndDate);
            sql.append(" AND TO_CHAR(DAO.CREATE_TIME,'YYYY-MM-DD') <= :applyEndDate ");
        }
        if (StringUtils.isNotBlank(applyStartDate) && StringUtils.isBlank(applyEndDate)) {
            applyEndDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
            paramsMap.put("applyEndDate", applyEndDate);
            paramsMap.put("applyStartDate", applyStartDate);
            sql.append(" AND TO_CHAR(DAO.CREATE_TIME,'YYYY-MM-DD')  BETWEEN :applyStartDate and :applyEndDate ");
        }
        if (StringUtils.isNotBlank(applyStartDate) && StringUtils.isNotBlank(applyEndDate)) {
            paramsMap.put("applyEndDate", applyEndDate);
            paramsMap.put("applyStartDate", applyStartDate);
            sql.append(" AND TO_CHAR(DAO.CREATE_TIME,'YYYY-MM-DD')  BETWEEN :applyStartDate and :applyEndDate ");
        }
        String applyUser = actQueryParamDto.getApplyUser();
        if (StringUtils.isNotBlank(applyUser)) {
            paramsMap.put("applyUser", "%" + applyUser + "%");
            sql.append(" AND  DAO.APPLY_USER LIKE:applyUser ");
        }
        String applyStatus = actQueryParamDto.getApplyStatus();
        if (StringUtils.isNotBlank(applyStatus)) {
            paramsMap.put("applyStatus", applyStatus);
            if("3".equals(applyStatus)||"4".equals(applyStatus)){
                sql.append("  AND DAO.APPLY_STATUS =:applyStatus ");
            }else {
                sql.append(" AND DAO.STATUS =:applyStatus ");
            }

        }
        String payStatus = actQueryParamDto.getPayStatus();
        if (StringUtils.isNotBlank(payStatus)) {
            paramsMap.put("payStatus", payStatus);
            sql.append("  AND DAO.PAY_STATUS=:payStatus ");
        }
        sql.append(" order by createTime DESC ");
        return baseDAO.findBySQLPage_comm(
                sql.toString(),
                actQueryParamDto.getCurrent(),
                actQueryParamDto.getPageSize(),
                paramsMap,
                ActAuditInfoVo.class);
    }

    /**
     * Title：getApplyInfoByApplyCode <br>
     * Description： 根据申领单号获取申领信息<br>
     * author：王建文 <br>
     * date：2020-3-19 13:38 <br>
     *
     * @param applyCode 申领单号
     * @return
     */
    public ActAuditPaxInfoVo getApplyInfoByApplyCode(String applyCode) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("applyCode", applyCode);
        sql.append(" SELECT DAO.APPLY_USER AS paxName,DAO.ID_NO AS idNo,DAO.APPLY_STATUS AS applyStatus, ");
        sql.append(" DAO.TELEPHONE AS telephone,DAO.APPLY_CODE AS applyCode, ");
        sql.append(" DAO.STATUS AS status,DAO.TRANS_AMOUNT AS payMoney,DAO.IMG_URL AS imgUrl,DAO.PAY_STATUS AS payStatus, ");
        sql.append(" get_dp_actOrder_latest_opinion(" + applyCode + ") AS auditRemark, ");
        sql.append("get_user_name(get_dp_actOrder_latest_auditor(" + applyCode + ")) AS lastAuditor, ");
        sql.append(" get_dp_actOrder_latest_time(" + applyCode + ") AS lastAuditTime");
        sql.append(" FROM DP_APPLY_ORDER DAO WHERE DAO.APPLY_CODE=:applyCode ");
        return baseDAO.findOneBySql(sql.toString(), paramsMap, ActAuditPaxInfoVo.class);
    }

    /**
     * Title：getActPaxInfoByApplyCode <br>
     * Description： 根据申领单号查询申领旅客信息<br>
     * author：王建文 <br>
     * date：2020-3-19 13:41 <br>
     *
     * @param applyCode,payStatus
     * @return
     */
    public List<ActAuditPaxInfoVo> getActPaxInfoByApplyCode(String applyCode, String payStatus) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("applyCode", applyCode);
        sql.append(" SELECT P.* FROM ( ");
        sql.append(" SELECT DPI.PAX_NAME AS paxName,DAP.PAX_ID AS paxId,DPI.SEGMENT AS segment,DPI.ID_TYPE AS idType, ");
        sql.append(" DPI.ID_NO AS idNo,DPI.TELEPHONE AS telephone,DPI.TKT_NO AS tktNo, ");
        sql.append(" NVL(get_dp_pax_actMoney(DAP.PAX_ID,DAP.ORDER_ID," + payStatus + "),0) AS payMoney, ");
        sql.append(" DAP.STATUS AS status,DAP.ACTING_ROLE AS actRole, ");
        sql.append(" CONCAT(DAP.AUDIT_REMARK ,nvl2(DAP.REMARK,';'||DAP.REMARK,DAP.REMARK)) AS auditRemark, ");
        sql.append(" DAP.IMG_URL AS imgUrl,DAP.APPLY_CODE AS applyCode, ");
        sql.append(" DAP.FREEZE_REMARK AS freezeRemark,DAP.FAILURE_REMARK AS failureRemark,  ");
        sql.append(" DAP.AUDIT_TIME AS lastAuditTime,get_user_name(DAP.AUDIT_USER) AS lastAuditor,  ");
        sql.append(" ROW_NUMBER() OVER(partition by DAP.PAX_ID ORDER by DPI.CREATE_TIME DESC) AS exitCount ");
        sql.append(" ,DOI.FLIGHT_NO flightNo,DOI.FLIGHT_DATE flightDate");
        sql.append(" FROM DP_APPLY_PAX DAP ");
        sql.append(" LEFT JOIN DP_PAX_INFO DPI ON DPI.PAX_ID=DAP.PAX_ID ");
        sql.append(" LEFT JOIN DP_ORDER_INFO DOI ON DOI.ORDER_ID  = DPI.ORDER_ID ");
        sql.append(" WHERE DAP.APPLY_CODE=:applyCode ");
        sql.append(" )P WHERE P.exitCount=1 ");
        return (List<ActAuditPaxInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, ActAuditPaxInfoVo.class);
    }

    /**
     * Title：updateApplyOrderInfo <br>
     * Description： 更新申领单信息<br>
     * author：王建文 <br>
     * date：2020-3-19 13:44 <br>
     *
     * @param applyCode,status
     * @return
     */
    public void updateApplyOrderInfo(String applyCode, String status, String applyStatus) {
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_APPLY_ORDER SET ");
        sql.append(" STATUS=?, ");
        sql.append(" APPLY_STATUS=?, ");
        Date auditDate = new Date();
        sql.append(" AUDIT_TIME=? ");
        sql.append(" WHERE APPLY_CODE=? ");
        baseDAO.batchUpdate(sql.toString(), status, applyStatus, auditDate, applyCode);
    }

    /**
     * Title：updateDpPaxInfoStatus <br>
     * Description： 更新旅客领取状态<br>
     * author：王建文 <br>
     * date：2020-3-19 13:46 <br>
     *
     * @param paxId，status
     * @return
     */
    public void updateDpPaxInfoStatus(String paxId, String status) {
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_PAX_INFO SET ");
        sql.append(" RECEIVE_STATUS=? ");
        sql.append(" WHERE PAX_ID=? AND RECEIVE_STATUS !='1'");
        baseDAO.batchUpdate(sql.toString(), status, paxId);
    }

    /**
     * Title：updatePaxAuditInfo <br>
     * Description： 更新申领旅客审核信息<br>
     * author：王建文 <br>
     * date：2020-3-19 13:47 <br>
     *
     * @param actUserAuditParam,currentUserId,auditTime
     * @return
     */
    public void updatePaxAuditInfo(ActUserAuditParamDto actUserAuditParam, String currentUserId, String auditTime) {
        StringBuffer sql = new StringBuffer();
        String status = actUserAuditParam.getAuditOpinion();
        sql.append(" UPDATE DP_APPLY_PAX SET ");
        sql.append(" STATUS=?, ");
        sql.append(" AUDIT_TIME=?, ");
        String refuse = actUserAuditParam.getRefuse();
        if (StringUtils.isNotBlank(refuse)) {
            sql.append(" AUDIT_REMARK=?, ");
        }
        String remark = actUserAuditParam.getRemark();
        if (StringUtils.isNotBlank(remark)) {
            sql.append(" REMARK=?, ");
        }
        sql.append(" AUDIT_USER=? ");
        String applyCode = actUserAuditParam.getApplyCode();
        String paxId = actUserAuditParam.getPaxId();
        sql.append(" WHERE APPLY_CODE=? and PAX_ID=?");
        if (StringUtils.isNotBlank(refuse) && StringUtils.isBlank(remark)) {
            baseDAO.batchUpdate(sql.toString(), status, auditTime, refuse, currentUserId, applyCode, paxId);
        }
        if (StringUtils.isNotBlank(refuse) && StringUtils.isNotBlank(remark)) {
            baseDAO.batchUpdate(sql.toString(), status, auditTime, refuse, remark, currentUserId, applyCode, paxId);
        }
        if (StringUtils.isBlank(refuse) && StringUtils.isBlank(remark)) {
            baseDAO.batchUpdate(sql.toString(), status, auditTime, currentUserId, applyCode, paxId);
        }
    }

    /**
     * Title：getLastAuditTime <br>
     * Description： 获取最新审核时间<br>
     * author：王建文 <br>
     * date：2020-3-19 13:49 <br>
     *
     * @param applyCode 申领单号
     * @return
     */
    public String getLastAuditTime(String applyCode) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("applyCode", applyCode);
        sql.append(" SELECT Max(TO_CHAR(DAOA.AUDIT_TIME,'YYYY-MM-DD hh24:mi:ss')) AS createTime ");
        sql.append(" from DP_APPLY_ORDER_AUDIT DAOA ");
        sql.append(" WHERE DAOA.APPLY_CODE=:applyCode and (STATUS=1 OR STATUS=2) ");
        return baseDAO.findOneBySql(sql.toString(), paramsMap, ActConfigInfoVo.class).getCreateTime();
    }

    /**
     * Title：updateApplyOrder <br>
     * Description： 更新申领单信息<br>
     * author：王建文 <br>
     * date：2020-3-19 13:52 <br>
     *
     * @param applyCode 申领号
     * @return
     */
    public void updateApplyOrder(String applyCode) {
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_APPLY_ORDER SET ");
        sql.append(" STATUS='0', ");
        sql.append(" APPLY_STATUS='0', ");
        sql.append(" AUDIT_TIME=null ");
        sql.append(" WHERE APPLY_CODE=? ");
        baseDAO.batchUpdate(sql.toString(), applyCode);
    }

    /**
     * Title：resetActAuditPaxInfo <br>
     * Description： 代领审核撤回<br>
     * author：王建文 <br>
     * date：2020-3-19 13:54 <br>
     *
     * @param applyCode 申领单号
     * @return
     */
    public void resetActAuditPaxInfo(String applyCode) {
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_APPLY_PAX SET ");
        sql.append(" STATUS='0', ");
        sql.append(" AUDIT_TIME=null, ");
        sql.append(" AUDIT_REMARK=null, ");
        sql.append(" REMARK=null, ");
        sql.append(" AUDIT_USER=null ");
        sql.append(" WHERE APPLY_CODE=?");
        baseDAO.batchUpdate(sql.toString(), applyCode);
    }

    /**
     * Title：quickPay <br>
     * Description：代领审核快速支付标识<br>
     * author：王建文 <br>
     * date：2020-3-19 13:59 <br>
     *
     * @param applyCode 申领单号
     * @return
     */
    public void quickPay(String applyCode) {
        //1.更新申领主表快速支付标识
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_APPLY_ORDER SET ");
        sql.append(" QUICK_PAY='1' ");
        sql.append(" WHERE APPLY_CODE=? ");
        baseDAO.batchUpdate(sql.toString(), applyCode);
    }

    /**
     * Title：getActAuditRecordByApplyCode <br>
     * Description： 根据申领单号查询审核记录<br>
     * author：王建文 <br>
     * date：2020-3-17 14:23 <br>
     *
     * @param applyCode 申领单号
     * @return
     */
    public List<ActAuditRecordVo> getActAuditRecordByApplyCode(String applyCode) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("applyCode", applyCode);
        sql.append(" SELECT DAOA.STATUS AS status,get_user_name(DAOA.AUDITOR) AS auditor, ");
        sql.append(" TO_CHAR(DAOA.AUDIT_TIME,'YYYY-MM-DD hh24:mi:ss') AS auditTime ");
        sql.append(" FROM DP_APPLY_ORDER_AUDIT DAOA ");
        sql.append(" WHERE DAOA.APPLY_CODE=:applyCode ");
        sql.append(" ORDER BY DAOA.AUDIT_TIME DESC ");
        return (List<ActAuditRecordVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, ActAuditRecordVo.class);
    }

    /**
     * Title：getActOrderMessage <br>
     * Description：获取代领审核需要发送的信息<br>
     * author：王建文 <br>
     * date：2020-4-13 19:46 <br>
     *
     * @param
     * @return
     */
    public List<ActMessageVo> getActOrderMessage() {
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT DAO.APPLY_USER AS applyUser,DAO.APPLY_CUST_NUM AS applyCustNum, ");
        sql.append(" DAO.APPLY_CODE AS applyCode,DAO.TRANS_AMOUNT AS transAmount ");
        sql.append(" FROM DP_APPLY_ORDER DAO ");
        sql.append(" WHERE DAO.APPLY_STATUS='0' AND DAO.APPLY_WAY='1' AND DAO.SEND_STATUS IS NULL ");
        return (List<ActMessageVo>) baseDAO.findBySQL_comm(sql.toString(), new HashMap<>(), ActMessageVo.class);
    }

    public List<Map<String, Object>> getActOrderMessageFlightNoAndFlightDate(String applyCode) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("applyCode", applyCode);
        sql.append(" SELECT O.FLIGHT_NO AS flightNo,O.FLIGHT_DATE AS flightDate ");
        sql.append(" FROM DP_APPLY_PAX DAP ");
        sql.append(" LEFT JOIN DP_PAX_INFO DPI ON  ");
        sql.append(" DPI.PAX_ID=DAP.PAX_ID ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ON ");
        sql.append(" O.ORDER_ID=DPI.ORDER_ID ");
        sql.append(" WHERE DAP.APPLY_CODE=:applyCode ");
        sql.append(" AND O.FLIGHT_NO IS NOT NULL  AND O.FLIGHT_DATE IS NOT NULL ");
        sql.append(" GROUP BY O.FLIGHT_NO,O.FLIGHT_DATE ");
        return (List<Map<String, Object>>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, null);
    }
    /**
     * Title：getHasActRoleUser <br>
     * Description：获取具有代领审核权限的人员<br>
     * author：王建文 <br>
     * date：2020-6-19 15:58 <br>
     * @param
     * @return
     */
    public List<Map<String,Object>> getHasActRoleUser(String roleId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("roleId", roleId);
        sql.append(" SELECT E.TUNO AS ACCOUNT FROM EMPLOYEE E ");
        sql.append(" LEFT JOIN USER_ROLE UR ON UR.EMPLOYEE_ID=E.ID ");
        sql.append(" WHERE UR.ROLE_ID=:roleId ");
        return (List<Map<String, Object>>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, null);
    }
    public List<Map<String, Object>> getApplyPaxIdByApplyCode(String applyCode) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("applyCode", applyCode);
        sql.append(" SELECT DAP.PAX_ID AS paxId ");
        sql.append(" FROM DP_APPLY_PAX DAP ");
        sql.append(" WHERE DAP.APPLY_CODE=:applyCode ");
        return (List<Map<String, Object>>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, null);
    }

    public void updateActSendStatus(String applyCode) {
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_APPLY_ORDER  ");
        sql.append(" SET SEND_STATUS='1' ");
        sql.append(" WHERE APPLY_CODE=? ");
        baseDAO.batchUpdate(sql.toString(), applyCode);
    }

    /**
     * Title：getOverTimeActOrder <br>
     * Description： 代领申领单获取审核通过未快速支付单子<br>
     * author：王建文 <br>
     * date：2020-4-15 17:40 <br>
     *
     * @param
     * @return
     */
    public List<Map<String, Object>> getOverTimeActOrder() {
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT APPLY_CODE AS applyCode, ");
        sql.append(" AUDIT_TIME AS auditTime ");
        sql.append(" FROM DP_APPLY_ORDER ");
        sql.append(" WHERE APPLY_WAY=1 AND APPLY_STATUS=1 AND PAY_STATUS=0 AND （QUICK_PAY IS NULL OR QUICK_PAY=0) ");
        return (List<Map<String, Object>>) baseDAO.findBySQL_comm(sql.toString(), new HashMap<>(), null);
    }

    /**
     * Title：getOverTimeActRefuseOrder <br>
     * Description：获取代领审核未<br>
     * author：王建文 <br>
     * date：2020-4-27 14:13 <br>
     *
     * @param
     * @return
     */
    public List<Map<String, Object>> getOverTimeActRefuseOrder() {
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT APPLY_USER AS applyUser, ");
        sql.append(" APPLY_CODE AS applyCode, ");
        sql.append(" TELEPHONE AS telephone, ");
        sql.append(" get_dp_apply_flightno(APPLY_CODE) AS flightNo, ");
        sql.append(" get_dp_apply_flightdate(APPLY_CODE) AS flightDate ");
        sql.append(" FROM DP_APPLY_ORDER ");
        sql.append(" WHERE APPLY_WAY=1 AND APPLY_STATUS=2  AND IS_SEND IS NULL ");
        sql.append(" AND AUDIT_TIME+(1/24/60)*(SELECT content from DP_CONFIG_INFO  WHERE TYPE=0 AND STATUS=0) <= sysdate ");
        return (List<Map<String, Object>>) baseDAO.findBySQL_comm(sql.toString(), new HashMap<>(), null);
    }

    public void sendMessage(String applyCode) {
        //1.更新申领主表短信发送标识
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_APPLY_ORDER SET ");
        sql.append(" IS_SEND='1' ");
        sql.append(" WHERE APPLY_CODE=? ");
        baseDAO.batchUpdate(sql.toString(), applyCode);
    }
}