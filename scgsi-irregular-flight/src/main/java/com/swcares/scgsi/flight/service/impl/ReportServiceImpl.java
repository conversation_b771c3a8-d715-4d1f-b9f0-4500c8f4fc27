package com.swcares.scgsi.flight.service.impl;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.flight.dao.impl.ReportDaoImpl;
import com.swcares.scgsi.flight.dto.ReportQueryParamDto;
import com.swcares.scgsi.flight.service.ReportService;
import com.swcares.scgsi.flight.vo.ReportInfoVo;
import com.swcares.scgsi.util.AesEncryptUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.service.impl <br>
 * Description：航延报表serviceImpl <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月30日 15:52 <br>
 * @version v1.0 <br>
 */
@Service
public class ReportServiceImpl implements ReportService {
    @Resource
    private ReportDaoImpl reportDao;

    @Override
    public QueryResults getReportDataPage(ReportQueryParamDto reportQueryParamDto) {
        QueryResults queryResults=reportDao.getReportDataPage(reportQueryParamDto);
        List<ReportInfoVo> reportInfoVoList=(List<ReportInfoVo>)queryResults.getList();
        if(reportInfoVoList.size()>0){
            //处理起始航站，到达航站，支付状态，支付失败原因，最后审核人
            List<ReportInfoVo> showReportInfoVoList=handReportList(reportInfoVoList);
            showReportInfoVoList.forEach(e->{
                e.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, e.getTelephone()));
            });

            queryResults.setList(showReportInfoVoList);
        }else{
            queryResults.setList(new ArrayList<>());
        }
        return queryResults;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFlag(String[] ids, String status) {
        reportDao.updateFlag(ids, status);
    }

    @Override
    public List<ReportInfoVo> getExportData(ReportQueryParamDto reportQueryParamDto) {
        List<ReportInfoVo> reportInfoVoList=reportDao.getExportData(reportQueryParamDto);
        if(reportInfoVoList.size()>0){
            return handReportList(reportInfoVoList);
        }else{
            return new ArrayList<>();
        }
    }
    private  List<ReportInfoVo> handReportList(List<ReportInfoVo> reportInfoVoList){
            //处理起始航站，到达航站，支付状态，支付失败原因，最后审核人
            List<ReportInfoVo> showReportInfoVoList=new ArrayList<>();
            reportInfoVoList.forEach(reportInfoVo->{
                Map<String,Object> map=reportDao.handSpecialReportFiled(reportInfoVo.getOrgCity(),reportInfoVo.getDstCity(),reportInfoVo.getPaxId(),reportInfoVo.getOrderId());
                if(null!=map){
                    String orgCity = map.get("ORGCITY") == null ? "" : map.get("ORGCITY").toString();
                    String dstCity = map.get("DSTCITY") == null ? "" : map.get("DSTCITY").toString();
                    String payStatus = map.get("PAYSTATUS") == null ? "" :map.get("PAYSTATUS").toString();
                    String payFailRemark = map.get("PAYFAILREMARK")== null ? "" :map.get("PAYFAILREMARK").toString();
                    String lastAuditor = map.get("LASTAUDITOR")== null ? "" :map.get("LASTAUDITOR").toString();
                    reportInfoVo.setOrgCity(orgCity);
                    reportInfoVo.setDstCity(dstCity);
                    if("0".equals(payStatus)){
                        reportInfoVo.setPayStatus("待支付");
                    }
                    if("1".equals(payStatus)){
                        reportInfoVo.setPayStatus("已支付");
                    }
                    if("2".equals(payStatus)){
                        reportInfoVo.setPayStatus("支付失败");
                    }
                    if("已领取".equals(reportInfoVo.getReceiveStatus())){
                        reportInfoVo.setPayFailRemark("");
                    }else{
                        reportInfoVo.setPayFailRemark(payFailRemark);
                    }

                    reportInfoVo.setLastAuditor(lastAuditor);
                }
                showReportInfoVoList.add(reportInfoVo);
            });
           return showReportInfoVoList;
    }
}