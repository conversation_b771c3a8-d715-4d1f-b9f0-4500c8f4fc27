package com.swcares.scgsi.flight.service.impl;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.flight.dao.impl.ReportDaoImpl;
import com.swcares.scgsi.flight.dto.ReportQueryParamDto;
import com.swcares.scgsi.flight.service.ReportService;
import com.swcares.scgsi.flight.vo.ReportInfoVo;
import com.swcares.scgsi.util.AesEncryptUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.service.impl <br>
 * Description：航延报表serviceImpl <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月30日 15:52 <br>
 * @version v1.0 <br>
 */
@Service
public class ReportServiceImpl implements ReportService {
    @Resource
    private ReportDaoImpl reportDao;

    @Override
    public QueryResults getReportDataPage(ReportQueryParamDto reportQueryParamDto) {
        QueryResults queryResults=reportDao.getReportDataPage(reportQueryParamDto);
        List<ReportInfoVo> reportInfoVoList=(List<ReportInfoVo>)queryResults.getList();
        if(reportInfoVoList.size()>0){
            //处理起始航站，到达航站，支付状态，支付失败原因，最后审核人
            List<ReportInfoVo> showReportInfoVoList=handReportList(reportInfoVoList);
            showReportInfoVoList.forEach(e->{
                e.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, e.getTelephone()));
            });

            queryResults.setList(showReportInfoVoList);
        }else{
            queryResults.setList(new ArrayList<>());
        }
        return queryResults;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFlag(String[] ids, String status) {
        reportDao.updateFlag(ids, status);
    }

    @Override
    public List<ReportInfoVo> getExportData(ReportQueryParamDto reportQueryParamDto) {
        List<ReportInfoVo> reportInfoVoList=reportDao.getExportData(reportQueryParamDto);
        if(reportInfoVoList.size()>0){
            return handReportList(reportInfoVoList);
        }else{
            return new ArrayList<>();
        }
    }
    private  List<ReportInfoVo> handReportList(List<ReportInfoVo> reportInfoVoList){
            //现在数据已经在主查询中获取，只需要进行简单的数据转换
            List<ReportInfoVo> showReportInfoVoList=new ArrayList<>();
            reportInfoVoList.forEach(reportInfoVo->{
                // 使用查询中获取的城市名称，如果为空则使用原始值
                if(reportInfoVo.getOrgCityName() != null && !reportInfoVo.getOrgCityName().isEmpty()){
                    reportInfoVo.setOrgCity(reportInfoVo.getOrgCityName());
                }
                if(reportInfoVo.getDstCityName() != null && !reportInfoVo.getDstCityName().isEmpty()){
                    reportInfoVo.setDstCity(reportInfoVo.getDstCityName());
                }

                // 处理支付状态转换
                String payStatus = reportInfoVo.getFinalPayStatus() != null ?
                    reportInfoVo.getFinalPayStatus() : reportInfoVo.getPayStatus();
                if("0".equals(payStatus)){
                    reportInfoVo.setPayStatus("待支付");
                } else if("1".equals(payStatus)){
                    reportInfoVo.setPayStatus("已支付");
                } else if("2".equals(payStatus)){
                    reportInfoVo.setPayStatus("支付失败");
                }

                // 处理支付失败原因：如果已领取则清空失败原因
                if("已领取".equals(reportInfoVo.getReceiveStatus())){
                    reportInfoVo.setPayFailRemark("");
                }
                // payFailRemark已经在查询中获取，无需额外处理

                // lastAuditor已经在查询中获取，无需额外处理

                showReportInfoVoList.add(reportInfoVo);
            });
           return showReportInfoVoList;
    }
}