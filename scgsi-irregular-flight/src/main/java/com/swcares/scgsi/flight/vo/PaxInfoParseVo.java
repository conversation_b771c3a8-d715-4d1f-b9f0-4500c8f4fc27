package com.swcares.scgsi.flight.vo;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月23日 15:32 <br>
 * @version v1.0 <br>
 */
@Data
public class PaxInfoParseVo {
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 性别
     */
    private String gender;
    /**
     * 主仓位
     */
    private String mainClass;
    /**
     * 子仓位
     */
    private String sellClass;
    /**
     * 旅客姓名
     */
    private String psgName;
    /**
     * 到达航站三字码
     */
    private String dest;
    /**
     * 是婴儿时为婴儿的编号
     */
    private String infantIdx;
    /**
     * 票号
     */
    private String etNum;
    /**
     * 航班号
     */
    private String flightNum;
    /**
     * 始发地三字码
     */
    private String orig;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 订票时间
     */
    private String printTicketTime;
    /**
     * 航段
     */
    private String segment;
    /**
     * 旅客id
     */
    private String idx;
    /**
     * 证件号码
     */
    private String idNum;
    /**
     * 旅客状态 AC-值机，CL-订座取消，XR-直接取消等等
     */
    private String status;
    /**
     * 总赔偿次数
     */
    private String payCount;
    /**
     * 总赔金额
     */
    private String payMoney;
    /**
     * 是否是儿童
     */
    private String isChild;
    /**
     * 是否婴儿
     */
    private String isInfant;
    /**
     * 婴儿姓名
     */
    private String infantName;
    /**
     * 行李编号
     */
    private String bagTag;
    /**
     * 行李重量
     */
    private String bagWht;

    /**
     * pnr
     */
    private String pnr;
    /**
     * 旅客取消状态
     */
    private String cancel;
}