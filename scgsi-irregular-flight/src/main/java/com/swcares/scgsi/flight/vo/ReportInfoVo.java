package com.swcares.scgsi.flight.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：航延报表返回实体 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月30日 15:28 <br>
 * @version v1.0 <br>
 */
@Data
@EncryptionClassz
public class ReportInfoVo {
    /**
     * 主键ID
     */
    private String id;
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 起始航站
     */
    private String orgCity;
    /**
     * 到达航站
     */
    private String dstCity;
    /**
     * 服务航站
     */
    private String serviceCity;
    /**
     * 机型
     */
    private String acType;
    /**
     * 赔付类型
     */
    private String payType;
    /**
     * 延误原因
     */
    private String lateReason;
    /**
     * 延误时长
     */
    private String delayTime;
    /**
     * 赔付单号
     */
    private String orderId;
    /**
     * 证件号
     */
    @Encryption
    private String idNo;
    /**
     * 申领人手机号
     */
    @Encryption
    private String telephone;
    /**
     * 赔付单状态
     */
    private String orderStatus;
    /**
     * 旅客姓名
     */
    private String paxName;
    /**
     * 票号
     */
    private String tktNo;
    /**
     * 总赔偿金额
     */
    private String totalPay;
    /**
     * 赔偿金额
     */
    private String payMoney;
    /**
     * 客票差价
     */
    private String tktPriceDiff;
    /**
     * 领取状态
     */
    private String receiveStatus;
    /**
     * 支付状态
     */
    private String payStatus;
    /**
     * 代领审核状态
     */
    private String actStatus;
    /**
     * 领取渠道
     */
    private String receiveChannel;
    /**
     * 领取方式
     */
    private String receiveWay;
    /**
     * 开户名
     */
    private String accountName;
    /**
     * 开户行
     */
    private String openBankName;
    /**
     * 领取账号
     */
    @Encryption
    private String getAccount;

    /**
     * 支付时间
     */
    private String payDate;
    /**
     * 过期时间
     */
    private String expireTime;
    /**
     * 申请人
     */
    private String applyUser;
    /**
     * 是否标记
     */
    private String isFlag;
    /**
     * 最终审核人
     */
    private String lastAuditor;

    /**
     * 航段
     */
    private String segment;

    /**
     * 备注
     */
    private String remark;
    /**
     * 支付失败原因
     */
    private String payFailRemark;
    /**
     * 旅客id
     */
    private String paxId;

    /**
     * 起始城市名称（转换后）
     */
    private String orgCityName;

    /**
     * 目标城市名称（转换后）
     */
    private String dstCityName;

    /**
     * 最终支付状态（优化查询后的结果）
     */
    private String finalPayStatus;
}
