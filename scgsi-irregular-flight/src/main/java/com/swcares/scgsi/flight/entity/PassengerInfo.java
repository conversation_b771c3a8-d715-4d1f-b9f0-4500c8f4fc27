package com.swcares.scgsi.flight.entity;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import org.hibernate.annotations.GenericGenerator;
import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.entity <br>
 * Description：赔付单旅客信息 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月03日 16:32 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "DP_PAX_INFO")
@Data
@EncryptionClassz
public class PassengerInfo {

    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    /**
     * 旅客ID
     */
    @Column(name = "PAX_ID")
    private String paxId;

    /**
     * 旅客姓名
     */
    @Column(name = "PAX_NAME")
    private String paxName;

    /**
     * 证件类型
     */
    @Column(name = "ID_TYPE")
    private String idType;

    /**
     * 证件号
     */
    @Column(name = "ID_NO")
    @Encryption
    private String idNo;

    /**
     * 性别C儿童M男F女
     */
    @Column(name = "SEX")
    private String sex;

    /**
     * 联系电话
     */
    @Column(name = "TELEPHONE")
    @Encryption
    private String telephone;

    /**
     * 航段
     */
    @Column(name = "SEGMENT")
    private String segment;

    /**
     * 起始航站三字码
     */
    @Column(name = "ORG_CITY_AIRP")
    private String orgCityAirp;

    /**
     * 到达航站三字码
     */
    @Column(name = "DST_CITY_AIRP")
    private String dstCityAirp;

    /**
     * 旅客状态AC-值机，CL-订座取消，XR-直接取消
     */
    @Column(name = "PAX_STATUS")
    private String paxStatus;

    /**
     * 主舱位
     */
    @Column(name = "MAIN_CLASS")
    private String mainClass;

    /**
     * 子舱位
     */
    @Column(name = "SUB_CLASS")
    private String subClass;

    /**
     * 票号
     */
    @Column(name = "TKT_NO")
    private String tktNo;

    /**
     * 行李号，多个逗号分隔
     */
    @Column(name = "PKG_NO")
    private String pkgNo;

    /**
     * 行李重量
     */
    @Column(name = "PKG_WEIGHT")
    private String pkgWeight;

    /**
     * 逾重行李重量
     */
    @Column(name = "PKG_OVER_WEIGHT")
    private String pkgOverWeight;

    /**
     * 携带婴儿标识
     */
    @Column(name = "WITH_BABY")
    private String isInfant;;
    /**
     * 儿童标识
     */
    @Column(name = "IS_CHILD")
    private String isChild;
    /**
     * 携带婴儿,婴儿名称
     */
    @Column(name = "BABY_PAX_NAME")
    private String babyName;

    /**
     * 购票时间
     */
    @Column(name = "TKT_ISSUE_DATE")
    private String tktDate;

    /**
     * 当前赔付单对应领取金额
     */
    @Column(name = "CURRENT_AMOUNT")
    private int currentAmount;

    /**
     * 领取渠道(0普通,1代领,现2金)
     */
    @Column(name = "RECEIVE_CHANNEL")
    private Integer receiveChannel;

    /**
     * 领取方式（0微信，1银联，2现金）
     */
    @Column(name = "RECEIVE_WAY")
    private Integer receiveWay;

    /**
     * 领取时间
     */
    @Column(name = "RECEIVE_TIME")
    private Date receiveTime;

    /**
     * 领取状态(0未领取,1已领取,2领取中)
     */
    @Column(name = "RECEIVE_STATUS")
    private Integer receiveStatus;

    /**
     * 赔付单ID
     */
    @Column(name = "ORDER_ID")
    private String orderId;

    /**
     * 是否标记0未标记1标记
     */
    @Column(name = "IS_FLAG")
    private int isFlag;

    /**
     * 旅客申领资格开关(默认0有资格，1取消领取资格)
     */
    @Column(name = "SWITCH")
    private int switchOff;

    /**
     * 创建人
     */
    @Column(name = "CREATE_ID")
    private String createId;

    /**
     * 创建日期
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;
    /**
     * PNR
     */
    @Column(name = "PNR")
    private String pnr;
}