package com.swcares.scgsi.flight.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：赔偿单管理旅客列表展示 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月06日 15:22 <br>
 * @version v1.0 <br>
 */
@Data
@EncryptionClassz
public class PaxInfoVo {
    /**
     * 旅客ID
     */
    private String paxId;
    /**
     * 旅客姓名
     */
    private String paxName;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号
     */
    @Encryption
    private String idNo;
    /**
     * 航段
     */
    private String segment;
    /**
     * 当前赔付单对应领取金额
     */
    private int currentAmount;
    /**
     * 领取状态(0未领取,1已领取)
     */
    private String receiveStatus;
    /**
     * 冻结状态1冻结
     */
    private String switchOff;
    /**
     * 值机状态
     */
    private String paxStatus;
    /**
     * 主舱位
     */
    private String mainClass;
    /**
     * 子舱位
     */
    private String subClass;
    /**
     * 票号
     */
    private String tktNo;
    /**
     * 购票时间
     */
    private String tktDate;
    /**
     * 性别C儿童M男F女
     */
    private String sex;
    /**
     * 婴儿名字
     */
    private String babyName;
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 总补偿次数
     */
    private String payCount;
    /**
     * 总赔偿金额
     */
    private String payMoney;
    /**
     * 起始航站三字码
     */
    private String orgCityAirp;

    /**
     * 到达航站三字码
     */
    private String dstCityAirp;
    /**
     * 联系电话
     */
    @Encryption
    private String telephone;

    /**
     * 儿童标识
     */
    private String isChild;

    /**
     * pnr
     */
    private String pnr;

    /**
     * 婴儿标识
     */
    private String isInfant;

    /**
     * 支付状态
     */
    private String payStatus;

    /**
     * 支付失败原因
     */
    private String payFailRemark;

}