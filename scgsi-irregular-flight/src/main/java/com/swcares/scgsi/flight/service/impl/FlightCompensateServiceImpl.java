package com.swcares.scgsi.flight.service.impl;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.aop.EncryptFieldAop;
import com.swcares.scgsi.common.model.form.ContentTraceForm;
import com.swcares.scgsi.constant.SmsConstant;
import com.swcares.scgsi.dao.repository.FocFlightInfoRepository;
import com.swcares.scgsi.dict.service.impl.SysDictDataServiceImpl;
import com.swcares.scgsi.entity.FocFlightInfo;
import com.swcares.scgsi.flight.dto.*;
import com.swcares.scgsi.flight.enums.OrderStatusEnum;
import com.swcares.scgsi.flight.enums.PaxStatusEnum;
import com.swcares.scgsi.flight.vo.*;
import com.swcares.scgsi.service.TraceService;
import com.swcares.scgsi.sms.api.SmsService;
import com.swcares.scgsi.sms.entity.SmsTemplate;
import com.swcares.scgsi.sms.form.SmsTemplateForm;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.util.AuthenticationUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.swcares.scgsi.audit.dao.impl.OrderAuditDaoImpl;
import com.swcares.scgsi.audit.enums.AuditProcessType;
import com.swcares.scgsi.audit.service.OrderAuditService;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.form.FlightInfoListForm;
import com.swcares.scgsi.common.model.view.OriginalSegmentView;
import com.swcares.scgsi.flight.dao.CompensatesInfoDao;
import com.swcares.scgsi.flight.dao.FlightsInfoDao;
import com.swcares.scgsi.flight.dao.OrdersInfoDao;
import com.swcares.scgsi.flight.dao.PassengerInfoDao;
import com.swcares.scgsi.flight.dao.impl.FlightCompensateDaoImpl;
import com.swcares.scgsi.flight.entity.CompensatesInfo;
import com.swcares.scgsi.flight.entity.FlightsInfo;
import com.swcares.scgsi.flight.entity.OrdersInfo;
import com.swcares.scgsi.flight.entity.PassengerInfo;
import com.swcares.scgsi.flight.service.FlightCompensateService;
import com.swcares.scgsi.service.FlightInfoService;
import com.swcares.scgsi.util.DateUtils;

/**
 * ClassName：com.swcares.scgsi.flight.service.impl <br>
 * Description：不正常航班补偿service <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月03日 15:42 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class FlightCompensateServiceImpl implements FlightCompensateService {
    // 经济舱标识
    private static final String E_CLASS_TYPE = "1";

    // 公务舱标识
    private static final String B_CLASS_TYPE = "2";

    // 公务舱舱位
    private static final String[] B_CLASSSIGN = {"C", "P", "D", "I"};
    // 公务舱主舱位舱位
    private static final String B_MAIN_CLASSSIGN = "C";

    //赔偿类型 异常行李1、
    private static final String STATUS_ABNORMAL_BAGGAGE = "1";

    //赔偿类型 超售旅客2、
    private static final String STATUS_OVERBOOK = "2";
    @Resource
    private TraceService traceService;

    @Resource
    private FlightsInfoDao flightsInfoDao;

    @Resource
    private CompensatesInfoDao compensatesInfoDao;

    @Resource
    private OrdersInfoDao ordersInfoDao;

    @Resource
    private PassengerInfoDao passengerInfoDao;

    @Resource
    private FlightCompensateDaoImpl flightCompensateDao;

    @Resource
    private OrderAuditService orderAuditService;
    @Resource
    private FlightInfoService flightInfoService;
    @Resource
    private SmsService smsService;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private FlightCompensateServiceImpl flightCompensateService;

    @Override
    public String saveOrderInfo(OrderInfoParamDto orderInfoParamDto) {
        String flightDate = orderInfoParamDto.getOrderInfo().getFlightDate();
        String flightNo = orderInfoParamDto.getOrderInfo().getFlightNo();
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String createUser = (String) authentication.getPrincipal();
        int paxSize = orderInfoParamDto.getPaxInfoList().size();

        log.info("【不正常航班补偿单创建】加锁对象：航班日期【{}】，航班号【{}】，申请人工号【{}】，该单子下旅客人数【{}】", flightDate, flightNo, createUser, paxSize);

        RLock r = redisson.getLock(flightDate + flightNo + createUser + paxSize);
        if(!r.tryLock()){
            log.info("【不正常航班补偿单创建】加锁对象：航班日期【{}】，航班号【{}】，申请人工号【{}】，该单子下旅客人数【{}】，获取锁失败，说明该单子已经被创建了~", flightDate, flightNo, createUser, paxSize);
            throw new BusinessException(MessageCode.COMPENSATION_DUPLICATE_SUBMISSION.getCode());
        }else{
            log.info("【不正常航班补偿单创建】加锁对象：航班日期【{}】，航班号【{}】，申请人工号【{}】，该单子下旅客人数【{}】，获取锁 成功！ ", flightDate, flightNo, createUser, paxSize);
            try{
                //补偿单保存的业务方法
                return flightCompensateService.doSaveOrderInfo(orderInfoParamDto);
            }finally {
                r.unlock();
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public String doSaveOrderInfo(OrderInfoParamDto orderInfoParamDto) {
        // 补偿标准转换为集合
        List<CompensatesInfo> compensateInfoList = orderInfoParamDto.getCompensateInfoList();
        // 赔偿旅客
        List<PassengerInfo> paxInfoList = orderInfoParamDto.getPaxInfoList();
        //解密传输过程中的电话号码
        paxInfoList.forEach(e->{
            e.setTelephone(AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, e.getTelephone()));
        });
        Date createTime = new Date();
        ServiceOrderInfoDto serviceOrderInfoDto = orderInfoParamDto.getOrderInfo();
        String orderId = handleServiceOrderId(serviceOrderInfoDto.getPayType());
        OrdersInfo oldOrdersInfo = null;
        if (StringUtils.isNotBlank(serviceOrderInfoDto.getOrderId())) {
            oldOrdersInfo = ordersInfoDao.findByOrderId(serviceOrderInfoDto.getOrderId());
            // 删除关联的赔偿单信息
            flightCompensateDao.deleteOrderInfo(serviceOrderInfoDto.getOrderId());
        }
        if(StringUtils.isNotBlank(serviceOrderInfoDto.getOrderId())){
            orderId=serviceOrderInfoDto.getOrderId();
        }
        Authentication authentication =AuthenticationUtil.getAuthentication();
        String createUser =(String) authentication.getPrincipal();
        // 保存航延航班信息
        saveCompensateFlightInfo(createTime, createUser, serviceOrderInfoDto, orderId);
        // 保存赔付标准
        saveCompensateInfo(compensateInfoList, createTime, createUser, orderId);
        //加密旅客信息证件号和电话号码
        EncryptFieldAop.manualEncrypt(paxInfoList);
        // 保存旅客信息
        int totalMoney =
                savePaxInfo(createTime, createUser, orderId, compensateInfoList, paxInfoList);
        log.info("【不正常航班补偿单创建】保存赔付单信息成功，计算的金额为【{}】", totalMoney);
        // 保存赔付单信息
        serviceOrderInfoDto.setTotalMoney(String.valueOf(totalMoney));
        saveOrderInfo(createTime, createUser, orderId, serviceOrderInfoDto,oldOrdersInfo);
        log.info("【不正常航班补偿单创建】保存赔付单信息成功，赔偿单Id【{}】", orderId);
        if ("1".equals(serviceOrderInfoDto.getStatus())) {
            // 审核流程
            log.info("【不正常航班补偿单创建】新建赔付单流程提交:"+createUser);
            orderAuditService.launchAuditProcess(createUser,
                    "", orderId,
                    serviceOrderInfoDto.getAuditor());
        }
        log.info("【不正常航班补偿单创建】【{}】新建赔付单成功:【{}】", createUser, orderId);
        return orderId;
    }

    /**
     * Title：handleServiceOrderId <br>
     * Description：处理服务单号 18位（年月日时分+6位序列号 ）最后一位区分单子类型<br>
     * author：王建文 <br>
     * date：2020-3-3 16:17 <br>
     *
     * @param payType 不正常航班赔偿0、异常行李1、超售旅客2
     * @return String
     */
    private String handleServiceOrderId(String payType) {
        // 精确时间到分12位
        String orderId = DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMM);
        // 随机5位数
        int random5 = (int) ((Math.random() * 9 + 1) * 10000);
        orderId += String.valueOf(random5);
        // 加上赔偿类型
        orderId += payType;
        return orderId;
    }

    /**
     * Title：saveCompensateFlightInfo <br>
     * Description： 保存航延航班信息<br>
     * author：王建文 <br>
     * date：2020-3-3 17:16 <br>
     *
     * @param createTime          创建时间
     * @param createUser          创建人
     * @param serviceOrderInfoDto
     * @return
     */
    private void saveCompensateFlightInfo(Date createTime, String createUser,
            ServiceOrderInfoDto serviceOrderInfoDto, String orderId) {
        FlightsInfo flightInfo = new FlightsInfo();
        flightInfo.setOrderId(orderId);
        flightInfo.setFlightNo(serviceOrderInfoDto.getFlightNo());
        flightInfo.setFlightDate(serviceOrderInfoDto.getFlightDate());
        FlightInfoListForm form1 = new FlightInfoListForm();
        form1.setFlightNum(serviceOrderInfoDto.getFlightNo());
        form1.setFlightDate(serviceOrderInfoDto.getFlightDate().replaceAll("-", "/"));
        OriginalSegmentView originalSegmentView = flightInfoService.getOriginalSegment(form1);
        flightInfo.setSegment(originalSegmentView.getSegment().replace(" ", ""));
        flightInfo.setCreateTime(createTime);
        flightInfo.setCreateId(createUser);
        flightInfo.setAcType(serviceOrderInfoDto.getAcType());
        flightInfo.setFlightId(serviceOrderInfoDto.getFlightId());
        flightInfo.setStd(serviceOrderInfoDto.getStd());
        flightInfo.setSta(serviceOrderInfoDto.getSta());
        flightInfo.setEtd(serviceOrderInfoDto.getEtd());
        flightInfo.setLateReason(serviceOrderInfoDto.getLateReason());
        flightInfo.setPlaneCode(serviceOrderInfoDto.getPlaneCode());
        flightsInfoDao.save(flightInfo);
    }

    /**
     * Title：saveCompensateInfo <br>
     * Description： 保存赔付标准<br>
     * author：王建文 <br>
     * date：2020-3-3 17:37 <br>
     *
     * @param compensateInfoList 赔付标准集合
     * @param createTime         创建时间
     * @param createUser         创建人
     * @param orderId            服务单号
     * @return
     */
    private void saveCompensateInfo(List<CompensatesInfo> compensateInfoList, Date createTime,
            String createUser, String orderId) {
        List<CompensatesInfo> compensateInfos = new ArrayList<>();
        for (CompensatesInfo compensateInfo : compensateInfoList) {
            compensateInfo.setOrderId(orderId);
            compensateInfo.setCreateTime(createTime);
            compensateInfo.setCreateId(createUser);
            compensateInfos.add(compensateInfo);
        }
        compensatesInfoDao.saveAll(compensateInfos);
    }

    @Autowired
    FocFlightInfoRepository focFlightInfoRepository;
    /**
     * Title：saveOrderInfo <br>
     * Description： 保存服务单信息<br>
     * author：王建文 <br>
     * date：2020-3-3 17:45 <br>
     *
     * @param createTime 创建时间
     * @param createUser 创建人
     * @param orderId    服务单号
     * @return
     */
    public void saveOrderInfo(Date createTime, String createUser, String orderId,
            ServiceOrderInfoDto serviceOrderInfoDto,OrdersInfo oldOrdersInfo) {
        OrdersInfo orderInfo = new OrdersInfo();
        if (StringUtils.isNotBlank(serviceOrderInfoDto.getOrderId())) {
            orderInfo.setCreateTime(oldOrdersInfo.getCreateTime());
            orderInfo.setCreateId(oldOrdersInfo.getCreateId());
            orderInfo.setAllSegment(oldOrdersInfo.getAllSegment());
            orderInfo.setAutoCreate(oldOrdersInfo.getAutoCreate());
            if ("1".equals(serviceOrderInfoDto.getStatus())){
                orderInfo.setApplyDate(createTime);
                orderInfo.setApplyUser(createUser);
            }
        }else{
            orderInfo.setCreateTime(createTime);
            orderInfo.setCreateId(createUser);
            orderInfo.setAutoCreate(serviceOrderInfoDto.getAutoCreate());
            if ("1".equals(serviceOrderInfoDto.getStatus())){
                orderInfo.setApplyDate(createTime);
                orderInfo.setApplyUser(createUser);
            }
        }
        List<FocFlightInfo> filterFlights = getFilterFlights(serviceOrderInfoDto.getFlightNo(), serviceOrderInfoDto.getFlightDate().replace("-","/"), "D", null, null, null);
        if (!filterFlights.isEmpty()) {
            orderInfo.setAllSegment(filterFlights.stream()
                    .map(flightInfo -> flightInfo.getDepartPort() + "-" + flightInfo.getArrivalPort())
                    .collect(Collectors.joining(",")));
        }
        orderInfo.setUpdateUser(createUser);
        orderInfo.setUpdateTime(new Date());
        orderInfo.setOrderId(orderId);
        orderInfo.setFlightId(serviceOrderInfoDto.getFlightId());
        orderInfo.setFlightNo(serviceOrderInfoDto.getFlightNo());
        orderInfo.setFlightDate(serviceOrderInfoDto.getFlightDate());
        if ("1".equals(serviceOrderInfoDto.getStatus())) {
            orderInfo.setStatus("2");
        } else {
            orderInfo.setStatus(serviceOrderInfoDto.getStatus());
        }
        orderInfo.setServiceCity(serviceOrderInfoDto.getServiceCity());
        orderInfo.setSumMoney(serviceOrderInfoDto.getTotalMoney());
        orderInfo.setChoiceSegment(serviceOrderInfoDto.getChoiceSegment());
        orderInfo.setRemark(serviceOrderInfoDto.getRemark());
        ordersInfoDao.save(orderInfo);
    }

    public List<FocFlightInfo> getFilterFlights(String flightNo, String flightDate, String dOrI, String flgDelay, String flgCs, List<java.util.function.Predicate<FocFlightInfo>> predicate) {
        List<FocFlightInfo> result = new ArrayList<>();
        Specification<FocFlightInfo> specification = (root, query, cb) -> {
            List<Predicate> predicatesList = new ArrayList<>();
            if (!StringUtils.isBlank(flightNo)) {
                Predicate flightNumPredicate = cb.equal(root.get("flightNo"), flightNo);
                predicatesList.add(flightNumPredicate);
            }
            if (!StringUtils.isBlank(flightDate)) {
                Predicate flightDatePredicate = cb.equal(root.get("flightDate"), flightDate);
                predicatesList.add(flightDatePredicate);
            }
            if (!StringUtils.isBlank(dOrI)) {
                Predicate dOrIPredicate = cb.equal(root.get("dOrI"), dOrI);
                predicatesList.add(dOrIPredicate);
            }
            if (!StringUtils.isBlank(flgDelay)) {
                Predicate flgDelayPredicate = cb.isNotNull(root.get("flgDelay"));
                predicatesList.add(flgDelayPredicate);
                //predicatesList.add(cb.or(flgDelayPredicate, flgCsPredicate));
            }
            if (!StringUtils.isBlank(flgCs)) {
                Predicate flgCsPredicate = cb.isNotNull(root.get("flgCs"));
                predicatesList.add(flgCsPredicate);
            }
            return cb.and(predicatesList.toArray(new Predicate[predicatesList.size()]));
        };
        List<FocFlightInfo> focFlightInfos = focFlightInfoRepository.findAll(specification);
        if (predicate != null && !predicate.isEmpty()) {
            for (java.util.function.Predicate<FocFlightInfo> predicate_Iterator : predicate) {
                result.addAll(focFlightInfos.stream().filter(predicate_Iterator).collect(Collectors.toList()));
            }
            return result;
        }
        return focFlightInfos;
    }
    /**
     * Title：savePaxInfo <br>
     * Description：保存旅客信息 <br>
     * author：王建文 <br>
     * date：2020-3-3 18:12 <br>
     *
     * @param
     * @return
     */
    public int savePaxInfo(Date createTime, String createUser, String orderId,
            List<CompensatesInfo> compensateInfoList, List<PassengerInfo> paxInfoList) {
        // 获取赔偿单标准信息计算旅客赔偿金额 1-经济舱，2-公务舱)
        CompensatesInfo eCompensateInfo = null;
        CompensatesInfo bCompensateInfo = null;
        int priceSpread = 0;
        for (CompensatesInfo compensateInfo : compensateInfoList) {
            if (StringUtils.isNotBlank(compensateInfo.getClassType())) {
                if (E_CLASS_TYPE.equals(compensateInfo.getClassType())) {
                    eCompensateInfo = compensateInfo;
                }
                if (B_CLASS_TYPE.equals(compensateInfo.getClassType())) {
                    bCompensateInfo = compensateInfo;
                }
            } else {
                priceSpread = compensateInfo.getCpsNum();
            }

        }
        // 计算总金额
        int sumMoney = 0;
        // 5.处理旅客信息
        List<PassengerInfo> addPaxInfoList = new ArrayList<>();
        for (PassengerInfo paxInfo : paxInfoList) {
            paxInfo.setOrderId(orderId);
            paxInfo.setCreateTime(createTime);
            paxInfo.setCreateId(createUser);
            paxInfo.setReceiveStatus(0);
            if (priceSpread == 0) {
                if (Arrays.asList(B_CLASSSIGN).contains(paxInfo.getSubClass())&&B_MAIN_CLASSSIGN.equals(paxInfo.getMainClass())) {
                    // 公务舱儿童
                    if (StringUtils.isNotBlank(paxInfo.getIsChild()) && StringUtils.isBlank(paxInfo.getIsInfant())) {
                        int currentAmount =
                                bCompensateInfo.getCpsNum() * (bCompensateInfo.getChildStd() / 100);
                        paxInfo.setCurrentAmount(currentAmount);
                        sumMoney += currentAmount;
                    }
                    // 公务舱  儿童占座的婴儿
                    if (StringUtils.isNotBlank(paxInfo.getIsChild()) && StringUtils.isNotBlank(paxInfo.getIsInfant())) {
                        int currentAmount =
                                bCompensateInfo.getCpsNum() * (bCompensateInfo.getBabyStd() / 100);
                        paxInfo.setCurrentAmount(currentAmount);
                        sumMoney += currentAmount;
                    }

                    // 公务舱成人不携带婴儿
                    if (StringUtils.isBlank(paxInfo.getIsChild())
                            && StringUtils.isBlank(paxInfo.getIsInfant())) {
                        paxInfo.setCurrentAmount(bCompensateInfo.getCpsNum());
                        sumMoney += bCompensateInfo.getCpsNum();
                    }
                    // 公务舱成人携带婴儿 赔偿金额=成人+婴儿
                    if (StringUtils.isBlank(paxInfo.getIsChild()) && StringUtils.isNotBlank(paxInfo.getIsInfant())) {
                        int currentAmount =
                                bCompensateInfo.getCpsNum() * (bCompensateInfo.getBabyStd() / 100);
                        currentAmount += bCompensateInfo.getCpsNum();
                        paxInfo.setCurrentAmount(currentAmount);
                        sumMoney += currentAmount;
                    }
                } else {
                    // 经济舱赔偿金额判断
                    // 经济舱儿童
                    if (StringUtils.isNotBlank(paxInfo.getIsChild()) && StringUtils.isBlank(paxInfo.getIsInfant())) {
                        int currentAmount =
                                eCompensateInfo.getCpsNum() * (eCompensateInfo.getChildStd() / 100);
                        paxInfo.setCurrentAmount(currentAmount);
                        sumMoney += currentAmount;
                    }
                    // 经济舱  儿童占座的婴儿
                    if (StringUtils.isNotBlank(paxInfo.getIsChild()) && StringUtils.isNotBlank(paxInfo.getIsInfant())) {
                        int currentAmount =
                                eCompensateInfo.getCpsNum() * (eCompensateInfo.getBabyStd() / 100);
                        paxInfo.setCurrentAmount(currentAmount);
                        sumMoney += currentAmount;
                    }

                    // 经济舱成人不携带婴儿
                    if (StringUtils.isBlank(paxInfo.getIsChild())
                            && StringUtils.isBlank(paxInfo.getIsInfant())) {
                        paxInfo.setCurrentAmount(eCompensateInfo.getCpsNum());
                        sumMoney += eCompensateInfo.getCpsNum();
                    }
                    // 经济舱成人携带婴儿 赔偿金额=成人+婴儿
                    if (StringUtils.isBlank(paxInfo.getIsChild()) && StringUtils.isNotBlank(paxInfo.getIsInfant())) {
                        int currentAmount =
                                eCompensateInfo.getCpsNum() * (eCompensateInfo.getBabyStd() / 100);
                        currentAmount += eCompensateInfo.getCpsNum();
                        paxInfo.setCurrentAmount(currentAmount);
                        sumMoney += currentAmount;
                    }

                }
            } else {
                paxInfo.setCurrentAmount(priceSpread);
                sumMoney += priceSpread;
            }

            addPaxInfoList.add(paxInfo);
        }
        passengerInfoDao.saveAll(addPaxInfoList);
        return sumMoney;
    }

    @Override
    public QueryResults getFlightInfoPage(FlightQueryParamDto flightQueryParamDto) {
        return flightCompensateDao.getFlightInfoPage(flightQueryParamDto);
    }

    @Override
    public List<ServiceOrderInfoVo> getOrderInfoByFlightINoAndFlightDate(String flightNo,
            String flightDate,String payType,String status) {
        List<ServiceOrderInfoVo> dataList =  flightCompensateDao.getOrderInfoByFlightINoAndFlightDate(flightNo, flightDate,payType,status);
        dataList.forEach(serviceOrderInfoVo -> {serviceOrderInfoVo.setIsShowReEdit(
                orderAuditService.verifyIsShowReEdit(serviceOrderInfoVo.getOrderId(),serviceOrderInfoVo.getCreateId()
                        ,serviceOrderInfoVo.getStatus()));});
        return dataList;
    }

    @Override
    public Map<String, Object> getOrderDetailInfoByOrderId(String orderId) {
        Map<String, Object> dataMap = new HashMap<>();
        OrderDetailInfoVo orderDetailInfoVo = flightCompensateDao.getOrderDetailByOrderId(orderId);
        // 经济舱赔付标准
        CompensateInfoVo eCompensateInfoVo = flightCompensateDao.getCompensateInfo(orderId, "1");
        // 公务舱
        CompensateInfoVo bCompensateInfoVo = flightCompensateDao.getCompensateInfo(orderId, "2");
        dataMap.put("orderDetailInfo", orderDetailInfoVo);
        dataMap.put("eCompensateInfo", eCompensateInfoVo);
        dataMap.put("bCompensateInfoVo", bCompensateInfoVo);
        // 客票差价显示
        if ("0".equals(orderDetailInfoVo.getPayType())) {
            dataMap.put("tktPriceDiff", flightCompensateDao.getCompensateInfo(orderId, null)
                    .getCpsNum());
        } else {
            dataMap.put("tktPriceDiff", orderDetailInfoVo.getTktPriceDiff());
        }
        return dataMap;
    }

    @Override
    public CompensateInfoVo getCompensateInfo(String orderId, String classType) {
        return flightCompensateDao.getCompensateInfo(orderId, classType);
    }

    @Override
    public List<Map<String, Object>> getPaxOrderInfo(String paxId, String flightNo,
            String flightDate) {
        return flightCompensateDao.getPaxAllOrderInfo(paxId, flightNo, flightDate);
    }

    @Override
    public QueryResults getPaxInfoList(PaxInfoQueryParamDto paxInfoQueryParamDto) {
        return flightCompensateDao.getPaxInfoPage(paxInfoQueryParamDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatus(String orderId, String status) {
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser = (String) authentication.getPrincipal();
        log.info("--【PC-赔偿单确认发放或关闭接口】----操作人:" + currentUser + " ,请求参数：赔偿单号：" + orderId, " ,赔偿单状态：" + status);
        updateServiceOrderStatus(orderId, status);
    }

    /**
     * Title：updateServiceOrderStatus <br>
     * Description： 更新服务单状态<br>
     * author：王建文 <br>
     * date：2020-3-6 17:13 <br>
     *
     * @param orderId 服务单id
     * @param status  状态
     * @return
     */
    private void updateServiceOrderStatus(String orderId, String status) {
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser = (String) authentication.getPrincipal();
        // 更新赔付单状态
        flightCompensateDao.updateOrderStatusByOrderIdAndStatus(orderId, status);
        // 判断如果是关闭状态，冻结未领取申领单
        if (status.equals(OrderStatusEnum.CLOSE.getKey())){
            log.info("WEB关闭赔偿单-操作人工号:" + currentUser + " ,赔偿单号：" + orderId + " ,前端传入状态：" + status);
            List<Map<String, Object>> unPayApplyOrderList =
                    flightCompensateDao.getUnPayApplyOrder("",orderId);
            if (unPayApplyOrderList.size() > 0) {
                for (Map<String, Object> map : unPayApplyOrderList) {
                    String applyCode = map.get("APPLYCODE").toString();
                    String paxId = map.get("PAXID").toString();
                    String paxName=map.get("PAXNAME").toString();
                    // 更新申领表
                    flightCompensateDao.freezeApplyOrder(applyCode,"3");
                    // 更新申领旅客表
                    String remark="申请单中,代领旅客(";
                    remark+=paxName+")申领金额关联赔偿单已关闭,";
                    remark+="旅客需要重新验证获取最新赔偿数据!";
                    flightCompensateDao.freezeApplyPax(applyCode, paxId,"3",remark);
                }
            }
        }else{
            log.info("WEB确认发放操作人工号:" + currentUser + " ,赔偿单号：" + orderId + " ,前端传入状态：" + status);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePaxStatus(String[] paxIds, String status, String orderId) {
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser = (String) authentication.getPrincipal();
        log.info("---【操作旅客冻结，激活操作接口】----操作人工号：" + currentUser + " ，请求参数：paxIds=" + JSON.toJSONString(paxIds) + " ,status=" + status + " ,orderId=" + orderId);
        flightCompensateDao.updatePaxStatus(paxIds, status, orderId);
        String paxIds1="";
        for(String paxId:paxIds){
            paxIds1+=paxId+",";
        }
        List<Map<String, Object>> unPayApplyOrderList =
                flightCompensateDao.getUnPayApplyOrder(paxIds1,orderId);
        if (unPayApplyOrderList.size() > 0) {
            for (Map<String, Object> map : unPayApplyOrderList) {
                String applyCode = map.get("APPLYCODE").toString();
                String paxId = map.get("PAXID").toString();
                String applyStatus= map.get("APPLYSTATUS").toString();
                String auditStatus= map.get("STATUS").toString();
                if("1".equals(status)){
                    String paxName=map.get("PAXNAME").toString();
                    // 更新申领表
                    log.info("旅客冻结导致申领单冻结:"+paxId+"申领单号:"+applyCode);
                    if(!"2".equals(auditStatus)){
                        flightCompensateDao.freezeApplyOrder(applyCode,"4");
                    }
                    // 更新申领旅客表
                    for (String paxId1 : paxIds) {
                        if(paxId1.equals(paxId)){
                            String remark="申请单中,申领金额关联赔偿单代领旅客(";
                            remark+=paxName+")被冻结,";
                            remark+="旅客需要重新验证获取最新赔偿数据!";
                            flightCompensateDao.freezeApplyPax(applyCode, paxId,"4",remark);
                        }
                    }
                }else{
                    // 更新申领表
                    if("0".equals(auditStatus)){
                        flightCompensateDao.freezeApplyOrder(applyCode,"0");
                        // 更新申领旅客表
                        flightCompensateDao.freezeApplyPax(applyCode, paxId,"0","");
                    }
                    if(!"0".equals(auditStatus)){
                        flightCompensateDao.freezeApplyOrder(applyCode,auditStatus);
                    }

                }
            }
        }
    }

    @Override
    public Map<String, List<Map<String, Object>>> getSelectCityInfoByInitial() {
        List<Map<String, Object>> list = flightCompensateDao.getSelectCityInfo();
        return groupByInitialCityInfo(list);
    }

    /**
     * Title：groupByInitialCityInfo <br>
     * Description：TODO(通过首字母来分组航站数据) <br>
     * author：王磊 <br>
     * date：2020年4月17日 下午1:23:41 <br>
     * @param list <br>
     */
    private Map<String, List<Map<String, Object>>> groupByInitialCityInfo(
            List<Map<String, Object>> list) {
        Map<String, List<Map<String, Object>>> sub =
                list.stream().collect(
                        Collectors.groupingBy(node -> new String((String) node.get("CITY3CODE"))
                                .substring(0, 1)));
        return sub;
    }

    @Override
    public void sendMessageToPaxByPayType(String orderId, String payType) {
        //短信通知旅客领取
        String tempLateCode = "";
        if(STATUS_ABNORMAL_BAGGAGE.equals(payType)){
            tempLateCode = SmsConstant.BAGGAGE_AUDIT;
        }
        if(STATUS_OVERBOOK.equals(payType)){
            tempLateCode = SmsConstant.OVERBOOKING_AUDIT;
        }
        SmsTemplateForm smsTemplateForm=new SmsTemplateForm();
        smsTemplateForm.setTemplateCode(tempLateCode);
        SmsTemplate smsTemplate = smsService.queryTemplateByType(smsTemplateForm);
        if( null == smsTemplate){
            throw new BusinessException(MessageCode.SMS_TEMPLATE_IS_NULL.getCode());
        }
        PaxInfoQueryParamDto paxInfoQueryParamDto=new PaxInfoQueryParamDto();
        paxInfoQueryParamDto.setOrderId(orderId);
        List<PaxInfoVo> paxInfoVoList=flightCompensateDao.getPaxInfoList(paxInfoQueryParamDto);

        if(paxInfoVoList.size()>0){
            for(PaxInfoVo paxInfoVo:paxInfoVoList){
                if(StringUtils.isEmpty(paxInfoVo.getTelephone())) continue;
                //调用短信  telephones+=paxInfoVo.getTelephone()+","; telephones.substring(0,telephones.length()-1)
                smsService.smsSend(paxInfoVo.getTelephone(),smsTemplate.getTemplateContent(),paxInfoVo.getFlightNo(),paxInfoVo.getFlightDate(),tempLateCode,SmsConstant.SYS_SENDER );
            }
        }


    }

    @Autowired
    private SysDictDataServiceImpl sysDictDataServiceImpl;

    private final static String CROSS_DAY_FLIGHT = "CROSS_DAY_FLIGHT";
    public Map<String, Object> getReturnFlightPaxInfo(String flightNo, String flightDate, String choiceSegment, ContentTraceForm form, String selectSegment) {
        if(StringUtils.isNotBlank(selectSegment)){
            choiceSegment = selectSegment;
        }
        String[] segment = choiceSegment.split(",");
        List<Object> paxList = new ArrayList<>();
        // 运行口和销售口的对于航班跨天航班日期的定义不一样，所以在数据字典配置了的，查询trace接口日期需要+1
        List<Map<String, String>> list = sysDictDataServiceImpl.getSelectDict(CROSS_DAY_FLIGHT);
        String crossFlightDate = null;
        for (String segment1 : segment) {
            String org = segment1.substring(0, segment1.indexOf("-"));
            String dest = segment1.substring(segment1.indexOf("-") + 1, segment1.length());
            ContentTraceForm queryForm = new ContentTraceForm();
            queryForm.setFlightNum(flightNo);
            queryForm.setOrig(flightCompensateDao.getCityCodeInfoByCityName(org).getAirport3code());
            queryForm.setDest(flightCompensateDao.getCityCodeInfoByCityName(dest).getAirport3code());

            boolean mark = false;
            if(ObjectUtils.isNotEmpty(list)){
                //这个我是不想写value，之前没封装为对象，没办法
                crossFlightDate = list.get(0).get("VALUE");
                mark = crossFlightDate.contains(flightNo + "/" + queryForm.getOrig() + queryForm.getDest());
                if(mark){
                    flightDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(flightDate), 1));
                    log.info("监测到当前航班号+航段【{}】在数据字典中【{}】配置，航班日期需要加1，加1后的结果为【{}】",
                            flightNo + queryForm.getOrig() + queryForm.getDest(), crossFlightDate, flightDate);
                }
            }
            //日期是否+1
            queryForm.setFlightDate(flightDate);

            if (StringUtils.isNotBlank(form.getKeySearch())) {
                queryForm.setKeySearch(form.getKeySearch());
            }
            if (StringUtils.isNotBlank(form.getCheckStatus())) {
                queryForm.setCheckStatus(form.getCheckStatus());
            }
            if (StringUtils.isNotBlank(form.getPaxName())) {
                queryForm.setPaxName(form.getPaxName());
            }
            if (StringUtils.isNotBlank(form.getNotContainsN())) {
                queryForm.setNotContainsN(form.getNotContainsN());
            }
            if (StringUtils.isNotBlank(form.getIsCancel())) {
                queryForm.setIsCancel(form.getIsCancel());
            }
            if (StringUtils.isNotBlank(form.getCancelDate())) {
                queryForm.setCancelDate(form.getCancelDate());
            }
            if (StringUtils.isNotBlank(form.getIsPrintTktNo())) {
                queryForm.setIsPrintTktNo(form.getIsPrintTktNo());
            }
            if (StringUtils.isNotBlank(form.getTktStartDate())){
                queryForm.setTktStartDate(form.getTktStartDate());
            }
            if(StringUtils.isNotBlank(form.getTktEndDate())) {
                queryForm.setTktEndDate(form.getTktEndDate());
            }
            Object object=traceService.getPsgListByFilght(queryForm);
            if(null!=object){
                paxList.add(object);
            }
        }
        Map<String,Object> returnFlightPaxInfo=new HashMap<>();
        List<PaxInfoParseVo> paxInfoParseVoList = new ArrayList<>();
        List<PaxInfoParseVo> repeatPaxList=new ArrayList<>();
        //trace返回旅客数据中，证件号为空的存放在此队列
        List<PaxInfoParseVo> idNoEmptyList = new ArrayList<>();
        if(paxList.size()>0){
            for (Object object : paxList) {
                PaxParseDto parseObject = JSON.parseObject(object.toString(), PaxParseDto.class);
                List<PaxInfoParseVo> dataList = parseObject.getDataList();
                if (dataList.size() > 0) {
                    //处理重复旅客数据
                    Map<String,Object> flightPaxInfo = removeDuplicatePax(dataList);
                    List<PaxInfoParseVo> removeDuplicatePaxList = (List<PaxInfoParseVo>)flightPaxInfo.get("removeDuplicatePaxList");
                    List<PaxInfoParseVo> addRepeatPaxList = (List<PaxInfoParseVo>)flightPaxInfo.get("repeatPaxList");
                    idNoEmptyList.addAll((Collection<? extends PaxInfoParseVo>) flightPaxInfo.get("idNoEmptyList"));

                    if(addRepeatPaxList.size()>0){
                        repeatPaxList.addAll(addRepeatPaxList);
                    }

                    for (PaxInfoParseVo paxInfoParseVo : removeDuplicatePaxList) {
                        Map<String, Object> map =
                                flightCompensateDao.getPaxPayCountAndPayMoney(paxInfoParseVo.getIdx(),
                                        paxInfoParseVo.getFlightNum(), paxInfoParseVo.getFlightDate());
                        if (map.isEmpty()) {
                            paxInfoParseVo.setPayCount("0");
                            paxInfoParseVo.setPayMoney("0");
                        } else {
                            paxInfoParseVo.setPayCount(map.get("PAYCOUNT").toString());
                            paxInfoParseVo.setPayMoney(map.get("PAYMONEY").toString());
                        }
                        //处理旅客取消状态
                        if(StringUtils.isNotBlank(paxInfoParseVo.getStatus())){
                            if(PaxStatusEnum.CL.getKey().equals(paxInfoParseVo.getStatus())||PaxStatusEnum.XR.getKey().equals(paxInfoParseVo.getStatus())){
                                paxInfoParseVo.setCancel(PaxStatusEnum.build(paxInfoParseVo.getStatus()).getKey()+"("+PaxStatusEnum.build(paxInfoParseVo.getStatus()).getValue()+")");
                            }
                            paxInfoParseVo.setStatus(PaxStatusEnum.build(paxInfoParseVo.getStatus()).getKey()+"("+PaxStatusEnum.build(paxInfoParseVo.getStatus()).getValue()+")");
                        }else{
                            paxInfoParseVo.setStatus("未出票");
                        }
                        paxInfoParseVoList.add(paxInfoParseVo);
                    }
                }
            }
        }
        returnFlightPaxInfo.put("repeatPaxList", repeatPaxList);
        returnFlightPaxInfo.put("paxList", paxInfoParseVoList);
        returnFlightPaxInfo.put("idNoEmptyList", idNoEmptyList);
        return returnFlightPaxInfo;
    }
    /**
     * Title：removeDuplicatePax <br>
     * Description： 新建赔付单旅客，根据身份证号去重按照购票时间先后顺序<br>
     * author：王建文 <br>
     * date：2021-7-5 9:23 <br>
     * @param  dataList 未去重旅客数据
     * @return
     */
    private Map<String,Object> removeDuplicatePax(List<PaxInfoParseVo> dataList){
        Map<String,Object> flightPaxInfo=new HashMap<>();
        //根据身份证号分组保存各个数据
        List<Map<String, List<PaxInfoParseVo>>> newPaxList = new ArrayList<Map<String, List<PaxInfoParseVo>>>();
        //1.获取所有身份证号去重
        List<String> idNos=new ArrayList<>();
        for(PaxInfoParseVo paxInfoParseVo:dataList){
            if(StringUtils.isEmpty(paxInfoParseVo.getIdNum())){
                log.error("trace接口返回的证件号为空，当前旅客信息为【{}】", paxInfoParseVo.toString());
            }
            idNos.add(paxInfoParseVo.getIdNum());
        }
        //2.身份号去重
        HashSet hIdNosList = new HashSet(idNos);
        idNos.clear();
        idNos.addAll(hIdNosList);
        //3.循环身份证号，以身份证号分组保存数据
        for(int i=0;i<idNos.size();i++){
            Map<String, List<PaxInfoParseVo>> mapList=new HashMap<String, List<PaxInfoParseVo>>();
            List<PaxInfoParseVo> voList=new ArrayList<PaxInfoParseVo>();
            for(PaxInfoParseVo paxInfoParseVo :dataList){
                //dataList.stream().filter(p->p.getIdNum()==null).collect(Collectors.toList());
                if(StringUtil.isNotEmpty(idNos.get(i)) && idNos.get(i).equals(paxInfoParseVo.getIdNum())){
                    if(null==mapList.get(idNos.get(i))){
                        voList.add(paxInfoParseVo);
                        mapList.put(idNos.get(i), voList);
                    }else{
                        voList=mapList.get(idNos.get(i));
                        voList.add(paxInfoParseVo);
                        mapList.put(idNos.get(i),voList );
                    }
                }
            }
            newPaxList.add(mapList);
        }
        //4.找出每个身份证号的数据，大于1进行去重操作
        List<PaxInfoParseVo> removeDuplicatePaxList = new ArrayList<>();
        //5.重复旅客数据添加
        List<PaxInfoParseVo> repeatPaxList = new ArrayList<>();
        for(Map<String, List<PaxInfoParseVo>> map:newPaxList){
            for(String key:map.keySet()){
                List<PaxInfoParseVo> paxInfoParseVoList=map.get(key);
                //证件号为空的单独处理，此处是不为空的
                if(ObjectUtils.isNotEmpty(paxInfoParseVoList)){
                    if(paxInfoParseVoList.size()>1){
                        paxListSortByPrintTicketTime(paxInfoParseVoList);
                        removeDuplicatePaxList.add(paxInfoParseVoList.get(paxInfoParseVoList.size()-1));
                        repeatPaxList.add(paxInfoParseVoList.get(paxInfoParseVoList.size()-1));
                    }else{
                        removeDuplicatePaxList.addAll(paxInfoParseVoList);
                    }
                }
            }
        }

        handEncrypt(repeatPaxList, removeDuplicatePaxList);

        flightPaxInfo.put("idNoEmptyList", dataList.stream().filter(p->p.getIdNum()==null).collect(Collectors.toList()));

        List<PaxInfoParseVo> finalRemoveDuplicatePaxList = new ArrayList<>(removeDuplicatePaxList.size() * 2);
        finalRemoveDuplicatePaxList.addAll((Collection<? extends PaxInfoParseVo>) flightPaxInfo.get("idNoEmptyList"));
        finalRemoveDuplicatePaxList.addAll(removeDuplicatePaxList);
        flightPaxInfo.put("repeatPaxList", repeatPaxList);
        flightPaxInfo.put("removeDuplicatePaxList", finalRemoveDuplicatePaxList);
        return flightPaxInfo;
    }

    private void handEncrypt(List<PaxInfoParseVo> repeatPaxList, List<PaxInfoParseVo> removeDuplicatePaxList){
        /*if(ObjectUtils.isNotEmpty(repeatPaxList)){
            repeatPaxList.forEach(e->{
                e.setPhone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, e.getPhone()));
            });
        }*/

        if(ObjectUtils.isNotEmpty(removeDuplicatePaxList)){
            removeDuplicatePaxList.forEach(e->{
                e.setPhone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, e.getPhone()));
            });
        }
    }

    /**
     * Title： paxListSortByPrintTicketTime<br>
     * Description： 根据旅客购票时间升序排列<br>
     * author：王建文 <br>
     * date：2021-7-5 15:00 <br>
     * @param  list
     * @return
     */
    private  void paxListSortByPrintTicketTime(List<PaxInfoParseVo> list) {
        Collections.sort(list, new Comparator<PaxInfoParseVo>() {
            @Override
            public int compare(PaxInfoParseVo o1, PaxInfoParseVo o2) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    Date dt1 = format.parse(o1.getPrintTicketTime());
                    Date dt2 = format.parse(o2.getPrintTicketTime());
                    if (dt1.getTime() > dt2.getTime()) {
                        return 1;
                    } else if (dt1.getTime() < dt2.getTime()) {
                        return -1;
                    } else {
                        return 0;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return 0;
            }
        });
    }

    public List<FocFlightInfo> getFocFlightInfos(String flightNo, String flightDate, String choiceSegment) {
        flightDate = flightDate.replaceAll("-", "/");
        String[] segment = choiceSegment.split(",");
        String segment1 = segment[0];
        String org = segment1.substring(0, segment1.indexOf("-"));
        String dest = segment1.substring(segment1.indexOf("-") + 1, segment1.length());
        FlightInfoListForm form = new FlightInfoListForm();
        form.setFlightNum(flightNo);
        form.setFlightDate(flightDate);
        form.setOrig(flightCompensateDao.getCityCodeInfoByCityName(org).getAirport4code());
        form.setDest(flightCompensateDao.getCityCodeInfoByCityName(dest).getAirport4code());
        FocFlightInfo flightInfo = flightInfoService.getFlightInfoList(form).get(0);
        //处理延误原因
        List<Map<String,String>> flightSegments=flightInfoService.getFlightSegment(flightNo, flightDate);
        if(flightSegments.size()>0){
            String lateReason="";
            for(Map<String,String> flightSegment:flightSegments){
                FlightInfoListForm form1 = new FlightInfoListForm();
                form1.setFlightNum(flightNo);
                form1.setFlightDate(flightDate);
                form1.setOrig(flightCompensateDao.getCityCodeInfoByCityName(flightSegment.get("DEPART_PORT")).getAirport4code());
                form1.setDest(flightCompensateDao.getCityCodeInfoByCityName(flightSegment.get("ARRIVAL_PORT"))
                        .getAirport4code());
                FocFlightInfo flightInfo1 = flightInfoService.getFlightInfoList(form1).get(0);
                if(StringUtils.isNotBlank(flightInfo1.getDelay_reason())){
                    lateReason+=flightSegment.get("DEPART_PORT")+flightSegment.get("ARRIVAL_PORT")+":"+flightInfo1.getDelay_reason()+",";
                }
            }
            flightInfo.setDelay_reason(lateReason);
        }
        List<FocFlightInfo> showList = new ArrayList<>();
        if (segment.length > 1) {
            // 预计起飞时间
            List<String> etd=new ArrayList<>();
            String flightId = "";
            // 计划起飞时间
            List<String> std=new ArrayList<>();
            for (String segment2 : segment) {
                String org1 = segment2.substring(0, segment2.indexOf("-"));
                String dest1 = segment2.substring(segment2.indexOf("-") + 1, segment2.length());
                FlightInfoListForm form1 = new FlightInfoListForm();
                form1.setFlightNum(flightNo);
                form1.setFlightDate(flightDate);
                form1.setOrig(flightCompensateDao.getCityCodeInfoByCityName(org1).getAirport4code());
                form1.setDest(flightCompensateDao.getCityCodeInfoByCityName(dest1)
                        .getAirport4code());
                FocFlightInfo flightInfo1 = flightInfoService.getFlightInfoList(form1).get(0);
                if(StringUtils.isNotBlank(flightInfo1.getEtd())){
                    etd.add(org1 + ":" + flightInfo1.getEtd());
                }
                if(StringUtils.isNotBlank(flightInfo1.getStd())){
                    std.add(org1 + ":" + flightInfo1.getStd());
                }
                flightId += flightInfo1.getFlightId() + ",";

            }
            if(etd.size()>0){
                LinkedHashSet<String> hashSet =new LinkedHashSet<>(etd);
                ArrayList<String> hashEtd =new ArrayList<>(hashSet);
                String showEtd="";
                for(String etd1:hashEtd){
                    showEtd+=etd1+",";
                }
                flightInfo.setEtd(showEtd.substring(0,showEtd.length()-1));
            }
            if(std.size()>0){
                LinkedHashSet<String> hashSet =new LinkedHashSet<>(std);
                ArrayList<String> hashStd =new ArrayList<>(hashSet);
                String showStd="";
                for(String std1:hashStd){
                    showStd+=std1+",";
                }
                flightInfo.setStd(showStd.substring(0,showStd.length()-1));
            }
            flightInfo.setFlightId(flightId);
        }
        showList.add(flightInfo);
        return showList;
    }

    @Autowired
    private OrderAuditDaoImpl orderAuditDaoImpl;
    //状态6-驳回
    private static final String ORDER_STATUS_REJECT = "6";
    /*流程节点 1 发起*/
    private static final String PROCESS_START="1";
    /*草稿状态*/
    private static final String DRAFT_STATUS = "0";
    

    @Override
    public Object getEditPermission(String orderId) {
        OrdersInfo ordersInfo = ordersInfoDao.findByOrderId(orderId);
        if(ordersInfo == null) {
            return false;
        }
        String createUser = ordersInfo.getCreateId();
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        String taskId = orderAuditService.getUserTaskIdByOrderId(orderId,userId);
        String node = orderAuditDaoImpl.getAuditNode(StringUtils.isBlank(taskId)?"0":taskId);
        if((userId.equals(createUser) || "system".equals(createUser)) && DRAFT_STATUS.equals(ordersInfo.getStatus())){
            List<ServiceOrderInfoVo> dataList =  flightCompensateDao.getEditPermission(orderId,DRAFT_STATUS);
            if(dataList !=null && dataList.size()>0) {
                return true;
             }
            return false;
        }
        if((userId.equals(createUser) || "system".equals(createUser)) 
            && ORDER_STATUS_REJECT.equals(ordersInfo.getStatus())  
            && StringUtils.isNotBlank(taskId) && PROCESS_START.equals(node)){
            return true;
        }
        return false;
    }
}
