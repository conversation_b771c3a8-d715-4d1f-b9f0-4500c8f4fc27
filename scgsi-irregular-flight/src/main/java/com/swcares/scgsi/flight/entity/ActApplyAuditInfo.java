package com.swcares.scgsi.flight.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.flight.entity <br>
 * Description：代领旅客审核信息 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月16日 16:22 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "DP_APPLY_ORDER_AUDIT")
@Data
public class ActApplyAuditInfo {
    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;
    /**
     * 申领单号
     */
    @Column(name = "APPLY_CODE")
    private String applyCode;

    /**
     * 审核人
     */
    @Column(name = "AUDITOR")
    private String auditor;
    /**
     * 审核时间
     */
    @Column(name = "AUDIT_TIME")
    private Date auditTime;
    /**
     * 1通过2拒绝0撤回3快速支付
     */
    @Column(name = "STATUS")
    private String status;
    /**
     * 审核意见
     */
    @Column(name = "AUDIT_OPINION")
    private String auditOpinion;


    /**
     * 备注
     */
    @Column(name = "REMARK")
    private String remark;

}