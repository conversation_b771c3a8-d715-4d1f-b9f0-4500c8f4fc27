package com.swcares.scgsi.flight.service;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.flight.dto.ReportQueryParamDto;
import com.swcares.scgsi.flight.vo.ReportInfoVo;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.service <br>
 * Description：航延报表 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月30日 15:43 <br>
 * @version v1.0 <br>
 */
public interface ReportService {
    /**
     * Title：getReportDataPage <br>
     * Description：航延报表分页查询<br>
     * author：王建文 <br>
     * date：2020-3-30 15:50 <br>
     * @param  reportQueryParamDto 查询参数接收
     * @return
     */
    public QueryResults getReportDataPage(ReportQueryParamDto reportQueryParamDto);
    /**
     * Title：updateFlag <br>
     * Description： 标记<br>
     * author：王建文 <br>
     * date：2020-3-31 10:19 <br>
     * @param  ids 主键id
     * @param  status 状态
     * @return
     */
    public void updateFlag(String[] ids,String status);
    /**
     * Title：getExportData <br>
     * Description： TODO(用一句话描述该方法做什么)<br>
     * author：王建文 <br>
     * date：2020-4-2 14:24 <br>
     * @param  reportQueryParamDto 查询参数封装
     * @return
     */
    public List<ReportInfoVo> getExportData(ReportQueryParamDto reportQueryParamDto);

}
