package com.swcares.scgsi.flight.service.impl;

import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.aop.EncryptFieldAop;
import com.swcares.scgsi.common.model.form.FlightInfoListForm;
import com.swcares.scgsi.common.model.view.OriginalSegmentView;
import com.swcares.scgsi.fileuploadanddownload.UploadAndDownload;
import com.swcares.scgsi.flight.dao.ApplyOrderDao;
import com.swcares.scgsi.flight.dao.ApplyPaxDao;
import com.swcares.scgsi.flight.dao.impl.CashReceiveDaoImpl;
import com.swcares.scgsi.flight.dao.impl.FlightCompensateDaoImpl;
import com.swcares.scgsi.flight.dao.impl.PaxReceiveDaoImpl;
import com.swcares.scgsi.flight.dto.ApplyInfoDto;
import com.swcares.scgsi.flight.entity.ApplyOrderInfo;
import com.swcares.scgsi.flight.entity.ApplyPaxInfo;
import com.swcares.scgsi.flight.service.CashReceiveService;
import com.swcares.scgsi.flight.service.PaxInfoService;
import com.swcares.scgsi.flight.vo.*;
import com.swcares.scgsi.service.impl.FlightInfoServiceImpl;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.util.SeedIdUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * ClassName：com.swcares.scgsi.flight.service.impl <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月17日 15:10 <br>
 * @version v1.0 <br>
 */
@Service
public class CashReceiveServiceImpl implements CashReceiveService {
    @Resource
    private ApplyOrderDao applyOrderDao;

    @Resource
    private ApplyPaxDao applyPaxDao;

    @Resource
    private PaxReceiveDaoImpl paxReceiveDao;
    @Resource
    private SeedIdUtil seedIdUtil;

    @Resource
    private CashReceiveDaoImpl cashReceiveDao;
    @Resource
    private FlightInfoServiceImpl flightInfoService;
    @Resource
    private FlightCompensateDaoImpl flightCompensateDao;
    @Resource
    private PaxInfoService paxInfoService;
    @Resource
    private UploadAndDownload uploadAndDownload;
    @Override
    public List<CashOrderInfoVo> getCashOrderInfo(String flightNo, String flightDate) {
        return cashReceiveDao.getCashOrderInfo(flightNo, flightDate);
    }

    @Override
    public Map<String, Object> getCashOrderDetail(String orderId) {
        Map<String, Object> dataMap = new HashMap<>();
        //1.获取赔付详情
        CashOrderDetailVo cashOrderDetailVo = cashReceiveDao.getOrderDetail(orderId);
        //2.处理终审人，终审时间
        CashOrderDetailVo cashOrderDetailVo1=cashReceiveDao.getOrderLastAuditor(orderId);
        if(StringUtils.isNotBlank(cashOrderDetailVo1.getLastAuditor())&&StringUtils.isNotBlank(cashOrderDetailVo1.getAuditTime())){
            cashOrderDetailVo.setLastAuditor(cashOrderDetailVo1.getLastAuditor());
            cashOrderDetailVo.setAuditTime(cashOrderDetailVo1.getAuditTime());
        }
        dataMap.put("orderDetailInfo", cashOrderDetailVo);
        //处理公务舱经济舱赔付标准
        dataMap.put("eCompensateInfo", flightCompensateDao.getCompensateInfo(orderId, "1"));
        dataMap.put("bCompensateInfo", flightCompensateDao.getCompensateInfo(orderId, "2"));
        dataMap.put("tktPriceDiff", flightCompensateDao.getCompensateInfo(orderId, null)
                .getCpsNum());
        return dataMap;
    }

    @Override
    public List<String> getFlightSegment(String flightNo, String flightDate) {
        List<String> segmentList = new ArrayList<>();
        List<FlightInfoVo> flightInfoVoList = cashReceiveDao.getFlightSegment(flightNo, flightDate);
        for (FlightInfoVo flightInfoVo : flightInfoVoList) {
            segmentList.add(flightInfoVo.getSegment());
        }
        return segmentList;
    }

    @Override
    public List<CashServiceOrderVo> getPaxServiceOrder(String flightNo, String flightDate, String paxId) {
        return cashReceiveDao.getPaxServiceOrder(flightNo, flightDate, paxId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCashApplyInfo(ApplyInfoDto applyInfoDto) {
        List<AuthReceiveInfoVo> authReceiveInfoVoList = paxInfoService
                .getPaxInfoData(applyInfoDto.getIdNo(), applyInfoDto.getFlightNo(), applyInfoDto.getFlightDate());
        if (authReceiveInfoVoList.size() <= 0) {
            throw  new BusinessException(MessageCode.PAX_CASH_SUBMIT.getCode());
        }
        if (StringUtils.isNotBlank(applyInfoDto.getImgUrl())) {
            try {
                String img = uploadAndDownload.saveImg(applyInfoDto.getImgUrl());
                applyInfoDto.setImgUrl(img);
            }catch (Exception e){
                throw  new BusinessException(MessageCode.PAX_IMG_FAIL.getCode());
            }
        }
        String applyCode = seedIdUtil.getId();
        //1.保存申领主表
        ApplyOrderInfo applyOrderInfo = new ApplyOrderInfo();
        BeanUtils.copyProperties(applyInfoDto, applyOrderInfo);
        applyOrderInfo.setApplyCode(applyCode);
        applyOrderInfo.setApplyWay("2");
        applyOrderInfo.setStatus("1");
        applyOrderInfo.setAuditTime(new Date());
        applyOrderInfo.setPayDate(new Date());
        applyOrderInfo.setCreateTime(new Date());
        applyOrderInfo.setPayStatus("1");
        applyOrderInfo.setApplyCustNum("1");
        applyOrderInfo.setGetMoneyWay("2");
        applyOrderInfo.setReceiveTime(new Date());
        EncryptFieldAop.manualEncrypt(applyOrderInfo);
        applyOrderDao.save(applyOrderInfo);
        //2.保存领取旅客信息
        ApplyPaxInfo applyPaxInfo = new ApplyPaxInfo();
        applyPaxInfo.setApplyCode(applyCode);
        applyPaxInfo.setPaxId(applyInfoDto.getPaxId());
        applyPaxInfo.setStatus("1");
        applyPaxInfo.setActingRole("0");
        applyPaxInfo.setOrderId(applyInfoDto.getOrderId());
        //审核人待处理
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser =(String) authentication.getPrincipal();
        applyPaxInfo.setAuditUser(currentUser);
        applyPaxInfo.setAuditTime(DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS));
        applyPaxDao.save(applyPaxInfo);
        //3.更新旅客领取状态和领取方式
        paxReceiveDao.updatePaxReceiveStatus("2", "2", "1", applyInfoDto.getPaxId(),applyInfoDto.getOrderId());
    }

    @Override
    public Map<String, Object> getDelayFlightInfo(String flightNo, String flightDate) {
        Map<String, Object> dataMap = new HashMap<>();
        List<CashFlightInfoVo> cashFlightInfoVoList = (List<CashFlightInfoVo>) cashReceiveDao.getDelayFlightInfo(flightNo, flightDate);
        if (cashFlightInfoVoList.size() > 0) {
            CashFlightInfoVo  cashFlightInfoVo=cashFlightInfoVoList.get(0);
            FlightInfoListForm form = new FlightInfoListForm();
            form.setFlightNum(flightNo);
            form.setFlightDate(flightDate.replaceAll("-", "/"));
            OriginalSegmentView originalSegmentView = flightInfoService.getOriginalSegment(form);
            cashFlightInfoVo.setSta(originalSegmentView.getSta());
            cashFlightInfoVo.setStd(originalSegmentView.getStd());
            dataMap.put("flightInfo", cashFlightInfoVo);
            dataMap.put("orderInfo", cashReceiveDao.getCashOrderInfo(flightNo, flightDate));
        }else{
            throw new BusinessException(MessageCode.PAX_CASH_AUTH_FAIL.getCode());
        }
        return dataMap;
    }
}