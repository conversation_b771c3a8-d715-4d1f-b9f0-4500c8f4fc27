package com.swcares.scgsi.flight.service;

import com.swcares.scgsi.flight.entity.ActConfigInfo;
import com.swcares.scgsi.flight.vo.ActConfigInfoVo;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.service <br>
 * Description：代领审核配置service <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月15日 10:34 <br>
 * @version v1.0 <br>
 */
public interface ActConfigInfoService {
    /**
     * Title：updateActConfigInfo <br>
     * Description： 代领审核配置信息新增编辑<br>
     * author：王建文 <br>
     * date：2020-3-15 10:36 <br>
     * @param  type 类型 0时间1拒绝原因
     * @param  content 类容
     * @param  id 主键ID
     * @return
     */
    public void updateActConfigInfo(String type,String content,String id);
    /**
     * Title：deleteRefuseInfo <br>
     * Description：删除拒绝原因<br>
     * author：王建文 <br>
     * date：2020-3-15 11:38 <br>
     * @param  ids 主键ids数组集合
     * @return
     */
    public void deleteRefuseInfo(String[] ids);
    /**
     * Title：getDefaultBufferTime <br>
     * Description：代领审核配置获取默认缓冲时间<br>
     * author：王建文 <br>
     * date：2020-3-16 9:16 <br>
     * @param
     * @return
     */
    public String getDefaultBufferTime();
    /**
     * Title：getRefuseInfo <br>
     * Description： 获取拒绝原因列表展示<br>
     * author：王建文 <br>
     * date：2020-3-16 9:30 <br>
     * @param type 1拒绝原因2行李
     * @return
     */
    public List<ActConfigInfoVo> getRefuseInfo(String type);
}
