package com.swcares.scgsi.flight.dao;

import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.flight.entity.ApplyPaxInfo;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.dao <br>
 * Description：申领旅客信息dao <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月11日 14:29 <br>
 * @version v1.0 <br>
 */
public interface ApplyPaxDao extends BaseJpaDao<ApplyPaxInfo, String> {


    List<ApplyPaxInfo> findByApplyCode(@Param("APPLY_CODE") String applyCode);

}
