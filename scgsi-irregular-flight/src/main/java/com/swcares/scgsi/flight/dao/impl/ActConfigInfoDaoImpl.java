package com.swcares.scgsi.flight.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.flight.entity.ActConfigInfo;
import com.swcares.scgsi.flight.vo.ActConfigInfoVo;
import com.swcares.scgsi.util.AuthenticationUtil;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.dao.impl <br>
 * Description：代领审核配置自定义sql <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月19日 14:03 <br>
 * @version v1.0 <br>
 */
@Repository
public class ActConfigInfoDaoImpl {
    @Resource
    private BaseDAO baseDAO;
    /**
     * Title：deleteRefuseInfo <br>
     * Description：删除拒绝原因<br>
     * author：王建文 <br>
     * date：2020-3-15 11:38 <br>
     * @param  ids 主键ids数组集合
     * @return
     */
    public void deleteRefuseInfo(String[] ids) {
        for (String id : ids) {
            StringBuffer sql = new StringBuffer();
            sql.append(" UPDATE DP_CONFIG_INFO SET ");
            Authentication authentication = AuthenticationUtil.getAuthentication();
            String updateUser =(String) authentication.getPrincipal();
            Date updateTime = new Date();
            sql.append(" STATUS='1', ");
            sql.append(" UPDATE_USER=?, ");
            sql.append(" UPDATE_TIME=? ");
            sql.append(" where ID=? ");
            baseDAO.batchUpdate(sql.toString(), updateUser, updateTime, id);
        }
    }
    /**
     * Title：getDefaultBufferTime <br>
     * Description：代领审核配置获取默认缓冲时间<br>
     * author：王建文 <br>
     * date：2020-3-16 9:16 <br>
     * @param
     * @return
     */
    public String getDefaultBufferTime() {
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT CONTENT AS content ");
        sql.append(" FROM  DP_CONFIG_INFO ");
        sql.append(" WHERE TYPE=0 AND STATUS=0 ");
        List<ActConfigInfo> actConfigInfoList=(List<ActConfigInfo>)baseDAO.findBySQL_comm(sql.toString(), new HashMap<>(), ActConfigInfo.class);
        if(actConfigInfoList.size()>0){
            return actConfigInfoList.get(0).getContent();
        }
        return "";
    }
    /**
     * Title：getRefuseInfo <br>
     * Description： 获取拒绝原因列表展示<br>
     * author：王建文 <br>
     * date：2020-3-16 9:30 <br>
     * @param
     * @return
     */
    public List<ActConfigInfoVo> getRefuseInfo(String type) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("type", type);
        sql.append(" SELECT ID AS id,CONTENT AS content, ");
        sql.append(" get_user_name(CREATE_USER) AS createUser,TO_CHAR(CREATE_TIME,'YYYY-MM-DD hh24:mi:ss') AS createTime, ");
        sql.append(" get_user_name(UPDATE_USER) AS updateUser,TO_CHAR(UPDATE_TIME,'YYYY-MM-DD hh24:mi:ss') AS updateTime ");
        sql.append(" FROM  DP_CONFIG_INFO ");
        sql.append(" WHERE TYPE=:type AND STATUS=0 ");
        return (List<ActConfigInfoVo>) baseDAO.findBySQL_comm(sql.toString(),paramsMap, ActConfigInfoVo.class);
    }
}