package com.swcares.scgsi.flight.enums;

/**
 * ClassName：com.swcares.scgsi.flight.enums <br>
 * Description：领取渠道 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 09月08日 10:12 <br>
 * @version v1.0 <br>
 */
public enum ReceiveWayEnum {
    WEIXIN("0", "微信"),
    UNIONPAY("1", "银联"),
    CASH("2", "现金");

    private ReceiveWayEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static ReceiveWayEnum build(String key) {
        return build(key, true);
    }

    public static ReceiveWayEnum build(String key, boolean throwEx) {
        ReceiveWayEnum typeEnum = null;
        for (ReceiveWayEnum element : ReceiveWayEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + ReceiveWayEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
