package com.swcares.scgsi.flight.enums;

/**
 * ClassName：com.swcares.scgsi.flight.enums <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 09月07日 17:07 <br>
 * @version v1.0 <br>
 */
public enum DateTypeEnum {

    SYSDATE("1", "本日"),
    LAST_7_DAYS("2", "近7日"),
    MONTHS("3", "本月"),
    YEAR("4", "本年");

    private DateTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static DateTypeEnum build(String key) {
        return build(key, true);
    }

    public static DateTypeEnum build(String key, boolean throwEx) {
        DateTypeEnum typeEnum = null;
        for (DateTypeEnum element : DateTypeEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + DateTypeEnum.class.getSimpleName());
        }
        return typeEnum;
}
}
