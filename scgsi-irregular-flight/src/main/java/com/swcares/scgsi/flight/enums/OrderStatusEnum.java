package com.swcares.scgsi.flight.enums;

/**
 * ClassName：com.swcares.scgsi.flight.enums <br>
 * Description：赔付单状态枚举 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月06日 17:02 <br>
 * @version v1.0 <br>
 */
public enum OrderStatusEnum {
    DRAFT("0", "草稿"),
    AUDIT("1", "审核中"),
    PASS("2", "通过"),
    EFFECT("3", "生效"),
    CLOSE("4", "关闭"),
    NO_PASS("5", "未通过");

    private OrderStatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static OrderStatusEnum build(String key) {
        return build(key, true);
    }

    public static OrderStatusEnum build(String key, boolean throwEx) {
        OrderStatusEnum typeEnum = null;
        for (OrderStatusEnum element : OrderStatusEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + OrderStatusEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}