package com.swcares.scgsi.flight.dto;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.dto <br>
 * Description：服务单与前端交互对象创建赔偿单 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月03日 15:32 <br>
 * @version v1.0 <br>
 */
@Data
public class ServiceOrderInfoDto {
    /**
     * 赔付单号
     */
    private String orderId;
    /**
     * 航班
     */
    private String flightId;
    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 机型
     */
    private String acType;

    /**
     * 计划起飞时间
     */
    private String std;
    /**
     * 预计起飞
     */
    private String etd;

    /**
     * 计划到达时间
     */
    private String sta;

    /**
     * 延误原因
     */
    private String lateReason;

    /**
     * 补偿类型 不正常航班赔偿0、异常行李1、超售旅客2
     */
    private String payType;

    /**
     * 服务航站
     */
    private String serviceCity;

    /**
     * 所选航站
     */
    private String choiceSegment;

    private String allSegment;

    /**
     * 延误时长
     */
    private String delayTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 合计赔付金额
     */
    private String totalMoney;
    /**
     * 赔付单状态
     */
    private String status;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 飞机号
     */
    private String planeCode;

    private Boolean autoCreate;
}