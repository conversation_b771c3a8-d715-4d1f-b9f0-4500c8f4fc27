package com.swcares.scgsi.flight.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.swcares.scgsi.common.model.VO.TraceInfoDistinctVO;
import com.swcares.scgsi.common.model.form.ContentTraceForm;
import com.swcares.scgsi.dao.repository.FocFlightInfoRepository;
import com.swcares.scgsi.entity.FocFlightInfo;
import com.swcares.scgsi.flight.dao.OrdersInfoDao;
import com.swcares.scgsi.flight.dto.OrderInfoParamDto;
import com.swcares.scgsi.flight.dto.ServiceOrderInfoDto;
import com.swcares.scgsi.flight.entity.CompensatesInfo;
import com.swcares.scgsi.flight.entity.OrdersInfo;
import com.swcares.scgsi.flight.entity.PassengerInfo;
import com.swcares.scgsi.flight.service.FlightCompensateService;
import com.swcares.scgsi.flight.vo.PaxInfoParseVo;
import com.swcares.scgsi.service.FlightInfoService;
import com.swcares.scgsi.service.TraceService;
import com.swcares.scgsi.util.AesEncryptUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class AutoCreateIrregularOrderDomain {

    private static final Logger log = LoggerFactory.getLogger(AutoCreateIrregularOrderDomain.class);

    private static final String DOMESTIC = "D";

    private static final String SYSTEM = "system";

    private static final String CLOSE_STATUS = "4";

    private static final String DRAFT_STATUS = "0";

    private static final Integer TIME_FOUR = 4;

    private static final Integer TIME_EIGHT = 8;

    private static final long OUT_OF_TIME = 24 * 60 * 60 * 1000;

    private static final Integer TIME_MINUTE = 0;

    private static final String DELAY_FLAG = "1";

    private static final String PAY_TYPE = "0";

    private static final String COMPANY_REASON = "公司原因";

    private static final String REMARK = "仅限（授权）服务补救单必填\n" +
            "补救场景：\n" +
            "旅客诉求：\n" +
            "补救措施：\n" +
            "参考标准（仅供参考，现场补救要依据《地面服务补救场景及授权标准》执行）：" +
            "航班不正常补救（≤200）、住宿（≤150）、餐食（30-300）、地面交通（≤200）、摆渡车（50-200）、公司原因误机或漏乘（≤300）。";

    private final FocFlightInfoRepository focFlightInfoRepository;

    private final OrdersInfoDao ordersInfoDao;

    private final TraceService traceService;

    private final FlightCompensateService flightCompensateService;

    private final FlightInfoService flightInfoService;

    public AutoCreateIrregularOrderDomain(FocFlightInfoRepository focFlightInfoRepository, OrdersInfoDao ordersInfoDao,
                                          TraceService traceService, FlightCompensateService flightCompensateService,
                                          FlightInfoService flightInfoService) {
        this.focFlightInfoRepository = focFlightInfoRepository;
        this.ordersInfoDao = ordersInfoDao;
        this.traceService = traceService;
        this.flightCompensateService = flightCompensateService;
        this.flightInfoService = flightInfoService;
    }

    public void autoCreateOrdersTask() {
        createDelayFlightCompensationOrder(TIME_FOUR, 0);
        createDelayFlightCompensationOrder(TIME_EIGHT, 0);
    }

    public void closeAutoCreateOrdersTask() {
        List<OrdersInfo> ordersInfos = fetchOrdersInfo(null, null, null, null,
                PAY_TYPE, true, DRAFT_STATUS);
        for (OrdersInfo ordersInfo : ordersInfos) {
            Date createTime = ordersInfo.getCreateTime();
            if (new Date().getTime() - createTime.getTime() < OUT_OF_TIME ) {
                continue;
            }
            ordersInfo.setStatus(CLOSE_STATUS);
            ordersInfo.setUpdateUser(SYSTEM);
            ordersInfo.setUpdateTime(new Date());
            ordersInfo.setCloseTime(new Date());
            ordersInfo.setCloseUser(SYSTEM);
            ordersInfoDao.save(ordersInfo);
        }
    }

    public void createDelayFlightCompensationOrder(Integer hours, Integer minute) {
        Authentication auth = new UsernamePasswordAuthenticationToken(SYSTEM, null, new ArrayList<>());
        SecurityContext context = SecurityContextHolder.createEmptyContext();
        context.setAuthentication(auth);
        SecurityContextHolder.setContext(context);
        try {
            List<FocFlightInfo> focFlightInfos = getFilterFlights(null, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd")), DOMESTIC, DELAY_FLAG, null, fetchDelayPredicate(hours, minute));
            log.info("【不正常航班-getFilterFlights-自动建单航班获取】：{},信号量:{}", JSON.toJSONString(focFlightInfos), hours);
            focFlightInfos.forEach(focFlightInfo -> {
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
                    if (StringUtils.isBlank(focFlightInfo.getAtd()) ? timeDifference(LocalDateTime.now().format(formatter), LocalTime.parse(focFlightInfo.getStd()).format(formatter), hours, minute)
                            : timeDifference(LocalTime.parse(focFlightInfo.getAtd()).format(formatter), LocalTime.parse(focFlightInfo.getStd()).format(formatter), hours, minute)) {
                        createFlightCompensationOrder(buildServiceOrderInfoDto(focFlightInfo, hours), passengerInfoCover(focusSegmentPsg(focFlightInfo, hours)));
                    }
                }catch (Exception e){
                    log.error("【不正常航班-时间比对-自动建单航班号】：{},信号量:{},异常信息:{}", focFlightInfo.getFlightNo(), hours,e.getMessage());
                }
            });
        } finally {
            SecurityContextHolder.clearContext();
        }
    }


    /**
     * Title: 根据航段的信息查询旅客信息，如果AB则org=A 如果BC则dst=c
     *
     * @param focFlightInfo {@link FocFlightInfo}
     * @return List <{@link TraceInfoDistinctVO}>
     * <AUTHOR>
     * @date 2025/2/6 10:46
     * @since 2025/2/6
     */
    private List<PaxInfoParseVo> focusSegmentPsg(FocFlightInfo focFlightInfo, Integer hours) {
        List<FocFlightInfo> currentFlightSegment = getCurrentFlightSegment(getFilterFlights(focFlightInfo.getFlightNo(), focFlightInfo.getFlightDate(), DOMESTIC, null, null, null), focFlightInfo, hours);
        if (currentFlightSegment.isEmpty()){
            return null;
        }
        ContentTraceForm contentTraceForm = new ContentTraceForm();
        contentTraceForm.setNotContainsN("Y");
        contentTraceForm.setIsPrintTktNo("Y");
        contentTraceForm.setTktEndDate(StringUtils.replace(focFlightInfo.getFlightDate(), "/", "-")+" "+focFlightInfo.getStd());
        log.info("【不正常航班-获取旅客-自动建单航班信息】：{},trace参数:{}", JSON.toJSONString(focFlightInfo), JSON.toJSONString(contentTraceForm));
        Map<String, Object> returnFlightPaxInfo = flightCompensateService.getReturnFlightPaxInfo(focFlightInfo.getFlightNo(), focFlightInfo.getFlightDate().replace("/","-"), currentFlightSegment.stream()
                .map(flightInfo -> flightInfo.getDepartPort() + "-" + flightInfo.getArrivalPort())
                .collect(Collectors.joining(",")), contentTraceForm, null);
        List<PaxInfoParseVo> result = (List<PaxInfoParseVo>) returnFlightPaxInfo.get("paxList");
        if (!result.isEmpty()){
            result.forEach(e->{
                e.setPhone(AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, e.getPhone()));
            });
        }
        log.info("【不正常航班-获取旅客-自动建单航班号】：{},旅客数量:{}", focFlightInfo.getFlightNo(), result.size());
        return result;
    }

    /**
     * Title: 构建 {@link ServiceOrderInfoDto}，用于和之前的建单逻辑联系上。
     *
     * @param focFlightInfo {@link FocFlightInfo}
     * @return {@link ServiceOrderInfoDto}
     * <AUTHOR>
     * @date 2025/2/5 14:39
     * @since 2025/2/5
     */
    private ServiceOrderInfoDto buildServiceOrderInfoDto(FocFlightInfo focFlightInfo, Integer hours) {
        List<OrdersInfo> ordersInfos = fetchOrdersInfo(focFlightInfo.getFlightNo(), StringUtils.replace(focFlightInfo.getFlightDate(), "/", "-"),
                focFlightInfo.getDepartPort() + "-" + focFlightInfo.getArrivalPort(), null, PAY_TYPE, true, null);
        if ((hours == TIME_FOUR && ordersInfos.isEmpty()) || (hours == TIME_EIGHT && !ordersInfos.isEmpty() && ordersInfos.size() == 1)) {
            return getServiceOrderInfoDtoSimple(focFlightInfo, getCurrentFlightSegment(getFilterFlights(focFlightInfo.getFlightNo(), focFlightInfo.getFlightDate(), DOMESTIC, null, null, null), focFlightInfo, hours));
        }
        return null;
    }

    /**
     * Title: 获取所需的航班信息，通过航班四要素和predicate获取
     *
     * @param flightNo   String
     * @param flightDate String
     * @param dOrI       String
     * @param flgDelay   String
     * @param flgCs      String
     * @param predicate  Predicate
     * @return List <{@link FocFlightInfo}>
     * <AUTHOR>
     * @date 2025/2/5 10:19
     * @since 2025/2/5
     */
    public List<FocFlightInfo> getFilterFlights(String flightNo, String flightDate, String dOrI, String flgDelay, String flgCs, List<java.util.function.Predicate<FocFlightInfo>> predicate) {
        List<FocFlightInfo> result = new ArrayList<>();
        Specification<FocFlightInfo> specification = (root, query, cb) -> {
            List<Predicate> predicatesList = new ArrayList<>();
            if (!StringUtils.isBlank(flightNo)) {
                Predicate flightNumPredicate = cb.equal(root.get("flightNo"), flightNo);
                predicatesList.add(flightNumPredicate);
            }
            if (!StringUtils.isBlank(flightDate)) {
                Predicate flightDatePredicate = cb.equal(root.get("flightDate"), flightDate);
                predicatesList.add(flightDatePredicate);
            }
            if (!StringUtils.isBlank(dOrI)) {
                Predicate dOrIPredicate = cb.equal(root.get("dOrI"), dOrI);
                predicatesList.add(dOrIPredicate);
            }
            if (!StringUtils.isBlank(flgDelay)) {
                Predicate flgDelayPredicate = cb.isNotNull(root.get("flgDelay"));
                predicatesList.add(flgDelayPredicate);
                //predicatesList.add(cb.or(flgDelayPredicate, flgCsPredicate));
            }
            if (!StringUtils.isBlank(flgCs)) {
                Predicate flgCsPredicate = cb.isNotNull(root.get("flgCs"));
                predicatesList.add(flgCsPredicate);
            }
            return cb.and(predicatesList.toArray(new Predicate[predicatesList.size()]));
        };
        List<FocFlightInfo> focFlightInfos = focFlightInfoRepository.findAll(specification);
        if (predicate != null && !predicate.isEmpty()) {
            for (java.util.function.Predicate<FocFlightInfo> predicate_Iterator : predicate) {
                result.addAll(focFlightInfos.stream().filter(predicate_Iterator).collect(Collectors.toList()));
            }
            return result;
        }
        return focFlightInfos;
    }

    // 延误的条件
    public List<java.util.function.Predicate<FocFlightInfo>> fetchDelayPredicate(int hours, int minute) {
        List<java.util.function.Predicate<FocFlightInfo>> predicates = new ArrayList<>();
        predicates.add((focFlightInfo -> StringUtils.isBlank(focFlightInfo.getAtd()) &&
                timeDifference(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm")), focFlightInfo.getStd(), hours, minute)));
        predicates.add((focFlightInfo -> StringUtils.isNoneBlank(focFlightInfo.getAtd()) &&
                timeDifference(focFlightInfo.getAtd(), focFlightInfo.getStd(), hours, minute)));
        return predicates;
    }

    // 取消的条件
    public List<java.util.function.Predicate<FocFlightInfo>> fetchCancelPredicate() {
        List<java.util.function.Predicate<FocFlightInfo>> predicates = new ArrayList<>();
        predicates.add((focFlightInfo -> StringUtils.isNotBlank(focFlightInfo.getFlgCs())
                && StringUtils.isNotBlank(focFlightInfo.getCs_reason())
                && focFlightInfo.getCs_reason().contains(COMPANY_REASON)
                && (StringUtils.isBlank(focFlightInfo.getAdjustType()) || !"0".equals(focFlightInfo.getAdjustType()))
        ));
        return predicates;
    }

    private List<OrdersInfo> fetchOrdersInfo(String flightNum, String flightDate, String choiceSegment, String serviceCity, String payType, Boolean autoCreate, String status) {
        Specification<OrdersInfo> specification = (root, query, cb) -> {
            List<Predicate> predicatesList = new ArrayList<>();
            if (!StringUtils.isBlank(flightNum) && !StringUtils.isBlank(flightDate)) {
                Predicate flightNumPredicate = cb.equal(root.get("flightNo"), flightNum);
                Predicate flightDatePredicate = cb.equal(root.get("flightDate"), flightDate);
                predicatesList.add(cb.and(flightNumPredicate, flightDatePredicate));
            }
            if (!StringUtils.isBlank(choiceSegment)) {
                Predicate segmentPredicate = cb.like(root.get("choiceSegment"), "%" + choiceSegment + "%");
                predicatesList.add(segmentPredicate);
            }
            if (!StringUtils.isBlank(serviceCity)) {
                Predicate serviceCityPredicate = cb.equal(root.get("serviceCity"), serviceCity);
                predicatesList.add(serviceCityPredicate);
            }
            if (!StringUtils.isBlank(status)) {
                Predicate statusPredicate = cb.equal(root.get("status"), status);
                predicatesList.add(statusPredicate);
            }
            if (!StringUtils.isBlank(payType)) {
                Predicate payTypePredicate = cb.equal(root.get("payType"), payType);
                predicatesList.add(payTypePredicate);
            }
            if (autoCreate) {
                Predicate autoCreatePredicate = cb.equal(root.get("autoCreate"), true);
                predicatesList.add(autoCreatePredicate);
            }
            return cb.and(predicatesList.toArray(new Predicate[predicatesList.size()]));
        };
        return ordersInfoDao.findAll(specification);
    }

    /**
     * Title: 根据航班查询旅客信息
     *
     * @param flightNum  String
     * @param flightDate String
     * @param orig       String
     * @param dst        String
     * @param isCancel   String
     * @return List <{@link TraceInfoDistinctVO}>
     * <AUTHOR>
     * @date 2025/2/6 11:10
     * @since 2025/2/6
     */
    private List<TraceInfoDistinctVO> getPsgListByFlight(String flightNum, String flightDate, String orig, String dst, String isCancel) {
        ContentTraceForm form = new ContentTraceForm();
        if (StringUtils.isBlank(flightNum) || StringUtils.isBlank(flightDate)) {
            return null;
        }
        form.setFlightDate(flightDate);
        form.setNotContainsN("Y");
        form.setFlightNum(flightNum);
        if (!StringUtils.isBlank(orig)) {
            form.setOrig(orig);
        }
        if (!StringUtils.isBlank(dst)) {
            form.setDest(dst);
        }
        if (!StringUtils.isBlank(isCancel)) {
            form.setIsCancel(null);
        }
        Object psgListByFlight = traceService.getPsgListByFilght(form);
        if (psgListByFlight != null) {
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(psgListByFlight));
            return JSONArray.parseArray(jsonObject.get("dataList").toString(), TraceInfoDistinctVO.class).stream().distinct().collect(Collectors.toList());
        }
        return null;
    }

    /**
     * Title: 用于旅客信息的转换，适配上历史的创建逻辑
     *
     * @param traceInfoDistinctVOList List<{@link TraceInfoDistinctVO}>
     * @return List <{@link PassengerInfo}>
     * <AUTHOR>
     * @date 2025/2/6 10:01
     * @since 2025/2/6
     */
    private List<PassengerInfo> passengerInfoCover(List<PaxInfoParseVo> traceInfoDistinctVOList) {
        if (traceInfoDistinctVOList == null || traceInfoDistinctVOList.isEmpty()) {
            return null;
        }
        List<PassengerInfo> result = new ArrayList<>();
        traceInfoDistinctVOList.forEach(v -> {
            PassengerInfo passengerInfo = new PassengerInfo();
            BeanUtils.copyProperties(v, passengerInfo);
            passengerInfo.setPaxName(v.getPsgName());
            passengerInfo.setIdNo(v.getIdNum());
            passengerInfo.setPaxStatus(v.getStatus());
            passengerInfo.setTktNo(v.getEtNum());
            passengerInfo.setSubClass(v.getSellClass());
            passengerInfo.setMainClass(v.getMainClass());
            passengerInfo.setTktDate(v.getPrintTicketTime());
            passengerInfo.setPaxId(v.getIdx());
            passengerInfo.setOrgCityAirp(v.getOrig());
            passengerInfo.setDstCityAirp(v.getDest());
            passengerInfo.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, v.getPhone()));
            result.add(passengerInfo);
        });
        return result;
    }

    /**
     * Title: 获取到当前航班的航班，包含直接和AC的航班信息
     *
     * @param filterFlights List <{@link FocFlightInfo}>
     * @param currentFlight {@link FocFlightInfo}
     * @param semaphore     {@link Integer}
     * @return List <{@link FocFlightInfo}>
     * <AUTHOR>
     * @date 2025/2/6 11:03
     * @since 2025/2/6
     */
    public List<FocFlightInfo> getCurrentFlightSegment(List<FocFlightInfo> filterFlights, FocFlightInfo currentFlight, Integer semaphore) {
        List<FocFlightInfo> result = new ArrayList<>();
        if (filterFlights == null || filterFlights.isEmpty()) {
            return result;
        }
        if (filterFlights.size() == 1) {
            List<OrdersInfo> ordersInfos = fetchOrdersInfo(currentFlight.getFlightNo(), StringUtils.replace(currentFlight.getFlightDate(), "/", "-"),
                    currentFlight.getDepartPort() + "-" + currentFlight.getArrivalPort(), null, PAY_TYPE, true, null);
            if ((semaphore == TIME_FOUR && ordersInfos.isEmpty()) || (semaphore == TIME_EIGHT && !ordersInfos.isEmpty() && ordersInfos.size() == 1)) {
                result.add(currentFlight);
                log.info("【不正常航班-flightInfo_单程-自动建单航班号】：{},信号量：{},详细数据:{}", currentFlight.getFlightNo(), semaphore, JSON.toJSONString(result));
            }
            return result;
        }
        Map<String, List<FocFlightInfo>> collect = filterFlights.stream().collect(Collectors.groupingBy(FocFlightInfo::getPod));
        FocFlightInfo flightInfo_BC = collect.values().stream()
                .filter(focFlightInfos -> focFlightInfos.size() == 1)
                .map(focFlightInfos -> focFlightInfos.get(0))
                .findFirst()
                .orElse(null);
        if (flightInfo_BC == null) {
            return result;
        }
        FocFlightInfo flightInfo_AB = collect.values().stream()
                .filter(focFlightInfos -> focFlightInfos.size() == 2)
                .flatMap(List::stream)
                .filter(flightInfo -> flightInfo.getArrivalPort().equals(flightInfo_BC.getDepartPort())) // 过滤符合条件的
                .findFirst()
                .orElse(null);
        FocFlightInfo flightInfo_AC = collect.values().stream()
                .filter(focFlightInfos -> focFlightInfos.size() == 2)
                .flatMap(List::stream)
                .filter(flightInfo -> flightInfo.getArrivalPort().equals(flightInfo_BC.getArrivalPort()) && flightInfo.getDepartPort().equals(flightInfo_AB.getDepartPort()))
                .findFirst()
                .orElse(null);
        if (flightInfo_AB != null && flightInfo_AC != null && currentFlight.getFlightId().equals(flightInfo_AB.getFlightId())) {
            List<OrdersInfo> ordersInfos = fetchOrdersInfo(flightInfo_AC.getFlightNo(), StringUtils.replace(flightInfo_AC.getFlightDate(), "/", "-"),
                    flightInfo_AC.getDepartPort() + "-" + flightInfo_AC.getArrivalPort(), null, PAY_TYPE, true, null);
            if (semaphore == TIME_FOUR && ordersInfos.isEmpty()) {
                result.add(flightInfo_AB);
                result.add(flightInfo_AC);
                log.info("【不正常航班-flightInfo_ABAC-4h自动建单航班号】：{},信号量：{},详细数据:{}", currentFlight.getFlightNo(), semaphore, JSON.toJSONString(result));
            }
            // 只有等于1的时候才创建，因为等于0 也就是当前没有创建4h的第一个单子，下次循环再来的时候即可，如果大于1 说明创建了一个4h和一个8h的单子，所以不需要再创建了
            // 避免连续的创建两次8h，就目前的情况来看，连续两次8h也没有什么问题，目前的表结构只有加一点日志进行记录排查后续问题，没有相应的标识来标记延误时常
            if (semaphore == TIME_EIGHT && !ordersInfos.isEmpty() && ordersInfos.size() == 1) {
                result.add(flightInfo_AB);
                result.add(flightInfo_AC);
                log.info("【不正常航班-flightInfo_ABAC-8h自动建单航班号】：{},信号量：{},详细数据:{}", currentFlight.getFlightNo(), semaphore, JSON.toJSONString(result));
            }
        }
        if (flightInfo_AB != null && flightInfo_AC != null && currentFlight.getFlightId().equals(flightInfo_BC.getFlightId())) {
            List<OrdersInfo> ordersInfoAC = fetchOrdersInfo(flightInfo_AC.getFlightNo(), StringUtils.replace(flightInfo_AC.getFlightDate(), "/", "-"),
                    flightInfo_AC.getDepartPort() + "-" + flightInfo_AC.getArrivalPort(), null, PAY_TYPE, true, null);
            List<OrdersInfo> ordersInfoBC = fetchOrdersInfo(flightInfo_BC.getFlightNo(), StringUtils.replace(flightInfo_BC.getFlightDate(), "/", "-"),
                    flightInfo_BC.getDepartPort() + "-" + flightInfo_BC.getArrivalPort(), null, PAY_TYPE, true, null);
            if (StringUtils.isNotBlank(flightInfo_AB.getFlgDelay())) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
                if (StringUtils.isBlank(flightInfo_AB.getAtd()) ? timeDifference(LocalDateTime.now().format(formatter), LocalTime.parse(flightInfo_AB.getStd()).format(formatter), TIME_FOUR, TIME_MINUTE)
                        : timeDifference(LocalTime.parse(flightInfo_AB.getAtd()).format(formatter), LocalTime.parse(flightInfo_AB.getStd()).format(formatter), TIME_FOUR, TIME_MINUTE)) {
                    if ((semaphore == TIME_FOUR && ordersInfoBC.isEmpty()) || (semaphore == TIME_EIGHT && !ordersInfoBC.isEmpty() && ordersInfoBC.size() == 1)) {
                        result.add(flightInfo_BC);
                        log.info("【不正常航班-flightInfo_BC-AB延误-自动建单航班号】：{},信号量：{},详细数据:{}", currentFlight.getFlightNo(), semaphore, JSON.toJSONString(result));
                    }
                } else {
                    if ((semaphore == TIME_FOUR && ordersInfoAC.isEmpty()) || (semaphore == TIME_EIGHT && !ordersInfoAC.isEmpty() && ordersInfoAC.size() == 1)) {
                        result.add(flightInfo_BC);
                        result.add(flightInfo_AC);
                        log.info("【不正常航班-flightInfo_BCAC-AB延误-自动建单航班号】：{},信号量：{},详细数据:{}", currentFlight.getFlightNo(), semaphore, JSON.toJSONString(result));
                    }
                }
            } else {
                if ((semaphore == TIME_FOUR && ordersInfoAC.isEmpty()) || (semaphore == TIME_EIGHT && !ordersInfoAC.isEmpty() && ordersInfoAC.size() == 1)) {
                    result.add(flightInfo_BC);
                    result.add(flightInfo_AC);
                    log.info("【不正常航班-flightInfo_BCAC-AB非延误-自动建单航班号】：{},信号量：{},详细数据:{}", currentFlight.getFlightNo(), semaphore, JSON.toJSONString(result));
                }
            }
        }
        log.info("【不正常航班-getCurrentFlightSegment-自动建单航班号】：{},航段:{},信号量：{},详细数据:{}", currentFlight.getFlightNo(), currentFlight.getDepartPort() + "-" + currentFlight.getArrivalPort(), semaphore, JSON.toJSONString(result));
        return result;
    }

    /**
     * Title: orders赔偿单信息组装
     *
     * @param focFlightInfo        {@link FocFlightInfo}
     * @param currentFlightSegment List <{@link FocFlightInfo}>
     * @return {@link ServiceOrderInfoDto}
     * <AUTHOR>
     * @date 2025/2/11 09:39
     * @since 2025/2/11
     */
    private ServiceOrderInfoDto getServiceOrderInfoDtoSimple(FocFlightInfo focFlightInfo, List<FocFlightInfo> currentFlightSegment) {
        if (currentFlightSegment == null || currentFlightSegment.isEmpty()) {
            return null;
        }
        List<FocFlightInfo> filterFlights = getFilterFlights(focFlightInfo.getFlightNo(), focFlightInfo.getFlightDate(), DOMESTIC, null, null, null);

        ServiceOrderInfoDto serviceOrderInfoDto = new ServiceOrderInfoDto();
        if (!filterFlights.isEmpty()) {
            serviceOrderInfoDto.setAllSegment(filterFlights.stream()
                    .map(flightInfo -> flightInfo.getDepartPort() + "-" + flightInfo.getArrivalPort())
                    .collect(Collectors.joining(",")));
        }else {
            serviceOrderInfoDto.setAllSegment(currentFlightSegment.stream()
                    .map(flightInfo -> flightInfo.getDepartPort() + "-" + flightInfo.getArrivalPort())
                    .collect(Collectors.joining(",")));
        }
        serviceOrderInfoDto.setChoiceSegment(currentFlightSegment.stream()
                .map(flightInfo -> flightInfo.getDepartPort() + "-" + flightInfo.getArrivalPort())
                .collect(Collectors.joining(",")));
        serviceOrderInfoDto.setFlightId(focFlightInfo.getFlightId());
        serviceOrderInfoDto.setFlightNo(focFlightInfo.getFlightNo());
        serviceOrderInfoDto.setFlightDate(StringUtils.replace(focFlightInfo.getFlightDate(), "/", "-"));
        serviceOrderInfoDto.setPayType(PAY_TYPE);
        serviceOrderInfoDto.setAcType(focFlightInfo.getAcType());
        serviceOrderInfoDto.setStd(focFlightInfo.getStd());
        serviceOrderInfoDto.setSta(focFlightInfo.getSta());
        serviceOrderInfoDto.setEtd(focFlightInfo.getEtd());
        serviceOrderInfoDto.setStatus(DRAFT_STATUS);
        serviceOrderInfoDto.setLateReason(focFlightInfo.getDelay_reason());
        serviceOrderInfoDto.setPlaneCode(focFlightInfo.getAcReg());
        serviceOrderInfoDto.setServiceCity(focFlightInfo.getDepartPort());
        serviceOrderInfoDto.setRemark(REMARK);
        serviceOrderInfoDto.setAutoCreate(true);
        log.info("【不正常航班-getServiceOrderInfoDtoSimple-自动建单航班号】：{},详细数据:{}", focFlightInfo.getFlightNo(), serviceOrderInfoDto);
        return serviceOrderInfoDto;
    }

    /**
     * Title: 统一调度，用于自动创建不正常航班的赔偿单
     *
     * @param orderInfo   {@link ServiceOrderInfoDto}
     * @param paxInfoList List {@link PassengerInfo}
     * <AUTHOR>
     * @date 2025/2/10 13:39
     * @since 2025/2/10
     */
    private void createFlightCompensationOrder(ServiceOrderInfoDto orderInfo, List<PassengerInfo> paxInfoList) {
        if (paxInfoList != null && !paxInfoList.isEmpty() && orderInfo != null) {
            OrderInfoParamDto orderInfoParamDto = new OrderInfoParamDto();
            orderInfoParamDto.setCompensateInfoList(getCompensationList());
            orderInfoParamDto.setOrderInfo(orderInfo);
            orderInfoParamDto.setPaxInfoList(paxInfoList);
            log.info("【不正常航班-createFlightCompensationOrder-自动建单航班号】：{},旅客数据:{}", orderInfo.getFlightNo(), paxInfoList.size());
            flightCompensateService.saveOrderInfo(orderInfoParamDto);
        }
    }


    /**
     * Title: 时间比对工具方法，判断延误时间
     *
     * @param startTime {@link String}
     * @param endTime   {@link String}
     * @param hours     {@link Integer}
     * @param minute    {@link Integer}
     * @return {@link Boolean}
     * <AUTHOR>
     * @date 2025/2/10 13:37
     * @since 2025/2/10
     */
    private Boolean timeDifference(String startTime, String endTime, int hours, int minute) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return false;
        }
        String[] start = startTime.split(":");
        int startInter = Integer.parseInt(start[0]) * 60 + Integer.parseInt(start[1]);
        String[] end = endTime.split(":");
        int endInter = Integer.parseInt(end[0]) * 60 + Integer.parseInt(end[1]);
        int abs = startInter - endInter;
        if (abs <= 0) {
            return false;
        }
        int hour = abs / 60;
        int min = abs % 60;
        if (hour > hours) {
            return true;
        }
        if (hour == hours && min > minute) {
            return true;
        }
        return false;
    }

    /**
     * Title: 获取补偿标准，后端固定赋值
     *
     * @return List <{@link CompensatesInfo}>
     * <AUTHOR>
     * @date 2025/2/10 13:35
     * @since 2025/2/10
     */
    private List<CompensatesInfo> getCompensationList() {
        List<CompensatesInfo> compensationList = new ArrayList<>();
        compensationList.add(new CompensatesInfo("1", 200, 100, 100));
        compensationList.add(new CompensatesInfo("2", 200, 100, 100));
        compensationList.add(new CompensatesInfo("", 0, 100, 100));
        return compensationList;
    }
}
