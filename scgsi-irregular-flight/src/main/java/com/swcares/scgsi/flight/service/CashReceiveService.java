package com.swcares.scgsi.flight.service;

import com.swcares.scgsi.flight.dto.ApplyInfoDto;
import com.swcares.scgsi.flight.vo.CashOrderInfoVo;
import com.swcares.scgsi.flight.vo.CashServiceOrderVo;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.service <br>
 * Description：现金领取 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月17日 15:09 <br>
 * @version v1.0 <br>
 */
public interface CashReceiveService {
    /**
     * Title：getCashOrderInfo <br>
     * Description： 根据航班号航班日期查看下面的赔付单<br>
     * author：王建文 <br>
     * date：2020-3-17 14:12 <br>
     * @param  flightNo 航班号
     * @param  flightDate 航班日期
     * @return
     */
    public List<CashOrderInfoVo> getCashOrderInfo(String flightNo,String flightDate);
    /**
     * Title：getCashOrderDetail <br>
     * Description：根据赔付单id获取赔付详情<br>
     * author：王建文 <br>
     * date：2020-3-17 17:00 <br>
     * @param  orderId 赔付单号
     * @return
     */
    public Map<String, Object> getCashOrderDetail(String orderId);
    /**
     * Title：getFlightSegment <br>
     * Description： 根据航班号航班日期获取赔付单下面旅客所有航段<br>
     * author：王建文 <br>
     * date：2020-3-17 19:29 <br>
     * @param  flightNo 航班号
     * @param  flightDate 航班日期
     * @return
     */
    public List<String> getFlightSegment(String flightNo,String flightDate);
    /**
     * Title：getPaxServiceOrder <br>
     * Description： 获取旅客赔偿单列表<br>
     * author：王建文 <br>
     * date：2020-3-18 9:52 <br>
     * @param  flightNo 航班号
     * @param  flightDate 航班日期
     * @param  paxId 旅客ID
     * @return
     */
    public List<CashServiceOrderVo> getPaxServiceOrder(String flightNo,String flightDate,String paxId);
    /**
     * Title：saveCashApplyInfo <br>
     * Description： 保存现金领取提交信息<br>
     * author：王建文 <br>
     * date：2020-3-18 10:52 <br>
     * @param  applyInfoDto 参数接收
     * @return
     */
    public void saveCashApplyInfo(ApplyInfoDto applyInfoDto);
    /**
     * Title：getDelayFlightInfo <br>
     * Description：现金领取航班信息查询<br>
     * author：王建文 <br>
     * date：2020-3-17 16:23 <br>
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     */
    public Map<String,Object> getDelayFlightInfo(String flightNo, String flightDate);
}
