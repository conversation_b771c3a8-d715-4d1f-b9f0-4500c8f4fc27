package com.swcares.scgsi.flight.vo;

import lombok.Data;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.vo <br>
 * Description：旅客申领返回前端对象信息<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月10日 15:16 <br>
 * @version v1.0 <br>
 */
@Data
public class AuthReceiveInfoVo   {
    /**
     * 旅客ID
     */
    private String paxId;
    /**
     * 旅客姓名
     */
    private String paxName;
    /**
     * 联系电话
     */
    private String telephone;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号
     */
    private String idNo;
    /**
     * 证件号明文
     */
    private String idNo1;

    /**
     * 性别C儿童M男F女
     */
    private String sex;
    /**
     * 婴儿名字
     */
    private String babyName;
    /**
     * 旅客总赔偿金额
     */
    private String payMoney;
    /**
     * 赔偿单号
     */
    private String orderId;
    /**
     * 票号
     */
    private String tktNo;
    /**
     * 儿童标识
     */
    private String isChild;
    /**
     * 携带婴儿标识
     */
    private String isInfant;
    /**
     * 领取次数
     */
    private int receiveCount;
    /**
     * 领取次数限制
     */
    private int limitCount;
    /**
     * 旅客申领，赔付单信息
     */
   private List<AuthPaxOrderInfoVo> orderInfo;
}