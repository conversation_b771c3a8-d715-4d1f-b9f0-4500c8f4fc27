package com.swcares.scgsi.flight.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.flight.dto.ApplyInfoDto;
import com.swcares.scgsi.flight.dto.ApplyParamDto;
import com.swcares.scgsi.flight.dto.ApplyPaxInfoDto;
import com.swcares.scgsi.flight.entity.ApplyPaxInfo;
import com.swcares.scgsi.flight.service.CashReceiveService;
import com.swcares.scgsi.flight.service.PaxInfoService;
import com.swcares.scgsi.flight.service.TransactionLockService;
import com.swcares.scgsi.flight.vo.AuthReceiveInfoVo;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.util.Asserts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/
 * Title：TransactionLockServiceImpl
 * Package：com.swcares.eupsi.flight.service.impl
 * Description：事务提交申领
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2022-05-18
 */
@Service
@Slf4j
public class TransactionLockServiceImpl implements TransactionLockService {

    private static final String APPLY_PAX_LOOK = "APPLY_PAX_LOOK";

    //分布式锁过期时间 秒
    private final int LEASE_TIME = 15;

    private final RedissonClient redisson;

    private final CashReceiveService cashReceiveService;

    private final PaxInfoService paxInfoService;

    public TransactionLockServiceImpl(RedissonClient redisson, CashReceiveService cashReceiveService, PaxInfoService paxInfoService) {
        this.redisson = redisson;
        this.cashReceiveService = cashReceiveService;
        this.paxInfoService = paxInfoService;
    }

    @Override
    public void saveNormalApplyInfo(ApplyParamDto applyParamDto) {
        log.info("【本人领取】普通申领发起:[{}]", JSONObject.toJSONString(applyParamDto));
        List<ApplyPaxInfoDto> paxInfos = applyParamDto.getPaxInfo();
        if (ObjectUtils.isEmpty(paxInfos) || paxInfos.isEmpty()) {
            throw new BusinessException(MessageCode.PAX_CASH_SUBMIT.getCode());
        }
        Set<String> paxIds = new HashSet<>();
        for (ApplyPaxInfo paxInfo : paxInfos) {
            paxIds.add(paxInfo.getPaxId());
        }
        RLock multiLock = this.getMultiLock(paxIds);
        multiLock.lock(LEASE_TIME, TimeUnit.SECONDS);
        try {
            paxInfoService.saveNormalApplyInfo(applyParamDto);
        } finally {
            multiLock.unlock();
            log.info("【本人领取】本人领取保存申领单结束，前端提交的参数为:【{}】", JSONObject.toJSONString(applyParamDto));
        }
    }

    @Override
    public void saveActApplyInfo(ApplyParamDto applyParamDto) {
        log.info("【代人领取】代领申领发起:[{}]", JSONObject.toJSONString(applyParamDto));
        List<ApplyPaxInfoDto> paxInfos = applyParamDto.getPaxInfo();
        if (ObjectUtils.isEmpty(paxInfos) || paxInfos.isEmpty()) {
            throw new BusinessException(MessageCode.PAX_CASH_SUBMIT.getCode());
        }
        Set<String> paxIds = new HashSet<>();
        for (ApplyPaxInfo paxInfo : paxInfos) {
            paxIds.add(paxInfo.getPaxId());
        }

        RLock multiLock = this.getMultiLock(paxIds);
        multiLock.lock(LEASE_TIME, TimeUnit.SECONDS);
        try {
            paxInfoService.saveActApplyInfo(applyParamDto);
        } finally {
            multiLock.unlock();
            log.info("【代人领取】代人领取保存申领单结束，前端提交的参数为:【{}】", JSONObject.toJSONString(applyParamDto));
        }
    }

    @Override
    public void saveCashApplyInfo(ApplyInfoDto applyInfoDto) {
        log.info("现金申领发起:【{}】", JSONObject.toJSONString(applyInfoDto));
        if(StringUtils.isNotEmpty(applyInfoDto.getTelephone())){
            applyInfoDto.setTelephone(AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, applyInfoDto.getTelephone()));
        }
        Asserts.isNotEmpty(applyInfoDto.getOrderId(), MessageCode.PARAM_EXCEPTION.getCode(), new String[]{"orderId"});
        List<AuthReceiveInfoVo> authReceiveInfoVoList = paxInfoService.getPaxInfoData(applyInfoDto.getIdNo(), applyInfoDto.getFlightNo(), applyInfoDto.getFlightDate());
        if (authReceiveInfoVoList.isEmpty()) {
            throw new BusinessException(MessageCode.PAX_CASH_SUBMIT.getCode());
        }

        Set<String> paxIds = new HashSet<>();
        for (AuthReceiveInfoVo e : authReceiveInfoVoList) {
            paxIds.add(e.getPaxId());
        }

        RLock multiLock = this.getMultiLock(paxIds);
        multiLock.lock(LEASE_TIME, TimeUnit.SECONDS);
        try {
            cashReceiveService.saveCashApplyInfo(applyInfoDto);
        } finally {
            multiLock.unlock();
            log.info("现金领取保存申领单结束，前端提交的参数为:【{}】", JSONObject.toJSONString(applyInfoDto));
        }
    }

    private RLock getMultiLock(Set<String> paxIds) {
        RLock[] paxArray = new RLock[paxIds.size()];
        int i = 0;
        for (String paxId : paxIds) {
            log.info("申领时事务锁序列值:【{}】,旅客id:【{}】", i, paxId);
            paxArray[i] = redisson.getLock(APPLY_PAX_LOOK +"_"+ paxId);
            i++;
        }
        return redisson.getMultiLock(paxArray);
    }
}