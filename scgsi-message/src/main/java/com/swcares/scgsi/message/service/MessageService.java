package com.swcares.scgsi.message.service;

import java.util.List;
import org.springframework.data.domain.Pageable;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.message.common.model.form.MessageListForm;
import com.swcares.scgsi.message.common.model.form.MessageSendForm;
import com.swcares.scgsi.message.common.model.vo.NewMessageVo;


/**
 * ClassName：com.swcares.scgsi.message.service.MessageService <br>
 * Description：消息服务层<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月3日 上午11:44:50 <br>
 * @version v1.0 <br>
 */
public interface MessageService {

    /**
     * Title：getNewMsgAmount <br>
     * Description：通过用户ID获取最新消息 <br>
     * author：王磊 <br>
     * date：2020年3月5日 下午4:39:27 <br>
     * @param userId 用户ID
     * @param isAudit 是否获取审核数据
     * @return 返回最新消息集合<br>
     */
    List<NewMessageVo> getNewMsgAmount(String userId, Integer source) throws Exception;

    /**
     * Title：getMsgList <br>
     * Description：消息列表(已读,未读,全部)通用函数 <br>
     * author：王磊 <br>
     * date：2020年3月5日 下午10:42:41 <br>
     * @param pageable
     * @param form
     * @param isAudit 是否审核标识用来判断是否是移动端还是pc端
     * @return <br>
     */
    QueryResults getMsgList(Pageable pageable, MessageListForm form, Integer source) throws Exception;

    /**
     * Title：updateMsgRead <br>
     * Description：根据用户id和消息id和消息类型更改消息标状态,同时会处理消息部门的发送状态,此部门有人已读后视为已发送,更改状态 <br>
     * author：王磊 <br>
     * date：2020年3月5日 下午4:40:00 <br>
     * @param msgId 消息ID
     * @param userID 用户ID
     * @param msgType  消息类型
     * @return <br>
     */
    void updateMsgRead(String[] msgId, String userID, String msgType) throws Exception;

    /**
     * Title：sendMsg<br>
     * Description：发送信息 <br>
     * author：王磊 <br>
     * date：2020年3月5日 下午4:40:00 <br>
     * @param msgId 消息ID
     * @param userID 用户ID
     * @return <br>
     */
    void sendMsg(MessageSendForm messageSendForm) throws Exception;
    
    /**
     * Title：sendMsgToUser <br>
     * Description：发送信息给用户 <br>
     * author：王磊 <br>
     * date：2020年3月5日 下午4:40:00 <br>
     * @param MessageSendForm 
     * @return <br>
     */
    void sendMsgToUser(MessageSendForm messageSendForm) throws Exception;
    
    /**
     * Title：sendMsgToDepartment <br>
     * Description：发送信息给部门 <br>
     * author：王磊 <br>
     * date：2020年3月5日 下午4:40:00 <br>
     * @param MessageSendForm 
     * @return <br>
     */
    void sendMsgToDepartment(MessageSendForm messageSendForm) throws Exception;
    
    /**
     * Title：sendMsgTask <br>
     * Description：诊断状态为未发送的部门通过定时任务扫描来重新发送 <br>
     * author：王磊 <br>
     * date：2020年4月8日 下午4:40:00 <br>
     * @return <br>
     */
    void sendMsgTask() throws Exception;

}
