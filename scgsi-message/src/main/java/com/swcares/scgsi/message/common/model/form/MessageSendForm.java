package com.swcares.scgsi.message.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * ClassName：com.swcares.message.common.model.from.MessageSendForm <br>
 * Description：发送消息form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月6日 下午3:07:11 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "消息发送表单对象")
public class MessageSendForm {
    /**消息类型*/
    @ApiModelProperty(value = "消息类型", required = true)
    private Integer msgType;
    /**消息类型名称*/
    @ApiModelProperty(value = "消息类型名称", required = true)
    private String msgTypeName;
    /**消息标题*/
    @ApiModelProperty(value = "消息标题", required = true)
    private String msgTitle;
    /**消息内容*/
    @ApiModelProperty(value = "消息内容", required = true)
    private String msgContent;
    /**发起人id*/
    @ApiModelProperty(value = "发起人id", required = true)
    private String msgUser;
    /**发送时间*/
    @ApiModelProperty(value = "发送时间", required = true)
    private Date msgDate;
    /**航班号*/
    @ApiModelProperty(value = "航班号", required = true)
    private String flightNo;
    /**航班日期*/
    @ApiModelProperty(value = "航班日期", required = true)
    private String flightDate;
    /**接收人员id*/
    @ApiModelProperty(value = "接收用户工号数组")
    private String[] msgReplyUser;
    /**接收部门id*/
    @ApiModelProperty(value = "接收部门ID数组")
    private String[] msgReplyDepartment;
    /**消息子类型*/
    @ApiModelProperty(value = "消息子类型")
    private String msgChildType;
    /**消息子类型名称*/
    @ApiModelProperty(value = "消息子类型名称")
    private String msgChildTypeName;
    /**pc端跳转地址*/
    @ApiModelProperty(value = "pc端跳转地址")
    private String pcUrl;
    /**手机端跳转地址*/
    @ApiModelProperty(value = "手机端跳转地址")
    private String mobileUrl;
    /**是否审核消息*/
    @ApiModelProperty(value = "是否审核消息(0非审核消息1审核消息)")
    private Integer isAudit ;
}
