package com.swcares.scgsi.message.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.message.entity.Message <br>
 * Description：新消息前段返回类<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月5日 下午4:04:08 <br>
 * @version v1.0 <br>
 */
@ApiModel("新消息提醒返回vo")
@Data
public class NewMessageVo {
    @ApiModelProperty("新消息数量")
    private Integer msgAmount;
    @ApiModelProperty("消息类型")
    private int msgType;
    @ApiModelProperty("消息类型名称")
    private String msgTypeName;
    @ApiModelProperty("时间")
    private String msgDate;
    @ApiModelProperty("消息内容")
    private String msgContent;
    @ApiModelProperty("航班号")
    private String flightNo;
    @ApiModelProperty("航班日期")
    private String flightDate;
}
