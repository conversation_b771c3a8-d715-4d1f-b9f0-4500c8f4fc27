package com.swcares.scgsi.message.entity;

import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import org.hibernate.annotations.GenericGenerator;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.message.entity.Message <br>
 * Description：消息发送实体类<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月4日 下午4:04:08 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "SYS_MESSAGE_SEND")
@Data
public class MessageSend {
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;
    @Column(name = "MSG_ID")
    private String msgId;
    /**接收人ID*/
    @Column(name = "MSG_USER")
    private String msgUser;
    /**接收人部门*/
    @Column(name = "MSG_DEPARTMENT")
    private String msgDepartment;
    /**关联接收人的消息状态1未读,2已读,3已回复*/
    @Column(name = "MSG_STATE")
    private int msgState = 1;
    /**消息类型(1旅客赔偿,2异常行李.3航班监控4.我的已发)*/
    @Column(name = "MSG_TYPE")
    private int msgType;
    /**消息发送时间*/
    @Column(name = "MSG_SEND_DATE")
    private Timestamp msgSendDate;
}
