package com.swcares.scgsi.message.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * ClassName：com.swcares.message.common.model.from.MessageListForm <br>
 * Description：消息列表表单对象 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月4日 下午3:28:13 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "消息列表表单对象")
public class MessageListForm {
    /** 用户ID */
    private String userID;
    /**当前页码*/
    @Min(value = 1, message = "页码最小为1")
    @NotNull(message = "页码不能为空")
    @ApiModelProperty(value = "当前页码", required = true)
    private Integer page;
    /** 每页条数 */
    @Min(value = 1, message = "每页条数最小为1")
    @NotNull(message = "每页条数不能为空")
    @ApiModelProperty(value = "每页条数", required = true)
    private Integer size;
    /** 消息类型(1旅客赔偿,2异常行李.3航班监控4.我的已发) */
    @NotNull(message = "消息类型不能为空(1旅客赔偿,2异常行李.3航班监控4.我的已发0.审核(移动端特有虚拟类型)) ")
    @ApiModelProperty(value = "消息类型", required = true)
    private Integer msgType;
    /** 消息状态(0全部,1未读,2已读,3历史消息) */
    @NotNull(message = "消息状态不能为空")
    @ApiModelProperty(value = "消息状态(0全部,1未读,2已读,3历史消息)", required = true)
    private Integer msgState;
    /** 查询开始时间 */
    @ApiModelProperty(value = "开始时间")
    private String beginDate;
    /** 查询结束时间 */
    @ApiModelProperty(value = "结束时间")
    private String endDate;
}
