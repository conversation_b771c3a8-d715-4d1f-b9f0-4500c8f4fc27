package com.swcares.scgsi.message.service.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.userenum.UserEnum;
import com.swcares.scgsi.hum.employee.entity.Employee;
import com.swcares.scgsi.message.common.model.form.MessageListForm;
import com.swcares.scgsi.message.common.model.form.MessageSendForm;
import com.swcares.scgsi.message.common.model.vo.NewMessageVo;
import com.swcares.scgsi.message.common.msgenum.MessageEnum;
import com.swcares.scgsi.message.common.utils.JpaMapToEntity;
import com.swcares.scgsi.message.dao.MessageDao;
import com.swcares.scgsi.message.dao.MessageJpaDao;
import com.swcares.scgsi.message.dao.MessageSendDepartmentJpaDao;
import com.swcares.scgsi.message.dao.MessageSendJpaDao;
import com.swcares.scgsi.message.entity.Message;
import com.swcares.scgsi.message.entity.MessageSend;
import com.swcares.scgsi.message.entity.MessageSendDepartment;
import com.swcares.scgsi.message.service.MessageService;
import com.swcares.scgsi.user.dao.UserJpaDao;

@Service
@Slf4j
public class MessageServiceImpl implements MessageService {
    /** 注入 */
    @Autowired
    private MessageJpaDao messageJpaDao;

    @Autowired
    private MessageDao messageDao;

    @Autowired
    private UserJpaDao userJpaDao;

    @Autowired
    private MessageSendJpaDao messageSendJpaDao;

    @Autowired
    private MessageSendDepartmentJpaDao messageSendDepartmentJpaDao;

    @Override
    public List<NewMessageVo> getNewMsgAmount(String userId, Integer source) throws Exception {
        List<NewMessageVo> message = new ArrayList<NewMessageVo>();
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        try {
            if (source == MessageEnum.MOBILE.getCode()) {
                list = messageJpaDao.getNewMsgAmountMobile(userId);
            } else {
                list = messageJpaDao.getNewMsgAmount(userId);
            }
            for (int i = 0; i < list.size(); i++) {
                message.add(JpaMapToEntity.mapToEntity(list.get(i), NewMessageVo.class));
            }
        } catch (IllegalAccessException e) {
            log.error("构建消息通知vo失败{}", e);
            throw e;
        } catch (InstantiationException e) {
            log.error("构建消息通知vo失败{}", e);
            throw e;
        } catch (Exception e) {
            log.error("获取新消息异常{}", e);
            throw e;
        }
        return message;
    }

    @Override
    public QueryResults getMsgList(Pageable pageable, MessageListForm form, Integer source)
            throws Exception {
        try {
            if (source == MessageEnum.MOBILE.getCode()) {
                return messageDao.getMsgListMobile(pageable, form);
            } else {
                return messageDao.getMsgList(pageable, form);
            }
        } catch (Exception e) {
            log.error("获取消息列表错误{}", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public void updateMsgRead(String[] msgId, String userID, String msgType) throws Exception {
        List<MessageSend> list = new ArrayList<MessageSend>();
        List<MessageSendDepartment> departList = new ArrayList<MessageSendDepartment>();
        // 判断是否通过类型来标记全部还是传入消息Id
        if (StringUtils.isNotEmpty(msgType)) {
            // 判断是否为虚拟的审核类型
            if (msgType.equals(MessageEnum.MSG_UN_SEND_STATE.getCode().toString())) {
                List<Map<String, Object>> result = messageSendJpaDao.findByIsAudit(userID);
                for (int i = 0; i < result.size(); i++) {
                    list.add(JpaMapToEntity.mapToEntity(result.get(i), MessageSend.class));
                }
            } else {
                list =
                        messageSendJpaDao.findByMsgUserAndMsgTypeAndMsgState(userID, new Integer(
                                msgType), MessageEnum.MSG_UN_READ.getCode());
            }
        } else {
            List<String> listIds = Arrays.asList(msgId);
            list = messageSendJpaDao.findByMsgUserAndMsgId(userID,listIds.get(0));
        }
        // 循环修改为已读状态
        for (MessageSend messageSend : list) {
            // 更改消息状态为已读
            messageSend.setMsgState(MessageEnum.MSG_READ.getCode());
            // 修改部门关联消息状态
            Optional<Employee> optional = userJpaDao.findById(userID);
            Employee employee = optional.get();
            // 获取部门关联消息数据
            MessageSendDepartment messageSendDepartment =
                    messageSendDepartmentJpaDao.findByMsgIdAndMsgDepartmentAndMsgSendState(
                            messageSend.getMsgId(), employee.getToId(),
                            MessageEnum.MSG_UN_SEND_STATE.getCode());
            if(messageSendDepartment != null){
                // 此部门有人点击后视为已发送更改状态
                messageSendDepartment.setMsgSendState(MessageEnum.MSG_SEND_STATE.getCode());
                departList.add(messageSendDepartment);
            }
        }
        messageSendJpaDao.saveAll(list);
        if (departList.size() > 0) {
            messageSendDepartmentJpaDao.saveAll(departList);
        }
    }

    @Override
    public void sendMsg(MessageSendForm messageSendForm) throws Exception {
        if (messageSendForm.getMsgReplyUser() != null
                && messageSendForm.getMsgReplyUser().length > 0) {
            sendMsgToUser(messageSendForm);
        } else if (messageSendForm.getMsgReplyDepartment() != null
                && messageSendForm.getMsgReplyDepartment().length > 0) {
            sendMsgToDepartment(messageSendForm);
        }
    }

    @Override
    @Transactional
    public void sendMsgToUser(MessageSendForm messageSendForm) throws Exception {
        try {
            Message message = new Message();
            BeanUtils.copyProperties(messageSendForm, message);
            message.setMsgDate(new Timestamp(new Date().getTime()));
            messageJpaDao.save(message);
            List<MessageSend> list = new ArrayList<MessageSend>();
            // 循环传入人员
            for (String tuNo : messageSendForm.getMsgReplyUser()) {
                if (StringUtils.isNotEmpty(tuNo)) {
                    MessageSend messageSend = new MessageSend();
                    messageSend.setMsgId(message.getMsgId());
                    messageSend.setMsgType(message.getMsgType());
                    // 通过人员工号获取用户信息
                    Employee employee = userJpaDao.findByTuNo(tuNo);
                    messageSend.setMsgDepartment(employee.getToId());
                    messageSend.setMsgUser(employee.getId());
                    messageSend.setMsgSendDate(new Timestamp(new Date().getTime()));
                    list.add(messageSend);
                }
            }
            // 批量保存发送对象
            messageSendJpaDao.saveAll(list);
        } catch (Exception e) {
            log.error("发送消息错误{}", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public void sendMsgToDepartment(MessageSendForm messageSendForm) throws Exception {
        try {
            Message message = new Message();
            BeanUtils.copyProperties(messageSendForm, message);
            message.setMsgDate(new Timestamp(new Date().getTime()));
            messageJpaDao.save(message);
            List<MessageSend> userSendlist = new ArrayList<MessageSend>();
            List<MessageSendDepartment> deptSendlist = new ArrayList<MessageSendDepartment>();
            // 循环传入人员
            for (String departId : messageSendForm.getMsgReplyDepartment()) {
                if (StringUtils.isNotEmpty(departId)) {
                    // 获取值班人员
                    List<Employee> empList =
                            userJpaDao.findByToIdAndIsOnDuty(departId, new Integer(
                                    UserEnum.ON_DUTY.getValue()));
                    if (empList.isEmpty()) {// 没有值班人员就不关联人员只关联部门设置为未发状态
                        MessageSendDepartment messageSendDepartment = new MessageSendDepartment();
                        messageSendDepartment.setMsgId(message.getMsgId());
                        messageSendDepartment.setMsgDepartment(departId);
                        deptSendlist.add(messageSendDepartment);
                    } else {// 如果有值班人员则发送
                        for (Employee emp : empList) {
                            MessageSend messageSend = new MessageSend();
                            messageSend.setMsgId(message.getMsgId());
                            messageSend.setMsgType(message.getMsgType());
                            messageSend.setMsgDepartment(emp.getToId());
                            messageSend.setMsgUser(emp.getId());
                            messageSend.setMsgSendDate(new Timestamp(new Date().getTime()));
                            userSendlist.add(messageSend);
                        }
                    }
                }
                // 批量保存发送对象
                if (!userSendlist.isEmpty()) {
                    messageSendJpaDao.saveAll(userSendlist);
                    userSendlist.clear();
                }
                // 批量保存消息关联部门池对象对象
                if (!deptSendlist.isEmpty()) {
                    messageSendDepartmentJpaDao.saveAll(deptSendlist);
                    deptSendlist.clear();
                }
            }
        } catch (Exception e) {
            log.error("发送消息错误{}", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public void sendMsgTask() throws Exception {
        List<MessageSendDepartment> listUnSend =
                messageSendDepartmentJpaDao.findByMsgSendState(MessageEnum.MSG_UN_SEND_STATE
                        .getCode());
        List<MessageSend> list = new ArrayList<MessageSend>();
        for (MessageSendDepartment messageSendDepartment : listUnSend) {
            // 获取值班人员
            List<Employee> empList =
                    userJpaDao.findByToIdAndIsOnDuty(messageSendDepartment.getMsgDepartment(),
                            new Integer(UserEnum.ON_DUTY.getValue()));
            // 循环传入人员
            for (Employee emp : empList) {
                List<MessageSend> messageSendList = messageSendJpaDao.findByMsgUserAndMsgId(emp.getId(),messageSendDepartment.getMsgId());
                if(messageSendList.size() <= 0 && messageSendList.isEmpty()){
                    MessageSend messageSend = new MessageSend();
                    messageSend.setMsgId(messageSendDepartment.getMsgId());
                    messageSend.setMsgType(messageSendDepartment.getMsgType());
                    messageSend.setMsgDepartment(emp.getToId());
                    messageSend.setMsgUser(emp.getId());
                    messageSend.setMsgSendDate(new Timestamp(new Date().getTime()));
                    list.add(messageSend);
                }
            }
            if (list.size() > 0) {
                // 批量保存发送对象
                messageSendJpaDao.saveAll(list);
            }
        }
    }

}
