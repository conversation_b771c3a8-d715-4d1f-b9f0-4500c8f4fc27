package com.swcares.scgsi.message.dao;

import java.util.List;
import org.springframework.data.repository.query.Param;
import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.message.entity.MessageSendDepartment;

/**
 * ClassName：com.swcares.scgsi.message.dao.MessageSendDepartmentJpaDao <br>
 * Description：部门消息dao <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月9日 上午10:42:15 <br>
 * @version v1.0 <br>
 */
public interface MessageSendDepartmentJpaDao extends BaseJpaDao<MessageSendDepartment, String> {

    /**
     * Title：findUNSendMsg <br>
     * Description：获取未发送成功的消息 <br>
     * author：王磊 <br>
     * date：2020年4月9日 下午12:18:17 <br>
     * @param string <br>
     */
    List<MessageSendDepartment> findByMsgSendState(@Param("msgSendState") int msgSendState);

    /**
     * 
     * Title：findByMsgIdAndMsgDepartmentAndMsgSendState <br>
     * Description：通过消息ID和部门ID和消息发送状态获取数据 <br>
     * author：王磊 <br>
     * date：2020年4月9日 下午8:24:22 <br>
     * @param msgId
     * @param msgDepartment
     * @param msgSendState
     * @return <br>
     */
    MessageSendDepartment findByMsgIdAndMsgDepartmentAndMsgSendState(@Param("msgId") String msgId,
            @Param("msgDepartment") String msgDepartment, @Param("msgSendState") int msgSendState);

}
