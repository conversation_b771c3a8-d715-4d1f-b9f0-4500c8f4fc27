package com.swcares.scgsi.message.dao;

import java.util.List;
import java.util.Map;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.message.entity.MessageSend;

/**
 * ClassName：com.swcares.message.dao.MessageSendJpaDao <br>
 * Description：消息模块数据层继承BaseJpaDao<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月3日 上午11:46:37 <br>
 * @version v1.0 <br>
 */
public interface MessageSendJpaDao extends BaseJpaDao<MessageSend, String> {

    /**
     * Title：getNewMsgAmount <br>
     * Description：获取某个用户和某个消息类型下所有消息 <br>
     * author：王磊 <br>
     * date：2020年3月5日 下午4:36:23 <br>
     * @param userId 用户id
     * @return <br>
     */
    List<MessageSend> findByMsgUserAndMsgTypeAndMsgState(@Param("msgUser") String msgUser,
            @Param("msgType") int msgType, @Param("msgState") int msgState) throws Exception;

    /**
     * Title：findByIsAudit <br>
     * Description：获取该用户下所有审核的未读消息 <br>
     * author：王磊 <br>
     * date：2020年3月5日 下午4:36:23 <br>
     * @param userId 用户id
     * @return <br>
     */
    @Query(
            value = "select t2.id as id,t2.msg_id as msgId,t2.msg_user as msgUser,t2.msg_department as msgDepartment,t2.msg_state as msgState,t2.msg_type as msgType,t2.msg_send_date as msgSendDate from sys_message t left join sys_message_send t2 on t.msg_id = t2.msg_id where  t.is_audit = 1 and t2.msg_user = ?1 and t2.msg_state = 1",
            nativeQuery = true)
    List<Map<String, Object>> findByIsAudit(String msgUser) throws Exception;

    /**
     * Title：findByMsgUserAndMsgIDAndMsgState <br>
     * Description：通过用户和消息ID获取数据 <br>
     * author：王磊 <br>
     * date：2020年3月5日 下午4:36:23 <br>
     * @param userId 用户id
     * @return <br>
     */
    List<MessageSend> findByMsgUserAndMsgId(@Param("msgUser") String msgUser,
            @Param("msgId") String msgId) throws Exception;

}
