package com.swcares.scgsi.message.dao;

import java.util.List;
import java.util.Map;
import org.springframework.data.jpa.repository.Query;
import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.message.entity.Message;

/**
 * ClassName：com.swcares.scgsi.message.dao.MessageJpaDao <br>
 * Description：消息模块数据层继承BaseJpaDao<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月3日 上午11:46:37 <br>
 * @version v1.0 <br>
 */
public interface MessageJpaDao extends BaseJpaDao<Message, String> {
    /**
     * Title：getNewMsgAmount <br>
     * Description：最新消息获取方法 <br>
     * author：王磊 <br>
     * date：2020年3月5日 下午4:36:23 <br>
     * @param userId 用户id
     * @return <br>
     */
    @Query(
            value = "select m.msg_type as msgType,m.msg_type_name as msgTypeName,m.msg_title as msgTitle,m.msg_content as msgContent,to_char(m.msg_date,'yyyy-mm-dd hh24:mi:ss') as msgDate,m.flight_no as flightNo,m.flight_date as flightDate,msg_amount as msgAmount from (select * from (select t.*,(select count(1) from sys_message t3 left join sys_message_send t4 on t3.msg_id = t4.msg_id where t4.msg_user = ?1 and t4.msg_state = 1 and t3.msg_type = t.msg_type) as msg_amount,row_number() over(partition by t.msg_type order by t.msg_date desc) rn from sys_message t left join sys_message_send t2 on t.msg_id = t2.msg_id where t2.msg_user = ?1) where rn = 1) m",
            nativeQuery = true)
    List<Map<String, Object>> getNewMsgAmount(String userId) throws Exception;
    
    /**
     * getNewMsgAmountAudit <br>
     * Description：移动端最新消息获取方法 <br>
     * author：王磊 <br>
     * date：2020年3月5日 下午4:36:23 <br>
     * @param userId 用户id
     * @return <br>
     */
    @Query(value = "select m.msg_type as msgType,m.msg_type_name as msgTypeName,m.msg_title as msgTitle,m.msg_content as msgContent,to_char(m.msg_date, 'yyyy-mm-dd hh24:mi:ss') as msgDate,m.flight_no as flightNo,m.flight_date as flightDate,msg_amount as msgAmount from (select * from (select t.*,(select count(1) from sys_message t3 left join sys_message_send t4 on t3.msg_id = t4.msg_id where t4.msg_user = ?1 and t3.is_audit = 0 and t4.msg_state = 1 and t3.msg_type = t.msg_type) as msg_amount,row_number() over(partition by t.msg_type order by t.msg_date desc) rn from sys_message t left join sys_message_send t2 on t.msg_id = t2.msg_id where t.is_audit = 0 and t2.msg_user = ?1) where rn = 1) m Union all select 0 as msgType,'审核' as msgTypeName,m.msg_title as msgTitle,m.msg_content as msgContent,to_char(m.msg_date, 'yyyy-mm-dd hh24:mi:ss') as msgDate,m.flight_no as flightNo,m.flight_date as flightDate,msg_amount as msgAmount from (select * from (select t.*,(select count(1) from sys_message t3 left join sys_message_send t4 on t3.msg_id = t4.msg_id where t4.msg_user = ?1 and t3.is_audit = 1 and t4.msg_state = 1 and t3.msg_type = t.msg_type) as msg_amount,row_number() over(partition by t.msg_type order by t.msg_date desc) rn from sys_message t left join sys_message_send t2 on t.msg_id = t2.msg_id where t.is_audit = 1 and t2.msg_user = ?1) where rn = 1) m",nativeQuery = true)
    List<Map<String, Object>> getNewMsgAmountMobile(String userId) throws Exception;
    
}
