package com.swcares.scgsi.message.entity;

import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * ClassName：com.swcares.scgsi.message.entity.Message <br>
 * Description：消息实体类<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月3日 下午4:04:08 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "SYS_MESSAGE")
@Data
public class Message {
    @Id
    @Column(name = "MSG_ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String msgId;
    /**消息类型(1旅客赔偿,2异常行李.3航班监控4.我的已发)*/
    @Column(name = "MSG_TYPE")
    private int msgType;
    /**消息标题*/
    @Column(name = "MSG_TITLE")
    private String msgTitle;
    /**消息内容*/
    @Column(name = "MSG_CONTENT")
    private String msgContent;
    /**发起人*/
    @Column(name = "MSG_USER")
    private String msgUser;
    /**发起人部门*/
    @Column(name = "MSG_DEPARTMENT")
    private String msgDepartment;
    /**时间*/
    @Column(name = "MSG_DATE")
    private Date msgDate;
    /**航班号*/
    @Column(name = "FLIGHT_NO")
    private String flightNo;
    /**航班日期*/
    @Column(name = "FLIGHT_DATE")
    private String flightDate;
    /**父级消息ID*/
    @Column(name = "PARENT_ID")
    private String parentId;
    /**消息主类型名称*/
    @Column(name = "MSG_TYPE_NAME")
    private String msgTypeName;
    /**消息子类型*/
    @Column(name = "MSG_CHILD_TYPE")
    private String msgChildType;
    /**消息子类型名称*/
    @Column(name = "MSG_CHILD_TYPE_NAME")
    private String msgChildTypeName;
    /**pc端跳转地址*/
    @Column(name = "PC_URL")
    private String pcUrl;
    /**手机端跳转地址*/
    @Column(name = "MOBILE_URL")
    private String mobileUrl;
    /**是否审核消息(0非审核消息,1审核消息)*/
    @Column(name = "IS_AUDIT")
    private int isAudit ;

    
    /** 消息和发送用户中间表 */
    @OneToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "SYS_MESSAGE_SEND", joinColumns = {@JoinColumn(name = "MSG_ID",
            referencedColumnName = "MSG_ID")})
    private List<MessageSend> messageSends;
    
    /** 消息和发送部门中间表 */
    @OneToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "SYS_MESSAGE_SEND_DEPARTMENT", joinColumns = {@JoinColumn(name = "MSG_ID",
            referencedColumnName = "MSG_ID")})
    private List<MessageSendDepartment> messageSendDepartment;
}
