package com.swcares.scgsi.message.dao;

import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.message.common.config.MessageType;
import com.swcares.scgsi.message.common.model.form.MessageListForm;
import com.swcares.scgsi.message.common.model.vo.MessageListVo;

/**
 * ClassName：com.swcares.scgsi.message.dao.MessageDao <br>
 * Description：调用basedao <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月11日 下午11:24:40 <br>
 * @version v1.0 <br>
 */
@Repository
public class MessageDao {
    @Autowired
    private BaseDAO baseDao;

    /**
     * Title：getMsgList <br>
     * Description：获取消息列表通用函数 <br>
     * author：王磊 <br>
     * date：2020年4月9日 下午6:02:06 <br>
     * @param pageable
     * @param form
     * @return <br>
     */
    public QueryResults getMsgList(Pageable pageable, MessageListForm form) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" select t.msg_id as msgId,t.msg_title as msgTitle,t.msg_content  as msgContent,t.msg_user as msgUser,t.msg_department as msgDepartment,");
        sql.append(" to_char(t.msg_date,'yyyy-mm-dd hh24:mi:ss') as msgDate,t.flight_no as flightNo,t.flight_date as flightDate,t2.msg_state as msgState ,d.tofname as msgDepartmentName,t.pc_url as moduleUrl  ");
        sql.append(" from sys_message t right join sys_message_send t2 on t.msg_id = t2.msg_id left join department d on  t.msg_department = d.id");
        sql.append(" where 1 = 1");
        String userId = form.getUserID();
        Integer msgType = form.getMsgType();
        Integer msgState = form.getMsgState();
        String beginDate = form.getBeginDate();
        String endDate = form.getEndDate();
        if (StringUtils.isNotBlank(userId)) {
            paramsMap.put("userId", userId);
            sql.append(" and t2.msg_user= :userId ");
        }
        if (msgType >= MessageType.MSG_TYPE_COMPENSATE) {
            paramsMap.put("msgType", msgType);
            sql.append(" and  t.msg_type =:msgType ");
        }
        // 获取已读和未读的消息(0和3表示全部和历史消息不需要加入条件)
        if (msgState > MessageType.MSG_STATE_ALL && msgState < MessageType.MSG_STATE_HISTORY) {
            paramsMap.put("msgState", msgState);
            sql.append(" and t2.msg_state= :msgState ");
        }
        if (StringUtils.isNotEmpty(beginDate) && StringUtils.isNotEmpty(endDate)) {
            paramsMap.put("endDate", endDate);
            paramsMap.put("beginDate", beginDate);
            sql.append(" and to_char(t.msg_date, 'yyyy-mm-dd') between :beginDate and :endDate ");
        }
        sql.append("order by t.msg_date desc ");
        QueryResults queryResults =
                baseDao.findBySQLPage_comm(sql.toString(), form.getPage(), form.getSize(),
                        paramsMap, MessageListVo.class);
        return queryResults;
    }

    /**
     * Title：getMsgListMobile <br>
     * Description：获取消息列表通用函数移动端 <br>
     * author：王磊 <br>
     * date：2020年4月9日 下午6:01:56 <br>
     * @param pageable
     * @param form
     * @return <br>
     */
    public QueryResults getMsgListMobile(Pageable pageable, MessageListForm form) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" select t.msg_id as msgId,t.msg_title as msgTitle,t.msg_content  as msgContent,t.msg_user as msgUser,t.msg_department as msgDepartment,");
        sql.append(" to_char(t.msg_date,'yyyy-mm-dd hh24:mi:ss') as msgDate,t.flight_no as flightNo,t.flight_date as flightDate,t2.msg_state as msgState ,d.tofname as msgDepartmentName,t.mobile_url as moduleUrl ");
        sql.append(" from sys_message t right join sys_message_send t2 on t.msg_id = t2.msg_id left join department d on  t.msg_department = d.id");
        sql.append(" where 1 = 1 ");
        String userId = form.getUserID();
        Integer msgType = form.getMsgType();
        Integer msgState = form.getMsgState();
        String beginDate = form.getBeginDate();
        String endDate = form.getEndDate();
        if (StringUtils.isNotBlank(userId)) {
            paramsMap.put("userId", userId);
            sql.append(" and t2.msg_user= :userId ");
        }
        if (msgType >= MessageType.MSG_TYPE_COMPENSATE) {
            paramsMap.put("msgType", msgType);
            sql.append(" and  t.msg_type =:msgType ");
            sql.append(" and  t.is_audit = 0 ");
        } else {
            sql.append(" and  t.is_audit = 1 ");
        }
        // 获取已读和未读的消息(0和3表示全部和历史消息不需要加入条件)
        if (msgState > MessageType.MSG_STATE_ALL && msgState < MessageType.MSG_STATE_HISTORY) {
            paramsMap.put("msgState", msgState);
            sql.append(" and t2.msg_state= :msgState ");
        }
        if (StringUtils.isNotEmpty(beginDate) && StringUtils.isNotEmpty(endDate)) {
            paramsMap.put("endDate", endDate);
            paramsMap.put("beginDate", beginDate);
            sql.append(" and to_char(t.msg_date, 'yyyy-mm-dd') between :beginDate and :endDate ");
        }
        sql.append("order by t.msg_date desc ");
        QueryResults queryResults =
                baseDao.findBySQLPage_comm(sql.toString(), form.getPage(), form.getSize(),
                        paramsMap, MessageListVo.class);
        return queryResults;
    }

}
