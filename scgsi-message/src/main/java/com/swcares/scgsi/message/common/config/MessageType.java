package com.swcares.scgsi.message.common.config;

/**
 * ClassName：com.swcares.message.common.config.MessageType <br>
 * Description：暂时用来配置消息类型 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月9日 下午4:20:23 <br>
 * @version v1.0 <br>
 */
public class MessageType {
    /**1旅客赔偿*/
    public static final int MSG_TYPE_COMPENSATE = 1;
    /**2异常行李*/
    public static final int MSG_TYPE_LUGGAGE = 2;
    /**0全部*/
    public static final int MSG_STATE_ALL = 0;
    /**1未读*/
    public static final int MSG_STATE_UNREAD = 1;
    /**2已读*/
    public static final int MSG_STATE_READ = 2;
    /**3历史消息*/
    public static final int MSG_STATE_HISTORY = 3;
}
