package com.swcares.scgsi.message.common.utils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
/**
 * ClassName：com.swcares.scgsi.message.common.utils.JpaMapToEntity <br>
 * Description：用于jpa转换对象使用<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月5日 下午3:42:53 <br>
 * @version v1.0 <br>
 */
public class JpaMapToEntity {
    /**
     * Title：mapToEntity <br>
     * Description：将JPA的map对象转换为实体类<br>
     * author：王磊 <br>
     * date：2020年3月5日 下午2:50:15 <br>
     * @param map
     * @param targetClass
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException <br>
     */
    public static <T> T mapToEntity(Map<String, Object> map, Class<T> targetClass)
            throws IllegalAccessException, InstantiationException {
        Class<?> superClass;
        Field[] fields;

        T target = targetClass.newInstance();
        // 接收targetClass的Field
        List<Field> targetfieldList = new LinkedList<>();

        superClass = targetClass;
        while (superClass != null && superClass != Object.class) {
            // 由于该方法只能获取superClass的参数(private,protect,public等任何声明),但无法获取父类的参数,这里我们迭代一波
            fields = superClass.getDeclaredFields();
            targetfieldList.addAll(Arrays.asList(fields));
            superClass = superClass.getSuperclass();
        }
        // 匹配并赋值
        for (Field targetfield : targetfieldList) {
            for (Map.Entry<String, Object> mapEntry : map.entrySet()) {
                if (targetfield.getName().toLowerCase().equals(mapEntry.getKey().toLowerCase())) {
                    // 暂时保存权限
                    boolean targetFlag = targetfield.isAccessible();
                    // 赋予权限
                    targetfield.setAccessible(true);
                    if(mapEntry.getValue() == null){
                        // 恢复原权限
                        targetfield.setAccessible(targetFlag);
                        break;
                    }
                    // 赋值(聚合函数取出为BigDecimal类型所以需要转换)
                    if (mapEntry.getValue().getClass() == BigDecimal.class) {
                        targetfield.set(target, new Integer(mapEntry.getValue().toString()));
                    //转换时间
                    } else {
                        targetfield.set(target, mapEntry.getValue());
                    }
                    // 恢复原权限
                    targetfield.setAccessible(targetFlag);
                    break;
                }
            }
        }
        return target;
    }
}
