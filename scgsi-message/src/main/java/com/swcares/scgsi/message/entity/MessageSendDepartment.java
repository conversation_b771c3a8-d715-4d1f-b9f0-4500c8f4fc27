package com.swcares.scgsi.message.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import org.hibernate.annotations.GenericGenerator;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.message.entity.MessageSendDepartment <br>
 * Description：在消息直接发送部门时会存入 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月9日 上午10:39:00 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "SYS_MESSAGE_SEND_DEPARTMENT")
@Data
public class MessageSendDepartment {
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;
    @Column(name = "MSG_ID")
    private String msgId;
    /**接收部门*/
    @Column(name = "MSG_DEPARTMENT")
    private String msgDepartment;
    /**关联部门的状态,如果部门内有人已读则改为1(0未读,1已读)*/
    @Column(name = "MSG_SEND_STATE")
    private int msgSendState = 0;
    /**消息类型(1旅客赔偿,2异常行李.3航班监控4.我的已发)*/
    @Column(name = "MSG_TYPE")
    private int msgType;
}
