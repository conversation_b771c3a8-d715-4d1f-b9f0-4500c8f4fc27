package com.swcares.scgsi.message.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.message.entity.Message <br>
 * Description：用户消息列表vo<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月5日 下午4:04:08 <br>
 * @version v1.0 <br>
 */
@ApiModel("消息列表返回vo")
@Data
public class MessageListVo {
    @ApiModelProperty("消息ID")
    private String msgId;
    @ApiModelProperty("消息标题")
    private String msgTitle;
    @ApiModelProperty("消息内容")
    private String msgContent;
    @ApiModelProperty("发送人ID")
    private String msgUser;
    @ApiModelProperty("发送人部门")
    private String msgDepartment;
    @ApiModelProperty("时间")
    private String msgDate;
    @ApiModelProperty("航班号")
    private String flightNo;
    @ApiModelProperty("航班日期")
    private String flightDate;
    @ApiModelProperty("消息状态")
    private Integer msgState;
    @ApiModelProperty("发送人部门名称")
    private String msgDepartmentName;
    @ApiModelProperty("跳转url")
    private String moduleUrl;
}
