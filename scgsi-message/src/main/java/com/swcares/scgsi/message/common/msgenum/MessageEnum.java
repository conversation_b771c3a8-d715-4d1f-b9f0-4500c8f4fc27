package com.swcares.scgsi.message.common.msgenum;

/**
 * ClassName：com.swcares.scgsi.message.common.msgenum.MessageEnum <br>
 * Description：消息常用数据枚举 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月9日 下午2:27:06 <br>
 * @version v1.0 <br>
 */
public enum MessageEnum {
    MSG_UN_READ(1), // 1未读,2已读
    MSG_READ(2), // 1未读,2已读
    MSG_UN_SEND_STATE(0), // 消息发送状态 0未发送,1已发送
    MSG_SEND_STATE(1), // 消息发送状态 0未发送,1已发送
    NO_AUDIT(1), // 消息发送状态 0不是审核消息
    AUDIT(0),// 消息发送状态 1是审核消息
    PC(1), // PC端标识
    MOBILE(0);// 移动端标识

    private MessageEnum(Integer code) {
        this.code = code;
    }

    /**
     * code值
     */
    private Integer code;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }


}
