package com.swcares.scgsi.message.common.msgenum;

/**
 * ClassName：com.swcares.scgsi.message.common.msgenum.MessageTypeEnum <br>
 * Description：消息类型枚举 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月9日 下午2:27:06 <br>
 * @version v1.0 <br>
 */
public enum MessageTypeEnum {

    /**赔偿单审核消息 */
    PASSENGERS_FOR_COMPENSATION(1, "旅客赔偿", 1, "赔偿单审核", "/compensation/CompensationAudit/AuditDetail?orderId={0}", "/examine/examineIndemnity?orderId={0}",1),
    /**赔偿单审核结果*/
    PASSENGERS_FOR_COMPENSATION_AUDIT_RESULT(1, "旅客赔偿", 2, "赔偿单审核结果", "/compensation/indemnitySheet?flightNo={0}&flightDate={1}", "",0),
    /** code: 消息类型 , msg: 消息标题 */
    ACT_FOR_PAX(6, "代领旅客审核", 1, "代领旅客审核", "/compensation/substitutePassenger", "",1),
    /** code: 消息类型 , msg: 消息标题 */
    ABNORMAL_LUGGAGE(2, "异常行李", 1, "异常行李", "/luggage/index?accidentId=", "/luggage/index?accidentId=",0),
    /**航延酒店-保障单*/
    HOTEL_ORDER_AUDIT(1, "航延酒店审核", 1, "航延酒店保障单审核结果", "/delayGuarantee/detailsOfFlightDelayGuaranteeSheet?editDirectly=0&orderId={0}&status={1}&isMail='1'", "/passengerSecurity/accommodationList/accommodationDetails?orderId={0}", 1),
    /**航延酒店-结算审核单*/
    HOTEL_SETTLE_AUDIT(1, "航延酒店审核", 2, "航延酒店结算审核单审核结果", "/delayGuarantee/settlementReview/accountingDetails?settleId={0}", "", 1)
    ;

    private MessageTypeEnum(Integer type, String typeName, Integer childType, String childTypeName,
                            String pcUrl, String mobileUrl,Integer isAudit) {
        this.type = type;
        this.typeName = typeName;
        this.childType = childType;
        this.childTypeName = childTypeName;
        this.pcUrl = pcUrl;
        this.mobileUrl = mobileUrl;
        this.isAudit = isAudit;
    }

    /**
     * 主类型值
     */
    private Integer type;

    /**
     * 主类型名称
     */
    private String typeName;

    /**
     * 子类型值
     */
    private Integer childType;

    /**
     * 子类型名称
     */
    private String childTypeName;

    /**
     * pc跳转地址
     */
    private String pcUrl;

    /**
     * 手机跳转地址
     */
    private String mobileUrl;

    /**
     * 子类型值
     */
    private Integer isAudit;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getChildType() {
        return childType;
    }

    public void setChildType(Integer childType) {
        this.childType = childType;
    }

    public String getChildTypeName() {
        return childTypeName;
    }

    public void setChildTypeName(String childTypeName) {
        this.childTypeName = childTypeName;
    }

    public String getPcUrl() {
        return pcUrl;
    }

    public void setPcUrl(String pcUrl) {
        this.pcUrl = pcUrl;
    }

    public String getMobileUrl() {
        return mobileUrl;
    }

    public void setMobileUrl(String mobileUrl) {
        this.mobileUrl = mobileUrl;
    }

    public Integer getIsAudit() {
        return isAudit;
    }

    public void setIsAudit(Integer isAudit) {
        this.isAudit = isAudit;
    }

}
