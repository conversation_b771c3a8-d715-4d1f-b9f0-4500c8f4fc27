package com.swcares.scgsi.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;
import io.swagger.annotations.Api;
import java.util.ArrayList;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.config.SwaggerConfig <br>
 * Description：swagger配置<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月10日 上午10:13:55 <br>
 * @version v1.0 <br>
 */
@Configuration
@EnableSwagger2
public class SwaggerConfig {
    
    @Value("${swagger.enable}")
    private Boolean enable;
    
    @Bean
    public Docket createRestApi() {
        // 在Header中添加JWT
        ParameterBuilder tokenPar = new ParameterBuilder();
        List<Parameter> params = new ArrayList<>();
        tokenPar.name("Authorization").description("token").modelRef(new ModelRef("string"))
                .parameterType("header").required(false).build();
        params.add(tokenPar.build());
        return new Docket(DocumentationType.SWAGGER_2).apiInfo(apiInfo())
                .enable(enable)
                .select()
                // Swagger中需要增加Jwt token的传入
                 .apis(RequestHandlerSelectors.withClassAnnotation(Api.class))
//                .apis(RequestHandlerSelectors.basePackage("com.swcares"))
                .paths(PathSelectors.any()).build().globalOperationParameters(params);
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder().title("山东航旅服接口").description("山东航旅服在线接口文档").version("1.0")
                .contact(new Contact("山东航旅服项目组", "", "")).build();
    }
}
