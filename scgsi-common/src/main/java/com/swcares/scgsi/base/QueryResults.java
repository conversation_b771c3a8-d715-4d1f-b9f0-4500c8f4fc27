package com.swcares.scgsi.base;

import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：封装查询结果集 <br>
 * Package：com.swcares.scgsi.common.base <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 02月13日 20:16 <br>
 * @version v1.0 <br>
 */
public class QueryResults {

    /** 结果列表 */
    private List<?> list;

    /** 分页工具对象 */
    private Pager pagination;

    /**
     * 获取list
     * @return list list
     */
    public List<?> getList() {
        return list;
    }

    /**
     * 设置list
     * @list the list to set
     */
    public void setList(List<?> list) {
        this.list = list;
    }

    /**
     * 获取pager
     * @return pager pager
     */
    public Pager getPagination() {
        return pagination;
    }

    /**
     * 设置pager
     * @pager the pager to set
     */
    public void setPager(Pager pagination) {
        this.pagination = pagination;
    }

}
