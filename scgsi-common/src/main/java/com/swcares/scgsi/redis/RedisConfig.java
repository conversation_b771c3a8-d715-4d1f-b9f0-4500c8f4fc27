package com.swcares.scgsi.redis;

import java.util.List;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.spring.data.connection.RedissonConnectionFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import redis.clients.jedis.JedisPoolConfig;

/**
 * ClassName：com.swcares.scgsi.redis.RedisConfig <br>
 * Description：配置<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月12日 上午10:13:55 <br>
 * @version v1.0 <br>
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "spring.redis.cluster")
public class RedisConfig {

    /** redis通信协议 */
    private final String REDISSION_AGREE_PREFIX = "redis://";

    private List<String> nodes;

    private String password;

    private Integer maxRedirects;

    private JedisPoolConfig poolConfig;

    private int pingConnectionInterval;


    /**
     * Title：connectionFactory <br>
     * Description：根据 RedisClusterConfiguration实例以及连接池配置信息创建 Jedis 连接工厂 <br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午3:21:14 <br>
     * @return <br>
     */
    @Bean
    @ConditionalOnProperty(prefix = "spring.redis.cluster", name = "type", havingValue = "jedis")
    public RedisConnectionFactory redisConnectionFactory() {
        RedisClusterConfiguration rcc = new RedisClusterConfiguration(nodes);
        rcc.setPassword(password);
        rcc.setMaxRedirects(maxRedirects);
        return new JedisConnectionFactory(rcc, poolConfig);
    }
    
    /**
     * Title：redission <br>
     * Description：创建redission配置信息 <br>
     * author：夏阳 <br>
     * date：2020年4月8日 下午7:58:47 <br>
     * @return <br>
     */
    @Bean
    @ConditionalOnProperty(prefix = "spring.redis.cluster", name = "type", havingValue = "redission")
    public RedissonClient redission() {
        //目前采用默认配置，后面部门参数可以从yml注入
        Config config = new Config();
        // 集群方式
        config.useClusterServers().setScanInterval(2000).addNodeAddress(list2Array(nodes))
                .setPassword(password).setPingConnectionInterval(pingConnectionInterval);

        RedissonClient redisson = Redisson.create(config);
        return redisson;
    }

    /**
     * Title：list2Array <br>
     * Description：从yml中的端口ip中增加协议头 <br>
     * author：夏阳 <br>
     * date：2020年4月8日 下午7:59:10 <br>
     * @param nodes
     * @return <br>
     */
    private String[] list2Array(List<String> nodes) {
        String[] nodeArray = new String[nodes.size()];
        for (int i = 0; i < nodes.size(); i++) {
            nodeArray[i] = REDISSION_AGREE_PREFIX + nodes.get(0);
        }
        return nodeArray;
    }

    /**
     * 
     * Title：redissonConnectionFactory <br>
     * Description：基于redission创建链接对象<br>
     * author：夏阳 <br>
     * date：2020年4月8日 下午7:59:52 <br>
     * @param redisson 初始化好的配置信息
     * @return <br>
     */
    @Bean
    @ConditionalOnProperty(prefix = "spring.redis.cluster", name = "type", havingValue = "redission")
    public RedissonConnectionFactory redissonConnectionFactory(RedissonClient redisson) {
        return new RedissonConnectionFactory(redisson);
    }

    /**
     * Title：redisTemplate <br>
     * Description：Jackson2JsonRedisSerializer实现 <br>
     * author：夏阳 <br>
     * date：2020年2月13日 下午2:11:50 <br>
     * @param redisConnectionFactory
     * @return <br>
     */
    @Bean
    @SuppressWarnings("deprecation")
    public RedisTemplate<Object, Object> redisTemplate(
            RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<Object, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);

        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);

        Jackson2JsonRedisSerializer<Object> jsonRedisSerializer =
                new Jackson2JsonRedisSerializer<>(Object.class);

        jsonRedisSerializer.setObjectMapper(om);
        redisTemplate.setDefaultSerializer(jsonRedisSerializer);

        redisTemplate.setKeySerializer(stringRedisSerializer);
        redisTemplate.setHashKeySerializer(stringRedisSerializer);

        redisTemplate.setValueSerializer(jsonRedisSerializer);
        redisTemplate.setHashValueSerializer(jsonRedisSerializer);

        redisTemplate.afterPropertiesSet();

        return redisTemplate;
    }


    /**
     * Title：redisTemplate <br>
     * Description：基于GenericJackson2JsonRedisSerializer实现 <br>
     * author：夏阳 <br>
     * date：2020年2月13日 下午2:11:16 <br>
     * @param redisConnectionFactory
     * @return <br>
     */
    /*@Bean
    public  RedisTemplate<Object, Object> redisTemplate(
            RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<Object, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory());

        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        GenericJackson2JsonRedisSerializer jsonRedisSerializer =
                new GenericJackson2JsonRedisSerializer(om);

        redisTemplate.setDefaultSerializer(jsonRedisSerializer);

        redisTemplate.setKeySerializer(stringRedisSerializer);
        redisTemplate.setHashKeySerializer(stringRedisSerializer);

        redisTemplate.setValueSerializer(jsonRedisSerializer);
        redisTemplate.setHashValueSerializer(jsonRedisSerializer);

        redisTemplate.afterPropertiesSet();

        return redisTemplate;
    }*/


}
