
/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：RedisService.java <br>
 * Package：com.swcares.scgsi.redis <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 * 
 * <AUTHOR> <br>
 *         date 2020年2月12日 下午3:07:28 <br>
 * @version v1.0 <br>
 */
package com.swcares.scgsi.redis;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import com.swcares.exception.SystemException;
import lombok.extern.slf4j.Slf4j;

/**   
 * ClassName：com.swcares.scgsi.redis.RedisService <br>
 * Description：redis操作封装类，包含5种数据类型的操作 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月12日 下午3:07:28 <br>
 * @version v1.0 <br>  
 */
@Slf4j
@Component
public class RedisService {

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    /**
     * Title：get <br>
     * Description：【String】获取缓存<br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午3:38:53 <br>
     * @param key
     * @return <br>
     */
    public Object get(String key) {
        return key == null ? null : redisTemplate.opsForValue().get(key);
    }

    /**
     * Title：set <br>
     * Description：【String】添加缓存<br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午3:39:44 <br>
     * @param key
     * @param value
     * @return true成功 false 失败<br>
     */
    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("添加string类型缓存出错:{}", e);
            return false;
        }

    }

    /**
     * Title：set <br>
     * Description：【String】 添加缓存并设置过期时间<br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午3:42:13 <br>
     * @param key
     * @param value
     * @param time 时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败<br>
     */
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error("添加string类型缓存并设置过期时间出错:{}", e);
            return false;
        }
    }

    /**
     * Title：incr <br>
     * Description： 【String】递增<br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午3:45:25 <br>
     * @param key
     * @param delta
     * @return <br>
     */
    public long incr(String key, long delta) {
        if (delta < 0) {
            throw new SystemException("递增因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * Title：incr <br>
     * Description： 【String】递减<br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午3:45:25 <br>
     * @param key
     * @param delta
     * @return <br>
     */
    public long decr(String key, long delta) {
        if (delta < 0) {
            throw new SystemException("递减因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, -delta);
    }

    /**
     * Title：hget <br>
     * Description：获取一组Map的键值对) <br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午3:48:00 <br>
     * @param key 键 不能为null
     * @param item 项 不能为null
     * @return <br>
     */
    public Object hget(String key, String item) {
        return redisTemplate.opsForHash().get(key, item);
    }


    /**
    * Title：hmget <br>
    * Description：获取指定Map的所有键值对 <br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午3:48:42 <br>
    * @param key
    * @return 对应的多个键值<br>
    */
    public Map<Object, Object> hmget(String key) {
        return redisTemplate.opsForHash().entries(key);
    }


    /**
    * Title：hmset <br>
    * Description：添加一个Map类型值<br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午3:49:17 <br>
    * @param key 键
    * @param map 对应多个键值
    * @return true 成功 false 失败<br>
    */
    public boolean hmset(String key, Map<String, Object> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            return true;
        } catch (Exception e) {
            log.error("添加一个Map类型值异常:{}", e);
            return false;
        }
    }

    /**
    * Title：hmset <br>
    * Description：添加一个Map类型值并设置时间 <br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午3:51:54 <br>
    * @param key 键
    * @param map 对应多个键值
    * @param time 时间(秒)
    * @return true成功 false失败<br>
    */
    public boolean hmset(String key, Map<String, Object> map, long time) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("添加一个Map类型值并设置时间异常:{}", e);
            return false;
        }
    }

    /**
    * Title：hset <br>
    * Description：向一张hash表中放入数据,如果不存在将创建 <br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午3:57:23 <br>
    * @param key 键
    * @param item 项
    * @param value 值
    * @return true 成功 false失败<br>
    */
    public boolean hset(String key, String item, Object value) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            return true;
        } catch (Exception e) {
            log.error("向一张hash表中放入数据,如果不存在将创建异常:{}", e);
            return false;
        }
    }

    /**
    * Title：hset <br>
    * Description：向一张hash表中放入数据,如果不存在将创建并设置过期时间 <br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午3:59:17 <br>
    * @param key 键
    * @param item 项
    * @param value 值
    * @param time 时间(秒)  注意:如果已存在的hash表有时间,这里将会替换原有的时间
    * @return true 成功 false失败<br>
    */
    public boolean hset(String key, String item, Object value, long time) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("向一张hash表中放入数据,如果不存在将创建并设置过期时间异常:{}", e);
            return false;
        }
    }

    /**
    * Title：hdel <br>
    * Description：删除hash表中的值<br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午4:00:01 <br>
    * @param key 键 不能为null
    * @param item 项 可以使多个 不能为null<br>
    */
    public void hdel(String key, Object... item) {
        redisTemplate.opsForHash().delete(key, item);
    }

    /**
    * Title：hHasKey <br>
    * Description：判断hash表中是否有该项的值 <br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午4:01:01 <br>
    * @param key 键 不能为null
    * @param item 项 不能为null
    * @return true 存在 false不存在<br>
    */
    public boolean hHasKey(String key, String item) {
        return redisTemplate.opsForHash().hasKey(key, item);
    }

    /**
    * Title：hincr <br>
    * Description：hash递增 如果不存在,就会创建一个 并把新增后的值返回 <br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午4:01:56 <br>
    * @param key 键
    * @param item 项
    * @param by 要增加几(大于0)
    * @return <br>
    */
    public double hincr(String key, String item, double by) {
        return redisTemplate.opsForHash().increment(key, item, by);
    }

    /**
    * Title：hdecr <br>
    * Description：hash递减 <br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午4:03:01 <br>
    * @param key 键
    * @param item 项
    * @param by 要减少几(小于0)
    * @return <br>
    */
    public double hdecr(String key, String item, double by) {
        return redisTemplate.opsForHash().increment(key, item, -by);
    }

    /**
    * Title：sGet <br>
    * Description：根据key获取Set中的所有值<br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午4:21:26 <br>
    * @param key
    * @return <br>
    */
    public Set<Object> sGet(String key) {
        try {
            return redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            log.error("根据key获取Set中的所有值异常:{}", e);
            return null;
        }
    }

    /**
    * Title：sHasKey <br>
    * Description：根据value从一个set中查询,是否存在<br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午4:24:25 <br>
    * @param key 键
    * @param value 值
    * @return true 存在 false不存在<br>
    */
    public boolean sHasKey(String key, Object value) {
        try {
            return redisTemplate.opsForSet().isMember(key, value);
        } catch (Exception e) {
            log.error("根据value从一个set中查询,是否存在:{}", e);
            return false;
        }
    }

    /**
    * Title：sSet <br>
    * Description：将数据放入set缓存 <br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午4:28:16 <br>
    * @param key 键
    * @param values 值 可以是多个
    * @return 成功个数 <br>
    */
    public long sSet(String key, Object... values) {
        try {
            return redisTemplate.opsForSet().add(key, values);
        } catch (Exception e) {
            log.error("将数据放入set缓存:{}", e);
            return 0;
        }
    }


    /**
    * Title：sSetAndTime <br>
    * Description：添加一个SET缓存并设置过期时间 <br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午4:28:50 <br>
    * @param key 键
    * @param time 时间(秒)
    * @param values 值 可以是多个
    * @return 成功个数<br>
    */
    public long sSetAndTime(String key, long time, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().add(key, values);
            if (time > 0) {
                expire(key, time);
            }
            return count;
        } catch (Exception e) {
            log.error("添加一个SET缓存并设置过期时间:{}", e);
            return 0;
        }
    }

    /**
     * 
     * Title：sGetSetSize <br>
     * Description： 获取set缓存的长度<br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午4:32:24 <br>
     * @param key
     * @return <br>
     */
    public long sGetSetSize(String key) {
        try {
            return redisTemplate.opsForSet().size(key);
        } catch (Exception e) {
            log.error("获取set缓存的长度:{}", e);
            return 0;
        }
    }

    /**
     * Title：setRemove <br>
     * Description：移除指定key的缓存<br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午4:33:02 <br>
     *  @param key 键
    * @param values 值 可以是多个
    * @return 移除的个数 <br>
     */
    public long setRemove(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().remove(key, values);
            return count;
        } catch (Exception e) {
            log.error("移除指定key的缓存:{}", e);
            return 0;
        }
    }

    /**
     * Title：lGet <br>
     * Description：获取list缓存的内容<br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午4:33:48 <br>
     * @param key 键
    * @param start 开始
    * @param end 结束  0 到 -1代表所有值
     * @return <br>
     */
    public List<Object> lGet(String key, long start, long end) {
        try {
            return redisTemplate.opsForList().range(key, start, end);
        } catch (Exception e) {
            log.error("获取list缓存的内容:{}", e);
            return null;
        }
    }

    /**
     * Title：lGetListSize <br>
     * Description： 获取list缓存的长度<br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午4:34:28 <br>
     * @param key
     * @return <br>
     */
    public long lGetListSize(String key) {
        try {
            return redisTemplate.opsForList().size(key);
        } catch (Exception e) {
            log.error("获取list缓存的长度:{}", e);
            return 0;
        }
    }

    /**
     * Title：lGetIndex <br>
     * Description：通过索引 获取list中的值<br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午4:35:21 <br>
     * @param key 键
     * @param index 索引  index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     * @return <br>
     */
    public Object lGetIndex(String key, long index) {
        try {
            return redisTemplate.opsForList().index(key, index);
        } catch (Exception e) {
            log.error("通过索引 获取list中的值:{}", e);
            return null;
        }
    }


    /**
     * Title：lSet <br>
     * Description：将list放入缓存<br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午4:36:29 <br>
     * @param key
     * @param value
     * @return <br>
     */
    public boolean lSet(String key, Object value) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            log.error("将list放入缓存:{}", e);
            return false;
        }
    }


    /**
     * Title：lSet <br>
     * Description：将list放入缓存并设置过期时间 <br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午4:37:00 <br>
     * @param key 键
     * @param value 值
     * @param time 时间(秒)
     * @return <br>
     */
    public boolean lSet(String key, Object value, long time) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("将list放入缓存并设置过期时间:{}", e);
            return false;
        }
    }

    /**
     * Title：lSet <br>
     * Description： 将list放入缓存<br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午4:37:59 <br>
     * @param key 键
     * @param value 值
     * @return <br>
     */
    public boolean lSet(String key, List<Object> value) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            return true;
        } catch (Exception e) {
            log.error("将list放入缓存:{}", e);
            return false;
        }
    }


    /**
     * 
     * Title：lSet <br>
     * Description：将list放入缓存<br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午4:39:12 <br>
     * @param key 键
     * @param value 值
     * @param time 时间(秒)
     * @return <br>
     */
    public boolean lSet(String key, List<Object> value, long time) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("将list放入缓存:{}", e);
            return false;
        }
    }

    /**
     * Title：lUpdateIndex <br>
     * Description：根据索引修改list中的某条数据 <br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午4:39:59 <br>
     * @param key 键
     * @param index 索引
     * @param value 值
     * @return <br>
     */
    public boolean lUpdateIndex(String key, long index, Object value) {
        try {
            redisTemplate.opsForList().set(key, index, value);
            return true;
        } catch (Exception e) {
            log.error("根据索引修改list中的某条数据:{}", e);
            return false;
        }
    }


    /**
     * Title：lRemove <br>
     * Description：移除N个值为value <br>
     * author：夏阳 <br>
     * date：2020年2月12日 下午4:40:37 <br>
     * @param key 键
     * @param count 移除多少个
     * @param value 值
     * @return 移除的个数
     * @return <br>
     */
    public long lRemove(String key, long count, Object value) {
        try {
            Long remove = redisTemplate.opsForList().remove(key, count, value);
            return remove;
        } catch (Exception e) {
            log.error("移除N个值为value:{}", e);
            return 0;
        }
    }

    /**
    * Title：expire <br>
    * Description： 指定缓存失效时间<br>
    * author：夏阳 <br>
    * date：2020年2月12日 下午3:54:03 <br>
    * @param key 键
    * @param time 时间(秒)
    * @return <br>
    */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            log.error("指定缓存失效时间异常:{}", e);
            return false;
        }
    }

    public void persistKey(String key) {
        redisTemplate.persist(key);
    }

    public long getKeyExpire(String key, TimeUnit timeUnit) {
        return redisTemplate.getExpire(key, timeUnit);
    }

    public void expireKeyAt(String key, Date date) {
        redisTemplate.expireAt(key, date);
    }


    public void deleteKey(String... keys) {
        Set<String> kSet = Stream.of(keys).map(k -> k).collect(Collectors.toSet());
        redisTemplate.delete(kSet);
    }

    public void deleteKey(String key) {
        redisTemplate.delete(key);
    }

    public boolean existsKey(String key) {
        return redisTemplate.hasKey(key);
    }

    public void renameKey(String oldKey, String newKey) {
        redisTemplate.rename(oldKey, newKey);
    }

    public boolean renameKeyNotExist(String oldKey, String newKey) {
        return redisTemplate.renameIfAbsent(oldKey, newKey);
    }

    /**
     * Title：getExpire <br>
     * Description：获取过期时间秒 <br>
     * author：王磊 <br>
     * date：2020年6月23日 下午2:34:08 <br>
     * @param key
     * @return 返回过期时间<br>
     */
    public Long getExpire(String key) {
        return redisTemplate.getExpire(key);
    }
}
