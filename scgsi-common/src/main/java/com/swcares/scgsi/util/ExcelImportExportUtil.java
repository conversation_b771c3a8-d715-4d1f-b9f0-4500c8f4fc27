package com.swcares.scgsi.util;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;

/**
 * ClassName：com.swcares.scgsi.util.ExcelUtil <br>
 * Description：excel文件导入导出 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月8日 上午9:36:47 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class ExcelImportExportUtil {

    /**
     * Title：exportExcel <br>
     * Description：这里用一句话描述这个方法的作用 <br>
     * author：王磊 <br>
     * date：2020年4月8日 下午3:48:07 <br>
     * @param response
     * @param fileName    文件名
     * @param projects    对象集合
     * @param columnNames 导出的excel中的列名
     * @param keys        对应的是对象中的字段名字
     * @throws IOException <br>
     */
    public static void exportExcel(HttpServletResponse response, String fileName, List<?> projects,
            String[] columnNames, String[] keys) throws IOException {
        ExcelWriter bigWriter = ExcelUtil.getWriter();
        ServletOutputStream out = null;
        try{
            for (int i = 0; i < columnNames.length; i++) {
                if (keys.length > 0 && columnNames.length > 0) {
                    bigWriter.addHeaderAlias(keys[i], columnNames[i]);
                }
                bigWriter.setColumnWidth(i, 20);
            }
            // 一次性写出内容，使用默认样式，强制输出标题
            bigWriter.write(projects);
            // response为HttpServletResponse对象
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            // test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
            // 下载文件能正常显示中文
            response.addHeader("Content-Disposition",
                    "attachment;fileName=" + new String((fileName + ".xlsx").getBytes("UTF-8"), "iso-8859-1"));
            out = response.getOutputStream();
            bigWriter.flush(out);
        }catch(IOException e){
            throw e;
        }finally{
            if(out != null){
                // 此处记得关闭输出Servlet流
                IoUtil.close(out);
            }
            // 关闭writer，释放内存
            bigWriter.close();
        }
    }

    /**
     * Title：importExcel <br>
     * Description：导入excel <br>
     * author：王磊 <br>
     * date：2020年4月8日 下午3:58:06 <br>
     * @param file
     * @return 返回格式化集合
     * @throws BusinessException
     * @throws IOException <br>
     */
    public static List<Map<String, Object>> importExcel(MultipartFile file,int sheetIndex)
            throws BusinessException, IOException {
        String fileName = file.getOriginalFilename();
        // 上传文件为空
        if (StringUtils.isEmpty(fileName)) {
            log.error("Excel文件导入失败");
            throw new BusinessException(MessageCode.EXCEL_NO_FILE.getCode());
        }
//        // 上传文件大小为1000条数据
//        if (file.getSize() > 1024 * 1024 * 10) {
//            log.error("upload | 上传失败: 文件大小超过10M，文件大小为：{}", file.getSize());
//            throw new BusinessException(MessageCode.EXCEL_OVERSIZED.getCode(),new String[]{});
//        }
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream(), sheetIndex);
        List<Map<String, Object>> readAll = reader.readAll();
        return readAll;
    }

}

