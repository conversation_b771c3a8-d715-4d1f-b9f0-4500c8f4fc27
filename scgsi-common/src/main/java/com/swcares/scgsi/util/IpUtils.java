package com.swcares.scgsi.util;

import java.net.DatagramSocket;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Optional;

/**
 * ClassName：com.swcares.scgsi.util.IpUtils <br>
 * Description：ip工具类<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月10日 下午5:20:04 <br>
 * @version v1.0 <br>
 */
public class IpUtils {
    
    public static void main(String[] args) throws SocketException {
        System.out.println(IpUtils.getLocalIp4Address());
        System.out.println(IpUtils.getLocalIp4AddressFromNetworkInterface());
    }
    
    /**
     * Title：getLocalIp4Address <br>
     * Description：获取的ip <br>
     * author：夏阳 <br>
     * date：2020年4月16日 下午5:34:55 <br>
     * @return 返回ip包含斜杠，用index来判断，不要用equals
     * @throws SocketException <br>
     */
    public static Optional<Inet4Address> getLocalIp4Address() throws SocketException {
        final List<Inet4Address> ipByNi = getLocalIp4AddressFromNetworkInterface();
        if (ipByNi.isEmpty() || ipByNi.size() > 1) {
            final Optional<Inet4Address> ipBySocketOpt = getIpBySocket();
            if (ipBySocketOpt.isPresent()) {
                return ipBySocketOpt;
            } else {
                return ipByNi.isEmpty() ? Optional.empty() : Optional.of(ipByNi.get(0));
            }
        }
        return Optional.of(ipByNi.get(0));
    }
    
    /**
     * Title：getLocalIp4AddressFromNetworkInterface <br>
     * Description：TODO(这里用一句话描述这个方法的作用) <br>
     * author：夏阳 <br>
     * date：2020年4月16日 下午5:35:19 <br>
     * @return
     * @throws SocketException <br>
     */
    public static List<Inet4Address> getLocalIp4AddressFromNetworkInterface()
            throws SocketException {
        List<Inet4Address> addresses = new ArrayList<>(1);
        Enumeration<?> e = NetworkInterface.getNetworkInterfaces();
        if (e == null) {
            return addresses;
        }
        while (e.hasMoreElements()) {
            NetworkInterface n = (NetworkInterface) e.nextElement();
            if (!isValidInterface(n)) {
                continue;
            }
            Enumeration<?> ee = n.getInetAddresses();
            while (ee.hasMoreElements()) {
                InetAddress i = (InetAddress) ee.nextElement();
                if (isValidAddress(i)) {
                    addresses.add((Inet4Address) i);
                }
            }
        }
        return addresses;
    }

    /**
     * 过滤回环网卡、点对点网卡、非活动网卡、虚拟网卡并要求网卡名字是eth或ens开头
     *
     * @param ni 网卡
     * @return 如果满足要求则true，否则false
     */
    private static boolean isValidInterface(NetworkInterface ni) throws SocketException {
        return !ni.isLoopback() && !ni.isPointToPoint() && ni.isUp() && !ni.isVirtual()
                && (ni.getName().startsWith("eth") || ni.getName().startsWith("ens"));
    }

    /**
     * 判断是否是IPv4，并且内网地址并过滤回环地址.
     */
    private static boolean isValidAddress(InetAddress address) {
        return address instanceof Inet4Address && address.isSiteLocalAddress()
                && !address.isLoopbackAddress();
    }
    
    /**
     * Title：getIpBySocket <br>
     * Description：TODO(这里用一句话描述这个方法的作用) <br>
     * author：夏阳 <br>
     * date：2020年4月16日 下午5:35:07 <br>
     * @return
     * @throws SocketException <br>
     */
    private static Optional<Inet4Address> getIpBySocket() throws SocketException {
        try (final DatagramSocket socket = new DatagramSocket()) {
            socket.connect(InetAddress.getByName("*******"), 10002);
            if (socket.getLocalAddress() instanceof Inet4Address) {
                return Optional.of((Inet4Address) socket.getLocalAddress());
            }
        } catch (UnknownHostException e) {
            throw new RuntimeException(e);
        }
        return Optional.empty();
    }
}
