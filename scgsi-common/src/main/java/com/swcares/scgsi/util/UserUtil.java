package com.swcares.scgsi.util;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.dict.entity.SysDictDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.user.util.UserUtil <br>
 * Description：用于获取用户信息的工具类，获取security用于对象查看@see AuthenticationUtil<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年6月18日 上午8:59:38 <br>
 * @version v1.0 <br>
 */
@Component
public class UserUtil {

    // 赔偿类型 不正常航班 0、（这个和OrderAuditServiceImpl一致，荣荣和建文没有提取公共类，后续提取后，需要挪到公共类）
   /* private static final String STATUS_IRREGULAR = "0";
    // 赔偿类型 异常行李1、
    private static final String STATUS_ABNORMAL_BAGGAGE = "1";
    // 赔偿类型 超售旅客2、
    private static final String STATUS_OVERBOOK = "2";
*/
    @Autowired
    private BaseDAO baseDAO;

    /**
     * Title：findBussiTypeByEmpId <br>
     * Description：通过用户id检索出用户业务类型，如张三id，返回超售类型或异常行李类型<br>
     * author：夏阳 <br>
     * date：2020年6月18日 上午9:02:35 <br>
     * @param tuNo 当前登录账号
     * @return <br>
     */
    @SuppressWarnings("unchecked")
    public List<String> findBussiTypeByEmpId(String tuNo) {
        List<Object> list = new ArrayList<>();
        list.add(tuNo);
        // 通过用户信息查询出部门id，再通过部门id在数据字典查询出对应的业务类型
        String sql =
                "select dc.data_code, dc.data_value from sys_dict dc where exists (select emp.toid from employee emp where emp.toid = dc.data_value and emp.TUNO = ? )";
        List<SysDictDataInfo> results =
                (List<SysDictDataInfo>) baseDAO.findBySQL_comm(sql, SysDictDataInfo.class, list);
        return handlerType(results);
    }

    /**
     * Title：handlerType <br>
     * Description：根据返回数据字典结果算出对应业务类型<br>
     * author：夏阳 <br>
     * date：2020年6月18日 上午9:49:32 <br>
     * @param results
     * @return List<String> 不正常航班 0 ;  异常行李1;  超售旅客2; <br>
     */
    private List<String> handlerType(List<SysDictDataInfo> results) {
        List<String> typeList = new ArrayList<>();
        for (SysDictDataInfo sysDictDataInfo : results) {
            typeList.add(sysDictDataInfo.getKey());
        }
        return typeList;
    }
}
