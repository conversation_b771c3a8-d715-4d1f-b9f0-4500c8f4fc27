package com.swcares.scgsi.util;

import java.io.ByteArrayOutputStream;

/**
 * ClassName：com.swcares.scgsi.util.Base64Util <br>
 * Description：base64编码转换工具类<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月24日 下午2:27:12 <br>
 * @version v1.0 <br>
 */
public class Base64Util {

    /** base64DecodeChars **/
    private static String base64EncodeStr =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+:";
    /**base64DecodeChars **/
    private static char[] base64EncodeChars = base64EncodeStr.toCharArray();
    /** base64DecodeChars**/
    private static byte[] base64DecodeChars;

    static {
        // 创建base64DecodeChars大小
        base64DecodeChars = new byte[128];
        for (int i = 0; i < 128; i++) {
            base64DecodeChars[i] = (byte) -1;
        }

        // 循环A-Z
        for (int i = 'A'; i <= 'Z'; i++) {
            base64DecodeChars[i] = (byte) base64EncodeStr.indexOf((char) (i));
        }
        // 循环a-z
        for (int i = 'a'; i <= 'z'; i++) {
            base64DecodeChars[i] = (byte) base64EncodeStr.indexOf((char) (i));
        }
        // 循环0-9
        for (int i = '0'; i <= '9'; i++) {
            base64DecodeChars[i] = (byte) base64EncodeStr.indexOf((char) (i));
        }
        base64DecodeChars['+'] = (byte) base64EncodeStr.indexOf('+');
        base64DecodeChars[':'] = (byte) base64EncodeStr.indexOf(':');
    }

    private Base64Util() {}

    /**
     * Title：encode <br>
     * Description：使用Base64转码<br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:09:44 <br>
     * @param data byte[]字节数组
     * @return String 字符串<br>
     */
    public static String encode(byte[] data) {
        // 创建builder
        StringBuilder sb = new StringBuilder();
        int len = data.length;
        int i = 0;
        // 初始化b1，b2，b3
        int b1, b2, b3;

        while (i < len) {
            // b1
            b1 = data[i++] & 0xff;
            // 循环
            if (i == len) {
                sb.append(base64EncodeChars[b1 >>> 2]);
                sb.append(base64EncodeChars[(b1 & 0x3) << 4]);
                sb.append("==");
                break;
            }
            // b2
            b2 = data[i++] & 0xff;
            if (i == len) {
                sb.append(base64EncodeChars[b1 >>> 2]);
                sb.append(base64EncodeChars[((b1 & 0x03) << 4) | ((b2 & 0xf0) >>> 4)]);
                sb.append(base64EncodeChars[(b2 & 0x0f) << 2]);
                sb.append("=");
                break;
            }
            // b3
            b3 = data[i++] & 0xff;
            sb.append(base64EncodeChars[b1 >>> 2]);
            sb.append(base64EncodeChars[((b1 & 0x03) << 4) | ((b2 & 0xf0) >>> 4)]);
            sb.append(base64EncodeChars[((b2 & 0x0f) << 2) | ((b3 & 0xc0) >>> 6)]);
            sb.append(base64EncodeChars[b3 & 0x3f]);
        }
        return sb.toString();
    }

    /**
     * Title：decode <br>
     * Description：使用base64解码<br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:10:09 <br>
     * @param data byte[]字节数组
     * @return byte[] 字节数组<br>
     */
    public static byte[] decode(byte[] data) {
        // 获取data长度
        int len = data.length;
        // 定义输出流
        ByteArrayOutputStream buf = new ByteArrayOutputStream(len);
        int i = 0;
        // 定义b1，b2，b3
        int b1, b2, b3, b4;

        while (i < len) {

            /* b1 */
            do {
                b1 = base64DecodeChars[data[i++]];
            } while (i < len && b1 == -1);
            if (b1 == -1) {
                break;
            }

            /* b2 */
            do {
                b2 = base64DecodeChars[data[i++]];
            } while (i < len && b2 == -1);
            if (b2 == -1) {
                break;
            }
            buf.write((int) ((b1 << 2) | ((b2 & 0x30) >>> 4)));

            /* b3 */
            do {
                b3 = data[i++];
                if (b3 == 61) {
                    return buf.toByteArray();
                }
                b3 = base64DecodeChars[b3];
            } while (i < len && b3 == -1);
            if (b3 == -1) {
                break;
            }
            buf.write((int) (((b2 & 0x0f) << 4) | ((b3 & 0x3c) >>> 2)));

            /* b4 */
            do {
                b4 = data[i++];
                if (b4 == 61) {
                    return buf.toByteArray();
                }
                b4 = base64DecodeChars[b4];
            } while (i < len && b4 == -1);
            if (b4 == -1) {
                break;
            }
            // 写入数据
            buf.write((int) (((b3 & 0x03) << 6) | b4));
        }
        // 返回
        return buf.toByteArray();
    }

}
