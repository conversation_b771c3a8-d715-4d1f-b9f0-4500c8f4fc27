/**
 * <AUTHOR>
 * @date 2015-05-06
 */
package com.swcares.scgsi.util;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import lombok.extern.slf4j.Slf4j;

/**
 * ClassName：com.swcares.scgsi.util.MD5EncryptUtils <br>
 * Description： MD5加密 JAVA的标准类库理论上功能也很强大，但由于虚拟机/运行时的实现太多，加之版本差异， 有些代码在不同环境下运行会出现奇怪的异常结果，尤其以涉及字符集的操作为甚。<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月24日 下午2:10:54 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class MD5EncryptUtils {

    /** 十六进制数字 **/
    private static final String[] HEXDIGITS =
            {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};

    private MD5EncryptUtils() {}

    /**
     * Title：byteArrayToHexString <br>
     * Description：转换字节数组为16进制字串<br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:12:08 <br>
     * @param b 字节数组
     * @return String16进制字串 <br>
     */
    private static String byteArrayToHexString(byte[] b) {
        StringBuilder resultSb = new StringBuilder();
        for (int i = 0; i < b.length; i++) {
            // 若使用本函数转换则可得到加密结果的16进制表示，即数字字母混合的形式
            resultSb.append(byteToHexString(b[i]));
        }
        return resultSb.toString();
    }

    /**
     * Title：byteToHexString <br>
     * Description：byte 类型转换成16进制字符串 <br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:26:39 <br>
     * @param b
     * @return <br>
     */
    private static String byteToHexString(byte b) {
        int n = b;
        if (n < 0) {
            n = 256 + n;
        }
        int d1 = n / 16;
        int d2 = n % 16;
        return HEXDIGITS[d1] + HEXDIGITS[d2];
    }

    /**
     * Title：encryptPassword <br>
     * Description： 只加密密码方法 <br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:12:35 <br>
     * @param password  用户输入的密码
     * @return resultString 将密码加密后的密文<br>
     */
    public static String encryptPassword(String password) {
        String resultString = password;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            resultString = byteArrayToHexString(md.digest(resultString.getBytes(CharsetUtil.UTF8)));
        } catch (NoSuchAlgorithmException ex) {
            resultString = "";
            log.error("NoSuchAlgorithmException异常。{}", ex);
        } catch (UnsupportedEncodingException e) {
            resultString = "";
            log.error("UnsupportedEncodingException异常。{}", e);
        }
        return resultString;
    }

    /**
     * Title：encryptUserAndPassword <br>
     * Description：将（用户名+密码）同时加密，做为一个密文串存于数据库中<br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:13:07 <br>
     * @param user 用户输入的用户名
     * @param password  将（用户名+密码）加密后的密文
     * @return <br>
     */
    public static String encryptUserAndPassword(String user, String password) {
        String origin = user + password;
        String resultString = null;
        try {
            resultString = origin;
            MessageDigest md = MessageDigest.getInstance("MD5");
            resultString = byteArrayToHexString(md.digest(resultString.getBytes(CharsetUtil.UTF8)));
        } catch (NoSuchAlgorithmException ex) {
            resultString = "";
            log.error("NoSuchAlgorithmException异常。{}", ex);
        } catch (UnsupportedEncodingException e) {
            resultString = "";
            log.error("NoSuchAlgorithmException异常。{}", e);
        }
        return resultString;
    }

    /**
     * 
     * 
     * @param user
     *            
     * @param password
     *            
     * @param encryptPassword
     *            由数据库中查询到的用户加密后的密码串
     * @return ;
     * @author：Gine 
     */
    /**
     * Title：checkEncryptUserAndPassword <br>
     * Description：验证登录用户所输入的用户名与密码是否正确， 此方法需要首先查询出该用户在数据库中所存储的密文 <br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:13:38 <br>
     * @param user 用户输入的用户名
     * @param password 用户输入的密码
     * @param encryptPassword 由数据库中查询到的用户加密后的密码串
     * @return boolean 验证通过返回true;否则返回false<br>
     */
    public static boolean checkEncryptUserAndPassword(String user, String password,
            String encryptPassword) {
        String origin = user + password;
        String resultString = null;
        try {
            resultString = origin;
            MessageDigest md = MessageDigest.getInstance("MD5");
            resultString = byteArrayToHexString(md.digest(resultString.getBytes(CharsetUtil.UTF8)));
        } catch (NoSuchAlgorithmException ex) {
            resultString = "";
            log.error("NoSuchAlgorithmException异常。{}", ex);
        } catch (UnsupportedEncodingException e) {
            resultString = "";
            log.error("NoSuchAlgorithmException异常。{}", e);
        }

        return resultString.equals(encryptPassword);
    }

    /**
     * Title：checkEncryptPassword <br>
     * Description：验证登录用户所输入的密码是否正确， 此方法需要首先查询出该用户在数据库中所存储的密文 <br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:14:32 <br>
     * @param password  用户输入的密码
     * @param encryptPassword  由数据库中查询到的用户加密后的密码串
     * @return boolean 验证通过返回true;否则返回false;<br>
     */
    public static boolean checkEncryptPassword(String password, String encryptPassword) {
        String resultString = null;
        try {
            resultString = password;
            MessageDigest md = MessageDigest.getInstance("MD5");
            resultString = byteArrayToHexString(md.digest(resultString.getBytes(CharsetUtil.UTF8)));
        } catch (NoSuchAlgorithmException ex) {
            resultString = "";
            log.error("NoSuchAlgorithmException异常。{}", ex);
        } catch (UnsupportedEncodingException e) {
            resultString = "";
            log.error("NoSuchAlgorithmException异常。{}", e);
        }

        return resultString.equals(encryptPassword);
    }

}
