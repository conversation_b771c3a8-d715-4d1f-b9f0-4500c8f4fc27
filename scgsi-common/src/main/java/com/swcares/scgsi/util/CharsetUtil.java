
package com.swcares.scgsi.util;

import java.io.UnsupportedEncodingException;
import org.apache.tomcat.util.codec.binary.Base64;
import lombok.extern.slf4j.Slf4j;


/**
 * ClassName：com.swcares.scgsi.util.CharsetUtil <br>
 * Description：编码转换工具类<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月24日 下午2:23:20 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class CharsetUtil {

    /** UTF-8编码 **/
    public static final String UTF8 = "UTF-8";

    /** GBK编码 **/
    public static final String GBK = "GBK";

    /** GB2312编码 **/
    public static final String GB2312 = "GB2312";

    /**
     * Add a private constructor to hide the implicit public one.
     * add by quanz 2015-10-15
     * <AUTHOR>
     */
    private CharsetUtil() {

    }

    /**
     * Title：convertCharset <br>
     * Description：功能描述：编码转换 <br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:23:53 <br>
     * @param argStr 源字符串，如abc123
     * @param orgCharset 源字符串编码，如GBK
     * @param dstCharset 目标字符串编码，如UTF-8
     * @return <br>
     */
    public static String convertCharset(String argStr, String orgCharset, String dstCharset) {
        // 创建base64对象
        Base64 encoder = new Base64();
        // 参数判空
        if (argStr == null) {
            return null;
        }
        try {
            // 转码
            String str = new String(Base64.decodeBase64(argStr), orgCharset);
            String text = new String(str.getBytes(dstCharset), dstCharset);
            // 返回转码后的str
            return new String(encoder.encode(text.getBytes(dstCharset)));
        } catch (UnsupportedEncodingException e) {
            log.error("不支持的字符编码：" + e.getMessage());
            return null;
        }
    }

    /**
     * Title：gbkToUtf8 <br>
     * Description：GBK转UTF-8<br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:24:49 <br>
     * @param argStr
     * @return <br>
     */
    public static String gbkToUtf8(String argStr) {
        // 创建base64对象
        Base64 encoder = new Base64();
        if (argStr == null) {
            return null;
        }
        try {
            // 转码gbk
            String str = new String(Base64.decodeBase64(argStr), GBK);
            // gbk转utf-8
            String text = new String(str.getBytes(UTF8), UTF8);
            // 返回转码结果
            return new String(encoder.encode(text.getBytes(UTF8)));
        } catch (UnsupportedEncodingException e) {
            log.error("不支持的字符编码：" + e.getMessage());
            return null;
        }
    }

    /**
     * Title：utf8ToGBK <br>
     * Description：UTF-8转GBK <br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:25:15 <br>
     * @param argStr
     * @return <br>
     */
    public static String utf8ToGBK(String argStr) {
        // 创建base64对象
        Base64 encoder = new Base64();
        // 参数判空
        if (argStr == null) {
            return null;
        }
        try {
            // 转utf-8
            String str = new String(Base64.decodeBase64(argStr), UTF8);
            // utf-8转gbk
            String text = new String(str.getBytes(GBK), GBK);
            // 构建gbk的返回结果
            return new String(encoder.encode(text.getBytes(GBK)));
        } catch (UnsupportedEncodingException e) {
            log.error("不支持的字符编码：" + e.getMessage());
            return null;
        }
    }

}
