package com.swcares.scgsi.util;

import java.io.UnsupportedEncodingException;
import java.security.Key;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.util.StringUtils;
import com.swcares.scgsi.aop.EncryptFieldAop;
import lombok.extern.slf4j.Slf4j;

/**
 * ClassName：com.swcares.scgsi.util.AesEncryptUtil <br>
 * Description：AES加密、解密工具类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月24日 下午2:07:00 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class AesEncryptUtil {

    /** 算法名称-AES **/
    public static final String ALGORITHM_AES = "AES";

    public static final String AES_KEY = "c2Nnc2kjMjAyMHN3Y2FyZXMkc2Nnc2kjMjAyMHN3Y2FyZXM=";
    public static final String AES_PHONE_KEY = "c2Nnc2kjMjAyNHNjZ3NpMDQkc2Nnc2kjMjAyNHNjZ3NpMDQ=";


    /** 转换名称-AES/CBC/PKCS5Padding **/
    public static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";

    /**
     * Title：aesEncryptScgsi <br>
     * Description：适用于山航条件查询时调用 <br>
     * author：夏阳 <br>
     * date：2020年10月19日 上午10:21:54 <br>
     * @param content
     * @return <br>
     */
    public static String aesEncryptScgsi(String content) {
        // 取密钥和偏转向量
        byte[] privateKey = new byte[16];
        byte[] iv = new byte[16];
        try {
            // 获取加密，需要的私钥、偏移向量
            getKeyIV(AES_KEY, privateKey, iv);

            // 两个参数，第一个为私钥字节数组；第二个为加密方式 AES或者DES
            Key keySpec = new SecretKeySpec(privateKey, ALGORITHM_AES);
            // 偏移向量
            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            // 使用Cipher的实例
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            // 初始化加密操作,传递加密的钥匙
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

            byte[] temp = cipher.doFinal(content.getBytes(CharsetUtil.UTF8));
            // 返回加密串，进行64位转码
            return EncryptFieldAop.KEY_PREFIX + Base64.encodeBase64String(temp);
        } catch (Exception e) {
            log.error("加密时异常{}", e);
            return "";
        }
    }

    /**
     * Title：aesEncrypt <br>
     * Description：AES加密 <br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:07:42 <br>
     * @param key
     * @param content
     * @return <br>
     */
    public static String aesEncrypt(String key, String content) {
        // 取密钥和偏转向量
        byte[] privateKey = new byte[16];
        byte[] iv = new byte[16];
        try {
            // 获取加密，需要的私钥、偏移向量
            getKeyIV(key, privateKey, iv);

            // 两个参数，第一个为私钥字节数组；第二个为加密方式 AES或者DES
            Key keySpec = new SecretKeySpec(privateKey, ALGORITHM_AES);
            // 偏移向量
            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            // 使用Cipher的实例
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            // 初始化加密操作,传递加密的钥匙
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

            byte[] temp = cipher.doFinal(content.getBytes(CharsetUtil.UTF8));
            // 返回加密串，进行64位转码
            return Base64.encodeBase64String(temp);
        } catch (Exception e) {
            log.error("加密时异常{}", e);
            return "";
        }
    }

    /**
     * Title：aesDecrypt <br>
     * Description：AES解密<br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:08:16 <br>
     * @param key 密钥
     * @param encryptStr 待解密的加密串
     * @return String 解密后的明文<br>
     */
    public static String aesDecrypt(String key, String encryptStr) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(encryptStr)) {
            log.info("解密时key值为空，返回空字符串");
            return "";
        }
        if(encryptStr.indexOf(EncryptFieldAop.KEY_PREFIX) > -1){
            encryptStr = encryptStr.replace(EncryptFieldAop.KEY_PREFIX, "");
        }
        try {
            // base64解码
            byte[] encBuf = Base64.decodeBase64(encryptStr);

            // 取密钥和偏转向量
            byte[] privateKey = new byte[16];
            byte[] iv = new byte[16];
            getKeyIV(key, privateKey, iv);

            // 两个参数，第一个为私钥字节数组；第二个为加密方式 AES或者DES
            Key keySpec = new SecretKeySpec(privateKey, ALGORITHM_AES);
            // 偏移向量
            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            // 使用Cipher的实例
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            // 初始化加密操作,传递加密的钥匙
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            // 返回加密串
            return new String(cipher.doFinal(encBuf), CharsetUtil.UTF8);
        } catch (Exception e) {
            log.error("解密时异常{}", e);
            return "";
        }
    }

    /**
     * Title：getKeyIV <br>
     * Description：获取加密、解密时需要的私钥、偏移向量<br>
     * author：夏阳 <br>
     * date：2020年2月24日 下午2:09:04 <br>
     * @param encryptKey
     * @param key
     * @param iv <br>
     */
    public static void getKeyIV(String encryptKey, byte[] key, byte[] iv) {
        // 密钥Base64解密
        byte[] buf = Base64.decodeBase64(encryptKey);
        int keyLength = key.length;
        // 私钥key
        for (int i = 0; i < keyLength; i++) {
            key[i] = buf[i];
        }

        // 偏移向量
        for (int i = 0; i < iv.length; i++) {
            iv[i] = buf[i + keyLength];
        }
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        //生产：wx81ad6744e1880274   e0a69fe46afcb2d0034bc75af8882047
        //c2Nnc2kjMjAyNHNjZ3NpMDQkc2Nnc2kjMjAyNHNjZ3NpMDQ=
       /* String privateKey = "scgsi#2024scgsi04$scgsi#2024scgsi04";
        // String privateKey = "cqrd#travelweb2#TWT_LIMIT_ETERM2222wwwwww#";
        System.out.println(privateKey.getBytes(CharsetUtil.UTF8));
        String key = Base64.encodeBase64String(privateKey.getBytes(CharsetUtil.UTF8));
        System.out.println(key);
        String a = aesEncrypt(key, "36527728");
        System.out.println(a);
        System.out.println(aesDecrypt(key, a));

        String s_utf8 = new String(Base64.decodeBase64(key), "UTF-8");
        System.out.println(s_utf8);*/
        System.out.println(AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, "oevwbaUG7V4hwezpLcMaGw=="));
    }
}
