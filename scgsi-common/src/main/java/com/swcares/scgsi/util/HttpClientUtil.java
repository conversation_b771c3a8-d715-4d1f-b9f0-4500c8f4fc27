package com.swcares.scgsi.util;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * ClassName：com.swcares.scgsi.util <br>
 * Description：http请求 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月05日 17:48 <br>
 * @version v1.0 <br>
 */
public class HttpClientUtil {


    public static String doPost(String targetURLString, String parameter,String clientId,String operationCode)
            throws IOException{

        if(targetURLString == null || "".equals(targetURLString)){
            throw new IllegalArgumentException("目标链接(targetURLString)不能为空！");
        }

        URL targetURL = new URL(targetURLString);
        HttpURLConnection urlConnection = (HttpURLConnection) targetURL.openConnection();

        urlConnection = initAConnectionByDefaultConfig(urlConnection,clientId,operationCode);

        urlConnection.setRequestMethod("POST");
        OutputStream os = urlConnection.getOutputStream();
        OutputStreamWriter osw = new OutputStreamWriter(os);

        if(parameter != null && !"".equals(parameter)){
            osw.write(parameter);
        }

        osw.flush();
        osw.close();
        return transmitInputStreamToHtmlString(urlConnection.getInputStream());
    }
    private static HttpURLConnection initAConnectionByDefaultConfig(HttpURLConnection connection
            ,String clientId,String operationCode){
        // HTTP传递xml
        connection.setRequestProperty("Content-Type", "text/xml");
        // 请求头固定参数
        connection.setRequestProperty("ClientId", clientId);
        connection.setRequestProperty("OperationCode", operationCode);
        connection.setDoOutput(true);
        connection.setDoInput(true);

        connection.setAllowUserInteraction(true);
        connection.setUseCaches(false);
        connection.setInstanceFollowRedirects(false);

        /* 设置连接超时时间 */ 
        connection.setConnectTimeout(30 * 1000);
        /* 设置从主机获取数据超时间 */
        connection.setReadTimeout(30 * 1000);
        return connection;
    }

    private static String transmitInputStreamToHtmlString(InputStream in) throws IOException{
        BufferedReader br = new BufferedReader(new InputStreamReader(in,"utf-8"));

        StringBuilder htmlContent = new StringBuilder("");

        String line = br.readLine();

        while(line != null){
            htmlContent.append(line.trim() + "\n");
            line = br.readLine();
        }
        in.close();
        return htmlContent.toString();
    }

}
