package com.swcares.scgsi.util;

import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 *
 * ClassName：com.swcares.scgsi.util <br>
 * Description：ID生成工具 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年 04月25日 14:39 <br>
 * @version v1.0 <br>
 */
@Component
public class SeedIdUtil {

    @Autowired
    private RedissonClient redisson;

    private static Long randomNumber;

    private static Long curIndex = Long.valueOf(0L);

    public  String getId()  {
        RLock lock = redisson.getLock("scgsi:SeedId");
        while (lock.isLocked()){
            lock = redisson.getLock("scgsi:SeedId");
        }
        lock.lock(10, TimeUnit.SECONDS);
        String result;
        try {
            Long index = null;
            // 从0到999 curIndex*100 curIndex 100-99900
            index = (curIndex = curIndex.longValue() + 1L).longValue() % 1000L;
            if (curIndex.longValue() >= 1000L) {
                curIndex = 0L;
            }
            // 随机数 1-10
            randomNumber = Long.valueOf(new Random().nextInt(100));
            result = String.valueOf((new Date()).getTime() * 100000L + index.longValue() * 100L
                    + randomNumber.longValue());
        }finally {
            lock.unlock();
        }
        return  result;
    }

}