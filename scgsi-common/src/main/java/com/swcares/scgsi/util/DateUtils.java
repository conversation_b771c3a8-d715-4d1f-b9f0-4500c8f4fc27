package com.swcares.scgsi.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.util <br>
 * Description：时间格式化工具类 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月03日 15:55 <br>
 * @version v1.0 <br>
 */
public class DateUtils {
    /**
     * 系统时间yyyy-MM-dd HH:mm:ss
     */
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    /**
     * 系统时间yyyy-MM-dd
     */
    public static final String YYYY_MM_DD= "yyyy-MM-dd";
    /**
     * 系统短时间yyyyMMddHHmm
     */
    public static final String YYYYMMDDHHMM= "yyyyMMddHHmm";
    /**
     * 系统短时间yyyyMMdd
     */
    public static final String YYYYMMDD= "yyyyMMdd";
    /**
     * 系统短时间yyyyMMddHHmmss
     */
    public static final String YYYYMMDDHHMMSS= "yyyyMMddHHmmss";

    /**
     * FOC航班日期格式
     */
    public static final String FOC_FLIGHT_DATE= "yyyy/MM/dd";
    /**
     * Title：parseDateToStr <br>
     * Description： 日期格式转换为String<br>
     * author：王建文 <br>
     * date：2020-3-3 16:01 <br>
     * @param date Date类型时间
     * @param timeFormat String类型格式
     * @return String
     */
    public static String parseDateToStr(Date date, String timeFormat){
        DateFormat dateFormat=new SimpleDateFormat(timeFormat);
        return dateFormat.format(date);
    }
    /**
     * Title：parseCurrentDateToStr <br>
     * Description： 当前日期格式转换为String<br>
     * author：王建文 <br>
     * date：2020-3-3 16:03 <br>
     * @param timeFormat String类型格式
     * @return String
     */
    public static String parseCurrentDateToStr(String timeFormat){
        DateFormat dateFormat=new SimpleDateFormat(timeFormat);
        return dateFormat.format(new Date());
    }
    /**
     * Title：parseStrToDate <br>
     * Description： String格式转换成日期<br>
     * author：王建文 <br>
     * date：2020-3-3 16:05 <br>
     * @param dateStr 时间字符串
     * @param timeFormat String类型格式
     * @return Date
     */
    public static Date parseStrToDate (String dateStr, String timeFormat){
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat(timeFormat);
        try{
            return simpleDateFormat.parse(dateStr);
        }catch (Exception e){
            return null;
        }
    }
    public static String parseStrToStr (String dateStr, String timeFormat){
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat(timeFormat);
        try{
            return simpleDateFormat.format(parseStrToDate(dateStr,timeFormat));
        }catch (Exception e){
            return null;
        }
    }

    /**
     * Title：timeComparison() <br>
     * Description： <br>
     * author：于琦海 <br>
     * date：2020/4/13 21:44 <br>
     * @param startDate String
     * @param endDate String
     * @return boolean
     */
    public static boolean timeComparison(String startDate, String endDate) {
        DateFormat df = new SimpleDateFormat("HH:mm");//创建日期转换对象HH:mm:ss为时分秒，年月日为yyyy-MM-dd
        Date dt1;//将字符串转换为date类型
        try {
            dt1 = df.parse(startDate);
            Date dt2 = df.parse(endDate);
            if (dt1.getTime() >= dt2.getTime()) {
                return true;
            }
        } catch (ParseException e) {
            return false;
        }
        return false;
    }

    /**
     * Title：formatStr <br>
     * Description：将日期格式转换为FOC需要的格式 <br>
     * author：于琦海 <br>
     * date：2020/4/23 9:49 <br>
     * @param: null
     * @return:
     */
    public static String formatStr(String time) {
        DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter outputFormat = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        return LocalDate.parse(time, inputFormat).format(outputFormat);
    }

}