package com.swcares.scgsi.util;

import org.apache.commons.lang3.StringUtils;
import net.sourceforge.pinyin4j.PinyinHelper;

/**
 * ClassName：com.swcares.scgsi.util.PinyinUtil <br>
 * Description：com.belerweb/pinyin4j包下实现的拼音工具类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月10日 下午1:49:58 <br>
 * @version v1.0 <br>
 */
public class PinyinUtil {

    /**
     * 
     * Title：getChineseCharactersOfAbbreviation <br>
     * Description：获取汉字首字母的方法。如： 张三 --> ZS <br>
     * author：夏阳 <br>
     * date：2020年4月10日 下午1:51:02 <br>
     * @param word 汉子字符串
     * @return 大写汉子首字母; 如果都转换失败,那么返回null<br>
     */
    public static String getChineseCharactersOfAbbreviation(String word) {
        String result = null;
        if (StringUtils.isNotEmpty(word)) {
            char[] charArray = word.toCharArray();
            StringBuffer sb = new StringBuffer();
            for (char ch : charArray) {
                // 逐个汉字进行转换， 每个汉字返回值为一个String数组（因为有多音字）
                String[] stringArray = PinyinHelper.toHanyuPinyinStringArray(ch);
                if (null != stringArray) {
                    sb.append(stringArray[0].charAt(0));
                }
            }
            if (sb.length() > 0) {
                result = sb.toString().toUpperCase();
            }
        }
        return result;
    }

    /**
     * Title：getChineseCharacters <br>
     * Description：获取汉字拼音的方法。如： 张三 --> zhangsan[说明：暂时解决不了多音字的问题，只能使用取多音字的第一个音的方案] <br>
     * author：夏阳 <br>
     * date：2020年4月10日 下午1:54:18 <br>
     * @param word 汉子字符串
     * @return 汉字拼音; 如果都转换失败,那么返回null<br>
     */
    public static String getChineseCharacters(String word) {
        String result = null;
        if (StringUtils.isNotEmpty(word)) {
            char[] charArray = word.toCharArray();
            StringBuffer sb = new StringBuffer();
            for (char ch : charArray) {
                // 逐个汉字进行转换， 每个汉字返回值为一个String数组（因为有多音字）
                String[] stringArray = PinyinHelper.toHanyuPinyinStringArray(ch);
                if (null != stringArray) {
                    // 把第几声这个数字给去掉
                    sb.append(stringArray[0].replaceAll("\\d", ""));
                }
            }
            if (sb.length() > 0) {
                result = sb.toString();
            }
        }
        return result;
    }
}
