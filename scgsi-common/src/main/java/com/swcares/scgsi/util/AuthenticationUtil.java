package com.swcares.scgsi.util;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：AuthenticationUtil <br>
 * Package：com.swcares.scgsi.util <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月21日 9:56 <br>
 * @version v1.0 <br>
 */
public class AuthenticationUtil {

    /**
     * Title：getAuthentication() <br>
     * Description：获取认证内容 <br>
     * author：于琦海 <br>
     * date：2020/4/21 9:58 <br>
     * @return Authentication
     */
    public static Authentication getAuthentication(){
       return  SecurityContextHolder.getContext().getAuthentication();
    }

}
