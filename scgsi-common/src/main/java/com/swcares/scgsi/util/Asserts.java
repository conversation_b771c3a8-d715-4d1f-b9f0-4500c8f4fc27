
/**  
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：Asserts.java <br>
 * Package：com.swcares.scgsi.util <br> 
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月5日 下午4:36:32 <br>
 * @version v1.0 <br>
 */ 
package com.swcares.scgsi.util;

import org.apache.commons.lang3.StringUtils;
import com.swcares.exception.BusinessException;

/**   
 * ClassName：com.swcares.scgsi.util.Asserts <br>
 * Description：Assertion utility class that assists in validating arguments.[使用该类注意理解断言和判空工具类的精髓，注意看方法的注释] <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月5日 下午4:36:32 <br>
 * @version v1.0 <br>  
 */
public class Asserts {
    
    /**
     * Title：isNull <br>
     * Description：为空继续执行逻辑，不为空抛出异常 <br>
     * author：夏阳 <br>
     * date：2020年4月22日 下午4:56:20 <br>
     * @param object 需要判断的对象
     * @param code message的异常码<br>
     */
    public static void isNull(Object object, String code) {
        if (object != null) {
            throw new BusinessException(code);
        }
    }
    
    /**
     * Title：isNull <br>
     * Description：为空继续执行逻辑，不为空抛出异常  <br>
     * author：夏阳 <br>
     * date：2020年4月22日 下午4:56:45 <br>
     * @param object 需要判断的对象
     * @param code message的异常码
     * @param param 需要替换的参数<br>
     */
    public static void isNull(Object object, String code, String[] param) {
        if (object != null) {
            throw new BusinessException(code, param);
        }
    }
    
    /**
     * Title：notNull <br>
     * Description：不为空继续执行逻辑，为空抛出异常 <br>
     * author：夏阳 <br>
     * date：2020年4月22日 下午4:57:08 <br>
     * @param object 需要判断的对象
     * @param code message的异常码 <br>
     */
    public static void notNull(Object object, String code) {
        if (object == null) {
            throw new BusinessException(code);
        }
    }
    
    /**
     * Title：notNull <br>
     * Description：不为空继续执行逻辑，为空抛出异常<br>
     * author：夏阳 <br>
     * date：2020年4月22日 下午4:57:54 <br>
     * @param object 需要判断的对象
     * @param code message的异常码
     * @param param 需要替换的参数<br>
     */
    public static void notNull(Object object, String code, String[] param) {
        if (object == null) {
            throw new BusinessException(code, param);
        }
    }
    
    /**
     * Title：isTrue <br>
     * Description：为true继续往下走，为false抛出异常 <br>
     * author：夏阳 <br>
     * date：2020年4月22日 下午4:59:29 <br>
     * @param expression 需要判断的表达式
     * @param code message的异常码<br>
     */
    public static void isTrue(boolean expression, String code) {
        if (!expression) {
            throw new BusinessException(code);
        }
    }
    
    /**
     * Title：isEmpty <br>
     * Description：为空继续往下走，不为空抛出异常 <br>
     * author：夏阳 <br>
     * date：2020年4月22日 下午5:00:30 <br>
     * @param text 需要判空的字符串
     * @param code message的异常码<br>
     */
    public static void isEmpty(String text, String code) {
        if (StringUtils.isNotEmpty(text)) {
            throw new BusinessException(code);
        }
    }
    
    /**
     * Title：isEmpty <br>
     * Description：为空继续往下走，不为空抛出异常 <br>
     * author：夏阳 <br>
     * date：2020年4月22日 下午5:01:22 <br>
     * @param text 需要判空的字符串
     * @param code message的异常码
     * @param param 需要替换的参数<br>
     */
    public static void isEmpty(String text, String code, String[] param) {
        if (StringUtils.isNotEmpty(text)) {
            throw new BusinessException(code, param);
        }
    }
    
    /**
     * Title：isNotEmpty <br>
     * Description：不为空继续往下走，为空抛出异常 <br>
     * author：夏阳 <br>
     * date：2020年4月22日 下午5:01:22 <br>
     * @param text 需要判空的字符串
     * @param code message的异常码
     * @param param 需要替换的参数<br>
     */
    public static void isNotEmpty(String text, String code) {
        if (StringUtils.isEmpty(text)) {
            throw new BusinessException(code);
        }
    }
    
    /**
     * Title：isNotEmpty <br>
     * Description：不为空继续往下走，为空抛出异常 <br>
     * author：夏阳 <br>
     * date：2020年4月22日 下午5:01:22 <br>
     * @param text 需要判空的字符串
     * @param code message的异常码
     * @param param 需要替换的参数<br>
     */
    public static void isNotEmpty(String text, String code, String[] param) {
        if (StringUtils.isEmpty(text)) {
            throw new BusinessException(code, param);
        }
    }
}
