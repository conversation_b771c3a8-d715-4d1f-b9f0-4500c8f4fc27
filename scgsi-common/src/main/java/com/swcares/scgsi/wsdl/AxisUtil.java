package com.swcares.scgsi.wsdl;

import java.rmi.RemoteException;
import java.util.Map;
import javax.xml.rpc.ServiceException;
import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.commons.lang3.ObjectUtils;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

/**
 * ClassName：com.swcares.scgsi.wsdl.AxisUtil <br>
 * Description：基于axis访问webservice接口 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月5日 下午4:03:10 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class AxisUtil {

    String test =
            "<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?><root><pageData start=\\\"0\\\" limit=\\\"15\\\" total=\\\"450\\\"><record><field name=\\\"tuIdc\\\" value=\\\"110101198001044587\\\"/><field name=\\\"tuCname\\\" value=\\\"李悦欣\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"11010519830724451X\\\"/><field name=\\\"tuCname\\\" value=\\\"刘国正\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"110222198802244834\\\"/><field name=\\\"tuCname\\\" value=\\\"高远3\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"110228199110115444\\\"/><field name=\\\"tuCname\\\" value=\\\"段新宇\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"120101196806212544\\\"/><field name=\\\"tuCname\\\" value=\\\"赵秀文\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"120107197907053944\\\"/><field name=\\\"tuCname\\\" value=\\\"陈欣2\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"120112199103130449\\\"/><field name=\\\"tuCname\\\" value=\\\"韩旭\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"120222199002174416\\\"/><field name=\\\"tuCname\\\" value=\\\"梁爽\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"120223199407124421\\\"/><field name=\\\"tuCname\\\" value=\\\"林珊珊\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"123445677990098876\\\"/><field name=\\\"tuCname\\\" value=\\\"777\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"130102198907081544\\\"/><field name=\\\"tuCname\\\" value=\\\"郭璞婕\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"130105198305032445\\\"/><field name=\\\"tuCname\\\" value=\\\"胡晓倩\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"130529198909244214\\\"/><field name=\\\"tuCname\\\" value=\\\"吴立龙\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"130802199012020444\\\"/><field name=\\\"tuCname\\\" value=\\\"马青原\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"130922198311134413\\\"/><field name=\\\"tuCname\\\" value=\\\"陈文林\\\"/></record></pageData></root>";

    /** 需要访问webservices的接口名 */
    public static final String INTERFACE_NAME = "INTERFACE_NAME";

    /** 请求xml报文 */
    public static final String REQ_XML_CONTENT = "REQ_XML_CONTENT";

    /**
     * Title：doUrl <br>
     * Description：调用远程webservice方法<br>
     * author：夏阳 <br>
     * date：2020年3月5日 下午4:33:07 <br>
     * @param map 包含两个内容，1 请求接口名  2 完整的请求xml
     * @param url 接口地址
     * @return <br>
     */
    public static Object doUrl(Map<String, String> map, String url) {
        return doUrl(map, url, null);
    }

    /**
     * Title：doUrl <br>
     * Description：调用远程webservice方法<br>
     * author：夏阳 <br>
     * date：2020年3月5日 下午4:33:07 <br>
     * @param map 包含两个内容，1 请求接口名  2 完整的请求xml  3.认证的鉴权信息
     * @param url 接口地址
     * @param userWsdl 鉴权对象
     * @return <br>
     */
    public static Object doUrl(Map<String, String> map, String url, UserWsdl userWsdl) {
        Service service = new Service();
        Call call = null;
        try {
            call = (Call) service.createCall();
        } catch (ServiceException e) {
            log.error("调用webservice接口，创建call对象异常，请求地址{}，异常堆栈\n{}", url, e);
            return null;
        }
        call.setTargetEndpointAddress(url);

        if (ObjectUtils.allNotNull(userWsdl)) {
            call.setUsername(userWsdl.getUserName());
            call.setPassword(userWsdl.getPassword());
        }

        call.setOperationName(map.get(INTERFACE_NAME));// WSDL里面描述的接口名称

        call.addParameter("userName", org.apache.axis.encoding.XMLType.XSD_DATE,
                javax.xml.rpc.ParameterMode.IN);// 接口的参数

        call.setReturnType(org.apache.axis.encoding.XMLType.XSD_STRING);// 设置返回类型，后期需要其他类型可以扩展到map里面
        Object returnStr = null;
        try {
            returnStr = call.invoke(new Object[] {map.get(REQ_XML_CONTENT)});
        } catch (RemoteException e) {
            log.error("调用远程webservice接口异常，请求报文，异常堆栈\n{}", map.get(REQ_XML_CONTENT), e);
        }
        return returnStr;
    }

    /**
     * Title：getJsonNode <br>
     * Description：用于员工信息获取目标节点的方法，递归调用获取节点【方法有局限性，主要是Json转换的普适性，使用前要多测试】 ，否则会造成死循环！！！<br>
     * author：夏阳 <br>
     * date：2020年3月9日 下午1:29:48 <br>
     * @param jsonObject
     * @param node
     * @return
     * @throws ClassCastException <br>
     */
    public static JSONObject getJsonNode(JSONObject jsonObject, String node)
            throws ClassCastException {
        try {
            for (String tempNode : jsonObject.keySet()) {
                if (node.equalsIgnoreCase(tempNode)) {
                    return jsonObject.getJSONObject(tempNode);
                } else {
                    jsonObject = jsonObject.getJSONObject(tempNode);
                }
            }
        } catch (Exception e) {
            log.error("获取目标节点失败，为空,jsonObject:{}, targetNode{}", jsonObject, node);
            return null;
        }
        return getJsonNode(jsonObject, node);
    }

}
