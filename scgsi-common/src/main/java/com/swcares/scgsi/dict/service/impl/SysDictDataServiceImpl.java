package com.swcares.scgsi.dict.service.impl;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.dict.dao.SysDictDataDao;
import com.swcares.scgsi.dict.dao.impl.DictDataServiceDaoImpl;
import com.swcares.scgsi.dict.entity.SysDictDataInfo;
import com.swcares.scgsi.dict.service.SysDictDataService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.dict.service.impl <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月20日 22:08 <br>
 * @version v1.0 <br>
 */
@Service
public class SysDictDataServiceImpl implements SysDictDataService {
    @Resource
    private SysDictDataDao dictDataDao;
    @Resource
    private DictDataServiceDaoImpl dictDataServiceDao;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SysDictDataInfo sysDictDataInfo) {
        String id=sysDictDataInfo.getId();
        if(StringUtils.isNotBlank(id)){
            dictDataServiceDao.deleteDictInfo(id);
        }
        sysDictDataInfo.setStatus("0");
        dictDataDao.save(sysDictDataInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(String ids, String status) {
        dictDataServiceDao.updateStatus(ids,status);
    }

    @Override
    public List<Map<String, String>> getSelectDict(String type) {
        return dictDataServiceDao.getSelectDict(type);
    }

    @Override
    public QueryResults getDictPage(String keySearch, int currentPage, int numPerPage) {
        return dictDataServiceDao.getDictPage(keySearch,currentPage,numPerPage);
    }
}