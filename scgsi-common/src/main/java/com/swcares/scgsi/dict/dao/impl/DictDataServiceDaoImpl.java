package com.swcares.scgsi.dict.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.dict.vo.SysDictDataInfoVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.dict.dao.impl <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月20日 22:12 <br>
 * @version v1.0 <br>
 */
@Repository
public class DictDataServiceDaoImpl {
    @Resource
    private BaseDAO baseDAO;
    /**
     * Title：updateStatus <br>
     * Description： 激活禁用数据字典<br>
     * author：王建文 <br>
     * date：2020-3-20 22:10 <br>
     * @param id  主键id
     * @param status 状态0激活1禁用
     * @return
     */
    public void updateStatus(String ids,String status){
        String [] ids1=ids.split(",");
        for(String id:ids1){
            StringBuffer sql = new StringBuffer();
            sql.append(" UPDATE SYS_DICT SET ");
            sql.append(" DATA_STATUS=? ");
            sql.append(" where ID=? ");
            baseDAO.batchUpdate(sql.toString(), status,id);
        }
    }
    /**
     * Title：getSelectDict <br>
     * Description：根据数据类型获取可用下拉选项<br>
     * author：王建文 <br>
     * date：2020-3-20 22:16 <br>
     * @param  type 数据类型
     * @return
     */
    public List<Map<String,String>> getSelectDict(String type){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("type", type);
        sql.append(" SELECT DATA_CODE AS key,DATA_VALUE AS value ");
        sql.append(" FROM SYS_DICT ");
        sql.append(" WHERE DATA_STATUS='0' ");
        sql.append(" AND DATA_TYPE=:type ");
        return (List<Map<String,String>>) baseDAO.findBySQL_comm(sql.toString(),paramsMap,null);
    }
    public QueryResults getDictPage(String keySearch,int currentPage,int numPerPage){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT ID AS id,DATA_TYPE AS type, ");
        sql.append(" DATA_CODE AS key,DATA_VALUE AS value, ");
        sql.append(" DATA_STATUS AS status,DATA_REMARK AS remark ");
        sql.append(" FROM SYS_DICT ");
        sql.append(" WHERE 1=1   ");
        if(StringUtils.isNotBlank(keySearch)){
            if(StringUtils.isNotBlank(keySearch)){
                paramsMap.put("keySearch", "%" + keySearch + "%");
                sql.append(" AND (DATA_TYPE LIKE:keySearch OR DATA_CODE LIKE:keySearch OR DATA_VALUE LIKE:keySearch) ");
            }
        }
        return baseDAO.findBySQLPage_comm(
                sql.toString(),
                currentPage,
                numPerPage,
                paramsMap,
                SysDictDataInfoVo.class);

    }
    /**
     * Title：deleteDictInfo <br>
     * Description： 删除数据字典<br>
     * author：王建文 <br>
     * date：2020-4-10 13:43 <br>
     * @param  id 主键ID
     * @return
     */
    public void deleteDictInfo(String id){
        StringBuffer sql = new StringBuffer();
        sql.append(" DELETE FROM SYS_DICT ");
        sql.append(" where ID=? ");
        baseDAO.batchUpdate(sql.toString(), id);
    }
}