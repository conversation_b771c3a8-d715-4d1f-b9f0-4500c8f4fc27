package com.swcares.scgsi.dict.dao;

import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.dict.entity.SysDictDataInfo;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.dict.dao <br>
 * Description：数据字典dao <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月20日 22:04 <br>
 * @version v1.0 <br>
 */
public interface SysDictDataDao extends BaseJpaDao<SysDictDataInfo,String>{


    /**
     * Title：findByTypeAndStatusAndValueLike <br>
     * Description： 根据类型、value 查询 code<br>
     * author：傅欣荣 <br>
     * date：2020/8/27 10:48 <br>
     * @param
     * @return
     */
    SysDictDataInfo findByTypeAndStatusAndValueLike(String type,String status,String value);

    List<SysDictDataInfo> findByTypeAndStatus(String type,String status);
}
