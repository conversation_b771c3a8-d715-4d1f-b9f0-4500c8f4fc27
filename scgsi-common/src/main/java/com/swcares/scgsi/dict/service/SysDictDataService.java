package com.swcares.scgsi.dict.service;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.dict.entity.SysDictDataInfo;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.dict.service <br>
 * Description：数据字典 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月20日 22:06 <br>
 * @version v1.0 <br>
 */
public interface SysDictDataService {
    /**
     * Title：save <br>
     * Description： 保存数据字典<br>
     * author：王建文 <br>
     * date：2020-3-20 22:07 <br>
     * @param  sysDictDataInfo 参数接收
     * @return
     */
    public void save(SysDictDataInfo sysDictDataInfo);

    /**
     * Title：updateStatus <br>
     * Description： 激活禁用数据字典<br>
     * author：王建文 <br>
     * date：2020-3-20 22:10 <br>
     * @param id  主键id
     * @param status 状态0激活1禁用
     * @return
     */
    public void updateStatus(String ids, String status);
    /**
     * Title：getSelectDict <br>
     * Description：根据数据类型获取可用下拉选项<br>
     * author：王建文 <br>
     * date：2020-3-20 22:16 <br>
     * @param  type 数据类型
     * @return
     */
    public List<Map<String,String>> getSelectDict(String type);
    /**
     * Title：getDictPage <br>
     * Description： TODO(用一句话描述该方法做什么)<br>
     * author：王建文 <br>
     * date：2020-4-9 15:37 <br>
     * @param  keySearch 关键词模糊查询
     * @param  currentPage 当前页默认1
     * @param  numPerPage 每页显示条数
     * @return
     */
    public QueryResults getDictPage(String keySearch, int currentPage, int numPerPage);
}
