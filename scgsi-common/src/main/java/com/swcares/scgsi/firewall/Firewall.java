package com.swcares.scgsi.firewall;

import org.springframework.stereotype.Component;

/**
 * ClassName：com.swcares.scgsi.firewall.Firewall <br>
 * Description：防火墙，实施访问频率控制接口 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年6月23日 上午10:58:10 <br>
 * @version v1.0 <br>
 */
@Component
public interface Firewall {

    /**
     * Title：addAccessRecord <br>
     * Description：添加记录 <br>
     * author：王磊 <br>
     * date：2020年6月23日 上午11:02:57 <br>
     * @param identification 标识支持ip和userid或其他访问唯一标识 <br>
     */
    public void addAccessRecord(FirewallParam param);

    /**
     * Title：isFrequentlyAccess <br>
     * Description：判断是否高频访问用户 <br>
     * author：王磊 <br>
     * date：2020年6月23日 上午11:03:17 <br>
     * @param identification 唯一标识
     */
    public void isFrequentlyAccess(FirewallParam param);

    /**
     * 获取用户访问次数
     * @param identification String
     * @return int 返回用户访问次数
     */
    public int getAccessCount(String identification);


    public int order();
    public FirewallTypeEnum getFirewallTyp();

    default void execute(FirewallParam param){
        if(!param.getType().equals(getFirewallTyp()))return;
        isFrequentlyAccess(param);
        addAccessRecord(param);
    }
}
