package com.swcares.scgsi.firewall.impl;

import cn.hutool.core.util.ObjectUtil;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.firewall.Firewall;
import com.swcares.scgsi.firewall.FirewallParam;
import com.swcares.scgsi.firewall.FirewallTypeEnum;
import com.swcares.scgsi.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 *根据IP来限制每分钟访问次数
 */
@Slf4j
@Component
public class FirewallMinuteIpImpl implements Firewall {


    // 访问间隔时间
    @Value("${firewall.minuteIp.frequentlyAccessDuration}")
    private String frequentlyAccessDuration;
    //限流时间
    @Value("${firewall.minuteIp.astrictTime}")
    private Integer astrictTime;
    // 访问时间内的次数阀值
    @Value("${firewall.minuteIp.frequentlyAccessThreshhold}")
    private String frequentlyAccessThreshhold;


    @Autowired
    private RedisService redisService;

    @Override
    public void addAccessRecord(FirewallParam param) {
        Integer count = (Integer) redisService.get(param.getType().getKey() + param.getParam());
        if (ObjectUtil.isNotEmpty(count)) {
            if (count < new Integer(frequentlyAccessThreshhold)) {
                count++;
                Long time = 0L;
                if (redisService.getExpire(param.getType().getKey() + param.getParam()) > 0) {
                    time = redisService.getExpire(param.getType().getKey() + param.getParam());
                } else {
                    time = new Long(frequentlyAccessDuration);
                }
                redisService.set(param.getType().getKey() + param.getParam(), count, time);
            } else {
                redisService.set(param.getType().getRestrictKey() + param.getParam(), count, astrictTime);
                log.info("用户IP{}访问频率过大每分钟超过{}次,限流{}秒",param.getParam(),frequentlyAccessThreshhold,astrictTime);
                throw new BusinessException(MessageCode.VISIT_EXCEED_THRESHOLD.getCode());
            }
        } else {
            count = 1;
            redisService.set(param.getType().getKey() + param.getParam(), count, new Long(
                    frequentlyAccessDuration));
        }
    }

    @Override
    public void isFrequentlyAccess(FirewallParam param) throws BusinessException {
        Integer count = (Integer) redisService.get(param.getType().getRestrictKey() + param.getParam());
        if (ObjectUtil.isNotEmpty(count)) {
            Long expire = redisService.getExpire(param.getType().getRestrictKey() + param.getParam());
            log.info("用户IP{}访问频率过大每分钟超过{}次,限流剩余时间{}秒",param.getParam(),frequentlyAccessThreshhold,expire);
            throw new BusinessException(MessageCode.VISIT_EXCEED_THRESHOLD.getCode());
        }
    }

    @Override
    public int getAccessCount(String identification) {
        return (Integer) redisService.get( getFirewallTyp().getKey() + identification);
    }

    @Override
    public int order() {
        return 30;
    }

    @Override
    public FirewallTypeEnum getFirewallTyp() {
        return FirewallTypeEnum.FIREWALL_TYPE_MINUTE_IP;
    }
}
