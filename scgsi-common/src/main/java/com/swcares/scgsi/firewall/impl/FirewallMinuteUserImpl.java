package com.swcares.scgsi.firewall.impl;

import com.swcares.scgsi.firewall.FirewallParam;
import com.swcares.scgsi.firewall.FirewallTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import cn.hutool.core.util.ObjectUtil;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.firewall.Firewall;
import com.swcares.scgsi.redis.RedisService;

/**
 * ClassName：com.swcares.scgsi.firewall.impl.FirewallForIp <br>
 * Description：根据用户IP来限制每分钟访问次数 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年6月23日 上午11:01:11 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Component
public class FirewallMinuteUserImpl  implements Firewall {
    // 访问间隔时间
    @Value("${firewall.minuteUser.frequentlyAccessDuration}")
    private String frequentlyAccessDuration;
    //限流时间
    @Value("${firewall.minuteUser.astrictTime}")
    private Integer astrictTime;
    // 访问时间内的次数阀值
    @Value("${firewall.minuteUser.frequentlyAccessThreshhold}")
    private String frequentlyAccessThreshhold;


    @Autowired
    private RedisService redisService;

    @Override
    public void addAccessRecord(FirewallParam param) {
        Integer count = (Integer) redisService.get(param.getType().getKey() + param.getParam());
        if (ObjectUtil.isNotEmpty(count)) {
            if (count < new Integer(frequentlyAccessThreshhold)) {
                count++;
                Long time = 0L;
                if (redisService.getExpire(param.getType().getKey() + param.getParam()) > 0) {
                    time = redisService.getExpire(param.getType().getKey() + param.getParam());
                } else {
                    time = new Long(frequentlyAccessDuration);
                }
                redisService.set(param.getType().getKey() + param.getParam(), count, time);
            } else {
                redisService.set(param.getType().getRestrictKey() + param.getParam(), count, astrictTime);
                log.info("用户ID{}访问频率过大每分钟超过{}次,限流{}秒",param.getParam(),frequentlyAccessThreshhold,astrictTime);
                throw new BusinessException(MessageCode.VISIT_EXCEED_THRESHOLD.getCode());
            }
        } else {
            count = 1;
            redisService.set(param.getType().getKey() + param.getParam(), count, new Long(
                    frequentlyAccessDuration));
        }
    }

    @Override
    public void isFrequentlyAccess(FirewallParam param) throws BusinessException {
        Integer count = (Integer) redisService.get(param.getType().getRestrictKey() + param.getParam());
        if (ObjectUtil.isNotEmpty(count)) {
            Long expire = redisService.getExpire(param.getType().getKey() + param.getParam());
            log.info("用户ID{}访问频率过大每分钟超过{}次,限流剩余时间{}秒",param.getParam(),frequentlyAccessThreshhold,expire);
            throw new BusinessException(MessageCode.VISIT_EXCEED_THRESHOLD.getCode());
        }
    }

    @Override
    public int getAccessCount(String identification) {
        return (Integer) redisService.get( getFirewallTyp().getKey() + identification);
    }

    @Override
    public int order() {
        return 50;
    }

    @Override
    public FirewallTypeEnum getFirewallTyp() {
        return FirewallTypeEnum.FIREWALL_TYPE_MINUTE_UASER;
    }
}
