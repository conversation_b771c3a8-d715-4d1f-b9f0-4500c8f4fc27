package com.swcares.scgsi.firewall;

public enum FirewallTypeEnum {
    FIREWALL_TYPE_MINUTE_UASER("1","用户id一分钟超频访问限制","FIREWALL_TYPE_MINUTE_UASER:","FIREWALL_TYPE_MINUTE_UASER_RESTRICT:"),
    FIREWALL_TYPE_MINUTE_OPENID("2","旅客openid一分钟超频访问限制","FIREWALL_TYPE_MINUTE_OPENID:","FIREWALL_TYPE_MINUTE_OPENID_RESTRICT:"),
    FIREWALL_TYPE_MINUTE_IP("3","用户IP一分钟超频访问限制","FIREWALL_TYPE_MINUTE_IP:","FIREWALL_TYPE_MINUTE_IP_RESTRICT:"),
    FIREWALL_TYPE_DAY_IP("4","用户IP一天超频访问限制","FIREWALL_TYPE_DAY_IP:","FIREWALL_TYPE_DAY_IP_RESTRICT:"),
    FIREWALL_TYPE_DAY_OPENID("5","旅客openid一天超频访问限制","FIREWALL_TYPE_DAY_OPENID:","FIREWALL_TYPE_DAY_OPENID_RESTRICT:");

    FirewallTypeEnum(String code, String explain, String key,String restrictKey) {
        this.code = code;
        this.explain = explain;
        this.key = key;
        this.restrictKey = restrictKey;
    }

    private String code;//限制类型编码
    private String explain;//说明
    private String key;//统计记录KEY
    private String restrictKey;//限制间隔KEY

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getExplain() {
        return explain;
    }

    public void setExplain(String explain) {
        this.explain = explain;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getRestrictKey() {
        return restrictKey;
    }

    public void setRestrictKey(String restrictKey) {
        this.restrictKey = restrictKey;
    }
}
