package com.swcares.scgsi.fileuploadanddownload;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;
import cn.hutool.extra.ftp.Ftp;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.file.FileUtils;
import com.swcares.scgsi.util.DateUtils;

/**
 * ClassName：com.swcares.scgsi.fileUploadAndDownload.UploadAndDownload <br>
 * Description：文件上传下载工具类<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月13日 上午16:13:55 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Component
@Getter
public class UploadAndDownload {
    @Value("${ftp.ip}")
    private String ftp_address; // ip

    @Value("${ftp.port}")
    private Integer ftp_port; // port

    @Value("${ftp.username}")
    private String ftp_username; // sftp username

    @Value("${ftp.password}")
    private String ftp_password; // sftp password

    @Value("${upload-file.remote-path}")
    // 正式目录
    private String remotePath;
    @Value("${upload-file.remote-path-temp}")
    // 临时目录
    private String remotePathTemp;

    @Value("${upload-file.file-suffix}")
    // 文件后缀
    private String fileSuffixs;


    @Value("${upload-file.file-max-size}")
    private String fileSize;

    /**
     * Title：ftpUpload <br>
     * Description：FTP文件上传 <br>
     * author：王磊 <br>
     * date：2020年2月14日 上午11:37:43 <br>
     * @param file 文件类
     * @param remote 保存文件名
     * @param remotePath 文件保存ftp的路径
     * @return 
     * @throws Exception <br>
     */
    public String ftpUpload(MultipartFile file, String remote,String currentUser) throws Exception {
        String path = null;
        Ftp ftp = null;
        ByteArrayInputStream swapStream = null;
        ByteArrayOutputStream baos = null;
        try {
            checkFileSuffix(file.getOriginalFilename());
            log.info("文件SERVICE上传开始:用户{},文件名{}!!!!", currentUser, remote);
            ftp = new Ftp(ftp_address, ftp_port, ftp_username, ftp_password);
            // path =
            // ftp.pwd() + remotePathTemp + "/"
            // + DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDD);
            path = remotePathTemp + "/" + DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDD);
            if (!ftp.exist(path)) {
                ftp.mkDirs(path);
            }
            log.info("文件SERVICE上传FTP:用户{},文件名{},文件大小{},上传路径{}!!!!", currentUser, remote,
                    file.getSize(), path);
            // 判断文件大小
            if (file.getSize() > new Integer(fileSize)) {
                Integer fileSizeM = new Integer(fileSize) / 1024 / 1024;
                log.error("文件SERVICE上传FTP:用户{},文件名{},文件大小{},上传文件超过设置大小!!!!", currentUser, remote,
                        file.getSize());
                throw new BusinessException(MessageCode.FILE_SIZE_ERROR.getCode(),
                        new String[] {fileSizeM.toString()});
            }
            boolean bool = ftp.upload(path, remote, file.getInputStream());
            if (!bool) {
                log.error("文件SERVICE上传FTP:用户{},文件名{},文件大小{},FTP上传文件出错!!!!", currentUser, remote,
                        file.getSize());
                throw new BusinessException(MessageCode.FILE_UPLOAD_ERROR.getCode());
            }
            // 文件大小
            int fileSize = 0;
            // 回读文件判断是否上传成功
            baos = new ByteArrayOutputStream();
            ftp.download(path, remote, baos);
            // 转换为输入流
            swapStream = new ByteArrayInputStream(baos.toByteArray());
            // 获取上传文件的大小
            fileSize = swapStream.available();
            if (fileSize <= 0) {
                log.error("文件SERVICE上传FTP:用户{},文件名{},文件大小{},FTP上传文件出错!!!!", currentUser, remote,
                        file.getSize());
                throw new BusinessException(MessageCode.FILE_UPLOAD_ERROR.getCode());
            }
        } catch (Exception e) {
            log.error("文件SERVICE上传FTP:用户{},文件名{},文件大小{},上传异常{}!!!!", currentUser, remote,
                    file.getSize(), e);
            throw e;
        } finally {
            if (ftp != null) {
                ftp.close();
            }
            if (baos != null) {
                baos.close();
            }
            if (swapStream != null) {
                swapStream.close();
            }
        }
        log.info("文件SERVICE上传结束:用户{},文件名{}!!!!", currentUser, path + "/" + remote);
        return path + "/" + remote;
    }

    /**
     * Title：checkFileSuffix <br>
     * Description：判断文件后缀是否符合上传要求 <br>
     * author：王磊 <br>
     * date：2020年6月8日 上午9:58:30 <br>
     * @param originalFilename
     * @throws BusinessException <br>
     */
    private void checkFileSuffix(String originalFilename) throws BusinessException {
        String[] fileSuffixArray = fileSuffixs.split(",");
        String fileSuffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        boolean b = true;
        if (StringUtils.isEmpty(fileSuffixArray[0])) {// 如果是空代表所有类型则不验证
            return;
        }
        for (String f : fileSuffixArray) {
            if (f.equals(fileSuffix)) {
                b = false;
            }
        }
        if (b) {// 判断文件后缀名
            throw new BusinessException(MessageCode.FILE_SUFFIX_ERROR.getCode(),
                    new String[] {fileSuffixs});
        }
    }

    /**
     * Title：upload <br>
     * Description：文件上传 <br>
     * author：王磊 <br>
     * date：2020年2月14日 上午11:37:43 <br>
     * @param MultipartFile 文件类
     * @param remote 保存文件名
     * @throws Exception <br>
     */
    public Map<String, String> upload(MultipartFile file, String remote) throws Exception {
        HashMap<String, String> map = new HashMap<String, String>();// 返回map key为文件名,value为路径
        try {
            if (!file.isEmpty()) {
                String path =
                        createDateDirs(remotePathTemp) + "/"
                                + DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDD) + "/";
                String fullPath = path + remote;
                File dest = new File(fullPath);
                // 检测是否存在目录
                if (!dest.getParentFile().exists()) {
                    dest.getParentFile().mkdirs();// 新建文件夹
                }
                file.transferTo(dest);// 文件写入
                map.put("fileName", remote);
                // map.put("filePath", path);
            }
        } catch (IllegalStateException e) {
            log.error("文件上传出错:{}", e);
            throw e;
        } catch (IOException e) {
            log.error("文件上传出错:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("文件上传出错:{}", e);
            throw e;
        }
        return map;
    }

    /**
     * Title：ftpDownload <br>
     * Description：FTP文件下载<br>
     * author：王磊 <br>
     * date：2020年2月14日 上午11:39:49 <br>
     * @param outputStream 输出流
     * @param remote 保存文件名
     * @param remotePath 文件保存ftp的路径
     * @throws Exception <br>
     */
    public void ftpDownload(OutputStream outputStream, String remote, String remotePath)
            throws Exception {
        Ftp ftp = null;
        try {
            ftp = new Ftp(ftp_address, ftp_port, ftp_username, ftp_password);
            if (null != remotePath || !("".equals(remotePath))) {
                ftp.download(remotePath, remote, outputStream);
            }
        } catch (Exception e) {
            log.error("FTP文件下载出错:{}", e);
            throw new BusinessException(MessageCode.FILE_DOENLOAD_ERROR.getCode());
        } finally {
            ftp.close();
        }
    }

    /**
     * Title：download <br>
     * Description：本地文件下载<br>
     * author：王磊 <br>
     * date：2020年2月14日 上午11:39:49 <br>
     * @param outputStream 输出流
     * @param remote 保存文件名
     * @param remotePath 文件保存的路径
     * @throws Exception <br>
     */
    public void download(OutputStream outputStream, String remote, String remotePath)
            throws Exception {
        BufferedInputStream inputStream = null;
        try {
            if (!isEmpty(remotePath)) {
                // 设置文件路径
                File file = new File(remotePath, remote);
                // 如果文件名存在，则进行下载
                if (file.exists()) {
                    inputStream = new BufferedInputStream(new FileInputStream(file));
                    byte[] buffer = new byte[1024];
                    int len = 0;
                    while ((len = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, len);
                    }
                } else {
                    throw new BusinessException(MessageCode.FILE_NOT_EXISTS.getCode());
                }
            }
        } catch (Exception e) {
            log.error("文件上下载出错:{}", e);
            throw e;
        } finally {
            close(inputStream);
            close(outputStream);
        }
    }

    /**
     * Title：isEmpty <br>
     * Description：判断文件名和路径是否为空<br>
     * author：王磊 <br>
     * date：2020年2月14日 上午11:40:14 <br>
     * @param str
     * @return <br>
     */
    public static boolean isEmpty(String str) {
        if (null == str || "".equals(str)) {
            return true;
        }
        return false;
    }

    /**
     * Title：close <br>
     * Description：关闭流 <br>
     * author：王磊 <br>
     * date：2020年2月14日 上午11:40:34 <br>
     * @param outputStreams <br>
     * @throws IOException 
     */
    public static void close(OutputStream... outputStreams) throws IOException {
        for (OutputStream outputStream : outputStreams) {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭输出流出错:{}", e);
                    throw e;
                }
            }
        }
    }

    /**
     * Title：close <br>
     * Description：关闭流 <br>
     * author：王磊 <br>
     * date：2020年2月14日 上午11:40:34 <br>
     * @param outputStreams <br>
     * @throws IOException 
     */
    public static void close(InputStream... inputStreams) throws IOException {
        for (InputStream inputStream : inputStreams) {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流出错:{}", e);
                    throw e;
                }
            }
        }
    }

    /**
     * Title：copy <br>
     * Description：把文件从临时目录拷贝到正式目录 <br>
     * author：王磊 <br>
     * date：2020年3月27日 上午11:21:50 <br>
     * @param srcFileName 待复制的文件名
     * @param descFileName 目标文件名
     * @return <br>
     */
    public boolean copy(String srcFileName, String descFileName) {
        return FileUtils.copyFile(srcFileName, descFileName);
    }

    /**
     * Title：createDateDirs <br>
     * Description：生成当天的目录并拼接 <br>
     * author：王磊 <br>
     * date：2020年3月27日 下午1:49:42 <br>
     * @param remotePath
     * @return <br>
     */
    public String createDateDirs(String remotePath) {
        // 获取跟目录---与jar包同级目录的upload目录下指定的子目录subdirectory
        File upload = null;
        try {
            // 本地测试时获取到的是"工程目录/target/classess/upload/subdirectory
            File path = new File(ResourceUtils.getURL("classpath:").getPath());
            if (!path.exists()) {
                path = new File("");
            }
            upload = new File(path.getAbsolutePath(), remotePath);
            // 如果不存在则创建目录
            if (!upload.exists()) {
                upload.mkdirs();
            }
            String realPath = upload + "/";
            return realPath;
        } catch (FileNotFoundException e) {
            throw new RuntimeException("获取服务器路径发生错误！");
        }
    }

    /**
     * Title：deleteTempOnTime <br>
     * Description：TODO(定时清理上传的临时文件夹) <br>
     * author：王磊 <br>
     * date：2020年3月27日 下午2:03:14 <br>
     * @param remotePath <br>
     * @throws IOException 
     */
    public void deleteTempOnTime() throws IOException {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, -2);// 删除两天前的临时文件夹
        // String path = createDateDirs(remotePathTemp);
        Ftp ftp = null;
        try {
            ftp = new Ftp(ftp_address, ftp_port, ftp_username, ftp_password);
            String path = ftp.pwd() + remotePathTemp;
            path = path + "/" + DateUtils.parseDateToStr(calendar.getTime(), DateUtils.YYYYMMDD);
            ftp.delDir(path);
        } catch (Exception e) {
            log.error("FTP回显出错:{}", e);
            throw new BusinessException(MessageCode.FILE_NOT_EXISTS.getCode());
        } finally {
            ftp.close();
        }
        // deleteAllImg(path);
    }

    /**
     * Title：copyFileUsingFileStreams <br>
     * Description： 文件copy,临时目录存放到正式目录<br>
     * author：王建文 <br>
     * date：2020-4-8 13:33 <br>
     * @param  source 临时目录 dest 正式目录
     * @return
     */
    public void copyFileUsingFileStreams(File source, File dest) throws IOException {
        InputStream input = null;
        OutputStream output = null;
        try {
            input = new FileInputStream(source);
            output = new FileOutputStream(dest);
            byte[] buf = new byte[1024];
            int bytesRead;
            while ((bytesRead = input.read(buf)) > 0) {
                output.write(buf, 0, bytesRead);
            }
        } finally {
            input.close();
            output.close();
        }
    }

    /**
     * Title：saveImg <br>
     * Description： 保存图片到正式目录<br>
     * author：王建文 <br>
     * date：2020-4-8 13:32 <br>
     * @param imgUrl 图片名称
     * @return
     */
    public String saveImg(String imgUrl) throws Exception {
        Ftp ftp = null;
        String officialPath = null;
        try {
            ftp = new Ftp(ftp_address, ftp_port, ftp_username, ftp_password);
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            String remote = imgUrl.substring(imgUrl.lastIndexOf("/") + 1, imgUrl.length());
            String tempPath = imgUrl.substring(0, imgUrl.lastIndexOf("/"));
            String officialRemotePath = ftp.pwd() + remotePath;
            officialPath = officialRemotePath + "/" + remote;
            ftp.download(tempPath, remote, out);
            if (!ftp.exist(officialRemotePath)) {
                ftp.mkDirs(officialRemotePath);
            }
            ftp.upload(officialRemotePath, remote, new ByteArrayInputStream(out.toByteArray()));
            ftp.delFile(tempPath + "/" + remote);
        } catch (Exception e) {
            log.error("FTP保存正式目录出错:{}", e);
        } finally {
            ftp.close();
        }
        return officialPath;
    }

    /**
     *
     * Title：deleteAllImg <br>
     * Description：删除指定目录所有图片 <br>
     * author：王建文 <br>
     * date：2019年7月17日 下午2:03:11 <br>
     * @param directoryPath 指定目录 <br>
     */
    public static void deleteAllImg(String directoryPath) {
        File dir = new File(directoryPath);
        File[] files = dir.listFiles();
        for (File file : files) {
            if (file.isDirectory()) {
                deleteAllImg(directoryPath);
            } else {
                file.delete();
            }
        }
    }

}
