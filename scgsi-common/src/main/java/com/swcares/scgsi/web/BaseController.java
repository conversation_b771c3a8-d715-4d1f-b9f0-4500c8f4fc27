package com.swcares.scgsi.web;

import com.swcares.scgsi.base.Pager;
import com.swcares.scgsi.base.QueryResults;

import java.util.HashMap;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.modules.controller <br>
 * Description：处理前端分页 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月25日 13:57 <br>
 * @version v1.0 <br>
 */
public class BaseController {
    /**
     * Title：returnPageInfo <br>
     * Description： 处理前端分页<br>
     * author：王建文 <br>
     * date：2020-3-25 14:02 <br>
     * @param  queryResults
     * @return
     */
    public Map<String,Object> returnPageInfo(QueryResults queryResults){
        Map<String,Object> map=new HashMap<>();
        map.put("data",queryResults.getList());
        Pager pager=queryResults.getPagination();
        map.put("total",pager.getTotal());
        map.put("current",pager.getCurrent());
        map.put("pageSize",pager.getPageSize());
        map.put("success",true);
        return map;
    }
}