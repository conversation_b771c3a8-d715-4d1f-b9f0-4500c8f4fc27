package com.swcares.scgsi.encryption;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 
 * ClassName：com.swcares.scgsi.encryption.EncryptionClassz <br>
 * Description：作用于需要加密的类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年10月16日 下午3:41:45 <br>
 * @version v1.0 <br>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
public @interface EncryptionClassz {

}
