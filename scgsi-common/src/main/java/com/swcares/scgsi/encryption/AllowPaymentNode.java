package com.swcares.scgsi.encryption;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * ClassName：com.swcares.scgsi.aop.AllowPaymentNode <br>
 * Description：用于支付定时任务的注解，加上该注解的方法，会进行ip比较，一致才能跑定时任务<br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月10日 下午4:53:22 <br>
 * @version v1.0 <br>
 */
@Documented
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface AllowPaymentNode {

}
