package com.swcares.scgsi.util;

/**
 * ClassName：com.swcares.scgsi.util.PinyinUtilTest <br>
 * Description：拼音测试类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月10日 下午1:56:50 <br>
 * @version v1.0 <br>
 */
//@RunWith(SpringRunner.class)
public class PinyinUtilTest {
    
//    @Test
    public void getChineseCharacters() {
        String word = "成都民航西南凯亚";
        System.out.println(PinyinUtil.getChineseCharacters(word));
        System.out.println(PinyinUtil.getChineseCharactersOfAbbreviation(word));
    }
}
