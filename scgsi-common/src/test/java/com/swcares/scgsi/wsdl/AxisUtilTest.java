package com.swcares.scgsi.wsdl;

import java.util.HashMap;
import java.util.Map;

//@RunWith(SpringRunner.class)
public class AxisUtilTest {
    
    private String url = "http://172.16.1.206/hum/?wsdl";
    
    private String mockResXml = "<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?><root><pageData start=\\\"0\\\" limit=\\\"15\\\" total=\\\"450\\\"><record><field name=\\\"tuIdc\\\" value=\\\"110101198001044587\\\"/><field name=\\\"tuCname\\\" value=\\\"李悦欣\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"11010519830724451X\\\"/><field name=\\\"tuCname\\\" value=\\\"刘国正\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"110222198802244834\\\"/><field name=\\\"tuCname\\\" value=\\\"高远3\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"110228199110115444\\\"/><field name=\\\"tuCname\\\" value=\\\"段新宇\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"120101196806212544\\\"/><field name=\\\"tuCname\\\" value=\\\"赵秀文\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"120107197907053944\\\"/><field name=\\\"tuCname\\\" value=\\\"陈欣2\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"120112199103130449\\\"/><field name=\\\"tuCname\\\" value=\\\"韩旭\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"120222199002174416\\\"/><field name=\\\"tuCname\\\" value=\\\"梁爽\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"120223199407124421\\\"/><field name=\\\"tuCname\\\" value=\\\"林珊珊\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"123445677990098876\\\"/><field name=\\\"tuCname\\\" value=\\\"777\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"130102198907081544\\\"/><field name=\\\"tuCname\\\" value=\\\"郭璞婕\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"130105198305032445\\\"/><field name=\\\"tuCname\\\" value=\\\"胡晓倩\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"130529198909244214\\\"/><field name=\\\"tuCname\\\" value=\\\"吴立龙\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"130802199012020444\\\"/><field name=\\\"tuCname\\\" value=\\\"马青原\\\"/></record><record><field name=\\\"tuIdc\\\" value=\\\"130922198311134413\\\"/><field name=\\\"tuCname\\\" value=\\\"陈文林\\\"/></record></pageData></root>";
    
    Map<String, String> map = new HashMap<String, String>();
    
    UserWsdl userWsdl = new UserWsdl();
    
    private String reqXml ="<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
            "<root><auth appCode=\"gssp\" authCode=\"e9f00a44-45a2-4b6c-945e-4d0b77158918\" ip=\"*********\" />" +
            "<pageQuery start=\"0\" limit=\"5\">" +
            "<filter field=\"tuAcct\" value=\"019786\" match=\"EQ\" />" +
            "<return field=\"tuIdc\" />" +
            "<return field=\"tuCname\" />" +
            "<return field=\"tuEname\" />" +
            "<return field=\"tuEmail\" />" +
            "<return field=\"toId\" />" +
            "<return field=\"tuAddre\" />" +
            "<return field=\"tuBdate\" />" +
            "<return field=\"tuFstdate\" />" +
            "<return field=\"tuMobile\" />" +
            "<return field=\"photo\" />" +
            "<return field=\"tuNo\" />" +
            "<return field=\"tuAcct\" />" +
            "</pageQuery></root>";
    
//    @Before
    public void testBefore() {
        map.put(AxisUtil.INTERFACE_NAME, "pageQueryUser");
        map.put(AxisUtil.REQ_XML_CONTENT, reqXml);
        
        userWsdl.setPassword("12344321");
        userWsdl.setUserName("ramstest");
    }
    
//    @Test
    public void testUserInterface(){
       
        Object resXml = AxisUtil.doUrl(map, url, userWsdl);
        System.out.println(resXml);
       // AxisUtil.handlerXml(resXml.toString());
    }
}
