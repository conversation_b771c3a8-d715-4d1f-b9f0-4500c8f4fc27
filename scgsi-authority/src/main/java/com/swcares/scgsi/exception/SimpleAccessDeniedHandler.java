package com.swcares.scgsi.exception;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;

/**
 * ClassName：com.swcares.scgsi.exception.SimpleAccessDeniedHandler <br>
 * Description：由于spring security的认证原理是通过注册到容器tomcat的filter链上，使得认证异常不能通过DispatcherServlet，所以@ExceptionHandler处理不到  <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月27日 上午9:28:08 <br>
 * @version v1.0 <br>
 */

public class SimpleAccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response,
            AccessDeniedException accessDeniedException) throws IOException, ServletException {
        SecurityGeneralException.hanlderSecurityException(request, response, accessDeniedException);
    }
}
