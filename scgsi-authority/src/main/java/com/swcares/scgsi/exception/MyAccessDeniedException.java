package com.swcares.scgsi.exception;

import org.springframework.security.access.AccessDeniedException;

public class MyAccessDeniedException extends AccessDeniedException {

    private static final long serialVersionUID = -8752810340172809737L;

    /** 错误代码 */
    private String code;

    /** 补充参数，比如{}为空，这个param可以设置进去 */
    private String[] params;

    /** 实际数据 */
    private Object data;

    public MyAccessDeniedException(String code) {
        super(code);
        this.code = code;
    }

    public MyAccessDeniedException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    public MyAccessDeniedException(String code, String[] params) {
        super(code);
        this.code = code;
        this.params = params;
    }

    public MyAccessDeniedException(String code, String msg, String... params) {
        super(msg);
        this.code = code;
        this.params = params;
    }

    public MyAccessDeniedException(String code, String[] params, Object data) {
        super(code);
        this.code = code;
        this.params = params;
        this.data = data;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String[] getParams() {
        return params;
    }

    public void setParams(String[] params) {
        this.params = params;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
    
    
}
