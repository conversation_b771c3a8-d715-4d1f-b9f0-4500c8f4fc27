package com.swcares.scgsi.exception;

import java.io.IOException;
import java.util.Locale;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.context.support.ResourceBundleMessageSource;
import com.alibaba.fastjson.JSONObject;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.scgsi.web.ResourceBundleMessageSourceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;

/**
 * ClassName：com.swcares.scgsi.exception.SecurityGeneralException <br>
 * Description：基于security的通用异常处理，和GenericResolverException作用层面不通，前者是基于tomcat的过滤器链，后者是基于spring-web的dispatcherServlet <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月27日 上午9:17:41 <br>
 * @see SimpleAccessDeniedHandler, SimpleAuthenticationEntryPoint
 * @version v1.0 <br>
 */
@Slf4j
public class SecurityGeneralException {
    
    /**
     * Title：hanlderSecurityException <br>
     * Description：security走该方法返回异常，返回的是RenderResult，否则输出的对象前端无法解析<br>
     * author：夏阳 <br>
     * date：2020年4月27日 上午9:28:59 <br>
     * @param request
     * @param response
     * @param ex 基于认证和鉴权的异常<br>
     */
    public static void hanlderSecurityException(HttpServletRequest request,
            HttpServletResponse response, Exception ex) {
        String code = null;
        String msg = null;
        Object data = null;
        String[] params = null;
        if (ex instanceof BusinessException) {
            BusinessException gex = BusinessException.class.cast(ex);
            // 设置状态码
            code = gex.getCode();
            msg = gex.getMessage();
            data = gex.getData();
            params = gex.getParams();
        } else if (ex instanceof AccessDeniedException) {
            code = MessageCode.NO_AUTH.getCode();
        } else {
            // 其他异常处理
            code = MessageCode.UN_KNOWN.getCode();
            // 日志记录
            log.error("异常code：{} - url: {} - param: {} -异常堆栈{}", code, request.getServletPath(),
                    params, ex);
        }

        try {
            // 国际化处理
            Locale local = Locale.SIMPLIFIED_CHINESE;
            ResourceBundleMessageSource rbms =
                    ResourceBundleMessageSourceFactory.get(local.toString());
            // 获取i18n信息
            msg = rbms.getMessage(String.valueOf(code), params, local);

        } catch (Exception e) {
            log.error("获取国际化message失败,loacl:{}, code:{}, {}", request.getLocale(), code, e);
            // 设置响应对象提示信息
            msg = "未在语言文件找到当前编码与对应的信息";
        }
        String respString = JSONObject.toJSONString(RenderResult.build(code, msg, data));
        response.setContentType("application/json;charset=utf8");
        try {
            response.getWriter().write(respString);
        } catch (IOException e) {
            log.error("spring-security异常处理，返回输出流异常，转换的json参数为[{}]，异常堆栈：", respString, e);
        }
    }
}
