package com.swcares.scgsi.common.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * ClassName：com.swcares.scgsi.service.impl.VerifyCodeServiceImpl <br>
 * Description：用于在AuthenticationFilter中读取yml文件验证码启动和关闭的配置 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月27日 下午7:02:38 <br>
 * @version v1.0 <br>
 */
@Configuration
@Getter
public class VerifyCodeConfiguration {
    @Value("${verifyCode.enable}")
    private Boolean verifyCodeEnable;
}
