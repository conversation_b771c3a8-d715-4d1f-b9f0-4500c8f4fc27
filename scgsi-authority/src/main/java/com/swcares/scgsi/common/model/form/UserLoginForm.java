package com.swcares.scgsi.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.codec.binary.Base64;
import com.swcares.scgsi.common.userenum.UserEnum;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.util.CharsetUtil;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：登陆表单 <br>
 * Package：com.swcares.scgsi.common.model.form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 02月13日 20:16 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "用户登陆对象")
@Slf4j
public class UserLoginForm {
	
	/** 用户账号 */
	@ApiModelProperty(value = "用户账号", required = true)
	@NotEmpty(message = "姓名为必填项")
	private String username;
	
	/** 密码 */
	@ApiModelProperty(value = "密码", required = true)
	@NotEmpty(message = "密码为必填项")
	private String password;
	
	/** 验证码 */
	@ApiModelProperty(value = "验证码", required = true)
	@NotEmpty(message = "验证码不能为空")
	private String code;
	
	/** 密钥 */
	@ApiModelProperty(value = "密钥", required = true)
	@NotEmpty(message = "非法请求")
	private String key;
	
	/** 是否记住（true or false）*/
    @ApiModelProperty(value = "是否记住", required = false)
    private String remember;
    
    public String getPassword(){
        String privateKey = UserEnum.PASSWORD_ENCRYPT_KEY.getValue();
        String key = null;
        try {
            key = Base64.encodeBase64String(privateKey.getBytes(CharsetUtil.UTF8));
        } catch (UnsupportedEncodingException e) {
            // TODO %CodeTemplates.catchblock.tododesc e.printStackTrace();
            log.error("登录密码加密错误{}",e);
        }
        return AesEncryptUtil.aesDecrypt(key, password);
    }
}
