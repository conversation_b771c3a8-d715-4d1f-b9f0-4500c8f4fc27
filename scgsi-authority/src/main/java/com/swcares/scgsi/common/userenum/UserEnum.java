package com.swcares.scgsi.common.userenum;

/**
 * ClassName：com.swcares.scgsi.login.common.loginenum.LoginEnum <br>
 * Description：登录所用配置枚举 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月26日 下午5:35:52 <br>
 * @version v1.0 <br>
 */
public enum UserEnum {

    USER_STATE_UNUSABLE("1"), // 用户不正常状态
    USER_STATE_USABLE("0"), // 正常状态
    REDIS_PWD_ERROR("pwdErrorNum"), // redis的key存入的后缀,密码错误次数
    REDIS_VERIFY_CODE("verifyCode"), // redis的key存入的后缀,验证码
    PWD_ERROR_NUM("5"), // 密码输入错误最多次数
    LOCK_OUT_STATE("1"), // 锁定用户
    EXPIRATION_TIME("60"),// 验证码过期时间
    REDIS_SMS_CODE("smsCode"), // redis的key存入的后缀,短信验证码发送短信客户端IP
    REDIS_SMS_RETRANSMISSION_TIME("smsRetransmission"), // 短信重发时效key的后缀
    SMS_CODE_TIME("300"), // 短信验证码过期时间
    SMS_RETRANSMISSION_TIME("180"), // 短信重发时限
    IP_SMS_RETRANSMISSION_TIME("60"), // IP发送短信限制时限
    IP_SMS_RETRANSMISSION_THRESHOLD("5"), // IP发送短信限制时限
    SMS_VERIFY("1"), // 密码找回短信验证
    SMS_VERIFY_CONTENT("找回密码短信验证码为:[%S],请确认是本人操作！-【山东航地面服务综合保障平台】"), // 短信找回发送内容
    MAIL_VERIFY("2"),// 密码找回邮件验证
    CACHE_TOKEN_TIME("60"),// web端单点token存入redis时间
    ON_DUTY("1"),// 用户值班状态
    OFF_DUTY("0"),// 用户解除值班状态
    SYS_FUNCTION("1"),// 启用系统功能
    TOKEN_KEY_SUFFIX("LOGIN_TOKEN"),// 存入redis的token值的key的后缀
    TOKEN_PAST_DUE("14400"),// 存入redis的token过期时间
    COMPETENCE_ROLE_NUM("1"),// 用户拥有管理员角色数量
    CHECKED_ROLE("1"),// 选中角色
    RESOURCE_PROT_H5("H"),// 资源的类别H5
    RESOURCE_PROT_WEB("W"),// 资源的类别web
    NOT_FIRST_LOGIN("0"),// 选中角色
    PASSWORD_ENCRYPT_KEY("scgsi#2020swcares$scgsi#2020swcares"),//密码加密key
    RESOURCES_REDIS_LOGIN("login:resource:"),//资源存在redis的key
    USER_MANUAL_SYNC("userManualSync"),//用户点击增量同步按钮阀值的字典表类型
    USER_MANUAL_SYNC_THRESHOLD("threshold"),//用户点击增量同步按钮阀值的字典表类型
    USER_MANUAL_SYNC_THRESHOLD_REDIS("user:manualsync"),//用户点击增量同步redis的key
    USER_MANUAL_SYNC_EXECUTION_REDIS("user:manualSyncExecution");//用户点击增量同步判断是否在执行
    private UserEnum(String value) {
        this.value = value;
    }

    /**
     *值
     */
    private String value;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

}
