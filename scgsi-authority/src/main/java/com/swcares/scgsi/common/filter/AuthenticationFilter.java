package com.swcares.scgsi.common.filter;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.FilterChain;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.SpringUtil;
import com.swcares.scgsi.common.config.SecurityUser;
import com.swcares.scgsi.common.config.VerifyCodeConfiguration;
import com.swcares.scgsi.common.model.form.UserLoginForm;
import com.swcares.scgsi.common.model.view.LoginView;
import com.swcares.scgsi.common.model.vo.ResourceTreeVo;
import com.swcares.scgsi.common.userenum.UserEnum;
import com.swcares.scgsi.common.utils.JwtTokenUtils;
import com.swcares.scgsi.entity.Resources;
import com.swcares.scgsi.exception.SecurityGeneralException;
import com.swcares.scgsi.hum.employee.dao.EmployeeDao;
import com.swcares.scgsi.hum.employee.entity.Employee;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.service.ResourcesService;
import com.swcares.scgsi.web.RenderResult;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：认证过滤器 <br>
 * Package：com.swcares.scgsi.common.filter <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 02月11日 19:16 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class AuthenticationFilter extends UsernamePasswordAuthenticationFilter {

    // 认证管理器
    private AuthenticationManager authenticationManager;

    UserLoginForm loginUser = null;

    // ~ Constructors
    // ===================================================================================================
    public AuthenticationFilter(AuthenticationManager authenticationManager) {
        this.authenticationManager = authenticationManager;
        // 默认为/login 和POST方法，对登陆的URI进行自定义
        super.setFilterProcessesUrl("/api/auth/login");
    }

    /**
     * Title：重写父类的attemptAuthentication() <br>
     * Description：尝试进行身份验证：AbstractAuthenticationProcessingFilter->doFilter <br>
     * author：于琦海 <br>
     * date：2020/4/2 11:13 <br>
     * @param request HttpServletRequest
     * @param response HttpServletResponse
     * @return Authentication
     */
    @Override
    public Authentication attemptAuthentication(HttpServletRequest request,
            HttpServletResponse response) {
        try {
            // EmployeeDao employeeDao = SpringUtil.getBean(EmployeeDao.class);

            VerifyCodeConfiguration verifyCodeConfiguration =
                    SpringUtil.getBean(VerifyCodeConfiguration.class);
            loginUser = new ObjectMapper().readValue(request.getInputStream(), UserLoginForm.class);
            loginUser.setUsername(loginUser.getUsername().trim());
            checkInputParam(loginUser.getCode(), MessageCode.SYS_LOGIN_PARAM_IS_NULL.getCode());
            checkInputParam(loginUser.getKey(), MessageCode.SYS_LOGIN_PARAM_IS_NULL.getCode());
            checkInputParam(loginUser.getUsername(), MessageCode.SYS_LOGIN_PARAM_IS_NULL.getCode());
            checkInputParam(loginUser.getPassword(), MessageCode.SYS_LOGIN_PARAM_IS_NULL.getCode());
            if (verifyCodeConfiguration.getVerifyCodeEnable()) {
                // TODO:参数校验，验证码校验，需要往里面添加
                checkVerifyCode(loginUser.getKey() + UserEnum.REDIS_VERIFY_CODE.getValue(),
                        loginUser.getCode());// 检查验证码
            }
        } catch (BusinessException e) {
            SecurityGeneralException.hanlderSecurityException(request, response, e);
            return null;
        } catch (IOException e) {
            log.error("转换登录rquest错误,异常信息:{}", e);
            SecurityGeneralException.hanlderSecurityException(request, response, e);
            return null;
        }
        // 这里面校验用户名密码以及用户的权限，重写方法的原因是因为个性化需求需要在验证用户名密码前校验参数以及验证码等操作
        return authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(loginUser
                .getUsername(), loginUser.getPassword(), new ArrayList<>()));
    }

    /**
     * Title：successfulAuthentication <br>
     * Description：成功验证后调用的方法，如果验证成功，就生成token并返回<br>
     * <AUTHOR> <br>
     * date 2020年4月28日 上午11:20:06 <br>
     * @param request
     * @param response
     * @param chain
     * @param authResult
     * @throws IOException
     * @throws BusinessException <br>
     * @see org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter#successfulAuthentication(javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse, javax.servlet.FilterChain, org.springframework.security.core.Authentication) <br>
     */
    @Override
    protected void successfulAuthentication(HttpServletRequest request,
            HttpServletResponse response, FilterChain chain, Authentication authResult)
            throws IOException, BusinessException {
        // 查看源代码会发现调用getPrincipal()方法会返回一个实现了`UserDetails`接口的对象
        // attemptAuthentication方法中源码跟踪，通过loginUser.getUsername查询到继承UserDetails的用户的信息
        SecurityUser userDetails = (SecurityUser) authResult.getPrincipal();
        // 此处默认记住密码为False，写JwtUtils的时候没有考虑周全，目前的逻辑暂时不满足记住密码
        String token =
                JwtTokenUtils.createToken(userDetails.getUsername(), userDetails.getAuthorities(),
                        userDetails.getId(), false);
        String refreshToken =
                JwtTokenUtils.createRefreshToken(userDetails.getUsername(), userDetails.getId());
        Employee employee = null;
        RedisService redisService = SpringUtil.getBean(RedisService.class);
        try {
            EmployeeDao employeeDao = SpringUtil.getBean(EmployeeDao.class);
            employee = employeeDao.findByTuNo(userDetails.getUsername());
            redisService.deleteKey(employee.getTuNo() + UserEnum.REDIS_PWD_ERROR);//成功后清除密码错误次数
            checkLoginState(employee);
            employee.setLoginDate(new Timestamp(System.currentTimeMillis()));
            employeeDao.save(employee);// 保存用户最新状态
        } catch (BusinessException e) {
            SecurityGeneralException.hanlderSecurityException(request, response, e);
            return;
        } catch (Exception e) {
            SecurityGeneralException.hanlderSecurityException(request, response, e);
            return;
        }
        // 但是这里创建的token只是单纯的token 按照jwt的规定,最后请求的格式应该是 `Bearer token`
        ResourcesService resourcesService = SpringUtil.getBean(ResourcesService.class);
        List<ResourceTreeVo> resList = new ArrayList<ResourceTreeVo>();
        Map<String, String> moduleMap = new HashMap<String, String>();
        List<Resources> resRedisList = new ArrayList<Resources>();
        // 暂时读取所有资源数据
        try {
            resList =
                    resourcesService.getAllResources(userDetails.getId(),
                            UserEnum.RESOURCE_PROT_WEB.getValue());
            moduleMap =
                    resourcesService.getModuleResources(employee.getId(),
                            UserEnum.RESOURCE_PROT_WEB.getValue());
            resRedisList = resourcesService.getResourcesByUserID(employee.getId());
        } catch (Exception e) {
            SecurityGeneralException.hanlderSecurityException(request, response, e);
            return;
        }
        redisService.set(userDetails.getId() + UserEnum.TOKEN_KEY_SUFFIX.getValue() + UserEnum.RESOURCE_PROT_WEB.getValue(),
                JwtTokenUtils.TOKEN_PREFIX + token, new Long(UserEnum.TOKEN_PAST_DUE.getValue()));// 把token存入redis并设置过期时间
        redisService
                .set(UserEnum.RESOURCES_REDIS_LOGIN.getValue() + employee.getId(), resRedisList);//资源存入redis
        LoginView<List<ResourceTreeVo>> loginView = new LoginView<List<ResourceTreeVo>>();
        BeanUtils.copyProperties(userDetails, loginView);
        loginView.setRefresh(JwtTokenUtils.TOKEN_PREFIX + refreshToken);
        loginView.setResource(resList);
        loginView.setAuthorization(JwtTokenUtils.TOKEN_PREFIX + token);
        loginView.setIsFirstLogin(employee.getIsFirstLogin());
        loginView.setModuleMap(moduleMap);
        response.setContentType(JwtTokenUtils.CONTENT_TYPE);
        // token和refreshToken放入Response headers中
        response.setHeader(JwtTokenUtils.TOKEN_HEADER, JwtTokenUtils.TOKEN_PREFIX + token);
        response.setHeader(JwtTokenUtils.REFRESH_TOKEN_HEADER, JwtTokenUtils.TOKEN_PREFIX
                + refreshToken);
        // 把前端所需要的信息封装返回
        response.getWriter().write(
                new ObjectMapper().writeValueAsString(RenderResult.success(loginView)));
    }

    /**
     * Title：unsuccessfulAuthentication <br>
     * Description：失败验证后调用的方法<br>
     * <AUTHOR> <br>
     * date 2020年4月28日 上午11:20:19 <br>
     * @param request
     * @param response
     * @param failed
     * @throws IOException <br>
     * @see org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter#unsuccessfulAuthentication(javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse, org.springframework.security.core.AuthenticationException) <br>
     */
    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request,
            HttpServletResponse response, AuthenticationException failed) throws IOException {
        try {
            EmployeeDao employeeDao = SpringUtil.getBean(EmployeeDao.class);
            Employee employee = employeeDao.findByTuNo(loginUser.getUsername());
            checkUserName(employee);// 验证用户
            checkPassword(employee, loginUser.getPassword());// 验证密码
        } catch (BusinessException e) {
            SecurityGeneralException.hanlderSecurityException(request, response, e);
            return;
        } catch (Exception e) {
            log.error("登录获取用户错误,异常信息:{}", e);
            SecurityGeneralException.hanlderSecurityException(request, response, e);
            return;
        }
    }

    /**
     * Title：setUsernameParameter() <br>
     * Description：修改登陆表单中的用户名的属性名称，当前使用的试默认:username <br>
     * author：于琦海 <br>
     * date：2020/4/2 11:21 <br>
     * @param usernameParameter String
     */
    @Override
    public void setUsernameParameter(String usernameParameter) {
        super.setUsernameParameter(usernameParameter);
    }

    /**
     * Title：setPasswordParameter() <br>
     * Description：修改登陆表单中的密码的属性名称，当前使用的试默认:password <br>
     * author：于琦海 <br>
     * date：2020/4/2 12:40 <br>
     * @param passwordParameter String
     */
    @Override
    public void setPasswordParameter(String passwordParameter) {
        super.setPasswordParameter(passwordParameter);
    }

    /**
     * Title：checkInputParam<br>
     * Description：验证传入参数是否为空 <br>
     * author：王磊 <br>
     * date：2020年4月4日 下午9:43:39 <br>
     * @param param 参数
     * @param code 错误码<br>
     */
    private void checkInputParam(String param, String code) throws BusinessException {
        if (StringUtils.isEmpty(param)) {
            throw new BusinessException(code);
        }
    }

    /**
     * Title：checkVerifyCode <br>
     * Description：验证验证码 <br>
     * author：王磊 <br>
     * date：2020年3月25日 下午3:39:13 <br>
     * @param sessionId 
     * @param inputCode 验证码<br>
     */
    public void checkVerifyCode(String key, String inputCode) {
        RedisService redisService = SpringUtil.getBean(RedisService.class);
        String code = (String) redisService.get(key);// 获取验证码
        if (StringUtils.isEmpty(code)) {
            throw new BusinessException(MessageCode.SYS_CODE_STALE.getCode());
        }
        if (!code.equals(inputCode)) {
            throw new BusinessException(MessageCode.SYS_CODE_ERROR.getCode());
        }
    }

    /**
     * Title：checkUserName <br>
     * Description：验证帐号是否存在 <br>
     * author：王磊 <br>
     * date：2020年3月25日 下午3:39:16 <br>
     * @param employee <br>
     */
    private void checkUserName(Employee employee) {
        if (employee == null) {
            throw new BusinessException(MessageCode.SYS_USER_NOT_EXIST_AND_PWD_ERROE.getCode());
        }
    }

    /**
     * Title：checkPassword <br>
     * Description：验证密码 <br>
     * author：王磊 <br>
     * date：2020年3月25日 下午3:39:22 <br>
     * @param employee
     * @param inputPassword<br>
     */
    @Transactional
    private void checkPassword(Employee employee, String inputPassword) {
        RedisService redisService = SpringUtil.getBean(RedisService.class);
        EmployeeDao employeeDao = SpringUtil.getBean(EmployeeDao.class);
        Integer errorNum =
                (Integer) redisService.get(employee.getTuNo() + UserEnum.REDIS_PWD_ERROR);// 获取错误次数
        PasswordEncoder createDelegatingPasswordEncoder =
                PasswordEncoderFactories.createDelegatingPasswordEncoder();
        if (!createDelegatingPasswordEncoder.matches(inputPassword, employee.getPassWord())) {
            if (errorNum == null) {
                errorNum = 1;
            } else {
                errorNum = errorNum + 1;
            }
            redisService.set(employee.getTuNo() + UserEnum.REDIS_PWD_ERROR, errorNum,
                    calSecondifferenceValue());
            if (errorNum >= new Integer(UserEnum.PWD_ERROR_NUM.getValue())) {// 密码错误超过阀值锁定帐号
                employee.setLockOutDate(new Timestamp(System.currentTimeMillis()));
                employee.setLockOutState(new Integer(UserEnum.LOCK_OUT_STATE.getValue()));
                employeeDao.save(employee);
            }
            throw new BusinessException(MessageCode.SYS_USER_NOT_EXIST_AND_PWD_ERROE.getCode(),
                    new String[] {errorNum.toString()});
        }
    }

    /**
     * Title：checkLoginState <br>
     * Description：验证登录用户状态 <br>
     * author：王磊 <br>
     * date：2020年3月25日 下午3:39:27 <br>
     * @param employee <br>
     */
    private void checkLoginState(Employee employee) {
        // 判断是否为禁用用户(0 启用、1 禁用)
        if (employee.getUserState().toString().equals(UserEnum.USER_STATE_UNUSABLE.getValue())) {
            throw new BusinessException(MessageCode.SYS_USER_STATE.getCode());
        }
        // 判断是否为锁定用户(0正常,1锁定)
        if (employee.getLockOutState().toString().equals(UserEnum.USER_STATE_UNUSABLE.getValue())) {
            throw new BusinessException(MessageCode.SYS_USER_FREEZED.getCode());
        }
    }

    /**
     * Title：calSecondifferenceValue <br>
     * Description：计算现在时间距离明天0点有多少秒 <br>
     * author：王磊 <br>
     * date：2020年3月25日 下午4:36:50 <br>
     * @return <br>
     */
    private Long calSecondifferenceValue() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (cal.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }
}
