package com.swcares.scgsi.common.model.view;

import lombok.Data;

import javax.persistence.Column;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ResourcesView <br>
 * Package：com.swcares.scgsi.common.model.view <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 05月07日 14:23 <br>
 * @version v1.0 <br>
 */
@Data
public class ResourcesView {

    /** 主键 */
    @Column(name = "ID")
    private String id;

    /** 模块名称 */
    @Column(name = "NAME")
    private String name;

    /** 模块类型，页面-菜单-资源 */
    @Column(name = "TYPES")
    private Integer types;

    /** 状态 */
    @Column(name = "STATUS")
    private Boolean status;

    private String statusName;

    /** URL */
    @Column(name = "RESOURCE_URL")
    private String resourceUrl;

    /** 包含URL */
    @Column(name = "CONTAIN_URL")
    private String containUrl;

    /** 图标ICON */
    @Column(name = "ICON")
    private String icon;

    /** 端口 */
    @Column(name = "PORT")
    private String port;

    @Column(name = "PORTNAME")
    private String portName;

    /** 雪花算法生成资源代码 */
    @Column(name = "RESOURCE_CODE", nullable = false)
    private String resourceCode;

    /** 父亲ID:如果为根节点则为@ROOT */
    @Column(name="PID",nullable = false)
    private String pid;

    private String pidName;

    /** 组件ID */
    @Column(name="COMPONENTS_ID")
    private String componentsId;

    /** 功能权限 系统基础功能权限:1 业务功能权限:0*/
    @Column(name = "FUNCTIONAL")
    private String functional;

    /** 备注 */
    @Column(name="REMARKS")
    private String remarks;


}
