package com.swcares.scgsi.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：后台用户查询表单对象<br>
 * Package：com.swcares.scgsi.common.model.form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 02月13日 21:16 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "后台用户查询表单对象")
public class UserQueryForm {
	@Min(value=1,message="页码最小为1")
	@NotNull(message="页码不能为空")
	@ApiModelProperty(value = "当前页码", required = true)
	private Integer page;
	
	@Min(value=1,message="每页条数最小为1")
	@NotNull(message="每页条数不能为空")
	@ApiModelProperty(value = "每页条数", required = true)
	private Integer size;
	
	/** 姓名 */
	@ApiModelProperty(value = "姓名")
	private String name;
	
	/** 工号 */
	@ApiModelProperty(value = "工号")
	private String code;
}
