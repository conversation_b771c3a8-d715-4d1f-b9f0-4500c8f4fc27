package com.swcares.scgsi.common.model.view;

import lombok.Data;

import java.util.List;


@Data
public class UserInfoView {
	/** 主键ID */
	private String id;

	/** 用户账号 */
	private String code;

	/** 姓名 */
	private String name;

	/** 部门 */
	private String orgDepartmentId;
	
	/** 职位 */
	private String position;
	
	/** 描述 */
	private String description;
	
	/** 账号状态 */
	private String state;

	/** 创建时间 */
	private String createTime;

	/** 更新时间 */
	private String updateTime;
	
	/** 角色id列表 */
	private List<String> roleIds;
	
	/** 角色名称列表 */
	private List<String> roleNames;
}