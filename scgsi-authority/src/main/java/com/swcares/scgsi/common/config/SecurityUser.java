package com.swcares.scgsi.common.config;

import com.swcares.scgsi.entity.Resources;
import com.swcares.scgsi.entity.User;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：提供用户的核心信息 <br>
 * Package：com.swcares.scgsi.common.config <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月02日 19:16 <br>
 * @version v1.0 <br>
 */
@Data
@NoArgsConstructor
public class SecurityUser implements UserDetails {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 用户名 */
    private String username;

    /** 密码 */
    private String password;

    /** 权限 */
    private List<Resources> resources;

    /**
     * Title：SecurityUser() <br>
     * Description：构造方法创建UserDetails对象 <br>
     * author：于琦海 <br>
     * date：2020/4/2 17:01 <br>
     * @param user User
     * @param resources List<Resources>
     */
    public SecurityUser(User user,List<Resources> resources) {
        id = user.getId();
        username = user.getTuNo();
        password = user.getPassWord();
        this.resources = resources;
    }


    /**
     * 返回授予用户的权限
     */
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        List<String> codes = new ArrayList<>();
        getResources().forEach(resources -> codes.add(resources.getName()));
        return codes.stream().map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
    }

    /**
     * 获取密码
     */
    @Override
    public String getPassword() {
        return password;
    }

    /**
     * 获取用户名
     */
    @Override
    public String getUsername() {
        return username;
    }

    /**
     * 账号是否未过期，默认是false
     */
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 账号是否未锁定
     */
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 账号凭证是否未过期，默认是false
     */
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     *  默认为已启用
     */
    @Override
    public boolean isEnabled() {
        return true;
    }
}