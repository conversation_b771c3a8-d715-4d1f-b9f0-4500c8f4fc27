package com.swcares.scgsi.common.filter;

import com.swcares.scgsi.firewall.FirewallParam;
import com.swcares.scgsi.firewall.FirewallTypeEnum;
import io.jsonwebtoken.Claims;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.SpringUtil;
import com.swcares.scgsi.common.config.VerifyCodeConfiguration;
import com.swcares.scgsi.common.userenum.UserEnum;
import com.swcares.scgsi.common.utils.JwtTokenUtils;
import com.swcares.scgsi.entity.Resources;
import com.swcares.scgsi.exception.SecurityGeneralException;
import com.swcares.scgsi.firewall.Firewall;
import com.swcares.scgsi.redis.RedisService;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：授权过滤器 <br>
 * Package：com.swcares.scgsi.common.filter <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 02月11日 19:16 <br>
 * @version v1.0 <br>
 */
public class AuthorizationFilter extends BasicAuthenticationFilter {

    public AuthorizationFilter(AuthenticationManager authenticationManager) {
        super(authenticationManager);
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
            FilterChain chain) throws IOException, ServletException {
        String tokenHeader = request.getHeader(JwtTokenUtils.TOKEN_HEADER);
        // 如果请求头中没有Authorization信息
        if (tokenHeader == null || !tokenHeader.startsWith(JwtTokenUtils.TOKEN_PREFIX)) {
            backToView(request, response, MessageCode.NO_AUTH);
            return;
        }
        String token = tokenHeader.replace(JwtTokenUtils.TOKEN_PREFIX, "");
        /*
         * if (JwtTokenUtils.isExpiration(token)){ // 过期了需要处理，告诉前端刷新，进入刷新接口
         * backToView(request,response,MessageCode.REFRESH); return; }
         */
        try {
            // 高频访问判断
            checkAccessRecord(tokenHeader, request);
            // token互踢
            if (!this.compareToken(tokenHeader)) {
                backToView(request, response, MessageCode.LOGIN_AGAIN);
                return;
            }
            // 如果请求头中有token,则进行解析,并且设置认证信息
            UsernamePasswordAuthenticationToken authentication = getAuthentication(tokenHeader);
            if (Objects.isNull(authentication)) {
                // 用户没有任何权限
                backToView(request, response, MessageCode.NO_AUTH);
                return;
            }
            // 登陆成功的时候已经设置过一次了，此次设置就是为了从redis获取实时的权限
            SecurityContextHolder.getContext().setAuthentication(authentication);
            super.doFilterInternal(request, response, chain);
        } catch (BusinessException e) {
            // 用户没有任何权限
            backToView(request, response, e.getCode());
            return;
        }
    }

    /**
     * Title：backToView（） <br>
     * Description：对象回显到前端 <br>
     * author：于琦海 <br>
     * date：2020/4/3 16:38 <br>
     * @param response HttpServletResponse
     * @param codeEnum Enum<MessageCode>
     */
    private void backToView(HttpServletRequest request, HttpServletResponse response,
            Enum<MessageCode> codeEnum) {
        MessageCode messageCode = (MessageCode) codeEnum;
        SecurityGeneralException.hanlderSecurityException(request, response, new BusinessException(
                messageCode.getCode()));
    }

    /**
     * Title：backToView <br>
     * Description：对象回显到前端 <br>
     * author：王磊 <br>
     * date：2020年6月15日 下午2:52:47 <br>
     * @param request
     * @param response
     * @param code <br>
     */
    private void backToView(HttpServletRequest request, HttpServletResponse response, String code) {
        SecurityGeneralException.hanlderSecurityException(request, response, new BusinessException(
                code));
    }

    /**
     * Title：compareToken <br>
     * Description：比对token互踢 <br>
     * author：于琦海 <br>
     * date：2020/4/26 10:15 <br>
     * @param tokenHeader String
     * @return boolean
     */
    private boolean compareToken(String tokenHeader) {
        String token = tokenHeader.replace(JwtTokenUtils.TOKEN_PREFIX, "");
        String id = String.valueOf(JwtTokenUtils.getTokenBody(token).get("id"));
        RedisService redisService = SpringUtil.getBean(RedisService.class);
        Object objectW = null;
        Object objectH = null;
        // 获取web的token
        objectW =
                redisService.get(id + UserEnum.TOKEN_KEY_SUFFIX.getValue()
                        + UserEnum.RESOURCE_PROT_WEB.getValue());
        // 获取H5的token
        objectH =
                redisService.get(id + UserEnum.TOKEN_KEY_SUFFIX.getValue()
                        + UserEnum.RESOURCE_PROT_H5.getValue());
        if (tokenHeader.equals(objectW) || tokenHeader.equals(objectH)) {// 判断当前用户web的token和h5token其中有一个匹配则通过
            return true;
        } else {
            return false;
        }

    }

    /**
     * 功能描述：这里从token中获取用户信息并新建一个token
     * @param tokenHeader String
     * @return UsernamePasswordAuthenticationToken
     * @author：qhyu
     * @date：2018年9月29日
     */
    private UsernamePasswordAuthenticationToken getAuthentication(String tokenHeader) {
        RedisService redisService = SpringUtil.getBean(RedisService.class);
        String token = tokenHeader.replace(JwtTokenUtils.TOKEN_PREFIX, "");
        String id = String.valueOf(JwtTokenUtils.getTokenBody(token).get("id"));
        // web端和h5资源没有的时候不应该影响公众号
        Object resourceObject = redisService.get(UserEnum.RESOURCES_REDIS_LOGIN.getValue() + id);
        // 通过id 或者username去获取用户的资源生成UsernamePasswordAuthenticationToken
        Claims claims = JwtTokenUtils.getTokenBody(token);
        String username = claims.getSubject();
        if (StringUtils.isBlank(username)) {
            return null;
        }
        if (Objects.nonNull(resourceObject)) {
            List<String> codes = new ArrayList<>();
            List<Resources> resources =
                    JSON.parseArray(JSON.toJSONString(resourceObject), Resources.class);
            resources.forEach(resource -> codes.add(resource.getResourceCode()));
            List<SimpleGrantedAuthority> authList =
                    codes.stream().map(SimpleGrantedAuthority::new).collect(Collectors.toList());
            return new UsernamePasswordAuthenticationToken(username,
                    claims.get("id", String.class), authList);
        }
        return new UsernamePasswordAuthenticationToken(username, claims.get("id", String.class),
                new ArrayList<>());
    }

    /**
     * Title：checkAccessRecord <br>
     * Description：检查用户高频访问的情况 <br>
     * author：王磊 <br>
     * date：2020年6月23日 下午2:11:38 <br>
     * @param tokenHeader token头
     * @param request 通过request获取ip<br>
     */
    private void checkAccessRecord(String tokenHeader, HttpServletRequest request) {
        String token = tokenHeader.replace(JwtTokenUtils.TOKEN_PREFIX, "");
        List<FirewallParam> firewallParams = firewallParamBuild(JwtTokenUtils.getTokenBody(token), request);
        firewallExecute(firewallParams);
    }

    private List<FirewallParam> firewallParamBuild(Claims claims,HttpServletRequest request) {
        String id = String.valueOf(claims.get("id"));// 获取用户id
        String sub = String.valueOf(claims.get("sub"));// 获取用户id
        String clientIP = getIpAddress(request);// 获取客户端ip
        List<FirewallParam> list = new ArrayList<>();
        if(id.equals(sub)){
            FirewallParam firewallParamOpenid = new FirewallParam();
            firewallParamOpenid.setType(FirewallTypeEnum.FIREWALL_TYPE_MINUTE_OPENID);
            firewallParamOpenid.setParam(id);
            list.add(firewallParamOpenid);


            FirewallParam firewallParamDayOpenid = new FirewallParam();
            firewallParamDayOpenid.setType(FirewallTypeEnum.FIREWALL_TYPE_DAY_OPENID);
            firewallParamDayOpenid.setParam(id);
            list.add(firewallParamDayOpenid);
        }else{
            FirewallParam firewallParamUaser = new FirewallParam();
            firewallParamUaser.setType(FirewallTypeEnum.FIREWALL_TYPE_MINUTE_UASER);
            firewallParamUaser.setParam(id);
            list.add(firewallParamUaser);
        }

        FirewallParam firewallParamIp = new FirewallParam();
        firewallParamIp.setType(FirewallTypeEnum.FIREWALL_TYPE_MINUTE_IP);
        firewallParamIp.setParam(clientIP);
        list.add(firewallParamIp);


        FirewallParam firewallParamDayIp = new FirewallParam();
        firewallParamDayIp.setType(FirewallTypeEnum.FIREWALL_TYPE_DAY_IP);
        firewallParamDayIp.setParam(clientIP);
        list.add(firewallParamDayIp);

        return list;
    }

    private void firewallExecute(List<FirewallParam> list) {
        Map<String, Firewall> beansOfType = SpringUtil.getApplicationContext().getBeansOfType(Firewall.class);
        Firewall[] values = beansOfType.values().toArray(new Firewall[beansOfType.values().size()]);

        Firewall temp;
        for (int i = 0; i < values.length - 1; i++) {
            for (int j = 0; j < values.length - i - 1; j++) {
                if (values[j].order() > values[j + 1].order()) {
                    temp = values[j];
                    values[j] = values[j + 1];
                    values[j + 1] = temp;
                }
            }
        }
        for (int i = 0; i <= values.length - 1; i++) {
            for (FirewallParam e : list) {
                values[i].execute(e);
            }
        }

    }

    /**
     * Title：getIpAddress <br>
     * Description：通过request获取ip <br>
     * author：王磊 <br>
     * date：2020年6月23日 下午2:24:16 <br>
     * @param request
     * @return <br>
     */
    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (logger.isInfoEnabled()) {
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
                if (logger.isInfoEnabled()) {
                }
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
                if (logger.isInfoEnabled()) {
                }
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
                if (logger.isInfoEnabled()) {
                }
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
                if (logger.isInfoEnabled()) {
                }
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
                if (logger.isInfoEnabled()) {
                }
            }
        } else if (ip.length() > 15) {
            String[] ips = ip.split(",");
            for (int index = 0; index < ips.length; index++) {
                String strIp = (String) ips[index];
                if (!("unknown".equalsIgnoreCase(strIp))) {
                    ip = strIp;
                    break;
                }
            }
        }
        return ip;
    }
}
