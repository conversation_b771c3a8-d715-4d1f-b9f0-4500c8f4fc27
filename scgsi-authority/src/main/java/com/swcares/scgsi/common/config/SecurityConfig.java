package com.swcares.scgsi.common.config;

import com.swcares.scgsi.common.filter.AuthorizationFilter;
import com.swcares.scgsi.exception.SimpleAccessDeniedHandler;
import com.swcares.scgsi.exception.SimpleAuthenticationEntryPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import com.swcares.scgsi.common.filter.AuthenticationFilter;

/**
 *功能描述：security配置类(启动方法注解)
 * @author：qhyu
 * @date：2019年1月18日
 */
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled=true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    @Autowired
    /** 因为UserDetailsService的实现类实在太多，这里设置一下我们要注入的实现类 */
    @Qualifier("userDetailsServiceImpl")
    private UserDetailsService userDetailsService;

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        //创建默认的DelegatingPasswordEn编码器
        auth.userDetailsService(userDetailsService).passwordEncoder(PasswordEncoderFactories.createDelegatingPasswordEncoder());
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
                .cors().and().csrf().disable()
                .authorizeRequests()
                .anyRequest().access("@uriMatchingServiceImpl.hasPermission(request,authentication)")
                .and()
                .addFilter(new AuthenticationFilter(authenticationManager()))
                .addFilter(new AuthorizationFilter(authenticationManager())).sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS);
        http.exceptionHandling().authenticationEntryPoint(new SimpleAuthenticationEntryPoint())
                .accessDeniedHandler(new SimpleAccessDeniedHandler());
    }

    /**
     * 配置Web安全策略.
     */
    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers("/swagger-ui.html")
                .antMatchers("/webjars/**")
                .antMatchers("/v2/**")
                .antMatchers("/swagger-resources/**")
                .antMatchers("/api/login/tokenLoginExternal")
                .antMatchers("/api/login/tokenLogin")
                .antMatchers("/api/login/getCodeImage")
                .antMatchers("/api/login/forgetPassword")
                .antMatchers("/api/weixin/**")
                .antMatchers("/api/yee/**")
                .antMatchers("/api/uploadAndDownload/**")
                .antMatchers("/api/h5/login/tokenLoginByH5")
                .antMatchers("/api/login/logOut")
                .antMatchers("/api/sys/dict/allowlogin")
                .antMatchers("/api/sys/dict/getSelectDict")
                .antMatchers("/api/dp/sys/deployProcess")
                .antMatchers("/api/h5/login/logOut")
                .antMatchers("/api/sda/**");
    }

}
