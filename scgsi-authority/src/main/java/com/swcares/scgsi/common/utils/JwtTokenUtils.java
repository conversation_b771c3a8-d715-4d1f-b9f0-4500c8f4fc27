package com.swcares.scgsi.common.utils;

import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：JWT工具类，对JWT进行封装 <br>
 * Package：com.swcares.scgsi.common.utils <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 02月13日 20:16 <br>
 * @version v1.0 <br>
 */
public class JwtTokenUtils {

	/** 值可以从yml文件中获取 */
	public static final String TOKEN_HEADER = "Authorization";

	public static final String REFRESH_TOKEN_HEADER = "refresh";

	public static final String TOKEN_PREFIX = "Bearer ";

	private static final String SECRET = "scgsijwtsecret";

	private static final String ISS = "scgsi";

	public static final String CONTENT_TYPE = "application/json;charset=utf8";

	/** 目前按照需求为4小时 */
	private static final long EXPIRATION = 14400L;

	/** 刷新时间为8小时，为过期时间+4小时*/
	private static final long REFRESH_EXPIRATION = 28800L;

	/** 选择了记住我之后的过期时间为7天 */
	private static final long EXPIRATION_REMEMBER = 604800L;

	/**
	 * Title：createToken() <br>
	 * Description：创建Token <br>
	 * author：于琦海 <br>
	 * date：2020/2/10 13:58 <br>
	 * @param authorities Collection<? extends GrantedAuthority>
	 * @param id String
	 * @param isRememberMe boolean
	 * @param username String
	 * @return String
	 */
	public static String createToken(String username, Collection<? extends GrantedAuthority> authorities, String id, boolean isRememberMe) {
		long expiration = isRememberMe ? EXPIRATION_REMEMBER : EXPIRATION;
		return Jwts.builder()
				.signWith(SignatureAlgorithm.HS512, SECRET)
				.setIssuer(ISS)
				.setSubject(username)
				.claim("id", id)
				.setIssuedAt(new Date())
				.setExpiration(new Date(System.currentTimeMillis() + expiration * 1000))
				.compact();
	}

	/**
	 * Title：createRefreshToken（） <br>
	 * Description：创建刷新token <br>
	 * author：于琦海 <br>
	 * date：2020/4/3 11:13 <br>
	 * @param username String
	 * @param id String
	 * @return String
	 */
	public static String createRefreshToken(String username, String id) {
		return Jwts.builder()
				.signWith(SignatureAlgorithm.HS512, SECRET)
				.setIssuer(ISS)
				.setSubject(username)
				// 只是为了区别refreshToken和token
				.claim("refresh", REFRESH_TOKEN_HEADER)
				.claim("id", id)
				.setIssuedAt(new Date())
				.setExpiration(new Date(System.currentTimeMillis() + REFRESH_EXPIRATION * 1000))
				.compact();
	}

	/**
	 * Title：getUsername() <br>
	 * Description：从token中获取用户名 <br>
	 * author：于琦海 <br>
	 * date：2020/2/10 14:07 <br>
	 * @param token String
	 * @return String
	 */
	public static String getUsername(String token) {
		return getTokenBody(token).getSubject();
	}

	/**
	 * Title：isExpiration() <br>
	 * Description：是否已过期 <br>
	 * author：于琦海 <br>
	 * date：2020/2/10 14:09 <br>
	 * @param token String
	 * @return boolean
	 */
	public static boolean isExpiration(String token){
		return getTokenBody(token).getExpiration().before(new Date());
	}

	/**
	 * Title：getTokenBody() <br>
	 * Description：获取token中得body <br>
	 * author：于琦海 <br>
	 * date：2020/2/10 14:09 <br>
	 * @param token String
	 * @return Claims
	 */
	public static Claims getTokenBody(String token){
		Claims body;
		try {
			 body = Jwts.parser()
					.setSigningKey(SECRET)
					.parseClaimsJws(token)
					.getBody();
		}catch (Exception e){
			throw new BusinessException(MessageCode.REFRESH.getCode());
		}
		return body;
	}
}

