package com.swcares.scgsi.service;

import java.util.List;
import java.util.Map;
import com.swcares.scgsi.common.model.vo.ResourceTreeVo;
import com.swcares.scgsi.entity.Resources;

/**
 * ClassName：com.swcares.scgsi.service.ResourcesBaseDao <br>
 * Description：资源service接口 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月15日 下午4:07:39 <br>
 * @version v1.0 <br>
 */
public interface ResourcesService {
    
    /**
     * Title：getAllResources <br>
     * Description：获取资源菜单数据 <br>
     * author：王磊 <br>
     * date：2020年4月15日 下午1:40:09 <br>
     * @param userId 
     * @param resourceProt 
     * @return <br>
     */
    public List<ResourceTreeVo> getAllResources(String userId, String resourceProt) throws Exception;

    /**
     * Title：getModuleResources <br>
     * Description：获取组件按钮 <br>
     * author：王磊 <br>
     * date：2020年5月8日 下午6:33:13 <br>
     * @param id
     * @param resourceProt
     * @return <br>
     */
    public Map<String, String> getModuleResources(String id, String resourceProt);
    
    /**
     * Title：getResourcesByUserID <br>
     * Description：根据用户ID去查询该用户的所有资源 <br>
     * author：王磊 <br>
     * date：2020年5月12日 下午6:13:04 <br>
     * @param userId
     * @return <br>
     */
    public  List<Resources> getResourcesByUserID(String userId);
}
