package com.swcares.scgsi.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.swcares.scgsi.common.model.vo.ResourceTreeVo;
import com.swcares.scgsi.dao.ResourcesDao;
import com.swcares.scgsi.entity.Resources;
import com.swcares.scgsi.service.ResourcesService;

@Service
@Slf4j
public class ResourcesServiceImpl implements ResourcesService {

    @Resource
    private ResourcesDao resourcesDao;

    /**
     * Title：getAllResources <br>
     * Description：获取所有资源菜单数据<br>
     * author：王磊 <br>
     * date：2020年4月15日 下午1:40:09 <br>
     * @return <br>
     */
    @Override
    public List<ResourceTreeVo> getAllResources(String userId, String resourceProt) {
        List<ResourceTreeVo> list = resourcesDao.getAllResources(userId,resourceProt);
        return buildTree(list);
    }

    /**
     * Title：buildTree <br>
     * Description：构建资源树 <br>
     * author：王磊 <br>
     * date：2020年4月15日 下午4:15:21 <br>
     * @param nodes
     * @return <br>
     */
    public static List<ResourceTreeVo> buildTree(List<ResourceTreeVo> nodes) {
        Map<String, List<ResourceTreeVo>> sub =
                nodes.stream().filter(node -> !node.getPid().equals("@ROOT"))
                        .collect(Collectors.groupingBy(node -> node.getPid()));
        nodes.forEach(node -> node.setChildren(sub.get(node.getId())));
        return nodes.stream().filter(node -> node.getPid().equals("@ROOT"))
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, String> getModuleResources(String id, String resourceProt) {
        List<Resources> list = resourcesDao.getModuleResources(id,resourceProt);
        Map<String, String>  map = new HashMap<String, String>();
        for(Resources resource : list){
            map.put(resource.getComponentsId(), resource.getComponentsId());
        }
        return map;
    }

    @Override
    public List<Resources> getResourcesByUserID(String userId) {
        return resourcesDao.getResourcesByUserID(userId);
    }
}
