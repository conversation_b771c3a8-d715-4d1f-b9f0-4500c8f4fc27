package com.swcares.scgsi.service.impl;

import com.alibaba.fastjson.JSON;
import com.swcares.scgsi.base.SpringUtil;
import com.swcares.scgsi.entity.Resources;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.service.UriMatchingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：UriMatchingServiceImol <br>
 * Package：com.swcares.scgsi.service.impl <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 05月12日 14:40 <br>
 * @version v1.0 <br>
 */
@Component("uriMatchingServiceImpl")
@Slf4j
public class UriMatchingServiceImpl implements UriMatchingService {

    private AntPathMatcher antPathMatcher = new AntPathMatcher();

    public static final String PAX_WEIXIN_RESOURCE="weixin:pax:openidResource:";


    @Override
    public boolean hasPermission(HttpServletRequest request, Authentication authentication) {
        RedisService redisService = SpringUtil.getBean(RedisService.class);
        Object principal = authentication.getCredentials();
        String userId = String.valueOf(principal);
        Object object = redisService.get(PAX_WEIXIN_RESOURCE + userId);
        List<Resources> openIdResource = JSON.parseArray(String.valueOf(object), Resources.class);
        boolean hasPermission = false;
        if (Objects.nonNull(principal)) {
            Set<String> urls = new HashSet<>();
            //将所有H5端的放入白名单
            //ResourcesDao bean = SpringUtil.getBean(ResourcesDao.class);
            //List<Resources> h5Resources = bean.getH5Resources();
            /*if (Objects.nonNull(h5Resources) && h5Resources.size() > 0) {
                List<String> h5List =
                        h5Resources.stream().map(Resources::getResourceCode).collect(Collectors.toList());
                urls.addAll(h5List);
            }*/
            if (Objects.nonNull(openIdResource) && openIdResource.size() > 0) {
                List<String> openIdResourceList =
                        openIdResource.stream().map(Resources::getContainUrl).collect(Collectors.toList());
                urls.addAll(openIdResourceList);
            }
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            if (Objects.nonNull(authorities)) {
                List<String> authUrl =
                        authorities.stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());
                urls.addAll(authUrl);
                // web消息
                urls.add("/api/message/**");
                // h5应用和消息
                urls.add("/api/h5/message/**");
                urls.add("/api/h5/user/getUserDetail");
                urls.add("/api/h5/user/updateOnDutyState");
                // web部门相关所有接口
                urls.add("/api/department/**");
                // 解除值班
                urls.add("/api/user/updateOnDutyState");
                //退出登录
                urls.add("/api/login/logOut");
                // web修改密码
                urls.add("/api/login/updatePassword");
                //web修改角色权限
                urls.add("/api/resource/updateRoleResources");
                // 用户详情
                urls.add("/api/user/getUserDetail");
                // 收藏菜单列表
                urls.add("/api/user/favoriteMenu");
                // 取消和收藏菜单
                urls.add("/api/user/cancelOrKeep");
                //部署审核流程
                urls.add("/api/dp/sys/deployProcess");
                urls.add("/api/department/manualSync");
                //旅客端领取+图形验证码
                urls.add("/api/h5/login/getCodeImage");
                //山航接口权限
                urls.add("/api/sda/**");
            }
            //测试环境不开放，生产打开注释
            log.info("uriMatchingServiceImpl 权限访问 ，当前登录人：[{}] ,请求url:[{}],权限urls：[{}]",userId,request.getRequestURI(),urls);
            // 注意这里不能用equal来判断，因为有些URL是有参数的，所以要用AntPathMatcher来比较
            for (String url : urls) {
                List<String> str = Arrays.asList(url.split(";"));
                for (int i = 0; i < str.size(); i++) {
                    if (antPathMatcher.match(str.get(i).trim(), request.getRequestURI())) {
                        hasPermission = true;
                        break;
                    }
                }
            }
        }
        return hasPermission;
    }

}
