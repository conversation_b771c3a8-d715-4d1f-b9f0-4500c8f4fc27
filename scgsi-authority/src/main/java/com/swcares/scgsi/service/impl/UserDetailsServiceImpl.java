package com.swcares.scgsi.service.impl;

import com.swcares.scgsi.common.config.SecurityUser;
import com.swcares.scgsi.dao.ResourcesDao;
import com.swcares.scgsi.dao.UserDao;
import com.swcares.scgsi.entity.Resources;
import com.swcares.scgsi.entity.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class UserDetailsServiceImpl implements UserDetailsService {
	
	@Resource
	private UserDao userDao;

	@Resource
	private ResourcesDao resourcesDao;

	/**
	 * Locates the user based on the username. In the actual implementation, the search
	 * may possibly be case sensitive, or case insensitive depending on how the
	 * implementation instance is configured. In this case, the <code>UserDetails</code>
	 * object that comes back may have a username that is of a different case than what
	 * was actually requested..
	 *
	 * @param username the username identifying the user whose data is required.
	 *
	 * @return a fully populated user record (never <code>null</code>)
	 *
	 * @throws UsernameNotFoundException if the user could not be found or the user has no
	 * GrantedAuthority
     *
     * <AUTHOR>
	 */
	@Override
	public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userDao.getUserByTuNo(username);
        if (Objects.isNull(user)){
            return null;
        }
        // 根据user中的ID查询到所有的角色，然后通过角色查询出所有的资源，并且需要去重
		List<Resources> resourcesList = resourcesDao.getResourcesByUserID(user.getId());
        return new SecurityUser(user,resourcesList);
    }

}
