package com.swcares.scgsi.controller;


import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.swcares.scgsi.common.model.form.UserLoginForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：用户管理控制层 <br>
 * Package：com.swcares.scgsi.controller <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 02月13日 21:16 <br>
 * @version v1.0 <br>
 */
@Api(tags = "用户管理接口")
@RestController
@RequestMapping(path = "/api")
public class UserManagerController {

    /**
     * Title：login() <br>
     * Description：用户登录(此接口只用于在swagger中显示) <br>
     * author：于琦海 <br>
     * date：2020/2/17 9:31 <br>
     * @param form
     */
    @ApiOperation(value = "用户登录接口")
    @PostMapping("/auth/login")
    public void login(@RequestBody UserLoginForm form) {
        // 此处不会进入，所以不需要加@valid进行Form验证
    }

}
