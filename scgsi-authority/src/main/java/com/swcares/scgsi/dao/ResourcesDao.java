package com.swcares.scgsi.dao;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.view.ResourcesView;
import com.swcares.scgsi.common.model.vo.ModuleTreeVO;
import com.swcares.scgsi.common.model.vo.ResourceTreeVo;
import com.swcares.scgsi.entity.Resources;
import com.swcares.scgsi.entity.RoleResources;
import com.swcares.scgsi.entity.UserRole;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ResourcesDao <br>
 * Package：com.swcares.scgsi.dao <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月02日 16:19 <br>
 * @version v1.0 <br>
 */
@Repository
public class ResourcesDao {

    @Resource
    private BaseDAO baseDAO;

    /**
     * Title：getResourcesByUserID（） <br>
     * Description：根据用户ID去查询该用户的所有资源 <br>
     * author：于琦海 <br>
     * date：2020/4/2 16:20 <br>
     * @param userId String
     * @return List<Resources>
     */
    public List<Resources> getResourcesByUserID(String userId) {
        StringBuffer sql =
                new StringBuffer(
                        "SELECT DISTINCT r.* FROM RESOURCES_SCGSI r LEFT JOIN ROLE_RESOURCES "
                                + "rr on r.ID = rr.RESOURCES_ID WHERE r.STATUS = 1 AND rr.ROLE_ID IN ( "
                                + " SELECT t.ID FROM ROLE t LEFT JOIN USER_ROLE s ON s.ROLE_ID = t.ID WHERE t.STATUS = 1 AND s.EMPLOYEE_ID =:userId )");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("userId", userId);
        return (List<Resources>) baseDAO
                .findBySQL_comm(sql.toString(), parameters, Resources.class);
    }

    /**
     * Title：getResourcesByRoleID（） <br>
     * Description：根据角色ID去查询该用户的所有资源 <br>
     * author：于琦海 <br>
     * date：2020/4/2 16:20 <br>
     * @param roleId String
     * @return List<Resources>
     */
    public List<ModuleTreeVO> getResourcesByRoleID(String roleId) {
        StringBuffer sql =
                new StringBuffer(
                        "SELECT T.ID as key,T.NAME as title,T.PID，T.PORT FROM RESOURCES_SCGSI T "
                                + "LEFT JOIN ROLE_RESOURCES RR ON RR.RESOURCES_ID = T.ID WHERE RR.ROLE_ID =:roleId");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("roleId", roleId);
        return (List<ModuleTreeVO>) baseDAO.findBySQL_comm(sql.toString(), parameters,
                ModuleTreeVO.class);
    }

    /**
     * Title：getResources() <br>
     * Description：根据模块名称和模块类型查询资源列表（分页） <br>
     * author：于琦海 <br>
     * date：2020/4/8 10:36 <br>
     * @param parameters Map<String, Object>
     * @param sql String
     * @param current Integer
     * @param pageSize Integer
     * @return QueryResults
     */
    public QueryResults getResources(String sql, Map<String, Object> parameters, Integer current,
            Integer pageSize) {
        return baseDAO.findBySQLPage_comm(sql, current, pageSize, parameters, ResourcesView.class);
    }

    /**
     * Title：batchDisabled（） <br>
     * Description：批量启用停用 <br>
     * author：于琦海 <br>
     * date：2020/4/8 14:24 <br>
     * @param values Object[]
     * @param status Boolean
     */
    @Transactional
    public void batchDisabled(Object[] values, Boolean status) {
        List<Object> params = new LinkedList<>();
        StringBuffer sql = new StringBuffer("UPDATE RESOURCES_SCGSI SET STATUS = ?");
        params.add(status);
        sql.append(" where ID in (");
        for (Object obj : values) {
            if (obj != null) {
                sql.append(" ").append("?,");
                params.add(obj);
            }
        }
        sql.delete(sql.length() - 1, sql.length());
        sql.append(" ").append(")");
        baseDAO.batchUpdate(sql.toString(), params);
    }

    /**
     * Title：getModuleRoot() <br>
     * Description：获取模块树根节点 <br>
     * author：于琦海 <br>
     * date：2020/4/8 17:25 <br>
     * @return  List<ModuleTreeVO>
     */
    public List<ModuleTreeVO> getModuleRoot() {
        StringBuffer sql =
                new StringBuffer(
                        "SELECT id as key,name as title,pid,port FROM RESOURCES_SCGSI WHERE STATUS = '1'");
        return (List<ModuleTreeVO>) baseDAO
                .findBySQL_comm(sql.toString(), null, ModuleTreeVO.class);
    }

    /**
     * Title：getResourcesName（） <br>
     * Description：获取资源名称 <br>
     * author：于琦海 <br>
     * date：2020/4/15 18:18 <br>
     * @param id String
     * @return String
     */
    public String getResourcesName(String id) {
        StringBuilder sql = new StringBuilder("select name from RESOURCES_SCGSI where ID =:id");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("id", id);
        List<Map<String, Object>> results =
                (List<Map<String, Object>>) baseDAO
                        .findBySQL_comm(sql.toString(), parameters, null);
        if (results.size() > 0) {
            Map<String, Object> stringObjectMap = results.get(0);
            return (String) stringObjectMap.get("NAME");
        }
        return null;
    }

    /**
     * Title：getAllResources <br>
     * Description：获取所有未禁用的资源 <br>
     * author：王磊 <br>
     * date：2020年4月15日 下午1:45:39 <br>
     * @return <br>
     */
    @SuppressWarnings("unchecked")
    public List<ResourceTreeVo> getAllResources(String userId, String resourceProt) {
        StringBuffer sql =
                new StringBuffer(
                        "SELECT DISTINCT(RS.ID),RS.NAME,RS.TYPES AS TYPE,RS.RESOURCE_URL AS PATH,RS.CONTAIN_URL AS CONTAINPATH,RS.ICON,RS.PID FROM RESOURCES_SCGSI RS "
                                + "LEFT JOIN ROLE_RESOURCES RR ON RR.RESOURCES_ID = RS.ID "
                                + "LEFT JOIN USER_ROLE UR ON UR.ROLE_ID =RR.ROLE_ID  "
                                + "LEFT JOIN ROLE R ON RR.ROLE_ID = R.ID  "
                                + "WHERE RS.STATUS = 1 AND R.STATUS = 1 AND RS.TYPES != 0 AND UR.EMPLOYEE_ID =:userId AND RS.PORT = :resourceProt");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("userId", userId);
        parameters.put("resourceProt", resourceProt);
        return (List<ResourceTreeVo>) baseDAO.findBySQL_comm(sql.toString(), parameters,
                ResourceTreeVo.class);
    }
    
    /**
     * Title：getModuleResources <br>
     * Description：获取组件 <br>
     * author：王磊 <br>
     * date：2020年5月8日 下午6:46:15 <br>
     * @param userId
     * @param resourceProt
     * @return <br>
     */
    @SuppressWarnings("unchecked")
    public List<Resources> getModuleResources(String userId, String resourceProt) {
        StringBuffer sql =
                new StringBuffer(
                        "SELECT DISTINCT(RS.COMPONENTS_ID) FROM RESOURCES_SCGSI RS "
                                + "LEFT JOIN ROLE_RESOURCES RR ON RR.RESOURCES_ID = RS.ID "
                                + "LEFT JOIN USER_ROLE UR ON UR.ROLE_ID =RR.ROLE_ID  "
                                + "LEFT JOIN ROLE R ON RR.ROLE_ID = R.ID  "
                                + "WHERE RS.STATUS = 1 AND R.STATUS = 1 AND RS.TYPES = 0 AND UR.EMPLOYEE_ID =:userId AND RS.PORT = :resourceProt");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("userId", userId);
        parameters.put("resourceProt", resourceProt);
        return (List<Resources>) baseDAO.findBySQL_comm(sql.toString(), parameters,
                Resources.class);
    }

    /**
     * Title：findRolesResourceByUid <br>
     * Description：通过用户ID查询出所有的角色类型为管理员角色下的所有资源 <br>
     * author：于琦海 <br>
     * date：2020/4/20 14:34 <br>
     * @param id String
     * @return List<ModuleTreeVO>
     */
    public List<ModuleTreeVO> findRolesResourceByUid(String competence, String id) {
        StringBuilder sql =
                new StringBuilder("SELECT distinct RS.ID as KEY,RS.NAME as TITLE,RS.PID,RS.PORT FROM "
                        + "RESOURCES_SCGSI RS"
                        + " LEFT JOIN ROLE_RESOURCES RR ON RS.ID = RR.RESOURCES_ID "
                        + "WHERE RR.ROLE_ID IN ("
                        + "SELECT R.ID FROM ROLE R LEFT JOIN USER_ROLE UR ON R.ID = UR.ROLE_ID "
                        + "WHERE UR.EMPLOYEE_ID=:id AND R.COMPETENCE='0')");
        if ("1".equals(competence)) {
            sql.append(" and RS.FUNCTIONAL ='0' ");
        }
        Map<String, Object> parameters = new HashMap<>(1);
        parameters.put("id", id);
        return (List<ModuleTreeVO>) baseDAO.findBySQL_comm(sql.toString(), parameters,
                ModuleTreeVO.class);
    }

    /**
     * Title：removeRolesBind <br>
     * Description：去除角色的资源绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 14:25 <br>
     * @param sql String
     * @param parameters String
     */
    public void removeRolesBind(String sql, String parameters) {
        if (StringUtils.isNotBlank(parameters)){
            baseDAO.batchUpdate(sql, parameters);
        }
    }

    /**
     * Title：bindResources <br>
     * Description：资源绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 14:34 <br>
     * @param resources List<RoleResources>
     */
    @Transactional
    public void bindResources(List<RoleResources> resources) {
        StringBuilder begin = new StringBuilder("begin ");
        List<Object> params = new LinkedList<>();
        for (RoleResources roleResources : resources) {
            StringBuilder sql =
                    new StringBuilder(
                            "INSERT INTO ROLE_RESOURCES(role_id, resources_id) VALUES (?,?);");
            params.add(roleResources.getRoleID());
            params.add(roleResources.getResourcesID());
            begin.append(sql);
        }
        StringBuilder end = new StringBuilder("end; ");
        begin.append(end);
        baseDAO.batchUpdate(begin.toString(), params);
    }

    /**
     * Title：bindUsers <br>
     * Description：用户绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 16:02 <br>
     * @param resources List<UserRole>
     */
    @Transactional
    public void bindUsers(List<UserRole> resources) {
        StringBuilder begin = new StringBuilder("begin ");
        List<Object> params = new LinkedList<>();
        for (UserRole userRole : resources) {
            StringBuilder sql =
                    new StringBuilder("INSERT INTO USER_ROLE(employee_id, role_id)  VALUES  (?,?);");
            params.add(userRole.getEmployeeId());
            params.add(userRole.getRoleId());
            begin.append(sql);
        }
        StringBuilder end = new StringBuilder("end; ");
        begin.append(end);
        baseDAO.batchUpdate(begin.toString(), params);
    }

    /**
     * Title：getRootResource <br>
     * Description：获取当前节点资源的父亲节点 <br>
     * author：于琦海 <br>
     * date：2020/5/8 15:41 <br>
     * @param resourceStr String
     * @return List<Resources>
     */
    public List<Resources> getRootResource(String resourceStr,String id) {
        StringBuilder sql = new StringBuilder("select distinct(ID), name, types, status, resource_url, contain_url, icon, port, resource_code, pid, remarks, components_id, functional " +
                "from RESOURCES_SCGSI start with PID=:resourceStr and ID=:id connect by prior PID=ID");
        Map<String,Object> parameters = new HashMap<>();
        parameters.put("resourceStr",resourceStr);
        parameters.put("id",id);
        return (List<Resources>) baseDAO.findBySQL_comm(sql.toString(),parameters,Resources.class);
    }

    /**
     * Title：removeBindUsers <br>
     * Description：用户和角色解除绑定 <br>
     * author：王磊 <br>
     * date：2020年5月7日 下午3:39:58 <br>
     * @param resources <br>
     */
    @Transactional
    public void removeBindUsers(List<UserRole> userRoles) {
        StringBuilder begin = new StringBuilder("begin ");
        List<Object> params = new LinkedList<>();
        for (UserRole userRole : userRoles) {
            StringBuilder sql =
                    new StringBuilder("DELETE  USER_ROLE WHERE EMPLOYEE_ID = ? AND ROLE_ID = ?;");
            params.add(userRole.getEmployeeId());
            params.add(userRole.getRoleId());
            begin.append(sql);
        }
        StringBuilder end = new StringBuilder("end; ");
        begin.append(end);
        baseDAO.batchUpdate(begin.toString(), params);
    }
    
    /**
     * Title：getUserRoleByRoleId <br>
     * Description：通过角色ID获取角色用户关联表数据 <br>
     * author：王磊 <br>
     * date：2020年5月11日 下午3:07:02 <br>
     * @param roleId
     * @return <br>
     */
    @SuppressWarnings("unchecked")
    public List<UserRole> getUserRoleByRoleId(String roleId) {
        StringBuffer sql =
                new StringBuffer(
                        "SELECT * FROM USER_ROLE  "
                                + "WHERE ROLE_ID = :roleId");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("roleId", roleId);
        return (List<UserRole>) baseDAO.findBySQL_comm(sql.toString(), parameters,
                UserRole.class);
    }

    /**
     * Title：getH5Resources <br>
     * Description：获取所有H5的资源，进行测试过滤时使用 <br>
     * author：于琦海 <br>
     * date：2020/5/12 22:13 <br>
     * @return List<Resources>
     */
    public List<Resources> getH5Resources(){
        StringBuffer sql = new StringBuffer("SELECT * FROM RESOURCES_SCGSI WHERE PORT = 'H'");
        return (List<Resources>) baseDAO.findBySQL_comm(sql.toString(), null,Resources.class);
    }
}
