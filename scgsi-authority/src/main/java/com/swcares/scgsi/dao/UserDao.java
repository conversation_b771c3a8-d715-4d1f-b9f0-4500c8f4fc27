package com.swcares.scgsi.dao;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.entity.User;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：UserDao <br>
 * Package：com.swcares.scgsi.dao <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月02日 14:50 <br>
 * @version v1.0 <br>
 */
@Repository
public class UserDao {

    @Resource
    private BaseDAO baseDAO;

    /**
     * Title：getUserByTuAcct() <br>
     * Description：根据系统账号查询用户实体 <br>
     * author：于琦海 <br>
     * date：2020/4/2 14:51 <br>
     * @param tuNo String
     * @return User
     */
    public User getUserByTuNo(@Valid @NotNull String tuNo) {
        StringBuffer sql = new StringBuffer("SELECT * FROM EMPLOYEE WHERE 1 = 1 ");
        sql.append(" and TUNO =:tuNo");
        Map<String, Object> parameters = new HashMap<>(1);
        parameters.put("tuNo", tuNo);
        return baseDAO.findOneBySql(sql.toString(), parameters, User.class);
    }
}
