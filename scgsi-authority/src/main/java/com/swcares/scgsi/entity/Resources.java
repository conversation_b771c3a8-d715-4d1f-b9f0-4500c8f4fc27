package com.swcares.scgsi.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Entity
@Table(name = "RESOURCES_SCGSI")
@Data
public class Resources {

	/** 主键 */
	@Id
	@Column(name = "ID")
	@GeneratedValue(generator = "uuid2")
	@GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
	private String id;

	/** 模块名称 */
	@Column(name = "NAME")
	private String name;
	
	/** 模块类型，页面-菜单-资源 */
	@Column(name = "TYPES")
	private Integer types;

	/** 状态 */
	@Column(name = "STATUS")
	private Boolean status;

	/** URL */
	@Column(name = "RESOURCE_URL")
	private String resourceUrl;

	/** 包含URL */
	@Column(name = "CONTAIN_URL")
	private String containUrl;

	/** 图标ICON */
	@Column(name = "ICON")
	private String icon;

	/** 端口 */
	@Column(name = "PORT")
	private String port;

	/** 后端资源路径 */
	@Column(name = "RESOURCE_CODE", nullable = false)
	private String resourceCode;

	/** 父亲ID:如果为根节点则为@ROOT */
	@Column(name="PID",nullable = false)
	private String pid;

	/** 组件ID */
	@Column(name="COMPONENTS_ID")
	private String componentsId;

	/** 功能权限 系统基础功能权限:1 业务功能权限:0*/
	@Column(name = "FUNCTIONAL")
	private String functional;

	/** 备注 */
	@Column(name="REMARKS")
	private String remarks;
}
