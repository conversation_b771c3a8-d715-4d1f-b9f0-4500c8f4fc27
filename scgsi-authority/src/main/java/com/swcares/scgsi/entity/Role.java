package com.swcares.scgsi.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Entity
@Table(name = "ROLE")
@Data
public class Role {

	/** 主键 */
	@Id
	@Column(name = "ID")
	@GeneratedValue(generator = "uuid2")
	@GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
	private String id;

	/** 角色代码 */
	@Column(name="CODE")
	private String code;

	/** 角色名称 */
	@Column(name="NAME",nullable = false)
	private String name;

	/** 角色类型 0管理员（限制1个员工），1普通(不限制) */
	@Column(name="COMPETENCE")
	private String competence;

	/** 角色描述 */
	@Column(name="DESCRIPTION")
	private String description;

	/** 状态 */
	@Column(name="STATUS")
	private String status;

	/** 创建人 */
	@Column(name="FOUNDER")
	private String founder;

	/** 创建时间 */
	@Column(name = "CREATETIME", nullable = false)
	private String createTime;

	/** 最后修改人 */
	@Column(name="MODIFY_PERSON")
	private String modifyPerson;

	/** 最后修改时间 */
	@Column(name="MODIFY_TIME")
	private String modifyTime;

	/** 部门ID */
	@Column(name = "DEPARTMENT_ID")
	private String departmentId;

}
