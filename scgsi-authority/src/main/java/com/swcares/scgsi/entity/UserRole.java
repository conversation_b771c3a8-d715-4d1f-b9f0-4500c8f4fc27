package com.swcares.scgsi.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：用户角色中间表 <br>
 * Package：com.swcares.scgsi.entity <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月02日 14:24 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "USER_ROLE")
@Data
public class UserRole {

    @Id
    @Column(name = "EMPLOYEE_ID")
    private String employeeId;

    @Column(name = "ROLE_ID")
    private String roleId;

}
