<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>scgsi</artifactId>
		<groupId>com.swcares</groupId>
        <version>1.1.20</version>
	</parent>

	<artifactId>scgsi-authority</artifactId>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-common</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-third</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-exception</artifactId>
		</dependency>
		<!-- 引入权限框架 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<!-- JWT -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
		</dependency>
	</dependencies>
</project>