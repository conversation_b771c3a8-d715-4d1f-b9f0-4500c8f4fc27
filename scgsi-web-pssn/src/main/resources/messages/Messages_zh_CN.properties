#0000-0099****代表系统级别提示
#1000-9999****代表基础模块的提示


#################################系统操作提示#################################
#000000=请求成功  已在系统枚举类中配置 
0000=未知错误
0001=操作未授权
0002=会话过期
0=操作成功
-1=操作失败
0009=短期内多次发起请求,请稍后再试!
#############################################################################


#################################通用异常提示 #################################
0003={0}参数不能为空
0004=请重新登陆!
0005=系统基础配置信息为空
0006=通讯错误
0007=数据不存在
#############################################################################


#################################用户模块#################################
100100=验证码已过期，请重新获取！
100101=验证码不匹配，请重新输入！
100102=用户已被锁定，请联系系统管理员进行解锁！
100103=该账号在系统中不存在，请重新输入
100104=该账号还未启用，请联系系统管理员分配账号权限！
100105=系统密码错误（该密码为本系统密码，非门户网密码），请重新输入。一天连续输错密码五次账号将被锁定，请谨慎输入（当前连续输错{0}次）
100106=当前密码为初始密码，请立即修改密码！
100107=旧密码错误
100108=单点登录验证失败
100109=单点登录验证获取数据为空
100110=账号或手机号或验证码错误，请重新输入。
100111=输入参数为空，请检查参数。
100112=帐号错误，请重新输入！
100113=用户管理员角色超过规定数量！
100116=验证码推送失败，请稍后再试！
100117=验证码已推送过,如未收到请间隔{0}秒后再次点击获取！
100118=客户端发送密码找回短信已超过阀值,请稍后再试！
#############################################################################


#################################DataGrid模块#################################
100109=清除数据库{0}表失败

#############################################################################


#################################quartzJob模块#################################
100150=创建定时任务失败:任务名称:{0}
100151=更新定时任务失败:任务名称:{0}
100152=删除定时任务失败:任务名称:{0}
100153=恢复定时任务失败:任务名称:{0}
100154=定时任务执行失败:任务名称:{0}
100155=定时任务暂停失败:任务名称:{0}
#############################################################################


#################################部门模块#################################
100120=新增部门失败！失败原因：{0}
100121=新增部门失败！失败原因：{0}

#############################################################################


#################################机构模块#################################
100130=新增公司失败！失败原因：{0}
100131=新增部门失败！失败原因：{0}

#############################################################################

#################################Excel报错#################################
100200=导入文件不存在！
100201=导入失败: 文件大小不能超过{0}M！



#################################短信模块#################################
100400=没有查询到该短信记录，请联系管理员！
100401=短信模板主键为空，请检查输入参数！
100402=该短信模板已经存在，请核验后再操作！
100403=该类型短信模板，不存在，请配置！
100404=该电话号码当日发送验证码次数已达到上限，请联系管理员！
#############################################################################

#################################H5超售模块#################################
100500=超售赔偿单赔偿金额不能为小于1！
100501=超售旅客id查询无数据！
100502=超售赔偿金额前后端计算不一致，请核对！
100503=超售列表数据详情查询-改签航班时差计算，异常！
100504=超售改签航班时差，查无对应规则！请联系管理员配置！
100505=超售退票查无对应规则！请联系管理员配置！
100506=改签航班计划起飞时间不能为空！
100507=验证失败，非本航站出发航班，请核对后再输入！
100508=验证失败，只能由发起人进行发放操作！

#################################文件操作#################################
100600=文件上传失败！
100601=文件不存在！
100602=文件下载失败！
100603=上传文件失败: 文件大小不能超过{0}M！
100604=上传文件类型不符合规范,请重新选择文件上传{0}！
#############################################################################

#################################审核模块#################################
100700=审核-用户再次发起-赔偿单审核处理异常！
100701=审核-赔偿单审核发起流程，异常! 赔偿单id:{0}！
100702=审核-发起审核-当前orderId已发起过审核流程，用户不是流程发起人不能发起审核,异常! 赔偿单id:{0} 发起人：{1}
100703=审核-taskId不能为空！
100704=审核-赔偿单审核提交数据AOC角色审批||发起，HandleUser不能为空！
100705=审核-发起推送信息，接收人为null！
100706=赔偿单查询无数据，赔偿单号：{0}
100707=查询数据-服务航站不能为空
100708=赔偿单已审核，不可再次审核！
100709=审核人员选择错误，不能选择用户本人:{0}！
100710=审核人员字段解密错误!
100711=审核消息发送失败！
########################################################################


#################################三方支付#################################
100800=授权code不能为空！
100801=授权state不能为空！
100802=授权openid不能为空！
########################################################################


#################################微信公众号领取#################################
100900=未查询到相关信息，请核对航班号、航班日期、购票证件号是否正确！
100901=验证码已推送过,如未收到请5分钟后再次点击获取!
100902=短信推送失败,请稍后再试!", "短信推送失败,请稍后再试!
100903=验证码已过期请重新点击获取!
100904=验证码输入错误!
100905=赔偿信息发生变更，请重新验证!
100906=银联实名认证失败:{0}!
100907=图片上传异常!
100908=旅客信息发生变更，请重新验证!
100909=旅客超出申领次数限制:{0}
100910=旅客在该航班下的赔偿信息已经领取成功，请稍后查询或联系工作人员!
100911=请先验证手机短信验证码！
100912=未查询到相关信息，请核对航班号、航班日期、购票证件号、手机号是否与申领预留信息一致！
########################################################################

#################################现金领取#################################
102000=未查询到输入航班对应的赔付信息！
102001=旅客信息发生变更，请重新验证!
########################################################################

#################################WEB服务补偿#################################
101902=未从鲁雁管家(TRACE接口)获取到旅客数据，请联系管理员

##########################航延住宿补偿####################################
103009=保障单不存在
103007=该审批任务已被处理!