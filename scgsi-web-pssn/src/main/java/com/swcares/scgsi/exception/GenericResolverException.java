package com.swcares.scgsi.exception;

import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.i18n.RequestAcceptHeaderLocaleResolver;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.scgsi.web.ResourceBundleMessageSourceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.Locale;

/**
 * 
 * ClassName：com.swcares.core.exception.GenericExceptionHandler <br>
 * Description：通用异常处理<br>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2019年1月25日 下午1:20:46 <br>
 * @version v1.0 <br>
 */
@Slf4j
@RestControllerAdvice
public class GenericResolverException {

  @Autowired
  private RequestAcceptHeaderLocaleResolver localeResolver;

  /**
   * Title：exceptionHandler <br>
   * Description：通用异常处理方法<br>
   * author：夏阳 <br>
   * date：2019年2月26日 上午11:32:04 <br>
   * @param request 当前请求的request对象
   * @param ex 异常类
   * @return <br>
   */
  @ExceptionHandler(Exception.class)
  public RenderResult<?> exceptionHandler(HttpServletRequest request, Exception ex) {
    String code = null;
    String msg = null;
    Object data = null;
    String[] params = null;
    if (ex instanceof BusinessException) {
      BusinessException gex = BusinessException.class.cast(ex);
      // 设置状态码
      code = gex.getCode();
      msg = gex.getMessage();
      data = gex.getData();
      params = gex.getParams();
    } else {
      // 其他异常处理
      code = MessageCode.UN_KNOWN.getCode();
      // 日志记录
      log.error("异常code：{} - url: {} - param: {} -异常堆栈{}", code, request.getServletPath(), params, ex);
    }

    try {
      // 国际化处理
      Locale local = localeResolver.resolveLocale(request);

      // TODO默认采取中文????????????????????
      local = Locale.SIMPLIFIED_CHINESE;
      //  local = Locale.US;

      ResourceBundleMessageSource rbms = ResourceBundleMessageSourceFactory.get(local.toString());

      // 获取i18n信息
      msg = rbms.getMessage(String.valueOf(code), params, local);

    } catch (Exception e) {
      log.error("获取国际化message失败,loacl:{}, code:{}, {}", request.getLocale(), code, e);
      // 设置响应对象提示信息
      msg = "未在语言文件找到当前编码与对应的信息";
    }

    return RenderResult.build(code, msg, data);
  }
}
