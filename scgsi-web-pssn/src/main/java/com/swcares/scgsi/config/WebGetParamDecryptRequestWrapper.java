package com.swcares.scgsi.config;


import com.swcares.scgsi.util.AesEncryptUtil;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.IOException;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class WebGetParamDecryptRequestWrapper extends HttpServletRequestWrapper {
    private Map<String, String[]> decryptedParams;


    public WebGetParamDecryptRequestWrapper(HttpServletRequest request) throws IOException{
        super(request);
        decryptedParams = decryptParameters(request.getParameterMap());
    }




    private Map<String, String[]> decryptParameters(Map<String, String[]> params) {
        Map<String, String[]> decryptedParams = new HashMap<>();
        params.forEach((key, values) -> {
            String[] decryptedValues = new String[values.length];
            for (int i = 0; i < values.length; i++) {
                // 这里调用你的解密方法进行解密
                decryptedValues[i] = values[i]==null?values[i]:decryptParam(values[i]);
            }
            decryptedParams.put(key, decryptedValues);
        });
        return decryptedParams;
    }




    private String decryptParam(String param) {
        // 实现参数解密的逻辑
        param= AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY,param);
        // 返回解密后的参数
        return param; // 示例中直接返回原始参数，没有进行解密
    }

    @Override
    public String getParameter(String name) {
        if (decryptedParams == null) {
            decryptedParams = decryptParameters(super.getParameterMap());
        }
        String[] values = decryptedParams.get(name);
        return values != null && values.length > 0 ? values[0] : null;
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        if (decryptedParams == null) {
            decryptedParams = decryptParameters(super.getParameterMap());
        }
        return Collections.unmodifiableMap(decryptedParams);
    }

    @Override
    public Enumeration<String> getParameterNames() {
        return Collections.enumeration(decryptedParams.keySet());
    }

    @Override
    public String[] getParameterValues(String name) {
        return decryptedParams.get(name);
    }







}
