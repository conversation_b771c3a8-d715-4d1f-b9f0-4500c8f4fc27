package com.swcares.scgsi.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.swcares.scgsi.util.AesEncryptUtil;
import io.swagger.models.HttpMethod;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Map;

/**
 * 参数解密拦截器
 */
public class WebParamDecryptFilter implements Filter {


    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        String header = ((HttpServletRequest) servletRequest).getHeader("Content-Type");
        if (header!=null  &&  header.contains("multipart/form-data")) {
            filterChain.doFilter(servletRequest, servletResponse);
        } else {
            HttpServletRequestWrapper webParamDecryptRequestWrapper = null;
            String methodType = ((HttpServletRequest) servletRequest).getMethod();
            if (!(HttpMethod.OPTIONS.toString().equals(methodType))) {
                if (whiteList(((HttpServletRequest) servletRequest).getRequestURI())) {
                    filterChain.doFilter(servletRequest, servletResponse);
                }else if (methodType.equals("GET")) {
                    webParamDecryptRequestWrapper = new WebGetParamDecryptRequestWrapper((HttpServletRequest) servletRequest);
                } else if (servletRequest.getParameterMap().size() > 0) {
                    webParamDecryptRequestWrapper = new WebFormParamDecryptRequestWrapper((HttpServletRequest) servletRequest);
                } else {
                    try {
                        WebBodyParamDecryptRequestWrapper webBodyParamDecryptRequestWrapper = new WebBodyParamDecryptRequestWrapper((HttpServletRequest) servletRequest);
                        preHandle(webBodyParamDecryptRequestWrapper);
                        webParamDecryptRequestWrapper = webBodyParamDecryptRequestWrapper;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

            }

            filterChain.doFilter(webParamDecryptRequestWrapper == null ? servletRequest : webParamDecryptRequestWrapper, servletResponse);
        }
    }

    private boolean whiteList(String url) {
        String[] listRegularExpression = new String[]{
                "/api/uploadAndDownload"
        };
        String[] listUnequivocal = new String[]{

        };
        for (String u : listRegularExpression) {
            if (url.startsWith(u)) {
                return true;
            }
        }
        for (String u : listUnequivocal) {
            if (url.startsWith(u)) {
                return true;
            }
        }
        return false;
    }



    public void preHandle(WebBodyParamDecryptRequestWrapper request) throws Exception {
        //仅当请求方法为POST时修改请求体
        if (!request.getMethod().equalsIgnoreCase("POST")) {
            return;
        }
        //读取原始请求体
        StringBuilder originalBody = new StringBuilder();
        String line;
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()))) {
            while ((line = reader.readLine()) != null) {
                originalBody.append(line);
            }
        }
        String bodyText = originalBody.toString();
        String s = AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, bodyText);
        request.setBody(s);

    }

}
