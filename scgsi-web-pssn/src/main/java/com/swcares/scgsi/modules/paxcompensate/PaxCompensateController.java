package com.swcares.scgsi.modules.paxcompensate;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.custom.LogOperation;
import com.swcares.scgsi.overbook.dto.OrderPaxFreezeDto;
import com.swcares.scgsi.overbook.dto.PaxCompensateListQueryDto;
import com.swcares.scgsi.overbook.dto.PaxInfoQueryDto;
import com.swcares.scgsi.overbook.service.PaxCompensateService;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.modules.paxCompensate <br>
 * Description：H5-旅客赔偿模块 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月27日 10:28 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/h5/dp/mobile/paxCompensate")
@Api(tags = "H5-旅客赔偿")
public class PaxCompensateController extends BaseController {

    @Resource
    private PaxCompensateService paxCompensateService;

    /**
     * Title：getPaxCompensateList <br>
     * Description： H5-旅客赔偿-列表查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/27 10:31 <br>
     * @param  paxCompensateListQueryDto
     * @return
     */
    @ApiOperation(value = "列表查询")
    @GetMapping("getPaxCompensateList")
    public RenderResult getPaxCompensateList(@Valid PaxCompensateListQueryDto paxCompensateListQueryDto){
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        paxCompensateListQueryDto.setUserId(userId);
        return RenderResult.success(returnPageInfo(paxCompensateService.getPaxCompensateList(paxCompensateListQueryDto)));
    }

    /**
     * Title：queryAuditDetails <br>
     * Description： H5-旅客赔偿-赔偿单详情<br>
     * author：傅欣荣 <br>
     * date：2020/3/27 15:39 <br>
     * @param
     * @return
     */
    @ApiOperation(value = "赔偿单详情")
    @GetMapping("getPaxCompensateDetails")
    public RenderResult getPaxCompensateDetailsInfo(@ApiParam(name = "orderId", value = "赔偿单id") String orderId){
        return RenderResult.success(paxCompensateService.getPaxCompensateDetails(orderId));
    }
    
    /**
     * Title：getOrderPaxInfo <br>
     * Description： 获取赔偿单旅客列表<br>
     * author：傅欣荣 <br>
     * date：2020/3/30 9:58 <br>
     * @param  
     * @return 
     */
    @ApiOperation(value = "获取赔偿单旅客列表")
    @GetMapping("getOrderPaxInfo")
    public RenderResult getOrderPaxInfo(@Valid PaxInfoQueryDto paxInfoQueryDto){
        Map<String, Object> data = paxCompensateService.getOrderPaxInfo(paxInfoQueryDto);
        data.put("paxList",returnPageInfo((QueryResults)data.get("paxList")));
        return RenderResult.success(data);
    }

    /**
     * Title： getOrderPaxSegment<br>
     * Description： 根据赔偿单号，查询所选航段<br>
     * author：傅欣荣 <br>
     * date：2020/5/9 9:59 <br>
     * @param
     * @return
     */
    @GetMapping("getOrderPaxSegment")
    public RenderResult getOrderPaxSegment(String orderId){
        try {
            return RenderResult.success(paxCompensateService.getOrderPaxSegment(orderId));
        } catch (Exception e) {
            return RenderResult.fail();
        }
    }

    /**
     * Title：freezeOrderPax <br>
     * Description： 冻结服务单旅客<br>
     * author：傅欣荣 <br>
     * date：2020/3/30 14:29 <br>
     * @param
     * @return
     */
    @ApiOperation(value = "冻结旅客")
    @PostMapping("freezeOrderPax")
    @LogOperation(operationUrl="/api/h5/dp/mobile/paxCompensate/freezeOrderPax",remark="冻结旅客")
    public RenderResult freezeOrderPax(@Valid @RequestBody OrderPaxFreezeDto orderPaxFreezeDto){
        try {
            String[] paxIds = orderPaxFreezeDto.getPaxIds().split(",");
            paxCompensateService.freezeOrderPax(orderPaxFreezeDto.getOrderId(),paxIds);
            return RenderResult.success();
        } catch (Exception e) {
            return RenderResult.fail();
        }

    }

    /**
     * Title：unfreezeOrderPax <br>
     * Description： 解除冻结服务单旅客<br>
     * author：傅欣荣 <br>
     * date：2020/3/30 14:29 <br>
     * @param
     * @return
     */
    @ApiOperation(value = "解除冻结旅客")
    @PostMapping("unfreezeOrderPax")
    @LogOperation(operationUrl="/api/h5/dp/mobile/paxCompensate/unfreezeOrderPax",remark="解除冻结旅客")
    public RenderResult unfreezeOrderPax(@Valid @RequestBody OrderPaxFreezeDto orderPaxFreezeDto){
        try {
            String[] paxIds = orderPaxFreezeDto.getPaxIds().split(",");
            paxCompensateService.unfreezeOrderPax(orderPaxFreezeDto.getOrderId(),paxIds);
            return  RenderResult.success();
        } catch (Exception e) {
            return  RenderResult.fail();
        }
    }
    
}
