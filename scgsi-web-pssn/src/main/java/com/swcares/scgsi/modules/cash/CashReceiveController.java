package com.swcares.scgsi.modules.cash;

import com.swcares.scgsi.flight.dto.ApplyInfoDto;
import com.swcares.scgsi.flight.dto.ApplyQueryDto;
import com.swcares.scgsi.flight.service.CashReceiveService;
import com.swcares.scgsi.flight.service.PaxInfoService;
import com.swcares.scgsi.flight.service.TransactionLockService;
import com.swcares.scgsi.flight.vo.CashServiceOrderVo;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.modules.irregularflight <br>
 * Description：H5现金领取控制层 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月17日 16:15 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/h5/dp/mobile/cash")
@Api(tags = "现金领取接口")
public class CashReceiveController {
    @Resource
    private CashReceiveService cashReceiveService;

    @Resource
    private PaxInfoService paxInfoService;

    @Resource
    private TransactionLockService transactionLockServiceImpl;

    /**
     * Title：getDelayFlightInfo <br>
     * Description：现金领取航班信息查询<br>
     * author：王建文 <br>
     * date：2020-3-17 16:23 <br>
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     */
    @GetMapping("getDelayFlightInfo")
    @ApiOperation(value = "航班信息查询")
    public RenderResult getDelayFlightInfo(String flightNo, String flightDate) {
        return RenderResult.success(cashReceiveService.getDelayFlightInfo(flightNo, flightDate));
    }

    /**
     * Title：getOrderDetailInfo <br>
     * Description： 根据赔偿单号查看赔付详情<br>
     * author：王建文 <br>
     * date：2020-3-17 17:15 <br>
     *
     * @param orderId 赔偿单号
     */
    @GetMapping("getOrderDetailInfo")
    @ApiOperation(value = "根据赔偿单号查看赔付详情")
    public RenderResult getOrderDetailInfo(String orderId) {
        return RenderResult.success(cashReceiveService.getCashOrderDetail(orderId));
    }

    /**
     * Title：getSegmentInfo <br>
     * Description：获取航班对应下的航段<br>
     * author：王建文 <br>
     * date：2020-3-17 19:38 <br>
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     */
    @GetMapping("getSegmentInfo")
    @ApiOperation(value = "获取航班对应下的航段")
    public RenderResult getSegmentInfo(String flightNo, String flightDate) {
        return RenderResult.success(cashReceiveService.getFlightSegment(flightNo, flightDate));
    }

    /**
     * Title：getPaxInfo <br>
     * Description：旅客领取列表查询<br>
     * author：王建文 <br>
     * date：2020-3-17 19:55 <br>
     *
     * @param applyQueryDto 参数接收
     */
    @GetMapping("getPaxInfo")
    @ApiOperation(value = "旅客领取列表查询")
    public RenderResult getPaxInfo(ApplyQueryDto applyQueryDto) {
        return RenderResult.success(paxInfoService.getPaxApplyInfo(applyQueryDto));
    }

    @GetMapping("getServiceOrderInfo")
    @ApiOperation(value = "查看赔付单")
    public RenderResult getServiceOrderInfo(String flightNo, String flightDate, String paxId) {
        List<CashServiceOrderVo> cashServiceOrderVoList = cashReceiveService.getPaxServiceOrder(flightNo, flightDate, paxId);
        return RenderResult.success(cashServiceOrderVoList);
    }

    /**
     * Title：getApplyInfo <br>
     * Description：根据旅客id获取领取信息<br>
     * author：王建文 <br>
     * date：2020-3-18 10:28 <br>
     *
     * @param paxId 旅客id
     */
    @GetMapping("getApplyInfo")
    @ApiOperation(value = "根据旅客id获取领取信息")
    public RenderResult getApplyInfo(String paxId) {
        return RenderResult.success(paxInfoService.getApplyRecordInfo(paxId));
    }

    /**
     * Title：saveApplyInfo <br>
     * Description：保存现金申领提交信息<br>
     * author：王建文 <br>
     * date：2020-3-18 11:05 <br>
     *
     * @param applyInfoDto 申领信息参数接收
     */
    @PostMapping("saveApplyInfo")
    @ApiOperation(value = "保存现金申领提交信息")
    public RenderResult saveApplyInfo(@RequestBody ApplyInfoDto applyInfoDto) {
        transactionLockServiceImpl.saveCashApplyInfo(applyInfoDto);
        return RenderResult.success();
    }

}