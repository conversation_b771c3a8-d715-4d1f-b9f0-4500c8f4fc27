package com.swcares.scgsi.modules.user;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.swcares.scgsi.common.model.view.LoginView;
import com.swcares.scgsi.service.ResourcesService;
import com.swcares.scgsi.user.common.model.form.ForgetPasswordForm;
import com.swcares.scgsi.user.common.model.form.UpdatePasswordForm;
import com.swcares.scgsi.user.service.KaptchaService;
import com.swcares.scgsi.user.service.UserService;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.web.RenderResult;

/**
 * ClassName：com.swcares.scgsi.login.controller.LoginController <br>
 * Description：用户登录接口 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月12日 上午10:00:04 <br>
 * @version v1.0 <br>
 */
@Api(tags = "用户登录相关接口")
@RestController
@RequestMapping("/api/h5/login")
public class LoginController {
    @Autowired
    private UserService userService;

    @Autowired
    private KaptchaService kaptchaService;

    @Autowired
    private ResourcesService resourcesService;

    /**
     * Title：tokenLoginByH5 <br>
     * Description：H5单点登录方法 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:46:38 <br>
     * @param token H5登录后传入的token
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "单点登录")
    @PostMapping("/tokenLoginByH5")
    public RenderResult<Object> tokenLoginByH5(@ApiParam(name = "token", value = "token值",
            required = true) @RequestParam(name = "token") String token) throws Exception {
        Assert.notNull(token, "token值不能为空!");
        LoginView<Object> loginView = userService.authTokenForH5(token);// 系统账号
        return RenderResult.success(loginView);
    }

    /**
     * Title：getKaptchaImage <br>
     * Description：获取图形验证码方法 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:47:23 <br>
     * @param response
     * @param request
     * @throws Exception <br>
     */
    @GetMapping("/getCodeImage")
    @ApiOperation(value = "获取验证码图片", notes = "获取验证码图片")
    public void getKaptchaImage(HttpServletResponse response, HttpServletRequest request)
            throws Exception {
        kaptchaService.generateVerifyCodeImage(request, response);
    }

    /**
     * Title：updatePassword <br>
     * Description：用户修改密码方法 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:47:43 <br>
     * @param updatePasswordForm 修改密码的form
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "修改密码")
    @PostMapping("/updatePassword")
    public RenderResult<Object> updatePassword(
            @Valid @RequestBody UpdatePasswordForm updatePasswordForm) throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String userId = (String) auth.getCredentials();
        updatePasswordForm.setUserId(userId);
        userService.updatePassword(updatePasswordForm);
        return RenderResult.success();
    }

    /**
     * Title：forgetPassword <br>
     * Description：忘记密码方法 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:48:12 <br>
     * @param forgetPasswordForm 忘记密码修改的form
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "忘记密码")
    @PostMapping("/forgetPassword")
    public RenderResult<Object> forgetPassword(HttpServletRequest request,
            @Valid @RequestBody ForgetPasswordForm forgetPasswordForm) throws Exception {
        userService.forgetPassword(request,forgetPasswordForm.getUserName(),
                forgetPasswordForm.getPhoneNum(), forgetPasswordForm.getSmsCode(),
                forgetPasswordForm.getPassword(), forgetPasswordForm.getType());
        return RenderResult.success();
    }

    /**
     * Title：logOut <br>
     * Description：用户登出方法 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:49:32 <br>
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "登出")
    @GetMapping("/logOut")
    public RenderResult<Object> logOut() throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String userId = (String) auth.getCredentials();
        userService.logOut(userId);
        return RenderResult.success();
    }

}
