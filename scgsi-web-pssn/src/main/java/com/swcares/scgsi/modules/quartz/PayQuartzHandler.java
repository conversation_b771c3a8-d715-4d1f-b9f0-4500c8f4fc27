package com.swcares.scgsi.modules.quartz;

import cn.hutool.json.JSONUtil;
import com.swcares.scgsi.aop.EncryptFieldAop;
import com.swcares.scgsi.encryption.AllowPaymentNode;
import com.swcares.scgsi.flight.dao.AoTransRecordDao;
import com.swcares.scgsi.flight.dao.impl.PaxReceiveDaoImpl;
import com.swcares.scgsi.flight.entity.AoTransRecord;
import com.swcares.scgsi.flight.entity.ApplyOrderInfo;
import com.swcares.scgsi.flight.service.ApplyOrderQuartzService;
import com.swcares.scgsi.flight.service.PaxInfoService;
import com.swcares.scgsi.service.wx.WxPayService;
import com.swcares.scgsi.service.wx.WxTransactionType;
import com.swcares.scgsi.service.wx.bean.WxQueryResult;
import com.swcares.scgsi.service.wx.bean.WxTransferOrder;
import com.swcares.scgsi.service.wx.bean.WxTransferResult;
import com.swcares.scgsi.service.yee.YeePayService;
import com.swcares.scgsi.service.yee.YeeTransactionType;
import com.swcares.scgsi.service.yee.bean.YeeQueryResult;
import com.swcares.scgsi.service.yee.bean.YeeQueryResultOrder;
import com.swcares.scgsi.service.yee.bean.YeeTransferOrder;
import com.swcares.scgsi.service.yee.bean.YeeTransferResult;
import com.swcares.scgsi.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.modules.quartz <br>
 * Description：航延支付定时任务处理 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 04月07日 16:51 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class PayQuartzHandler {
    @Resource
    private WxPayService wxPayService;
    @Resource
    private YeePayService yeePayService;
    @Resource
    private ApplyOrderQuartzService applyOrderQuartzService;
    @Resource
    private AoTransRecordDao aoTransRecordDao;
    @Resource
    private PaxInfoService paxInfoService;
    //支付备注
    private static final String PAY_REMARK = "山航补偿金";
    //微信
    private static final String PAY_TYPE_WEIXIN = "0";
    //微信支付成功标识
    private static final String PAY_SUCCESS = "SUCCESS";
    //微信查询返回的转账状态-处理中
    private static final String PAY_PROCESSING = "PROCESSING";
    //微信查询返回的转账状态-失败
    private static final String PAY_FAILED = "FAILED";
    //微信支付返回结果-失败
    private static final String PAY_FAIL = "FAIL";
    //易宝
    private static final String PAY_TYPE_YEE = "1";

    //银行处理状态 出款已成功到账 s 	I 出款已发至银行，银行正在处理
    private static final String YEE_BANK_STATUS="S";
    //银行处理状态
    private static final String YEE_TRANSFER_STATUS="0026";

    //支付状态 - 0-未支付
    private static final String PAY_STATUS_UNPAID = "0";
    //支付状态 - 1-已支付
    private static final String PAY_STATUS_PAID = "1";
    //支付状态 - 2-支付失败
    private static final String PAY_STATUS_FAIL = "2";
    //支付状态 - 3-支付处理中
    private static final String PAY_STATUS_INPROCESS = "3";

    //领取状态 - 0未领取
    private static final String RECEIVE_STATUS_UNPAID = "0";
    //领取状态 - 1已领取
    private static final String RECEIVE_STATUS_PAID = "1";

    //防止定时任务产生的并发，加锁
    private final String PAY_LOCK_NAME = "PAY_QUARTZ_HANDLER_LOCK";

    //以下条件认为是失败：姓名校验出错、余额不足、无法给未实名用户付款、今日付款次数超过限制、没有该接口权限、用户账户收款异常、已经达到今日付款总额上限/已达到付款给此用户额度上限
    String errorCode = "NAME_MISMATCH*NOTENOUGH*V2_ACCOUNT_SIMPLE_BAN*SEND_MONEY_LIMIT*NO_AUTH*PAYEE_ACCOUNT_ABNORMAL*MONEY_LIMIT";

    @Value("${spring.profiles.active}")
    private String profiles;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private PayQuartzHandler payQuartzHandler;

    @AllowPaymentNode
    public void payHandler() {
        log.info("------->>>航延补偿支付定时任务开始");
        RLock lock = redisson.getLock(PAY_LOCK_NAME);
        if(lock.tryLock()){
            try{
                payQuartzHandler.doHandler();
            }catch (Exception e){
                log.error("航延补偿支付定时任务异常，信息：", e);
            }finally {
                lock.unlock();
            }
        }
        log.info("------->>>航延补偿支付定时任务结束");
    }


    @Transactional(rollbackFor = Exception.class)
    public void doHandler() {
        //查询待支付订单 不正常航班模块
        List<ApplyOrderInfo> dataList = applyOrderQuartzService.findUnpaidOrderInfo();
        log.info("------->>>航延补偿支付定时任务开始，查询待支付订单共计{}条", dataList.size());
        if (dataList.size() < 1) {
            log.info("------->>>航延补偿支付定时任务结束，查询无待支付订单！！！");
            return;
        }
        for (ApplyOrderInfo orderInfo : dataList) {
            log.info("------->>>航延补偿支付定时任务，开始处理订单-订单号：{}, 申领人：{}，领取方式(0微信，1银联,2现金)：{}， 申领单支付状态(0未支付,1已支付,2支付失败3处理中)：{}", orderInfo.getApplyCode(), orderInfo.getApplyUser(), orderInfo.getGetMoneyWay(), orderInfo.getPayStatus());

            //测试环境修改金额
            if (profiles.equals("dev")) orderInfo.setTransAmount("1");

            AoTransRecord aoTransRecord = new AoTransRecord();
            aoTransRecord.setApplyCode(orderInfo.getApplyCode());
            aoTransRecord.setApplyAccount(orderInfo.getGetMoneyAccount());
            aoTransRecord.setPayType(orderInfo.getGetMoneyWay());
            aoTransRecord.setPayStartTime(new Date());
            aoTransRecord.setCreateTime(new Date());
            String payDate = ObjectUtils.isNotEmpty(orderInfo.getPayDate()) ? DateUtils.parseDateToStr(orderInfo.getPayDate(), DateUtils.YYYY_MM_DD_HH_MM_SS) :
                    DateUtils.parseDateToStr(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS);
            if (profiles.contains("pro")) {
                // 当前申领单在支付平台是未支付 && 当前申领单状态为未支付
                if (!confirmHasPaid(aoTransRecord, orderInfo) && PAY_STATUS_UNPAID.equals(orderInfo.getPayStatus())) {
                    log.info("航延补偿支付定时任务,调起支付接口,订单号：{}", orderInfo.getApplyCode());
                    conductPayment(aoTransRecord, orderInfo);
                }
            } else {
                log.info("航延补偿支付定时任务,测试环境模拟支付数据");
                aoTransRecord.setPayReturnTime(new Date());
                aoTransRecord.setReturnOrderNo("123");
                aoTransRecord.setTransCode(PAY_SUCCESS);
                aoTransRecord.setTransMsg("Test");
                aoTransRecord.setErrCode("Test");
                aoTransRecord.setErrCodeDes("Test");
                aoTransRecord.setTransAmount("0.1");
                aoTransRecord.setPayStatus(PAY_STATUS_PAID);
            }

            String receiveTime = DateUtils.parseDateToStr(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS);
            if (ObjectUtils.isNotEmpty(aoTransRecord.getPayReturnTime())) {
                receiveTime = DateUtils.parseDateToStr(aoTransRecord.getPayReturnTime(), DateUtils.YYYY_MM_DD_HH_MM_SS);
            }

            if(PAY_STATUS_PAID.equals(aoTransRecord.getPayStatus())){
                //1.成功的处理逻辑（1.支付记录表  2.申领单表的申领状态 - 已领取  3.赔偿单的旅客表状态 - 已领取）
                handlerSuccess(orderInfo, aoTransRecord, payDate, receiveTime);
            }else if(PAY_STATUS_FAIL.equals(aoTransRecord.getPayStatus())){
                //2.失败的逻辑处理（1.支付记录表(要考虑线上已有数据，所以区分第一次失败还是后面多次的失败，第一次失败才插入数据)  2.申领单表的申领状态 - 失败  3.赔偿单的旅客表状态 - 待领取）
                handlerFailed(orderInfo, aoTransRecord, payDate, receiveTime);
            }else if(PAY_STATUS_INPROCESS.equals(aoTransRecord.getPayStatus())){
                // 3.处理中的逻辑（1.支付记录表（针对首次支付时，支付状态是处理中，此时应该添加一天记录，其余不用加）  2.申领单表的申领状态 - 处理中 3.赔偿单的旅客表状态不用更新 - 表里面已经是领取中，此处不改，领取中的状态是建申领单的时候写进去的）
                handlerProcessing(orderInfo, aoTransRecord, payDate, receiveTime);
            }

            log.info("------->>>航延补偿支付定时任务，处理订单结束-订单号：{}", orderInfo.getApplyCode());
        }
    }

    private void handlerSuccess(ApplyOrderInfo orderInfo, AoTransRecord aoTransRecord, String payDate, String receiveTime){
        String receiveStatus = RECEIVE_STATUS_PAID;
        log.info("------->>>航延补偿支付定时任务【SUCCESS】，正在执行【添加DP_AO_TRANS_RECORD】- 申领订单号：{}，ao转账表的支付状态：{}",
                orderInfo.getApplyCode(), aoTransRecord.getPayStatus());
        EncryptFieldAop.manualEncrypt(aoTransRecord);
        AoTransRecord transRecord = aoTransRecordDao.save(aoTransRecord);
        log.info("------->>>航延补偿支付定时任务【SUCCESS】，【添加DP_AO_TRANS_RECORD】完毕，执行结果【{}】- 申领订单号：{}，ao转账表的支付状态：{}",
                JSONUtil.toJsonStr(transRecord), orderInfo.getApplyCode(), aoTransRecord.getPayStatus());

        //更新支付表状态:更新DP_APPLY_ORDER
        log.info("------->>>航延补偿支付定时任务【SUCCESS】，正在执行【更新DP_APPLY_ORDER】- 申领订单号：{}，ao转账表的支付状态：{},支付时间{}",
                orderInfo.getApplyCode(), aoTransRecord.getPayStatus(), receiveTime);
        int applyOrderCount = applyOrderQuartzService.updApplyOrderStatus(aoTransRecord.getApplyCode(),
                aoTransRecord.getPayStatus(), payDate, receiveTime);
        log.info("------->>>航延补偿支付定时任务【SUCCESS】，【更新DP_APPLY_ORDER】完毕，修改条数【{}】 - 申领订单号：{}，ao转账表的支付状态：{},支付时间{}",
                applyOrderCount, orderInfo.getApplyCode(), aoTransRecord.getPayStatus(), receiveTime);

        //更新赔偿单的旅客表：更新DP_PAX_INFO
        log.info("------->>>航延补偿支付定时任务【SUCCESS】，正在执行【更新DP_PAX_INFO】- 申领订单号：{}，ao转账表的支付状态：{},支付时间{}",
                orderInfo.getApplyCode(), receiveStatus, receiveTime);
        int count = applyOrderQuartzService.updOrderPaxStatus(aoTransRecord.getApplyCode(), receiveStatus,  receiveTime, true);
        log.info("------->>>航延补偿支付定时任务【SUCCESS】，【更新DP_PAX_INFO】完毕，修改条数【{}】- 申领订单号：{}，ao转账表的支付状态：{},支付时间{}",
                count, orderInfo.getApplyCode(), receiveStatus, receiveTime);
    }

    private void handlerFailed(ApplyOrderInfo orderInfo, AoTransRecord aoTransRecord, String payDate, String receiveTime){
        String receiveStatus = RECEIVE_STATUS_UNPAID;
        //申领单的状态是 失败  并且  查询微信最新支付结果也是失败 (return 的原因是，已经有一条失败的记录了，不需要再多次插入)
        if(PAY_STATUS_FAIL.equals(orderInfo.getPayStatus()) && PAY_STATUS_FAIL.equals(aoTransRecord.getPayStatus())) return;
        log.info("------->>>航延补偿支付定时任务【FAILED】，正在执行【添加DP_AO_TRANS_RECORD】- 申领订单号：{}，ao转账表的支付状态：{}",
                orderInfo.getApplyCode(), aoTransRecord.getPayStatus());
        EncryptFieldAop.manualEncrypt(aoTransRecord);
        AoTransRecord transRecord = aoTransRecordDao.save(aoTransRecord);
        log.info("------->>>航延补偿支付定时任务【FAILED】，【添加DP_AO_TRANS_RECORD】完毕，执行结果【{}】- 申领订单号：{}，ao转账表的支付状态：{}",
                JSONUtil.toJsonStr(transRecord), orderInfo.getApplyCode(), aoTransRecord.getPayStatus());

        //更新支付表状态:更新DP_APPLY_ORDER
        log.info("------->>>航延补偿支付定时任务【FAILED】，正在执行【更新DP_APPLY_ORDER】- 申领订单号：{}，ao转账表的支付状态：{},支付时间{}",
                orderInfo.getApplyCode(), aoTransRecord.getPayStatus(), receiveTime);
        int applyOrderCount = applyOrderQuartzService.updApplyOrderStatus(aoTransRecord.getApplyCode(), aoTransRecord.getPayStatus(), payDate, receiveTime);
        log.info("------->>>航延补偿支付定时任务【FAILED】，【更新DP_APPLY_ORDER】完毕，修改条数【{}】 - 申领订单号：{}，ao转账表的支付状态：{},支付时间{}",
                applyOrderCount, orderInfo.getApplyCode(), aoTransRecord.getPayStatus(), receiveTime);

        //更新赔偿单的旅客表：更新DP_PAX_INFO
        log.info("------->>>航延补偿支付定时任务【FAILED】，正在执行【更新DP_PAX_INFO】- 申领订单号：{}，ao转账表的支付状态：{},支付时间{}",
                orderInfo.getApplyCode(), receiveStatus, receiveTime);
        int count = applyOrderQuartzService.updOrderPaxStatus(aoTransRecord.getApplyCode(), receiveStatus,
                receiveTime, true);
        log.info("------->>>航延补偿支付定时任务【FAILED】，【更新DP_PAX_INFO】完毕，修改条数【{}】- 申领订单号：{}，ao转账表的支付状态：{},支付时间{}",
                count, orderInfo.getApplyCode(), receiveStatus, receiveTime);
    }

    private void handlerProcessing(ApplyOrderInfo orderInfo, AoTransRecord aoTransRecord, String payDate, String receiveTime){
        // 支付中 && 申领单状态是未支付【这样做是区分是否第一次支付中，不这样做ao表就没有多余转账记录了】
        if(PAY_STATUS_INPROCESS.equals(aoTransRecord.getPayStatus()) && PAY_STATUS_UNPAID.equals(orderInfo.getPayStatus())) {
            log.info("------->>>航延补偿支付定时任务【PROCESSING】，正在执行【添加DP_AO_TRANS_RECORD】- 申领订单号：{}，ao转账表的支付状态：{}",
                    orderInfo.getApplyCode(), aoTransRecord.getPayStatus());
            EncryptFieldAop.manualEncrypt(aoTransRecord);
            AoTransRecord transRecord = aoTransRecordDao.save(aoTransRecord);
            log.info("------->>>航延补偿支付定时任务【PROCESSING】，【添加DP_AO_TRANS_RECORD】完毕，执行结果【{}】- 申领订单号：{}，ao转账表的支付状态：{}",
                    JSONUtil.toJsonStr(transRecord), orderInfo.getApplyCode(), aoTransRecord.getPayStatus());
        }

        //更新支付表状态:更新DP_APPLY_ORDER
        log.info("------->>>航延补偿支付定时任务【PROCESSING】，正在执行【更新DP_APPLY_ORDER】- 申领订单号：{}，ao转账表的支付状态：{},支付时间{}",
                orderInfo.getApplyCode(), aoTransRecord.getPayStatus(), receiveTime);
        int applyOrderCount = applyOrderQuartzService.updApplyOrderStatus(aoTransRecord.getApplyCode(),
                aoTransRecord.getPayStatus(), payDate, receiveTime);
        log.info("------->>>航延补偿支付定时任务【PROCESSING】，【更新DP_APPLY_ORDER】完毕，修改条数【{}】 - 申领订单号：{}，ao转账表的支付状态：{},支付时间{}",
                applyOrderCount, orderInfo.getApplyCode(), aoTransRecord.getPayStatus(), receiveTime);

    }

    /**
     * Title： confirmHasPaid<br>
     * Description： 查询订单支付记录 <br>
     * author：傅欣荣 <br>
     * date：2020/4/7 20:18 <br>
     *
     * @param
     * @return
     */
    private boolean confirmHasPaid(AoTransRecord aoTransRecord, ApplyOrderInfo applyOrderInfo) {
        log.info("------->>>航延补偿支付定时任务，正在执行【查询】-订单号：{}，支付类型：{}",
                applyOrderInfo.getApplyCode(), applyOrderInfo.getGetMoneyWay());
        boolean flag = false;
        if (PAY_TYPE_WEIXIN.equals(applyOrderInfo.getGetMoneyWay())) {
            WxQueryResult wxQueryResult = (WxQueryResult) wxPayService.query(null, applyOrderInfo.getApplyCode(),
                    WxTransactionType.TRANSFERS_QUERY, WxQueryResult.class);
            log.info("------->>>航延补偿支付定时任务，正在执行【查询】-订单号：{}，支付类型：{}，查询反馈结果{}",
                    applyOrderInfo.getApplyCode(), applyOrderInfo.getGetMoneyWay(), wxQueryResult.toString());

            //支付成功的判断逻辑： status == SUCCESS
            if (PAY_SUCCESS.equalsIgnoreCase(wxQueryResult.getStatus())) {
                aoTransRecord.setPayStatus(PAY_STATUS_PAID);
                flag = true;

                //支付中的判断逻辑： status == PAY_PROCESSING
            }else if(PAY_PROCESSING.equalsIgnoreCase(wxQueryResult.getStatus())){
                aoTransRecord.setPayStatus(PAY_STATUS_INPROCESS);

                //支付失败的判断逻辑： status == FAILED
            }else if(PAY_FAILED.equalsIgnoreCase(wxQueryResult.getStatus())){
                aoTransRecord.setPayStatus(PAY_STATUS_FAIL);
            }

            encapsulatedByWeiXin(aoTransRecord, wxQueryResult);
        }

        if (PAY_TYPE_YEE.equals(applyOrderInfo.getGetMoneyWay())) {
            YeeQueryResult yeeQueryResult = yeePayService.query(applyOrderInfo.getApplyCode(), applyOrderInfo.getApplyCode(),
                    YeeTransactionType.TRANSFER_QUERY, YeeQueryResult.class);

            log.info("------->>>航延补偿支付定时任务，正在执行【查询】-订单号：{}，支付类型：{}，查询反馈结果{}",
                    applyOrderInfo.getApplyCode(), applyOrderInfo.getGetMoneyWay(), yeeQueryResult.toString());

            //默认处理中状态
            aoTransRecord.setPayStatus(PAY_STATUS_INPROCESS);

            List<YeeQueryResultOrder> yeeList = yeeQueryResult.getList();
            if (null != yeeList && yeeList.size() > 0) {
                if(yeeList.size() > 1){
                    //为什么只拿第一个？？？？ 因为业务是一笔笔支付的，所以只有一条.
                    log.error("------->>>航延补偿支付定时任务，易宝查询支付结果大于1条，理论上不会，需要验证，支付返回结果【{}】", JSONUtil.toJsonStr(yeeQueryResult));
                }

                // 易宝查询支付结果判断： https://open.yeepay.com/docs/products/dfdf/others/5e940886a8e9ea001ac6d0e2
                YeeQueryResultOrder yeeQueryResultOrder = yeeList.get(0);
                if (PAY_SUCCESS.equalsIgnoreCase(yeeQueryResult.getState())
                        && YEE_TRANSFER_STATUS.equalsIgnoreCase(yeeQueryResultOrder.getTransferStatusCode())
                        && YEE_BANK_STATUS.equalsIgnoreCase(yeeQueryResultOrder.getBankTrxStatusCode())) {
                    aoTransRecord.setPayStatus(PAY_STATUS_PAID);
                    flag = true;
                }
                aoTransRecord.setTransCode(yeeQueryResult.getErrorCode());
                encapsulatedByYee(aoTransRecord, yeeQueryResultOrder);
            }

            //需要给出哪些是失败 @TODO
        }
        return flag;
    }


    /**
     * Title：conductPayment <br>
     * Description：进行支付处理 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 15:20 <br>
     *
     * @param aoTransRecord
     * @param applyOrderInfo
     * @return
     */
    private void conductPayment(AoTransRecord aoTransRecord, ApplyOrderInfo applyOrderInfo) {
        log.info("------->>>航延补偿支付定时任务，正在执行【支付】-订单号：{}，支付类型：{}",
                applyOrderInfo.getApplyCode(), applyOrderInfo.getGetMoneyWay());
        if (PAY_TYPE_WEIXIN.equals(applyOrderInfo.getGetMoneyWay())) {
            payWeiXinHandler(aoTransRecord, applyOrderInfo);
        }

        if (PAY_TYPE_YEE.equals(applyOrderInfo.getGetMoneyWay())) {
            payYeeHandler(aoTransRecord, applyOrderInfo);
        }

    }


    /**
     * Title：payWeiXinHandler <br>
     * Description： 微信支付逻辑处理 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 18:57 <br>
     *
     * @param
     * @return
     */
    private void payWeiXinHandler(AoTransRecord aoTransRecord, ApplyOrderInfo applyOrderInfo) {
        WxTransferOrder tranOder = new WxTransferOrder();
        tranOder.setOutNo(applyOrderInfo.getApplyCode());
        tranOder.setAmount(new BigDecimal(applyOrderInfo.getTransAmount()));
        tranOder.setPayeeAccount(applyOrderInfo.getGetMoneyAccount());
        tranOder.setPayeeName(applyOrderInfo.getApplyUser());
        tranOder.setRemark(PAY_REMARK);
        tranOder.setTransactionType(WxTransactionType.TRANSFERS);
        log.info("------->>>航延补偿支付定时任务，【微信渠道】进行打款支付，正在执行【支付】-订单号：【{}】，支付类型：【{}】，发起请求的参数【{}】",
                applyOrderInfo.getApplyCode(), applyOrderInfo.getGetMoneyWay(), tranOder.toString());
        WxTransferResult transferResult = (WxTransferResult) wxPayService.transfer(tranOder, WxTransferResult.class);
        log.info("------->>>航延补偿支付定时任务，【微信渠道】进行打款支付，正在执行【支付】-订单号：【{}】，支付类型：【{}】，支付反馈结果【{}】",
                applyOrderInfo.getApplyCode(), applyOrderInfo.getGetMoneyWay(), transferResult.toString());
        //支付结果默认是处理中
        String payStatus = PAY_STATUS_INPROCESS;

        //参考支付文档：https://pay.weixin.qq.com/wiki/doc/api/tools/mch_pay.php?chapter=14_2
        //支付【成功】的判断逻辑： return_code == SUCCESS && result_code == SUCCESS
        if (PAY_SUCCESS.equalsIgnoreCase(transferResult.getResult_code())
                && PAY_SUCCESS.equalsIgnoreCase(transferResult.getReturn_code())) {
            aoTransRecord.setPayReturnTime(DateUtils.parseStrToDate(
                    transferResult.getPayment_time(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            aoTransRecord.setReturnOrderNo(transferResult.getPayment_no());
            //支付状态设置为已支付
            payStatus = PAY_STATUS_PAID;
        }

        //失败 return_code == SUCCESS && result_code == FAIL && errCode包含以上
        if (PAY_FAIL.equalsIgnoreCase(transferResult.getResult_code())
                && PAY_SUCCESS.equalsIgnoreCase(transferResult.getReturn_code())
                && errorCode.indexOf(transferResult.getErr_code().toUpperCase()) > -1) {
            aoTransRecord.setPayReturnTime(DateUtils.parseStrToDate(
                    transferResult.getPayment_time(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            //支付状态设置为已支付
            payStatus = PAY_STATUS_FAIL;

        }
        // 对支付中和部分支付失败（不知道是真失败，还是假失败）的逻辑不做处理，由定时任务去查询最新支付状态时做处理
        // @See PayQuartzHandler#confirmHasPaid

        aoTransRecord.setTransCode(transferResult.getReturn_code());
        aoTransRecord.setTransMsg(transferResult.getReturn_msg());
        aoTransRecord.setErrCode(transferResult.getErr_code());
        aoTransRecord.setErrCodeDes(transferResult.getErr_code_des());
        aoTransRecord.setTransAmount(applyOrderInfo.getTransAmount());
        aoTransRecord.setPayStatus(payStatus);
    }


    /**
     * Title：payYeeHandler <br>
     * Description： 易宝支付逻辑处理 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 18:57 <br>
     *
     * @param
     * @return
     */
    private void payYeeHandler(AoTransRecord aoTransRecord, ApplyOrderInfo applyOrderInfo) {
        YeeTransferOrder yeeTransferOrder = new YeeTransferOrder();
        yeeTransferOrder.setBatchNo(applyOrderInfo.getApplyCode());
        yeeTransferOrder.setOrderId(applyOrderInfo.getApplyCode());
        yeeTransferOrder.setAmount(new BigDecimal(applyOrderInfo.getTransAmount()));
        yeeTransferOrder.setAccountNumber(applyOrderInfo.getGetMoneyAccount());
        yeeTransferOrder.setAccountName(applyOrderInfo.getApplyUser());
        yeeTransferOrder.setBankCode(applyOrderInfo.getBankCode());
        yeeTransferOrder.setDesc(PAY_REMARK);
        yeeTransferOrder.setTransactionType(YeeTransactionType.TRANSFER_SEND);
        // 转账代付接口地址： https://open.yeepay.com/docs/apis/dfdf/options__rest__v1.0__balance__transfer_query
        YeeTransferResult yeeTransferResult = yeePayService.transfer(yeeTransferOrder, YeeTransferResult.class);
        log.info("------->>>航延补偿支付定时任务，正在执行【支付】-订单号：{}，支付类型：{}，支付反馈结果{}",
                applyOrderInfo.getApplyCode(), applyOrderInfo.getGetMoneyWay(), yeeTransferResult.toString());
        aoTransRecord.setTransCode(yeeTransferResult.getTransferStatusCode());
        aoTransRecord.setPayStatus(PAY_STATUS_INPROCESS);
        aoTransRecord.setTransAmount(applyOrderInfo.getTransAmount());
    }

    /**
     * Title： encapsulatedByWeiXin <br>
     * Description：封装转账记录表对象-微信反馈<br>
     * author：傅欣荣 <br>
     * date：2020/4/7 18:37 <br>
     *
     * @param
     * @return
     */
    private void encapsulatedByWeiXin(AoTransRecord aoTransRecord, WxQueryResult wxQueryResult) {
        if(StringUtils.isNotBlank(wxQueryResult.getPayment_amount())){
            int transAmount=Integer.valueOf(wxQueryResult.getPayment_amount())/100;
            aoTransRecord.setTransAmount(String.valueOf(transAmount));
        }
        aoTransRecord.setPayReturnTime(DateUtils.parseStrToDate(
                wxQueryResult.getPayment_time(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        aoTransRecord.setReturnOrderNo(wxQueryResult.getDetail_id());
        aoTransRecord.setTransCode(wxQueryResult.getReturn_code());
        aoTransRecord.setTransMsg(wxQueryResult.getReturn_msg());
        aoTransRecord.setErrCode(wxQueryResult.getErr_code());
        aoTransRecord.setErrCodeDes(wxQueryResult.getErr_code_des());

    }


    /**
     * Title： encapsulatedByYee <br>
     * Description： 封装转账记录表对象-易宝反馈<br>
     * author：傅欣荣 <br>
     * date：2020/4/7 18:37 <br>
     *
     * @param
     * @return
     */
    private void encapsulatedByYee(AoTransRecord aoTransRecord, YeeQueryResultOrder yeeQueryResultOrder) {
        aoTransRecord.setTransCode(yeeQueryResultOrder.getTransferStatusCode());
        aoTransRecord.setTransSubCode(yeeQueryResultOrder.getBankTrxStatusCode());
        aoTransRecord.setTransSubMsg(yeeQueryResultOrder.getBankMsg());
        aoTransRecord.setTransAmount(yeeQueryResultOrder.getAmount());
        aoTransRecord.setPayReturnTime(DateUtils.parseStrToDate(
                yeeQueryResultOrder.getFinishDate(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        aoTransRecord.setFeeAmount(yeeQueryResultOrder.getFee());

    }
}
