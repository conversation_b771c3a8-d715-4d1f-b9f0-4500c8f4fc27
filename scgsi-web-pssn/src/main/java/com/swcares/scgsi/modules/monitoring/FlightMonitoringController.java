package com.swcares.scgsi.modules.monitoring;

import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.common.model.VO.FlightMonitoringVO;
import com.swcares.scgsi.common.model.form.ContentTraceForm;
import com.swcares.scgsi.common.model.form.MobileFlightInfoListForm;
import com.swcares.scgsi.common.model.form.TraceQueryPassengerForm;
import com.swcares.scgsi.common.model.view.FlightMonitoringView;
import com.swcares.scgsi.common.model.view.FocFlightInfoDetailView;
import com.swcares.scgsi.entity.FocFlightInfo;
import com.swcares.scgsi.service.FlightInfoService;
import com.swcares.scgsi.service.TraceService;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FlightMonitoring <br>
 * Package：com.swcares.scgsi.modules.monitoring <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月12日 15:42 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping(path = "/api/h5/monitoring")
@Api(tags = "H5航班监控模块")
@Slf4j
public class FlightMonitoringController extends BaseController {

    @Autowired
    private FlightInfoService flightInfoService;

    @Autowired
    private TraceService traceService;
    /**
     * Title：getDepartmentTree() <br>
     * Description：获取航班监控列表 <br>
     * author：于琦海 <br>
     * date：2020/3/20 10:37 <br>
     * @return RenderResult<Object>
     */
    @ApiOperation(value = "获取航班监控列表")
    @PostMapping("/getFlightInfoList")
    public RenderResult<Object> getFlightInfoList(@Valid MobileFlightInfoListForm form){
        return RenderResult.success(super.returnPageInfo(flightInfoService.getH5FlightInfoList(form)));
    }

    /**
     * Title：getFlightCountInfo() <br>
     * Description： <br>
     * author：于琦海 <br>
     * date：2020/4/17 14:27 <br>
     * @param form MobileFlightInfoListForm
     * @return FlightMonitoringView
     */
    @ApiOperation(value = "获取航班监控表头统计数据")
    @PostMapping("/getFlightCountInfo")
    public RenderResult<FlightMonitoringView> getFlightCountInfo(@Valid MobileFlightInfoListForm form){
        return RenderResult.success(flightInfoService.getFlightCountInfo(form));
    }
    /**
     * Title：findFlightInfo（） <br>
     * Description：搜索航班号 <br>
     * author：于琦海 <br>
     * date：2020/4/14 9:37 <br>
     * @param flightNo String
     * @param flightDate String
     * @return RenderResult<List<FlightMonitoringVO>>
     */
    @ApiOperation(value = "搜索航班号")
    @PostMapping("/findFlightInfo")
    public RenderResult<List<FlightMonitoringVO>> findFlightInfo(String flightNo, String flightDate) {
        return RenderResult.success(flightInfoService.findFlightInfo(flightNo, flightDate));
    }

    /**
     * Title：getFlightDetail（） <br>
     * Description：获取航班详情 <br>
     * author：于琦海 <br>
     * date：2020/4/14 13:08 <br>
     * @param: null
     * @return Object
     */
    @ApiOperation(value = "获取航班详情")
    @GetMapping("/getFlightDetail")
    public RenderResult<FocFlightInfoDetailView> getFlightDetail(String flightId){
        FocFlightInfo flightInfoById = flightInfoService.getFlightInfoById(flightId);
        Map<String, Object> departMap = flightInfoService.getFlightInfoCodeAndName(flightInfoById.getPod());
        Map<String, Object> arrivalMap = flightInfoService.getFlightInfoCodeAndName(flightInfoById.getPoa());
        if (Objects.nonNull(departMap)) {
            flightInfoById.setPod((String) departMap.get("AIRPORT_3CODE"));
            flightInfoById.setDepartPort((String) departMap.get("CITY_CH_NAME"));
        } else {
            log.error("H5航班监控查询航班详情:航班号->{},航班日期->{},四字码->{}查询三字码出错，需补全三字码", flightInfoById.getFlightNo(),
                    flightInfoById.getFlightDate(), flightInfoById.getPoa());
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        }
        if (Objects.nonNull(arrivalMap)) {
            flightInfoById.setPoa((String) arrivalMap.get("AIRPORT_3CODE"));
            flightInfoById.setArrivalPort((String) arrivalMap.get("CITY_CH_NAME"));
        } else {
            log.error("H5航班监控查询航班详情:航班号->{},航班日期->{},四字码->{}查询三字码出错，需补全三字码", flightInfoById.getFlightNo(),
                    flightInfoById.getFlightDate(), flightInfoById.getPoa());
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        }
        FocFlightInfoDetailView focFlightInfoDetailView = new FocFlightInfoDetailView();
        BeanUtils.copyProperties(flightInfoById, focFlightInfoDetailView);
        focFlightInfoDetailView = flightInfoService.findAlternateInfo(focFlightInfoDetailView);
        return RenderResult.success(focFlightInfoDetailView);
    }

    /**
     * Title：passengerClassification（） <br>
     * Description：航班监控旅客分类查询 <br>
     * author：于琦海 <br>
     * date：2020/4/14 17:24 <br>
     * @param form TraceQueryPassengerForm
     * @return:
     */
    @ApiOperation(value = "航班监控旅客分类查询")
    @PostMapping("/passengerClassification")
    public RenderResult passengerClassification(@Valid TraceQueryPassengerForm form){
        ContentTraceForm contentTraceForm = new ContentTraceForm();
        BeanUtils.copyProperties(form,contentTraceForm);
        return RenderResult.success(flightInfoService.passengerClassification(traceService.getPsgListByFilght(contentTraceForm)));
    }
}
