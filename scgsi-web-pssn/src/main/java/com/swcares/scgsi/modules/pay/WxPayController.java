package com.swcares.scgsi.modules.pay;

import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.SpringUtil;
import com.swcares.scgsi.common.userenum.UserEnum;
import com.swcares.scgsi.common.utils.JwtTokenUtils;
import com.swcares.scgsi.flight.service.impl.PaxWeiResourceService;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.service.wx.WxPayService;
import com.swcares.scgsi.service.wx.bean.WxAuthentication;
import com.swcares.scgsi.service.wx.bean.WxAuthenticationResult;
import com.swcares.scgsi.util.Asserts;
import com.swcares.scgsi.util.wx.WXPayConstants;
import com.swcares.scgsi.web.RenderResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.swcares.scgsi.util.wx.WXPayConstants.SUCCESS;

/**
 * ClassName：com.swcares.scgsi.modules.controller.PayTestController <br>
 * Description：支付测试 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/weixin")
@Slf4j
public class WxPayController {

    @Resource
    private WxPayService wxPayService;

    @Autowired
    private PaxWeiResourceService paxWeiResourceService;


    /**
     * Title： getAuthCodeUrl<br>
     * Description： 授权url<br>
     * author：傅欣荣 <br>
     * date：2020/4/22 16:37 <br>
     * @param
     * @return
     */
    @GetMapping("authUrl")
    public RenderResult getAuthCodeUrl(){
        try {
            return RenderResult.success(wxPayService.authCodeUrl());
        } catch (Exception e) {
            return RenderResult.fail();
        }
    }

   /**
    * Title：getAuthCallback <br>
    * Description： 通过code换取openid<br>
    * author：傅欣荣 <br>
    * date：2020-4-2 16:12 <br>
    * @param
    * @return
    */
    @GetMapping("authCallback")
    public RenderResult getAuthCallback(String code, String state){
        log.info("微信授权回调返回参数:code[{}]====state[{}]",code,state);
        try {
            Asserts.isNotEmpty(code, MessageCode.WX_AUTH_CODE_IS_NULL.getCode());
            Asserts.isNotEmpty(state, MessageCode.WX_AUTH_STATE_IS_NULL.getCode());
            Map<String,Object> map=new HashMap<>();
            String openId = wxPayService.getOpenid(code);
            String token =
                    JwtTokenUtils.createToken(openId, new ArrayList<>(),
                            openId, false);
            log.info("微信授权回调返回参数-获取openid:code[{}]====state:[{}]=====openid:[{}]===token:[{}]",code,state,openId,token);
            map.put("openId",openId);
            map.put("token",JwtTokenUtils.TOKEN_PREFIX +token);
            paxWeiResourceService.setPaxWeChatResource(openId);
            RedisService redisService = SpringUtil.getBean(RedisService.class);
            redisService.set(openId + UserEnum.TOKEN_KEY_SUFFIX.getValue()+ UserEnum.RESOURCE_PROT_H5.getValue(),
                    JwtTokenUtils.TOKEN_PREFIX + token, new Long(UserEnum.TOKEN_PAST_DUE.getValue()));
            return RenderResult.success(map);
        } catch (Exception e) {
            return  RenderResult.fail();
        }
    }


    /**
     * Title：getPayIdentityToken <br>
     * Description： 获取实名认证-token及结果<br>
     * author：傅欣荣 <br>
     * date：2020/6/23 9:52 <br>
     * @param  
     * @return 
     */
    @GetMapping("realNameAuthCallback")
    public RenderResult getPayIdentityToken(String code,String openid, String state){
        log.info("微信实名授权回调返回参数:code[{}]====state[{}]=====openid[{}]",code,state,openid);
        Asserts.isNotEmpty(code, MessageCode.WX_AUTH_CODE_IS_NULL.getCode());
        Asserts.isNotEmpty(state, MessageCode.WX_AUTH_STATE_IS_NULL.getCode());
        Asserts.isNotEmpty(openid, MessageCode.WX_OPENID_IS_NULL.getCode());
        String token = wxPayService.getPayIdentityToken(code,openid);
        WxAuthentication wxAuthentication = new WxAuthentication();
        wxAuthentication.setOpenid(openid);
        wxAuthentication.setToken(token);
        wxAuthentication.setIdNo("");
        wxAuthentication.setName("");
        WxAuthenticationResult wxAuthenticationResult = wxPayService.authentication(wxAuthentication,WxAuthenticationResult.class);
        log.info("wx-【前端传入实名认证请求参数：】{},-实名认证结果：[{}]",wxAuthentication.toString(),wxAuthenticationResult.toString());
        if(WXPayConstants.SUCCESS.equals(wxAuthenticationResult.getReturn_code()) && SUCCESS.equals(wxAuthenticationResult.getResult_code())){
            return RenderResult.success(true);
        }
        return RenderResult.success(false);
    }
}
