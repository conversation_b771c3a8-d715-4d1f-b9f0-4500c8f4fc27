package com.swcares.scgsi.modules.pax;

import com.alibaba.fastjson.JSON;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.custom.LogOperation;
import com.swcares.scgsi.flight.dto.ApplyParamDto;
import com.swcares.scgsi.flight.dto.ApplyQueryDto;
import com.swcares.scgsi.flight.dto.ApplyValidateCodeDto;
import com.swcares.scgsi.flight.service.PaxInfoService;
import com.swcares.scgsi.flight.service.TransactionLockService;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.flight.controller <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月10日 15:45 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/dp/pax")
@Api(tags = "旅客微信公众号领取")
@Slf4j
public class PaxInfoController {

    @Resource
    private PaxInfoService paxInfoService;
    @Resource
    private TransactionLockService transactionLockServiceImpl;

    /**
     * Title：authPaxInfo <br>
     * Description： 旅客登录验证<br>
     * author：王建文 <br>
     * date：2020-3-10 15:47 <br>
     *
     * @param idNo       证件号
     * @param flightNo   航班号
     * @param flightDate 日期
     */
    @GetMapping("authPaxInfo")
    @ApiOperation(value = "旅客登录验证")
    @LogOperation(operationUrl="/api/dp/pax/authPaxInfo",remark="旅客登录验证")
    public RenderResult authPaxInfo(String idNo, String flightNo, String flightDate) {
        log.info("旅客登录验证:" + idNo + ":" + flightNo + ":" + flightDate);
        Map<String, Object> dataMap = paxInfoService.getPaxAuthInfo(idNo, flightNo, flightDate);
        return RenderResult.success(dataMap);
    }

    /**
     * Title：getAuthCode <br>
     * Description： 获取验证码<br>
     * author：王建文 <br>
     * date：2020-3-11 10:09 <br>
     *
     */
    @PostMapping("getAuthCode")
    @ApiOperation(value = "获取验证码")
    @LogOperation(operationUrl="/api/dp/pax/getAuthCode",remark="旅客领取-获取验证码")
    public RenderResult getAuthCode(@RequestBody ApplyValidateCodeDto dto) {
        //判断验证码是否已经发送过
        paxInfoService.getReceiveRandomCode(dto.getTelephone(),dto.getCodeKey(),dto.getCaptcha());
        return RenderResult.success();
    }

    @PostMapping("validateAuthCode")
    @ApiOperation(value = "验证用户申领输入验证码是否正确")
    public RenderResult validateAuthCode(@RequestBody ApplyValidateCodeDto dto) {
        paxInfoService.validateAuthCode(dto.getTelephone(),dto.getRandomCode());
        return RenderResult.success();
    }




    @PostMapping("/applyRecord/getAuthCode")
    @ApiOperation(value = "申领记录查询-获取验证码")
    public RenderResult getAuthCodeByApplyRecord(@RequestBody ApplyValidateCodeDto dto) {
        log.info("【申领记录查询-获取验证码】前端请求参数：" + JSON.toJSONString(dto));
        if(StringUtils.isEmpty(dto.getFlightDate()) || StringUtils.isEmpty(dto.getFlightNo())
            || StringUtils.isEmpty(dto.getIdNo()) || StringUtils.isEmpty(dto.getTelephone())){
            throw new BusinessException(MessageCode.SYS_LOGIN_PARAM_IS_NULL.getCode());
        }
        //判断验证码是否已经发送过
        paxInfoService.getReceiveRandomCode(dto);
        return RenderResult.success();
    }
    @PostMapping("/applyRecord/validateAuthCode")
    @ApiOperation(value = "申领记录查询-验证用户申领输入验证码是否正确")
    public RenderResult validateAuthCodeByApplyRecord(@RequestBody ApplyValidateCodeDto dto) {
        paxInfoService.validateAuthCode(dto);
        return RenderResult.success();
    }

    /**
     * Title：normalApply <br>
     * Description： 普通申领提交<br>
     * author：王建文 <br>
     * date：2020-3-12 9:52 <br>
     *
     * @param applyParamDto 前端参数接收
     */
    @PostMapping("normalApply")
    @ApiOperation(value = "普通申领信息提交")
    @LogOperation(operationUrl="/api/dp/pax/normalApply",remark="普通申领信息提交")
    public RenderResult normalApply(@RequestBody ApplyParamDto applyParamDto) {
        log.info("【本人领取】普通申领信息提交:" + JSON.toJSONString(applyParamDto));
        transactionLockServiceImpl.saveNormalApplyInfo(applyParamDto);
        return RenderResult.success();

    }

    /**
     * Title：saveActingApply <br>
     * Description：代领信息提交<br>
     * author：王建文 <br>
     * date：2020-3-12 16:02 <br>
     *
     * @param applyParamDto 前端参数接收
     */
    @PostMapping("saveActingApply")
    @ApiOperation(value = "代领信息提交")
    @LogOperation(operationUrl="/api/dp/pax/saveActingApply",remark="代领信息提交")
    public RenderResult saveActingApply(@RequestBody ApplyParamDto applyParamDto) {
        log.info("代领信息提交:" + JSON.toJSONString(applyParamDto));
        transactionLockServiceImpl.saveActApplyInfo(applyParamDto);
        return RenderResult.success();
    }

    /**
     * Title：applyRecordInfo <br>
     * Description：申领记录查询<br>
     * author：王建文 <br>
     * date：2020-3-12 16:02 <br>
     *
     * @param applyQueryDto 证件号，姓名必填
     */
    @GetMapping("applyRecordInfo")
    @ApiOperation(value = "申领记录查询")
    public RenderResult applyRecordInfo(ApplyQueryDto applyQueryDto) {
        if(StringUtils.isBlank(applyQueryDto.getIdNo())){
            return RenderResult.success(new ArrayList<>());
        }
        log.info("applyRecordInfo申领记录查询:" + JSON.toJSONString(applyQueryDto));
        return RenderResult.success(paxInfoService.getPaxApplyInfoValidateCode(applyQueryDto));
    }


    /**
     * Title：applyRecordDetailInfo <br>
     * Description：根据旅客id查询申领详情<br>
     * author：王建文 <br>
     * date：2020-3-12 16:56 <br>
     *
     * @param paxId 旅客id
     */
    @GetMapping("applyRecordDetailInfo")
    @ApiOperation(value = "根据旅客id查询申领详情")
    @LogOperation(operationUrl="/api/dp/pax/applyRecordDetailInfo",remark="根据旅客id查询申领详情")
    public RenderResult applyRecordDetailInfo(String paxId) {
        log.info("------------根据旅客id查询申领详情--查询参数[{}]",paxId);
        return RenderResult.success(paxInfoService.getApplyRecordInfo(paxId));
    }

    /**
     * Title：actApplyRecordInfo <br>
     * Description：旅客领取代领记录查询<br>
     * author：王建文 <br>
     * date：2020-3-13 11:14 <br>
     *
     * @param applyQueryDto 查询参数
     */
    @GetMapping("actApplyRecordInfo")
    @ApiOperation(value = "旅客领取代领记录查询")
    public RenderResult actApplyRecordInfo(ApplyQueryDto applyQueryDto) {
        log.info("------------旅客领取代领记录查询--查询参数[{}]",applyQueryDto.toString());
        if(StringUtils.isBlank(applyQueryDto.getIdNo())){
            return RenderResult.success(new ArrayList<>());
        }
        return RenderResult.success(paxInfoService.getActApplyInfo(applyQueryDto));
    }

    /**
     * Title：actApplyRecordDetailInfo <br>
     * Description： 代领旅客详情展示<br>
     * author：王建文 <br>
     * date：2020-3-13 17:36 <br>
     *
     * @param applyCode 申领单号
     */
    @GetMapping("actApplyRecordDetailInfo")
    @ApiOperation(value = "代领旅客详情展示")
    public RenderResult actApplyRecordDetailInfo(String applyCode, String flightNo, String flightDate) {
        return RenderResult.success(paxInfoService.getActApplyInfoByApplyCode(applyCode, flightNo, flightDate));
    }

}