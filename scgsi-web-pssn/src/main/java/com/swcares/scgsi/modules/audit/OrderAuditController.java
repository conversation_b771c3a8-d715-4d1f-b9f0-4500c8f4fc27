package com.swcares.scgsi.modules.audit;

import com.swcares.scgsi.audit.dto.AuditOrderPaxInfo;
import com.swcares.scgsi.audit.dto.DeptUserInfoDto;
import com.swcares.scgsi.audit.dto.OrderAuditProcessParamDto;
import com.swcares.scgsi.audit.dto.OrderAuditQueryParams;
import com.swcares.scgsi.audit.service.OrderAuditService;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.overbook.service.PaxCompensateService;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.modules.audit <br>
 * Description：赔偿单审核 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月13日 15:25 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/h5/dp/sys")
@Api(tags = "H5-审核")
public class OrderAuditController extends BaseController {
    @Resource
    private OrderAuditService orderAuditService;
    @Resource
    private PaxCompensateService paxCompensateService;

    /**
     * Title：queryOrderAuditList <br>
     * Description： H5 - 审核列表查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/26 9:36 <br>
     * @param
     * @return
     */
    @GetMapping("queryOrderAuditList")
    @ApiOperation(value = "审核列表查询")
    public RenderResult queryOrderAuditList(OrderAuditQueryParams orderAuditQueryParams){
        String createUser = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        orderAuditQueryParams.setUserId(createUser);
        return RenderResult.success(returnPageInfo(orderAuditService.queryOrderAuditList(orderAuditQueryParams)));
    }

    /**
     * Title： getAuditNode<br>
     * Description：  根据taskid 获取当前节点 2-aoc 3值班经理 <br>
     * author：傅欣荣 <br>
     * date：2020/4/15 10:28 <br>
     * @param
     * @return
     */
    @GetMapping("getAuditNode")
    public RenderResult getAuditNode(String taskId){
        return RenderResult.success(orderAuditService.getAuditNode(taskId));
    }


    /**
     * Title：queryAuditDetails <br>
     * Description： H5-审核赔偿单详情<br>
     * author：傅欣荣 <br>
     * date：2020/3/27 15:39 <br>
     * @param
     * @return
     */
    @GetMapping("getAuditCompensateDetails")
    @ApiOperation(value = "审核赔偿单详情")
    public RenderResult getAuditCompensateDetails(String orderId){
        return RenderResult.success(paxCompensateService.queryOrderAuditDetailsInfo(orderId));
    }



    /**
     * Title：getOrderPaxInfo <br>
     * Description： 获取赔偿单旅客列表<br>
     * author：傅欣荣 <br>
     * date：2020/3/30 9:58 <br>
     * @param
     * @return
     */
    @GetMapping("getAuditOrderPaxInfo")
    @ApiOperation(value = "赔偿单旅客列表")
    public RenderResult getAuditOrderPaxInfo(AuditOrderPaxInfo auditOrderPaxInfo){
        Map<String, Object> data = orderAuditService.getOrderPaxInfo(auditOrderPaxInfo);
        data.put("paxList",returnPageInfo((QueryResults)data.get("paxList")));
        return RenderResult.success(data);
    }

    /**
     * Title：getOrderAuditRecord <br>
     * Description： 获取赔偿单审核记录list<br>
     * author：傅欣荣 <br>
     * date：2020/3/13 15:40 <br>
     * @param  orderId
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("getOrderAuditRecord")
    @ApiOperation(value = "赔偿单审核记录")
    public RenderResult getOrderAuditRecord(String orderId) {
        return RenderResult.success(orderAuditService.findOrderAuditRecord(orderId));
    }



    /**
     * Title：getDeptUserInfo <br>
     * Description：获取部门人员信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/13 15:45 <br>
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("getDeptUserInfo")
    @ApiOperation(value = "获取部门人员信息")
    public RenderResult getDeptUserInfo( DeptUserInfoDto deptUserInfoDto) {
        try {
            return  RenderResult.success(returnPageInfo(orderAuditService.getDeptUserInfo(deptUserInfoDto)));
        } catch (Exception e) {
            return RenderResult.fail();
        }
    }



    /**
     * Title：saveOrderAuditRecord <br>
     * Description：处理赔偿单审批<br>
     * author：傅欣荣 <br>
     * date：2020/3/13 15:45 <br>
     * @param  orderAuditPd
     * @return com.swcares.scgsi.web.RenderResult
     */
    @PostMapping("handleAudit")
    @ApiOperation(value = "审批")
    public RenderResult saveOrderAuditRecord(@RequestBody OrderAuditProcessParamDto orderAuditPd) {
        String createUser = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        orderAuditPd.getOrderAuditInfo().setAuditor(createUser);
        orderAuditService.handleAuditProcess(orderAuditPd.getOrderAuditInfo());
        return RenderResult.success();
    }


}
