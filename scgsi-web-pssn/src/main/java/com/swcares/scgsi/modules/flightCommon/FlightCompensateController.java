package com.swcares.scgsi.modules.flightCommon;

import cn.hutool.json.JSONUtil;
import com.swcares.scgsi.audit.service.OrderAuditService;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.form.ContentTraceForm;
import com.swcares.scgsi.department.common.model.vo.DepartAndUserVO;
import com.swcares.scgsi.department.common.model.vo.KeySearchDepartVO;
import com.swcares.scgsi.department.service.impl.DepartmentServiceImpl;
import com.swcares.scgsi.flight.dto.FlightQueryParamDto;
import com.swcares.scgsi.flight.dto.OrderInfoParamDto;
import com.swcares.scgsi.flight.service.FlightCompensateService;
import com.swcares.scgsi.service.FlightInfoService;
import com.swcares.scgsi.user.service.UserService;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/h5/compensate")
@Api(tags = "h5不正常航班赔偿单建单流程")
@Slf4j
public class FlightCompensateController extends BaseController {

    private final DepartmentServiceImpl departmentService;

    private final FlightCompensateService flightCompensateService;

    private final FlightInfoService flightInfoService;

    private final OrderAuditService orderAuditService;

    private final UserService userService;

    public FlightCompensateController(DepartmentServiceImpl departmentService, FlightCompensateService flightCompensateService, FlightInfoService flightInfoService, OrderAuditService orderAuditService, UserService userService) {
        this.departmentService = departmentService;
        this.flightCompensateService = flightCompensateService;
        this.flightInfoService = flightInfoService;
        this.orderAuditService = orderAuditService;
        this.userService = userService;
    }

    @GetMapping("getOrderInfoByFlightINoAndFlightDate")
    @ApiOperation(value = "根据航班号航班日期查询所有的赔付单")
    public RenderResult getOrderInfoByFlightINoAndFlightDate(String flightNo, String flightDate, String payType, String status) {
        return RenderResult.success(flightCompensateService.getOrderInfoByFlightINoAndFlightDate(
                flightNo, flightDate, payType, status));
    }

    @GetMapping("getServiceOrderInfo")
    @ApiOperation(value = "赔偿单管理列表")
    public RenderResult getServiceOrderInfo(FlightQueryParamDto flightQueryParamDto) {
        QueryResults queryResults = flightCompensateService.getFlightInfoPage(flightQueryParamDto);
        return RenderResult.success(returnPageInfo(queryResults));
    }
    // 获取编辑权限
    @GetMapping("getEditPermission")
    @ApiOperation(value = "获取编辑权限")
    public RenderResult getEditPermission(String orderId) {
        return RenderResult.success(flightCompensateService.getEditPermission(orderId));
    }

    @GetMapping("getSegment")
    @ApiOperation(value = "通过航班号航班日期获取航段")
    public RenderResult getSegment(String flightNo, String flightDate) {
        flightDate = flightDate.replaceAll("-", "/");
        return RenderResult.success(flightInfoService.getFlightSegment(flightNo, flightDate));
    }

    @GetMapping("getPaxOrderInfo")
    @ApiOperation(value = "获取旅客赔偿次数详情")
    public RenderResult getPaxOrderInfo(String paxId, String flightNo, String flightDate) {
        return RenderResult.success(flightCompensateService.getPaxOrderInfo(paxId, flightNo,flightDate));
    }

    /**
     * Title：getFlightInfo <br>
     * Description： 获取航班信息<br>
     * author：王建文 <br>
     * date：2020-3-23 14:14 <br>
     *
     * @param flightNo      航班号
     * @param flightDate    航班日期YYYY/MM/DD
     * @param choiceSegment 航段
     */
    @GetMapping("getFlightInfo")
    @ApiOperation(value = "通过航班号航班日期获取航段")
    public RenderResult getFlightInfo(String flightNo, String flightDate, String choiceSegment) {
        return RenderResult.success(flightCompensateService.getFocFlightInfos(flightNo, flightDate, choiceSegment));
    }

    /**
     * Title：getPaxInfo <br>
     * Description： 航班四要素获取旅客信息<br>
     * author：王建文 <br>
     * date：2020-3-23 13:33 <br>
     *
     * @param flightNo      航班号
     * @param flightDate    航班日期
     * @param choiceSegment 航段
     */
    @GetMapping("getPaxInfo")
    @ApiOperation(value = "根据航班航班号所选航段获取旅客信息")
    public RenderResult getPaxInfo(String flightNo, String flightDate, String choiceSegment,
                                   ContentTraceForm form, String selectSegment) {
        return RenderResult.success(flightCompensateService.getReturnFlightPaxInfo(flightNo, flightDate, choiceSegment, form, selectSegment));
    }

    /**
     * Title： getIsAocByUserId<br>
     * Description：  查询userId 是否为aoc人员  0 存在，1不存在<br>
     * author：傅欣荣 <br>
     * date：2020/4/15 10:28 <br>
     *
     * @param
     * @return
     */
    @GetMapping("getIsAocByUserId")
    @ApiOperation(value = "查询userId 是否为aoc人员  0 存在，1不存在")
    public RenderResult getIsAocByUserId(String userId) {
        userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        return RenderResult.success(orderAuditService.getIsAocByUserId(userId));
    }

    @ApiOperation(value = "通过当前节点获取下级部门信息")
    @GetMapping("/getDepartment/{deptId}")
    public RenderResult<DepartAndUserVO> getDepartmentChildById(@PathVariable String deptId) {
        return RenderResult.success(departmentService.getDpartmentChildById(deptId));
    }

    @ApiOperation(value = "通过姓名工号精确查询部门信息")
    @GetMapping("/fetchDepartment/keySearch")
    public RenderResult<List<KeySearchDepartVO>> keySearchDepartment(String keySearch) {
        return RenderResult.success(departmentService.keySearchDepartment(keySearch));
    }

    @GetMapping("/retrieveUsers")
    @ApiOperation(value = "根据姓名和工号模糊查询用户信息", notes = "根据姓名和工号模糊查询用户信息")
    public RenderResult<Object> queryUserByNameAndNo(String keySearch) {
        return RenderResult.success(userService.queryUserByNameAndNo(keySearch));
    }

    @PostMapping("saveOrderInfo")
    @ApiOperation(value = "保存更新赔付单信息")
    public RenderResult saveOrderInfo(@RequestBody OrderInfoParamDto orderInfoParamDto) {
        log.info("创建赔偿单的时候前端参数为【{}】", JSONUtil.toJsonStr(orderInfoParamDto));
        try {
            String orderId = flightCompensateService.saveOrderInfo(orderInfoParamDto);
            return RenderResult.success(orderId);
        } catch (Exception e) {
            log.error("创建赔偿单的时候异常", e);
            return new RenderResult("1", e.getMessage());
        }
    }

    @PostMapping("updateOrderStatus")
    @ApiOperation(value = "赔偿单管理确认发放，关闭操作")
    public RenderResult updateOrderStatus(String orderId, String status) {
        try {
            flightCompensateService.updateOrderStatus(orderId, status);
            return RenderResult.success();
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }
}
