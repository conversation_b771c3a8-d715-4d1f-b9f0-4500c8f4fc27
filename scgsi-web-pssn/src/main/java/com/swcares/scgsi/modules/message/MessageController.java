package com.swcares.scgsi.modules.message;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.message.common.model.form.MessageListForm;
import com.swcares.scgsi.message.common.model.form.MessageSendForm;
import com.swcares.scgsi.message.common.model.vo.NewMessageVo;
import com.swcares.scgsi.message.common.msgenum.MessageEnum;
import com.swcares.scgsi.message.service.MessageService;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.web.RenderResult;

/**
 * ClassName：com.swcares.scgsi.login.controller.MessageController <br>
 * Description：消息系统接口 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月3日 上午11:32:04 <br>
 * @version v1.0 <br>
 */
@Api(tags = "消息接口")
@RestController
@RequestMapping("/api/h5/message")
public class MessageController {
    @Autowired
    private MessageService messageService;

    /** 按照时间排序 */
    // private static final String SORT = "msg_date";

    /**
     * Title：getNewMsgAmount <br>
     * Description：根据用户ID获取新消息提示数据<br>
     * author：王磊 <br>
     * date：2020年3月4日 下午04:59:30 <br>
     * @param userId 用户ID
     * @return <br>
     * @throws Exception 
     */
    @ApiOperation(value = "新消息提醒")
    @GetMapping("/getNewMsgAmount")
    public RenderResult<List<NewMessageVo>> getNewMsgAmount() throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String userId = (String) auth.getCredentials();
        List<NewMessageVo> message = messageService.getNewMsgAmount(userId,MessageEnum.MOBILE.getCode());
        return RenderResult.success(message);
    }

    /**
     * Title：getMsgList<br>
     * Description：消息列表(已读,未读,全部,历史消息)通用函数<br>
     * author：王磊 <br>
     * date：2020年3月4日 下午5:01:11 <br>
     * @param form 传入参数对象
     * @return <br>
     */
    @ApiOperation(value = "消息列表(已读,未读,全部,历史消息)通用函数")
    @GetMapping("/getMsgList")
    public RenderResult<QueryResults> getMsgList(@Valid MessageListForm form) throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String userId = (String) auth.getCredentials();
        form.setUserID(userId);
        Pageable pageable = PageRequest.of(form.getPage(), form.getSize());
        QueryResults queryResults = messageService.getMsgList(pageable, form,MessageEnum.MOBILE.getCode());
        return RenderResult.success(queryResults);
    }

    /**
     * Title：updateMsgRead <br>
     * Description：批量或单个标识为已读 <br>
     * author：王磊 <br>
     * date：2020年3月4日 下午5:03:53 <br>
     * @param msgId 消息id集合
     * @param userID 用户id
     * @return <br>
     */
    @ApiOperation(value = "批量或单个标识为已读")
    @PostMapping("/updateMsgRead")
    public RenderResult<Object> updateMsgRead(
            @ApiParam(name = "msgId", value = "消息ID集合") @RequestParam(name = "msgId") String[] msgId,
            @ApiParam(name = "msgType", value = "消息类型") @RequestParam(name = "msgType") String msgType)
            throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String userId = (String) auth.getCredentials();
        messageService.updateMsgRead(msgId, userId, msgType);
        return RenderResult.success();
    }

    /**
     * Title：updateMsgRead <br>
     * Description：批量或单个标识为已读<br>
     * author：王磊 <br>
     * date：2020年3月4日 下午5:03:53 <br>
     * @param msgId 消息id集合
     * @param userID 用户id
     * @return <br>
     */
    @ApiOperation(value = "发送消息")
    @PostMapping("/sendMsg")
    public RenderResult<Object> sendMsg(@Valid @RequestBody MessageSendForm messageSendForm)
            throws Exception {
            messageService.sendMsg(messageSendForm);
            return RenderResult.success();
    }
    /**暂时不实现接口
    // 显示发送详情
    // public RenderResult getSendMsgInfo(String msgId){
    //
    // }
    // 回复消息
    // public RenderResult getReplyMsgInfo(String msgId){
    //
    // }
    // 新增消息
    // public RenderResult saveMsg(){
    // msgTitle String
    // msgContent String
    // msgUser String
    // msgDate date
    // flightNo String
    // flightDate String
    // msgReplyUser String[]
    // msgReplyDepartment String[]
    // }
    // 消息回复
    // public RenderResult saveReplyMsg(String msgId,String msgUser,String msgContent){
    //
    // }
    // 航班消息展示
    // public RenderResult getFilghtMsg(String flightNo,
    // String flightBeginDate,
    // String flightEndDate,
    // String beginDate,
    // String endDate,
    // String[]msgSendDepartment,
    // String[]msgReplyDepartment ){
    // }
    */
}
