package com.swcares.scgsi.modules.overbook;

import com.swcares.scgsi.audit.enums.AuditProcessType;
import com.swcares.scgsi.audit.service.OrderAuditService;
import com.swcares.scgsi.overbook.dto.*;
import com.swcares.scgsi.overbook.service.OverBookService;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * ClassName：com.swcares.scgsi.modules.audit <br>
 * Description：旅客超售 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月13日 15:52 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/h5/dp/mobile/overBook")
@Api(tags = "H5-旅客超售")
@Slf4j
public class OverBookController extends BaseController {

    @Resource
    private OverBookService overBookService;

    @Resource
    private OrderAuditService orderAuditService;


    //保存提交
    private static final String SAVE_SUBMIT = "1";

    /**
     * Title：getOverBookInfo <br>
     * Description： H5旅客超售列表 -  查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:08 <br>
     * @param  overBookQueryDto
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("getOverBookInfo")
    @ApiOperation(value = "列表查询")
    public RenderResult getOverBookInfo(OverBookQueryDto overBookQueryDto) {
        return RenderResult.success(returnPageInfo(overBookService.findOverBookList(overBookQueryDto)));
    }

    /**
     * Title：getOverBookDetails <br>
     * Description： H5旅客超售详情<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:10 <br>
     * @param  orderId
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("getOverBookDetails")
    @ApiOperation(value = "旅客超售详情")
    public RenderResult getOverBookDetails(@ApiParam(name = "orderId", value = "赔偿单id") String orderId) {
        try {
            return RenderResult.success(overBookService.findOverDetails(orderId));
        } catch (Exception e) {
            return RenderResult.fail();
        }
    }

    
    /**
     * Title：getOrderDetails <br>
     * Description： H5旅客超售-查看赔偿单<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:11 <br>
     * @param  orderId
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("getOrderDetails")
    @ApiOperation(value = "旅客超售-赔偿单详情")
    public RenderResult getOrderDetails(@ApiParam(name = "orderId", value = "赔偿单id") String orderId) {
        return RenderResult.success(overBookService.findOrderDetails(orderId));
    }


    /**
     * Title：updateOrderStatus <br>
     * Description：H5旅客超售-去发放<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:14 <br>
     * @param
     * @return com.swcares.scgsi.web.RenderResult
     */
    @PostMapping("updateOrderStatus")
    @ApiOperation(value = "旅客超售-去发放")
    public RenderResult updateOrderStatus(@Valid @RequestBody OverOrderStatusSaveDto statusSaveDto) {
        overBookService.updateOrderStatus(statusSaveDto);
        return RenderResult.success();
    }

    /**
     * Title：getConfigInfo <br>
     * Description：H5旅客超售-规则查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:15 <br>
     * @param
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("getConfigInfo")
    @ApiOperation(value = "旅客超售-规则查询")
    public RenderResult getConfigInfo() {
        return RenderResult.success(overBookService.getConfigInfo());
    }

    /**
     * Title：overBookVerification <br>
     * Description： 航班验证<br>
     * author：傅欣荣 <br>
     * date：2020/3/22 12:45 <br>
     * @param
     * @return
     */
    @GetMapping("flightVerification")
    @ApiOperation(value = "旅客超售-航班验证")
    public RenderResult overBookVerification(@Valid OverBookVerificationDto overBookVerificationDto) {
        return RenderResult.success(overBookService.overBookVerification(overBookVerificationDto));
    }

    /**
     * Title：overBookVerification <br>
     * Description： 查询改签航班信息【计划起飞时间等】<br>
     * author：傅欣荣 <br>
     * date：2020/3/22 12:45 <br>
     * @param
     * @return
     */
    @GetMapping("getRebookFlightInfo")
    @ApiOperation(value = "旅客超售-改签航班信息")
    public RenderResult getRebookFlightInfo(@ApiParam(name = "flightNo", value = "航班号")String flightNo,
                @ApiParam(name = "flightDate", value = "航班时间")String flightDate,
                @ApiParam(name = "paxId", value = "旅客id")String paxId) {
        return RenderResult.success(overBookService.getRebookFlightInfo(flightNo,flightDate,paxId));
    }



    /**
     * Title：saveOverBook <br>
     * Description：保存||提交 改签|退票 <br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:16 <br>
     * @param  overBookInfoSaveDto
     * @return com.swcares.scgsi.web.RenderResult
     */
    @PostMapping("saveOverBookInfo")
    @ApiOperation(value = "旅客超售-草稿||提交")
    public RenderResult saveOverBook(@RequestBody OverBookInfoSaveDto overBookInfoSaveDto) {
        try {
            String orderId = overBookService.saveOverBookInfo(overBookInfoSaveDto.getOverBookInfoDto());
            if(overBookInfoSaveDto.getOverBookInfoDto().getIsCommit().equals(SAVE_SUBMIT)){
                // 进入审核，默认进入二级审核
                String createUser = (String) AuthenticationUtil.getAuthentication().getPrincipal();
                orderAuditService.launchAuditProcess(createUser, AuditProcessType.LAUNCH_PROCESS_KEY_AOC2.getNode(),orderId,null);
            }
            return RenderResult.success(orderId);
        } catch (Exception e) {
            log.error("H5-保存超售信息方法异常！！！[{}]",e);
            return RenderResult.fail();
        }
    }

    /**
     * Title： verifyPayMoney <br>
     * Description： 验证超售保存信息金额。- 测试使用<br>
     * author：傅欣荣 <br>
     * date：2020/4/17 17:00 <br>
     * @param
     * @return
     */
    @PostMapping("verifyPayMoney")
    public RenderResult verifyPayMoney(@RequestBody OverBookInfoSaveDto overBookInfoSaveDto){

        try {
            return RenderResult.success(overBookService.inspectPayMoney(overBookInfoSaveDto.getOverBookInfoDto()));
        } catch (Exception e) {
            return RenderResult.fail();
        }

    }




    /**
     * Title：getOverBookDraftInfo <br>
     * Description： H5旅客超售-草稿箱列表查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:19 <br>
     * @param
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("getOverBookDraftInfo")
    @ApiOperation(value = "旅客超售-草稿箱列表")
    public RenderResult getOverBookDraftInfo(@Valid OverDraftListQueryDto overDraftListQueryDto) {
        return RenderResult.success(returnPageInfo(overBookService.findOverDraftList(overDraftListQueryDto)));
    }

    /**
     * Title：getOverEditDetails <br>
     * Description： H5旅客超售-编辑超售信息详情<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:21 <br>
     * @param  overId
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("getOverEditDetails")
    @ApiOperation(value = "旅客超售-超售信息详情")
    public RenderResult getOverEditDetails(@ApiParam(name = "overId", value = "超售id") String overId) {
        return RenderResult.success(overBookService.findOverDraftDetails(overId));
    }


    /**
     * Title：delOverBook <br>
     * Description： H5旅客超售-删除超售草稿<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:21 <br>
     * @param  ids
     * @return com.swcares.scgsi.web.RenderResult
     */
    @PostMapping("delOverBook")
    @ApiOperation(value = "旅客超售-删除")
    public RenderResult delOverBook(@ApiParam(name = "ids", value = "超售id") String[] ids) {
        try {
            overBookService.delOverDraftInfo(ids);
            return RenderResult.success();
        } catch (Exception e) {
            return RenderResult.fail();
        }
    }




}