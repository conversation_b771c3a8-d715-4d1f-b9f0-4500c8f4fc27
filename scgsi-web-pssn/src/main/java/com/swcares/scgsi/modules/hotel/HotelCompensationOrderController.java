package com.swcares.scgsi.modules.hotel;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hotel.dao.HotelPaxInfoDao;
import com.swcares.scgsi.hotel.enums.HotelCompensateStatusEnums;
import com.swcares.scgsi.hotel.model.dto.*;
import com.swcares.scgsi.hotel.model.entity.HotelPaxInfo;
import com.swcares.scgsi.hotel.model.vo.HotelCompensateDetailVO;
import com.swcares.scgsi.hotel.model.vo.HotelCompensatePaxVO;
import com.swcares.scgsi.hotel.model.vo.HotelOrderInfoVO;
import com.swcares.scgsi.hotel.service.HotelCompensateService;
import com.swcares.scgsi.hotel.service.HotelCompensationOrderService;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：HotelCompensationOrderController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/10/8 14:23
 * @version： v1.0
 */
@RestController
@RequestMapping("/api/h5/hotel/compensation")
@Api(tags = "酒店补偿申请单H5")
public class HotelCompensationOrderController extends BaseController {

    @Autowired
    private HotelCompensationOrderService hotelCompensationOrderService;
    @Autowired
    HotelCompensateService hotelCompensateService;

    /**
     * @title getGuaranteeOrderList
     * @description H5保障住宿单列表
     * <AUTHOR>
     * @date 2022/10/8 16:00
     * @param dto
     * @return RenderResult<List<HotelOrderInfoVO>>
     *
     */
    @PostMapping("/orderList")
    @ApiOperation(value = "旅客保障住宿单列表")
    public RenderResult<Map<String, Object>> getGuaranteeOrderList(@RequestBody HotelCompensationOrderDTO dto){
        return RenderResult.success(returnPageInfo(hotelCompensationOrderService.getGuaranteeOrderList(dto)));
    }

    /**
     * @title getGuaranteeOrderDetail
     * @description 通过补偿干Id查询详情
     * <AUTHOR>
     * @date 2022/10/8 16:01
     * @param orderId
     * @return com.swcares.scgsi.web.RenderResult<com.swcares.scgsi.hotel.model.vo.HotelCompensateDetailVO>
     */
    @GetMapping("/orderDetail")
    @ApiOperation(value = "旅客保障住宿单详情")
    public RenderResult<HotelCompensateDetailVO> getGuaranteeOrderDetail(@ApiParam(value = "保障单主键Id",required = true) @RequestParam(value = "orderId") String orderId) throws Exception {
        return RenderResult.success(hotelCompensationOrderService.getGuaranteeOrderDetail(orderId));
    }

    /**
     * @title getPersonnelDetails
     * @description 获取保障旅客人员详情
     * <AUTHOR>
     * @date 2022/10/10 15:41
     * @param dto
     * @return RenderResult<List<HotelCompensatePaxVO>>
     */
    @PostMapping("/personalDetails")
    @ApiOperation(value = "保障旅客人员详情")
    public RenderResult<List<HotelCompensatePaxVO>> getPersonnelDetails(@RequestBody @Validated HotelCompensationPaxDTO dto){
        return RenderResult.success(hotelCompensationOrderService.getPersonnelDetails(dto));
    }

    @PostMapping("/orderExamineList")
    @ApiOperation(value = "保障住宿单审核列表")
    public RenderResult<Map<String, Object>> getOrderExamineList(@RequestBody @Validated HotelAuditParamsDTO dto){
        String createUser = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        dto.setUserId(createUser);
        return RenderResult.success(returnPageInfo(hotelCompensationOrderService.getOrderExamineList(dto)));
    }

    /**
     * @title auditOperation
     * @description 审核
     * <AUTHOR>
     * @date 2022/10/13 15:21
     * @param dto
     * @return com.swcares.scgsi.web.RenderResult
     */
    @PostMapping("/auditOperation")
    @ApiOperation(value = "赔偿单-审核")
    public RenderResult auditOperation(@RequestBody @Validated AuditCompleteParamsDTO dto) {
            hotelCompensateService.auditOperation(dto);
            return RenderResult.success();
    }

    @GetMapping("/submitOrder")
    @ApiOperation(value = "赔偿单-提交")
    public RenderResult submitOrder(@RequestParam("orderId")@ApiParam(value = "保障单id",required = true) String orderId){
            return RenderResult.success(hotelCompensationOrderService.submitOrder(orderId));
    }

    @PostMapping("updateOrderStatus")
    @ApiOperation(value = "赔偿单管理确认发放，关闭操作")
    public RenderResult updateOrderStatus(@RequestBody @Validated HotelOrderStatusParamDTO dto) {
        hotelCompensateService.updateOrderStatus(dto.getOrderId(), dto.getStatus());
        return RenderResult.success();
    }
}
