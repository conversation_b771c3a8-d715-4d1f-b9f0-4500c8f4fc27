package com.swcares.scgsi.modules.user;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.swcares.scgsi.user.common.model.vo.UserDetailVo;
import com.swcares.scgsi.user.service.UserService;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;

/**
 * ClassName：com.swcares.scgsi.login.controller.UserController <br>
 * Description：用户管理接口 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月12日 上午10:00:04 <br>
 * @version v1.0 <br>
 */
@Api(tags = "用户相关接口")
@RestController
@RequestMapping("/api/h5/user")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    /**
     * Title：updateOnDutyState <br>
     * Description：用户值班和解除值班方法 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:52:42 <br>
     * @param onDutyState 值班状态(0未值班1值班)
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "用户值班和解除值班(0未值班1值班)")
    @PostMapping("/updateOnDutyState")
    public RenderResult<Object> updateOnDutyState(
            @ApiParam(name = "onDutyState", value = "值班状态(0未值班1值班)",
                    required = true) @RequestParam(name = "onDutyState") String onDutyState)
            throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String userId = (String) auth.getCredentials();
        userService.updateOnDutyState(userId, onDutyState);
        return RenderResult.success();
    }

    /**
     * Title：getUserDetail <br>
     * Description：获取当前登录用户详情 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:53:13 <br>
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "获取用户详细信息")
    @GetMapping("/getUserDetail")
    public RenderResult<Object> getUserDetail()
            throws Exception {
        // 需修改为通过token获取用户
        Authentication auth = AuthenticationUtil.getAuthentication();
        String userId = (String) auth.getCredentials();
        UserDetailVo userDetailVo = userService.getUserDetail(userId);
        return RenderResult.success(userDetailVo);
    }

}
