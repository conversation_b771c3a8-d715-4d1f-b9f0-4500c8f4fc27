package com.swcares.scgsi.modules.pkg;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.flight.dao.impl.FlightCompensateDaoImpl;
import com.swcares.scgsi.flight.service.ActConfigInfoService;
import com.swcares.scgsi.flight.vo.ActConfigInfoVo;
import com.swcares.scgsi.pkg.dao.impl.PkgInfoServiceDaoImpl;
import com.swcares.scgsi.pkg.dto.PkgAuditParamInfoDto;
import com.swcares.scgsi.pkg.dto.PkgInfoDto;
import com.swcares.scgsi.pkg.dto.PkgInfoQueryParamDto;
import com.swcares.scgsi.pkg.dto.PkgParamInfoDto;
import com.swcares.scgsi.pkg.service.PkgInfoService;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.modules.pkg <br>
 * Description：异常行李 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月22日 9:32 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/h5/dp/pkg/mobile")
@Api(tags = "异常行李H5端接口")
public class PkgInfoController extends BaseController {
    @Resource
    private PkgInfoService pkgInfoService;

    @Resource
    private PkgInfoServiceDaoImpl pkgInfoServiceDao;

    @Resource
    private ActConfigInfoService actConfigInfoService;

    @Resource
    private FlightCompensateDaoImpl flightCompensateDao;
    @Resource
    private RedisService redisService;
    public static final String PAX_PKG_CREATE = "pkg:accident_create:";
    /**
     * Title：getPkgInfo <br>
     * Description： 异常行李H5查询列表<br>
     * author：王建文 <br>
     * date：2020-3-22 11:09 <br>
     *
     * @param queryParamDto 参数接收
     */
    @GetMapping("getPkgInfo")
    @ApiOperation(value = "异常行李H5查询列表")
    public RenderResult getPkgInfo(PkgInfoQueryParamDto queryParamDto) {
        QueryResults queryResults = pkgInfoService.getPkgInfoPage(queryParamDto);
        return RenderResult.success(returnPageInfo(queryResults));
    }

    /**
     * Title：authPaxPkgInfo <br>
     * Description：新建异常行李验证<br>
     * author：王建文 <br>
     * date：2020-4-7 13:36 <br>
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @param keySearch  关键词搜索
     */
    @GetMapping("authPaxPkgInfo")
    @ApiOperation(value = "新建异常行李验证")
    public RenderResult<Object> authPaxPkgInfo(String flightNo, String flightDate, String keySearch) {
        Map<String,Object> map=pkgInfoService.authUserInfo(flightNo,flightDate,keySearch);
        if(map.isEmpty()){
            return RenderResult.success("未找到旅客信息!");
        }else{
            return RenderResult.success(map);
        }
    }

    /**
     * Title：savePkgInfo <br>
     * Description： 异常行李创建<br>
     * author：王建文 <br>
     * date：2020-3-22 9:39 <br>
     *
     * @param pkgInfoParam 参数接收
     */
    @PostMapping("savePkgInfo")
    @ApiOperation(value = "异常行李创建")
    public RenderResult savePkgInfo(@RequestBody PkgParamInfoDto pkgInfoParam) {
        try {
            Map<String,Object> map=new HashMap<>();
            PkgInfoDto pkgInfo = pkgInfoParam.getPkgInfo();
            if(StringUtils.isNotEmpty(pkgInfo.getTelephone())){
                pkgInfo.setTelephone(AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, pkgInfo.getTelephone()));
            }
            if ("1".equals(pkgInfo.getOperateType())&& StringUtils.isBlank(pkgInfo.getAccidentId())) {
                //重复提交验证
                String pkgKey=pkgInfo.getPaxId()+pkgInfo.getAccidentType()+pkgInfo.getPkgNo();
                String paxCreatePkgReapeat = (String) redisService.get(PAX_PKG_CREATE+pkgKey);
                if (StringUtils.isNotBlank(paxCreatePkgReapeat)) {
                    map.put("code","1");
                    map.put("msg","1分钟内对同一旅客重复建异常行李事故单!");
                    return new RenderResult("0", "1分钟内对同一旅客重复建异常行李事故单!",map);
                } else {
                    redisService.set(PAX_PKG_CREATE + pkgKey, pkgKey, 60);
                }
                boolean flag = pkgInfoServiceDao.judgeIsExit(pkgInfo.getPkgNo(), pkgInfo.getAccidentType(),false);
                if (flag) {
                    map.put("code","1");
                    map.put("msg","同一行李号只能创建1个同行李类型的事故单!");
                    return new RenderResult("0", "同一行李号只能创建1个同行李类型的事故单!",map);
                }
                //建立少收行李时，应先根据条件判断该行李是否已有破损/内件缺失事故单
                if("2".equals(pkgInfo.getAccidentType())){
                    boolean flag1 = pkgInfoServiceDao.judgeIsExit(pkgInfo.getPkgNo(),"1",false);
                    if(flag1){
                        map.put("code","1");
                        map.put("msg","该行李号已有破损/内件缺失事故单!");
                        return new RenderResult("0", "该行李号已有破损/内件缺失事故单!",map);
                    }
                    //同一航站不可出现同一行李多收+少收类型共存
                    boolean flag2 = pkgInfoServiceDao.judgeIsExit(pkgInfo.getPkgNo(),"3",true);
                    if(flag2){
                        map.put("code","1");
                        map.put("msg","同一航站不可出现同一行李少收+多收类型共存!");
                        return new RenderResult("0", "同一航站不可出现同一行李少收+多收类型共存!",map);
                    }
                    boolean flag3 = pkgInfoServiceDao.judgeIsExit(pkgInfo.getPkgNo(),"4",true);
                    if(flag3){
                        map.put("code","1");
                        map.put("msg","同一航站不可出现同一行李内件缺失+少收类型共存!");
                        return new RenderResult("0", "同一航站不可出现同一行李内件缺失+少收类型共存!",map);
                    }
                }
                //同一航站不可出现同一行李破损+多收、多收+少收类型共存
                if("3".equals(pkgInfo.getAccidentType())){
                    boolean flag1 = pkgInfoServiceDao.judgeIsExit(pkgInfo.getPkgNo(),"2",true);
                    if(flag1){
                        map.put("code","1");
                        map.put("msg","同一航站不可出现同一行李多收+少收类型共存!");
                        return new RenderResult("0", "同一航站不可出现同一行李多收+少收类型共存!",map);
                    }
                    boolean flag2 = pkgInfoServiceDao.judgeIsExit(pkgInfo.getPkgNo(),"1",true);
                    if(flag2){
                        map.put("code","1");
                        map.put("msg","同一航站不可出现同一行李多收+破损类型共存!");
                        return new RenderResult("0", "同一航站不可出现同一行李多收+破损类型共存!",map);
                    }
                }
                if("1".equals(pkgInfo.getAccidentType())){
                    boolean flag1 = pkgInfoServiceDao.judgeIsExit(pkgInfo.getPkgNo(),"3",true);
                    if(flag1){
                        map.put("code","1");
                        map.put("msg","同一航站不可出现同一行李破损+多收类型共存!");
                        return new RenderResult("0", "同一航站不可出现同一行李破损+多收类型共存!",map);
                    }

                }

            }
            String accidentId=pkgInfoService.savePkgInfo(pkgInfo);
            map.put("code","0");
            map.put("msg",accidentId);
            return RenderResult.success(map);
        } catch (Exception e) {
            e.printStackTrace();
            return new RenderResult("1", e.getMessage());
        }
    }

    /**
     * Title：detailInfo <br>
     * Description： 行李异常详情<br>
     * author：王建文 <br>
     * date：2020-3-22 15:40 <br>
     *
     * @param accidentId 事故单号
     */
    @GetMapping("detailInfo")
    @ApiOperation(value = "行李异常详情")
    public RenderResult<Map<String, Object>> detailInfo(String accidentId) {
        return RenderResult.success(pkgInfoService.getPkgDetailInfo(accidentId));
    }
    @GetMapping("pkgOrderDetailInfo")
    @ApiOperation(value = "异常行李赔付单详情")
    public RenderResult<Map<String, Object>> pkgOrderDetailInfo(String orderId) {
        return RenderResult.success(pkgInfoService.pkgOrderDetailInfo(orderId));
    }

    /**
     * Title：pkgCase <br>
     * Description： 事故单结案<br>
     * author：王建文 <br>
     * date：2020-3-22 15:48 <br>
     *
     * @param accidentId 事故单号
     */
    @PostMapping("pkgCase")
    @ApiOperation(value = "事故单结案")
    public RenderResult pkgCase(String accidentId) {
        try {
            pkgInfoService.pkgCase(accidentId.split(","));
            return RenderResult.success();
        } catch (Exception e) {
            return RenderResult.fail();
        }
    }

    @PostMapping("deleteDraftPkg")
    @ApiOperation(value = "删除草稿箱数据")
    public RenderResult deleteDraftPkg(String accidentIds) {
        try {
            pkgInfoService.deleteDraftPkg(accidentIds);
            return  RenderResult.success();
        } catch (Exception e) {
            return  RenderResult.fail();
        }
    }

    @GetMapping("getPkgSizeInfo")
    @ApiOperation(value = "行李箱尺寸信息")
    public RenderResult getPkgSizeInfo() {
        List<String> list = new ArrayList<>();
        List<ActConfigInfoVo> actConfigInfoList = actConfigInfoService.getRefuseInfo("2");
        if (actConfigInfoList.size() > 0) {
            for (ActConfigInfoVo actConfigInfoVo : actConfigInfoList) {
                list.add(actConfigInfoVo.getContent());
            }
        }
        return RenderResult.success(list);
    }

    @GetMapping("getSelectCityInfo")
    @ApiOperation(value = "获取航站下拉选项")
    public RenderResult getSelectCityInfo() {
        return RenderResult.success(flightCompensateDao.getSelectCityInfo());
    }
    /**
     * Title：auditPkgInfo <br>
     * Description： 发起赔付<br>
     * author：王建文 <br>
     * date：2020-5-18 14:40 <br>
     * @param
     * @return
     */
    @PostMapping("auditPkgInfo")
    @ApiOperation(value = "发起赔付")
    public RenderResult<Object> auditPkgInfo(@RequestBody PkgAuditParamInfoDto pkgAuditParamInfoDto) {
        try{
            String orderId=pkgInfoService.auditPkgInfo(pkgAuditParamInfoDto);
            return RenderResult.success(orderId);
        }catch (Exception e){
            return RenderResult.fail();
        }
    }
    @PostMapping("updatePkgInfoImg")
    @ApiOperation(value = "更新异常行李图片")
    public RenderResult<Object> updatePkgInfoImg(String accidentId,String imgUrl) {
        try{
           pkgInfoService.updatePkgInfoImg(accidentId,imgUrl);
            return RenderResult.success();
        }catch (Exception e){
            return RenderResult.fail();
        }
    }
    @PostMapping("invalidPkgInfo")
    @ApiOperation(value = "作废无赔付单的行李事故单")
    public RenderResult<Object> invalidPkgInfo(String accidentId) {
        try{
            pkgInfoService.invalidPkgInfo(accidentId);
            return RenderResult.success();
        }catch (Exception e){
            return RenderResult.fail();
        }
    }
    @GetMapping("getPaxPkgHistoryByIdNo")
    @ApiOperation(value = "查看旅客异常行李事故单数量")
    public RenderResult<Object> getPaxPkgHistoryByIdNo(String idNo) {
        try{
            return RenderResult.success(pkgInfoService.getPaxPkgHistoryByIdNo(idNo));
        }catch (Exception e){
            return RenderResult.fail();
        }
    }
}