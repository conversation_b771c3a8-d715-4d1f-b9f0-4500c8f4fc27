package com.swcares.scgsi.modules.flightCommon;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.swcares.scgsi.flight.service.FlightCompensateService;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;

/**
 * ClassName：com.swcares.scgsi.modules.flightCommon.FlightCommonController <br>
 * Description：TODO(基础查询的controller) <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月17日 下午2:37:07 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/h5/flightCommon")
@Api(tags = "基础查询的controller")
public class FlightCommonController extends BaseController {
    @Resource
    private FlightCompensateService flightCompensateService;

    /**
     * Title：getSelectCityInfoByInitial <br>
     * Description：TODO(获取航站下拉选项通过首字母分组) <br>
     * author：王磊 <br>
     * date：2020年4月17日 下午1:34:21 <br>
     * @return <br>
     */
    @GetMapping("getSelectCityInfoByInitial")
    @ApiOperation(value = "获取航站下拉选项通过首字母分组")
    public RenderResult<Object> getSelectCityInfoByInitial() {
        return  RenderResult.success(flightCompensateService.getSelectCityInfoByInitial());
    }

    @GetMapping("getOrderInfoByFlightINoAndFlightDate")
    @ApiOperation(value = "根据航班号航班日期查询所有的赔付单")
    public RenderResult getOrderInfoByFlightINoAndFlightDate(String flightNo, String flightDate,String payType,String status) {
        return RenderResult.success(flightCompensateService.getOrderInfoByFlightINoAndFlightDate(
                flightNo, flightDate,payType,status));
    }

}