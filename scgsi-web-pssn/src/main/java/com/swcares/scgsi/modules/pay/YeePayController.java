package com.swcares.scgsi.modules.pay;

import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.service.yee.YeePayService;
import com.swcares.scgsi.service.yee.YeeTransactionType;
import com.swcares.scgsi.service.yee.bean.YeeAuthentication;
import com.swcares.scgsi.service.yee.bean.YeeAuthenticationResult;
import com.swcares.scgsi.service.yee.bean.YeeBankCardQueryResult;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.web.RenderResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.modules.pay <br>
 * Description：易宝-实名认证接口 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 04月22日 15:20 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/yee")
@Slf4j
public class YeePayController {

    @Resource
    private YeePayService yeePayService;

    @Resource
    RedisService redisService;

    private static final String SUCCESS = "SUCCESS";

    private static final String RETCODE_SUCCESS = "0000";

    /**
     * Title：authentication <br>
     * Description： yee-实名认证请求<br>
     * author：傅欣荣 <br>
     * date：2020/4/23 9:14 <br>
     * @param
     * @return
     */
    @PostMapping("authentication")
    public RenderResult authentication(YeeAuthentication yeeAuthentication){
        yeeAuthentication.setMobilePhone(AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, yeeAuthentication.getMobilePhone()));
        log.info("yee-【前端传入实名认证请求参数：】{}",yeeAuthentication.toString());
        String orderId = yeePayService.handlePayOrderId();
        yeeAuthentication.setOrderId(orderId);
        yeeAuthentication.setTransactionType(YeeTransactionType.AUTHENTICATION_SEND);

        YeeAuthenticationResult yeeAuthenticationResult = yeePayService.authentication(yeeAuthentication, YeeAuthenticationResult.class);
        log.info("yee-【前端传入实名认证请求参数：】{},-实名认证结果：{}",yeeAuthentication.toString(),yeeAuthenticationResult.toString());
        if(SUCCESS.equals(yeeAuthenticationResult.getStatus()) && SUCCESS.equals(yeeAuthenticationResult.getState())){
            return RenderResult.success(orderId);
        }
        return RenderResult.fail();
    }


    /**
     * Title： queryAuthentication<br>
     * Description： 实名认证结果查询<br>
     * author：傅欣荣 <br>
     * date：2020/4/23 9:12 <br>
     * @param  orderId 实名认证订单号
     * @param  streamOrderId yee流水号
     * @return
     */
    @GetMapping("queryAuthentication")
    public RenderResult queryAuthentication(String orderId ,String streamOrderId){
        log.info("yee-【前端查询实名认证结果，请求参数】orderId：{},streamOrderId:{}",orderId,streamOrderId);
        YeeAuthenticationResult yeeAuthenticationResult = yeePayService.authenticationQuery(orderId,streamOrderId,YeeTransactionType.AUTHENTICATION_QUERY,YeeAuthenticationResult.class);
        log.info("yee-【前端查询实名认证结果，请求参数】orderId：{},streamOrderId:{}。实名认证查询结果：{}",orderId,streamOrderId,yeeAuthenticationResult.toString());
        if(SUCCESS.equals(yeeAuthenticationResult.getStatus()) && SUCCESS.equals(yeeAuthenticationResult.getState())){
            return RenderResult.success();
        }
        return RenderResult.fail();
    }


    /**
     * Title： yeeBankCardQuery<br>
     * Description：根据卡号查询所属银行信息<br>
     * author：傅欣荣 <br>
     * date：2020/7/10 16:34 <br>
     * @param bankCard 银行卡号
     * @return
     */
    @PostMapping("bankCardQuery")
    public RenderResult yeeBankCardQuery(String bankCard){
        log.info("yee-【前端查询银行卡信息，请求参数】bankCard：[{}]",bankCard);

        //从redis取
        Map<String, Object> parametersCache = (Map<String, Object>)redisService.get(bankCard.substring(0,6));
        if(null != parametersCache)
        return RenderResult.success(parametersCache);



        YeeBankCardQueryResult yeeBankCardQueryResult =
                yeePayService.bankCardQuery(bankCard,YeeTransactionType.BANK_CARD_QUERY,YeeBankCardQueryResult.class);
        log.info("yee-【前端查询银行卡信息，请求参数】bankCard：[{}]，查询结果：[{}]",bankCard,yeeBankCardQueryResult.toString());

        if(SUCCESS.equals(yeeBankCardQueryResult.getState()) && RETCODE_SUCCESS.equals(yeeBankCardQueryResult.getRetCode())
            && StringUtils.isNotBlank(yeeBankCardQueryResult.getBankCode())
                && StringUtils.isNotBlank(yeeBankCardQueryResult.getBankName())){
            Map<String, Object> parameters = new HashMap<String, Object>();
            parameters.put("bankCode",yeeBankCardQueryResult.getBankCode());
            parameters.put("bankName",yeeBankCardQueryResult.getBankName());
            redisService.set(yeeBankCardQueryResult.getVerifyCode(),parameters);
            return RenderResult.success(parameters);
        }
        return RenderResult.fail();



    }


}
