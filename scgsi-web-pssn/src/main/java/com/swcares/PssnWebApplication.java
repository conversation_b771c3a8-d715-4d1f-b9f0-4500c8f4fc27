package com.swcares;

import com.swcares.scgsi.base.ScgsiRepositoryImpl;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication
@EnableAutoConfiguration(exclude = {
        org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class
        ,org.activiti.spring.boot.SecurityAutoConfiguration.class})
@EnableJpaRepositories(repositoryBaseClass= ScgsiRepositoryImpl.class)
public class PssnWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(PssnWebApplication.class, args);
    }
}
