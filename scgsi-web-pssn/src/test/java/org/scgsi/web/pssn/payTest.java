package org.scgsi.web.pssn;

import com.swcares.PssnWebApplication;
import com.swcares.scgsi.api.PayService;
import com.swcares.scgsi.service.wx.WxPayService;
import com.swcares.scgsi.service.yee.YeePayService;
import com.yeepay.g3.sdk.yop.config.SDKConfig;
import com.yeepay.g3.sdk.yop.config.support.ConfigUtils;
import com.yeepay.g3.sdk.yop.exception.YopClientException;
import com.yeepay.g3.sdk.yop.utils.JsonUtils;
import com.yeepay.shade.org.apache.commons.lang3.StringUtils;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

//@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {PssnWebApplication.class})
public class payTest {

    PayService wxService = null;
    YeePayService yeePayService = null;

    @PostConstruct
    public void init() {
        wxService = new WxPayService("wxinfo.properties");
        //
        yeePayService = new YeePayService("merchantInfo.properties");

        //----------------微信支付使用说明-------------------------
        //  1.在scgsi-web项目下引入scgsi-pay模块
        //  2.resources下新增 wxinfo.properties文件，修改配置商户信息
        //  3.按照此类调用方法进行，支付调用。必填参数，实体类中有说明。
        //-----------------------------------------

        //----------------易宝支付使用说明-------------------------
        //  1.在scgsi-web项目下引入scgsi-pay模块
        //  2.resources下新增config文件以及config下 yop_sdk_config_default.json(
        //  易宝支付指定，若有多个配置，另看官方说明https://open.yeepay.com/)
        //  3.新增配置文件merchantInfo.properties 实名认证接口需要
        //  4.按照此类调用方法进行，支付调用。必填参数，实体类中有说明。
        //-----------------------------------------

        //备注：项目中使用多个相同类型支付平台，统一加载初始化配置。
        // 会导致配置替换的问题，读取配置信息时，若key相同取出的值会有问题
        //c19509d9b35ae553
    }

    /**
     * 实名认证-请求 同步反馈认证结果
     */
    /*@Test
    public void renzhen(){
        YeeAuthentication yee = new YeeAuthentication();
        yee.setOrderId("****************");
        yee.setName("");
        yee.setIdNo("");
        yee.setIdType("身份证");
        yee.setMobilePhone("");
        yee.setBankCardNo("");
        yee.setTransactionType(YeeTransactionType.AUTHENTICATION_SEND);

        System.out.println(yeePayService.authentication(yee,YeeAuthenticationResult.class).toString());

       *//* 请求YO：参数{requestTime=[2020-04-22 17:37:46], idCardType=[ID], mobilePhone=[***********], bankCardNo=[6228480469817665075], idCardNo=[510322199712130563], authType=[FastRealNameVerify], requestNo=[****************], userName=[傅欣荣], merchantNo=[***********]}，结果{
            "status" : "SUCCESS",
                    "merchantNo" : "***********",
                    "code" : "00000",
                    "message" : "SUCCESS",
                    "requestNo" : "****************",
                    "authType" : "FastRealNameVerify",
                    "ybOrderId" : "CFC25fdccf8cef14711969f4cd02c7919ea"
        }*//*
    }

    *//**
     * 实名认证-查询 同步反馈认证结果
     *//*
    @Test
    public void renzhenQ(){
        System.out.println(
            yeePayService.authenticationQuery("****************",
                    "",
            YeeTransactionType.AUTHENTICATION_QUERY,YeeAuthenticationResult.class).toString());
    }

    *//**
     * 商户余额
     *//*
    @Test
    public void merchantBalance(){
        YeeMerBalanceResult result = yeePayService.merchantBalanceQuery(YeeMerBalanceResult.class);
        System.out.println(result.toString());
    }


    *//**
     * 易宝支付一笔 同步、异步
     *//*
    @Test
    public void yeePay(){
        YeeTransferOrder tranOder = new YeeTransferOrder();
        tranOder.setBatchNo("****************");
        tranOder.setOrderId("****************");
        tranOder.setAmount(new BigDecimal(1));
        tranOder.setAccountName("傅欣荣");
        tranOder.setAccountNumber("");
        tranOder.setBankCode("CHINAUNIONPAY");
        tranOder.setDesc("航延补偿银联测试");
        tranOder.setTransactionType(YeeTransactionType.TRANSFER_SEND);
      *//*  Map<String, Object> result =  yeePayService.transfer(tranOder);*//*
        YeeTransferResult result =  yeePayService.transfer(tranOder,YeeTransferResult.class);
        System.out.println(result);
    }

    *//**
     * 易宝查询一笔
     *//*
    @Test
    public void yeeQuery(){

        YeeQueryResult result =  yeePayService.query("****************",
                "****************",YeeTransactionType.TRANSFER_QUERY,YeeQueryResult.class);
        System.out.println(result);
    }

    *//**
     * 微信支付一笔
     *//*
    @Test
    public void wxPay(){
      //  {"access_token":
      //  "30_R_i-ijZ6-Y0wDaROB-Ip-REs_zG_50h-3VJdufrEt8rJPOmKXy2r3YVCT4aEAhsgz2IHObXX1RBlSHLnkgGwcRDIvt8xgKJjSeT_5jLmVyY",
      //  "expires_in":7200,
      //  "refresh_token":"30_OGfcikVuN1ktfpJHHaqGZ6inSFaBJtFrKtJZgkrbivWfzouT7I6UIucrWsn-MWFLSC3F7HcztCWFIJleIGXwagdRnGSd0ihv4B12s4CzUeQ",
      //  "openid":"ozSj30t2waSeyhUe7Z22VwfRj3ac","scope":"snsapi_base"}
        WxTransferOrder tranOder = new WxTransferOrder();
        tranOder.setOutNo("****************");
        tranOder.setAmount(new BigDecimal(1));
        //  ozSj30vfSnf7S6Lio8NLaKba5MeE
        tranOder.setPayeeAccount("ozSj30t2waSeyhUe7Z22VwfRj3ac");
        tranOder.setPayeeName("夏阳");
        tranOder.setRemark("测试航延补偿金");
        tranOder.setTransactionType(WxTransactionType.TRANSFERS);
        WxTransferResult result = (WxTransferResult)wxService.transfer(tranOder,WxTransferResult.class);
        System.out.println(result.toString());
    }

    *//**
     * 微信查询
     *//*
    @Test
    public void wxQuery(){
       // PayService wxService = new WxPayService("wxinfo.properties");
       WxQueryResult result = (WxQueryResult)wxService.query("","***************",WxTransactionType.TRANSFERS_QUERY,WxQueryResult.class);
        System.out.println(result.toString());

    }

    *//**
     * map 转 类对象
     * @throws IllegalAccessException
     * @throws InstantiationException
     *//*
    @Test
    public void testMaptoObj() throws IllegalAccessException, InstantiationException, JSONException {
      *//*  Map<String, Object> map = new HashMap<String, Object>();
        map.put("return_code","SUCCESS");
        map.put("return_msg","");
        map.put("mch_appid","wxec38b8ff840bd989");
        map.put("mchid","10013274");
        map.put("device_info","");
        map.put("nonce_str","lxuDzMnRjpcXzxLx0q");
        map.put("result_code","SUCCESS");
        map.put("partner_trade_no","10013574201505191526582441");
        map.put("payment_no","1000018301201505190181489473");
        map.put("payment_time","2015-05-19 15：26：59");
        WxTransferResult result = JSONObject.parseObject(JSONObject.toJSONString(map), WxTransferResult.class);
        System.out.println(result.toString());*//*

      *//*  String idTypes = "{'身份证':'ID'}";
        JSONObject jsonObject = new JSONObject(idTypes);
        //通过getString("")分别取出里面的信息
        String name = jsonObject.getString("身份证");
        System.out.println(name);*//*
        System.out.println(new Date());
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("return_code","SUCCESS");
        map.put("return_code","dddd");
        System.out.println(map.toString());
    }

    @Test*/
    public void initConfig(){
        String filePath = "config\\yop_sdk_config_default.json";
       // SDKConfigUtils.loadConfig(filePath);
        System.out.println("-----------------");
        InputStream isF = payTest.class.getResourceAsStream(File.pathSeparator+"config"+File.pathSeparator+"yop_sdk_config_default.json");
        //SDKConfig sdkConfig = loadConfig("/config/yop_sdk_config_default.json");

       System.out.println("-----------------"+ File.separator);

    }
    public static SDKConfig loadConfig(String configFile) {
        InputStream fis = null;

        SDKConfig config;
        try {
            InputStream is = payTest.class.getResourceAsStream("/" +configFile);
            fis = ConfigUtils.getInputStream(configFile);
            config = (SDKConfig) JsonUtils.loadFrom(fis, SDKConfig.class);
        } catch (Exception var11) {
            throw new YopClientException("Errors occurred when loading SDKConfig.", var11);
        } finally {
            if (null != fis) {
                try {
                    fis.close();
                } catch (IOException var10) {
                }
            }

        }

        if (StringUtils.endsWith(config.getServerRoot(), "/")) {
            config.setServerRoot(StringUtils.substring(config.getServerRoot(), 0, -1));
        }

        if (StringUtils.endsWith(config.getYosServerRoot(), "/")) {
            config.setYosServerRoot(StringUtils.substring(config.getYosServerRoot(), 0, -1));
        }

        return config;
    }
}
