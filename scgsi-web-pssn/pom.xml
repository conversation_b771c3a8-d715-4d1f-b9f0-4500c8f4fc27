<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.swcares</groupId>
		<artifactId>scgsi</artifactId>
        <version>1.1.20</version>
	</parent>

	<artifactId>scgsi-web-pssn</artifactId>
	<name>scgsi-web-pssn</name>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-common</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-message</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-user</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-pay</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-irregular-flight</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codehaus.janino</groupId>
			<artifactId>janino</artifactId>
		</dependency>
		<dependency>
			<groupId>com.oracle</groupId>
			<artifactId>ojdbc6</artifactId>
			<version>11.2.0.4.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/src/main/resources/lib/ojdbc6-11.2.0.4.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot-starter</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-overbooking</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-luggage</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-quartz</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-exception</artifactId>
		</dependency>
        <dependency>
            <groupId>com.swcares</groupId>
            <artifactId>scgsi-hotel</artifactId>
        </dependency>
    </dependencies>

	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<includeSystemScope>true</includeSystemScope>
					<!--fork:如果没有该项配置,整个devtools不会起作用 -->
					<fork>true</fork>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
