package com.swcares.scgsi.pkg.service.impl;

import com.swcares.scgsi.pkg.dao.impl.LittlePkgMessageDaoImpl;
import com.swcares.scgsi.pkg.service.LittlePkgMessageService;
import com.swcares.scgsi.pkg.vo.LittlePkgMessageVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.pkg.service.impl <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 04月09日 10:47 <br>
 * @version v1.0 <br>
 */
@Service
public class LittlePkgMessageServiceImpl implements LittlePkgMessageService {
    @Resource
    private LittlePkgMessageDaoImpl littlePkgMessageDao;
    @Override
    public List<LittlePkgMessageVo> getLittlePkgMessage() {
        return littlePkgMessageDao.getLittlePkgMessage();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLittlePkgSendStatus(String accidentId) {
        littlePkgMessageDao.updateLittlePkgSendStatus(accidentId);
    }
}