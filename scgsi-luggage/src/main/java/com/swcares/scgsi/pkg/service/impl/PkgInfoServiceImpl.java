package com.swcares.scgsi.pkg.service.impl;

import com.alibaba.fastjson.JSON;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.aop.EncryptFieldAop;
import com.swcares.scgsi.audit.constant.DpOrderConstant;
import com.swcares.scgsi.audit.service.OrderAuditService;
import com.swcares.scgsi.audit.vo.CompensationProgressVo;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.form.ContentIdxForm;
import com.swcares.scgsi.common.model.form.ContentTraceForm;
import com.swcares.scgsi.common.model.form.FlightInfoListForm;
import com.swcares.scgsi.common.model.view.OriginalSegmentView;
import com.swcares.scgsi.entity.FocFlightInfo;
import com.swcares.scgsi.fileuploadanddownload.UploadAndDownload;
import com.swcares.scgsi.flight.dao.FlightsInfoDao;
import com.swcares.scgsi.flight.dao.OrdersInfoDao;
import com.swcares.scgsi.flight.dao.PassengerInfoDao;
import com.swcares.scgsi.flight.dao.impl.FlightCompensateDaoImpl;
import com.swcares.scgsi.flight.dto.PaxParseDto;
import com.swcares.scgsi.flight.entity.FlightsInfo;
import com.swcares.scgsi.flight.entity.OrdersInfo;
import com.swcares.scgsi.flight.entity.PassengerInfo;
import com.swcares.scgsi.flight.vo.PaxInfoParseVo;
import com.swcares.scgsi.hum.employee.entity.Employee;
import com.swcares.scgsi.overbook.dao.impl.CompensateInfoDaoImpl;
import com.swcares.scgsi.overbook.dao.impl.OrderInfoDaoImpl;
import com.swcares.scgsi.overbook.vo.CompensateInfoVo;
import com.swcares.scgsi.overbook.vo.PaxCompensateDetailsVo;
import com.swcares.scgsi.pkg.dao.PkgInfoDao;
import com.swcares.scgsi.pkg.dao.impl.PkgInfoServiceDaoImpl;
import com.swcares.scgsi.pkg.dto.PkgAuditParamInfoDto;
import com.swcares.scgsi.pkg.dto.PkgInfoDto;
import com.swcares.scgsi.pkg.dto.PkgInfoQueryParamDto;
import com.swcares.scgsi.pkg.entity.PkgInfo;
import com.swcares.scgsi.pkg.enums.PkgAccidentTypeEnum;
import com.swcares.scgsi.pkg.service.PkgInfoService;
import com.swcares.scgsi.pkg.vo.*;
import com.swcares.scgsi.service.FlightInfoService;
import com.swcares.scgsi.service.TraceService;
import com.swcares.scgsi.user.dao.UserJpaDao;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.util.Asserts;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * ClassName：com.swcares.scgsi.pkg.service.impl <br>
 * Description：异常行李业务处理层 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月20日 16:06 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class PkgInfoServiceImpl implements PkgInfoService {
    //异常行李提交标识
    private static final String OPERATE_TYPE = "1";
    //图片临时目录标识
    private static final String IMG_TEMP_PATH = "temp";
    @Resource
    private PkgInfoDao pkgInfoDao;

    @Resource
    private PkgInfoServiceDaoImpl pkgInfoServiceDao;

    @Resource
    private PassengerInfoDao passengerInfoDao;

    @Resource
    private TraceService traceService;

    @Resource
    private FlightInfoService flightInfoService;

    @Resource
    private FlightsInfoDao flightsInfoDao;

    @Resource
    private OrdersInfoDao ordersInfoDao;

    @Resource
    private UploadAndDownload uploadAndDownload;

    @Resource
    private OrderAuditService orderAuditService;

    @Resource
    private UserJpaDao userJpaDao;

    @Resource
    private FlightCompensateDaoImpl flightCompensateDao;

    @Resource
    private OrderInfoDaoImpl orderInfoDaoImpl;

    @Resource
    private CompensateInfoDaoImpl compensateInfoDaoImpl;

    @Override
    public Map<String, Object> authUserInfo(String flightNo, String flightDate, String keySearch) {
        Map<String, Object> data = new HashMap<>();
        ContentTraceForm form = new ContentTraceForm();
        form.setFlightNum(flightNo);
        form.setFlightDate(flightDate.replaceAll("-", "/"));
        form.setKeySearch(keySearch);
        form.setIsCancel("Y");
        Object object = traceService.getPsgListByFilght(form);
        PaxParseDto parseObject = JSON.parseObject(object.toString(), PaxParseDto.class);
        List<PaxInfoParseVo> dataList = parseObject.getDataList();
        if (dataList.size() > 0) {
            PaxInfoParseVo paxInfoParseVo = dataList.get(0);
            data.put("flightNo", flightNo);
            data.put("flightDate", flightDate);
            FlightInfoListForm form1 = new FlightInfoListForm();
            form1.setFlightNum(flightNo);
            form1.setFlightDate(flightDate.replaceAll("-", "/"));
            OriginalSegmentView originalSegmentView = flightInfoService.getOriginalSegment(form1);
            data.put("segment", paxInfoParseVo.getSegment().replaceAll(" ",""));
            data.put("std", originalSegmentView.getStd());
            data.put("sta", originalSegmentView.getSta());
            data.put("acType", originalSegmentView.getAcType());
            data.put("acReg", originalSegmentView.getAcReg());
            data.put("paxName", paxInfoParseVo.getPsgName());
            List<Map<String, Object>> pkgInfoMapList = getPaxExitPkgInfo(paxInfoParseVo.getIdx());
            data.put("paxId", paxInfoParseVo.getIdx());
            data.put("tktNo", paxInfoParseVo.getEtNum());
            data.put("idType", paxInfoParseVo.getIdType());
            data.put("idNo", paxInfoParseVo.getIdNum());
            data.put("telephone", "");
            data.put("pkgWeight", paxInfoParseVo.getBagWht());
            data.put("paxPkgNo", paxInfoParseVo.getBagTag());
            data.put("exitPkgNo", pkgInfoMapList);
            data.put("isChild", paxInfoParseVo.getIsChild());
            data.put("babyName", paxInfoParseVo.getIsInfant());
            data.put("pkgSegment", paxInfoParseVo.getSegment().replaceAll(" ", ""));
        }
        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String savePkgInfo(PkgInfoDto pkgInfoDto) throws Exception {
        log.info("保存异常行李事故单接口，参数:{}",pkgInfoDto.toString());
        if ("null".equals(pkgInfoDto.getPayMoney())){
            pkgInfoDto.setPayMoney("");
        }
        String accidentId = pkgInfoDto.getAccidentId();
        if (StringUtils.isBlank(accidentId)) {
            int i=(int)(Math.random()*900)+100;
            //accidentId =PkgAccidentTypeEnum.build(pkgInfoDto.getAccidentType()).getValue()+ DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMMSS);
           accidentId =PkgAccidentTypeEnum.build(pkgInfoDto.getAccidentType()).getValue()+ DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMMSS)+String.valueOf(i);
        }
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String createUser = (String) authentication.getPrincipal();
        //String createUser ="609920051";
        Date createTime = new Date();
        Employee employee = userJpaDao.findByTuNo(createUser);
        String serviceCity = "";
        if (null != employee && StringUtils.isNotBlank(employee.getAirport3code())) {
            serviceCity = employee.getAirport3code();
        }
        pkgInfoDto.setServiceCity(serviceCity);
        //1.获取旅客
        PaxInfoParseVo paxInfoParseVo = getPaxInfo(pkgInfoDto.getPaxId());
        if (StringUtils.isNotBlank(pkgInfoDto.getTelephone())) {
            paxInfoParseVo.setPhone(pkgInfoDto.getTelephone());
        }
        //2.旅客创建
        List<PkgInfoVo> paxList = pkgInfoServiceDao.isExitPax(pkgInfoDto.getPaxId());
        //2.旅客创建
        if (paxList.size() > 0) {
            pkgInfoServiceDao.deletePkgPax(pkgInfoDto.getPaxId());
        }
        savePaxInfo(paxInfoParseVo, "", createTime, createUser, pkgInfoDto.getPayMoney());
        if (StringUtils.isNotBlank(pkgInfoDto.getAccidentId())) {
                pkgInfoServiceDao.deletePkgByAccidentId(pkgInfoDto.getAccidentId());
        }
        //2.根据旅客查询航班信息id
        FocFlightInfo flightInfo = getFlightInfo(paxInfoParseVo);
        //保存事故单信息
        savePkgInfo(pkgInfoDto, createUser, createTime, "", flightInfo.getFlightId(), accidentId, "0");
        return accidentId;
    }

    /**
     * Title：setOrderId <br>
     * Description：设置赔付单id<br>
     * author：王建文 <br>
     * date：2020-4-7 10:02 <br>
     *
     * @param orderId 赔付单号
     * @return
     */
    private String setOrderId(String orderId) {
        orderId = DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMM);
        //随机5位数
        int random5 = (int) ((Math.random() * 9 + 1) * 10000);
        orderId += String.valueOf(random5);
        //加上赔偿类型
        orderId += '1';
        return orderId;
    }

    /**
     * Title：getFlightInfo <br>
     * Description： 根据旅客信息获取航班信息<br>
     * author：王建文 <br>
     * date：2020-4-7 10:03 <br>
     *
     * @param paxInfoParseVo
     * @return
     */
    private FocFlightInfo getFlightInfo(PaxInfoParseVo paxInfoParseVo) {
        FlightInfoListForm form = new FlightInfoListForm();
        form.setFlightNum(paxInfoParseVo.getFlightNum());
        form.setFlightDate(paxInfoParseVo.getFlightDate().replaceAll("-", "/"));
        if (flightInfoService.getFlightInfoList(form).size() > 0) {
            return flightInfoService.getFlightInfoList(form).get(0);
        }
        return new FocFlightInfo();
    }

    /**
     * Title：saveOrderInfo <br>
     * Description：保存赔付单信息<br>
     * author：王建文 <br>
     * date：2020-4-7 10:04 <br>
     *
     * @param
     * @return
     */
    public void saveOrderInfo(Date createTime, String createUser, String orderId, FocFlightInfo focFlightInfo, String payMoney, String serviceCity,
            String paxSegment, String accidentId,String remark) {
        OrdersInfo orderInfo = new OrdersInfo();
        orderInfo.setOrderId(orderId);
        orderInfo.setFlightId(focFlightInfo.getFlightId());
        orderInfo.setFlightNo(focFlightInfo.getFlightNo());
        orderInfo.setFlightDate(focFlightInfo.getFlightDate().replaceAll("/", "-"));
        orderInfo.setCreateTime(createTime);
        orderInfo.setCreateId(createUser);
        orderInfo.setStatus("7");
        orderInfo.setServiceCity(flightCompensateDao.getCityCodeInfoByCityCode3(serviceCity).getCityChName());
        orderInfo.setSumMoney(payMoney);
        orderInfo.setChoiceSegment(paxSegment);
        orderInfo.setPayType(1);
        orderInfo.setRemark("");
        if(StringUtils.isNotBlank(remark)){
            orderInfo.setRemark(remark);
        }
        orderInfo.setAccidentId(accidentId);
        ordersInfoDao.save(orderInfo);
    }

    /**
     * Title：saveFlightInfo <br>
     * Description： 保存航班信息<br>
     * author：王建文 <br>
     * date：2020-4-7 10:04 <br>
     *
     * @param
     * @return
     */
    private void saveFlightInfo(String orderId, Date createTime, String createUser, FocFlightInfo focFlightInfo, String segment) {
        FlightsInfo flightInfo = new FlightsInfo();
        flightInfo.setOrderId(orderId);
        flightInfo.setFlightNo(focFlightInfo.getFlightNo());
        flightInfo.setFlightDate(focFlightInfo.getFlightDate().replaceAll("/", "-"));
        FlightInfoListForm form1 = new FlightInfoListForm();
        form1.setFlightNum(focFlightInfo.getFlightNo());
        form1.setFlightDate(focFlightInfo.getFlightDate().replaceAll("-", "/"));
        OriginalSegmentView originalSegmentView = flightInfoService.getOriginalSegment(form1);
        flightInfo.setSegment(originalSegmentView.getSegment().replaceAll(" ",""));
        flightInfo.setCreateTime(createTime);
        flightInfo.setCreateId(createUser);
        flightInfo.setAcType(focFlightInfo.getAcType());
        flightInfo.setFlightId(focFlightInfo.getFlightId());
        flightInfo.setStd(focFlightInfo.getFlightDate() + " " + focFlightInfo.getStd());
        flightInfo.setSta(focFlightInfo.getFlightDate() + " " + focFlightInfo.getSta());
        flightsInfoDao.save(flightInfo);
    }

    /**
     * Title：savePaxInfo <br>
     * Description： 保存旅客信息<br>
     * author：王建文 <br>
     * date：2020-4-7 10:05 <br>
     *
     * @param
     * @return
     */
    private void savePaxInfo(PaxInfoParseVo paxInfoParseVo, String orderId, Date createTime, String createUser, String money) {
        PassengerInfo paxInfo = new PassengerInfo();
        paxInfo.setOrderId(orderId);
        paxInfo.setCreateTime(createTime);
        paxInfo.setCreateId(createUser);
        paxInfo.setReceiveStatus(0);
        paxInfo.setCurrentAmount(0);
        if (StringUtils.isNotBlank(money)) {
            paxInfo.setCurrentAmount(Integer.valueOf(new BigDecimal(money).stripTrailingZeros().toPlainString()));
        }
        paxInfo.setPaxId(paxInfoParseVo.getIdx());
        paxInfo.setPaxName(paxInfoParseVo.getPsgName());
        paxInfo.setIdType(paxInfoParseVo.getIdType());
        paxInfo.setIdNo(paxInfoParseVo.getIdNum());
        paxInfo.setSex(paxInfoParseVo.getGender());
        paxInfo.setTelephone(paxInfoParseVo.getPhone());
        paxInfo.setSegment(paxInfoParseVo.getSegment().trim());
        paxInfo.setOrgCityAirp(paxInfoParseVo.getOrig());
        paxInfo.setDstCityAirp(paxInfoParseVo.getDest());
        paxInfo.setPaxStatus(paxInfoParseVo.getStatus());
        paxInfo.setMainClass(paxInfoParseVo.getMainClass());
        paxInfo.setSubClass(paxInfoParseVo.getSellClass());
        paxInfo.setTktNo(paxInfoParseVo.getEtNum());
        paxInfo.setIsInfant(paxInfoParseVo.getIsInfant());
        paxInfo.setIsChild(paxInfoParseVo.getIsChild());
        paxInfo.setTktDate(paxInfoParseVo.getPrintTicketTime());
        EncryptFieldAop.manualEncrypt(paxInfo);
        passengerInfoDao.save(paxInfo);
    }

    /**
     * Title：getPaxInfo <br>
     * Description： 根据旅客id获取旅客信息<br>
     * author：王建文 <br>
     * date：2020-4-7 10:05 <br>
     *
     * @param paxId 旅客id
     * @return
     */
    private PaxInfoParseVo getPaxInfo(String paxId) {
        ContentIdxForm contentIdxForm = new ContentIdxForm(paxId);
        Object object = traceService.getPsgByIdx(contentIdxForm);
        PaxParseDto parseObject = JSON.parseObject(object.toString(), PaxParseDto.class);
        return parseObject.getDataList().get(0);
    }

    /**
     * Title：savePkgInfo <br>
     * Description：保存异常行李信息<br>
     * author：王建文 <br>
     * date：2020-3-20 16:52 <br>
     *
     * @param
     * @return
     */
    private void savePkgInfo(PkgInfoDto pkgInfoDto, String createUser, Date createTime, String orderId, String flightId, String accidentId,
            String dataType) throws Exception {
        PkgInfo pkgInfo = new PkgInfo();
        BeanUtils.copyProperties(pkgInfoDto, pkgInfo);
        if (!OPERATE_TYPE.equals(pkgInfoDto.getOperateType())) {
            pkgInfoServiceDao.deletePkgByAccidentId(accidentId);
            pkgInfo.setStatus("2");
        } else {
            pkgInfo.setStatus("0");
        }
        //数据导入状态为已结案
        if ("1".equals(dataType)) {
            pkgInfo.setStatus("1");
        }
        if (StringUtils.isNotBlank(pkgInfoDto.getImgUrl())) {
            String[] imgs = pkgInfoDto.getImgUrl().split(",");
            String saveImg = "";
            for (String img : imgs) {
                String img1 = uploadAndDownload.saveImg(img);
                saveImg += img1 + ",";
            }
            pkgInfo.setImgUrl(saveImg);
        }
        if (StringUtils.isNotBlank(pkgInfoDto.getPayMoney())) {
            pkgInfo.setPayMoney(pkgInfoDto.getPayMoney());
        }
        if (StringUtils.isBlank(pkgInfoDto.getPayMoney())) {
            pkgInfo.setPayMoney("");
        }
        pkgInfo.setAccidentId(accidentId);
        pkgInfo.setCreateUser(createUser);
        pkgInfo.setCreateTime(createTime);
        pkgInfo.setOrderId(orderId);
        pkgInfo.setFlightId(flightId);
        pkgInfo.setDataType(dataType);
        log.info("保存异常行李事故单接口，保存结果:{}",pkgInfo.toString());
        EncryptFieldAop.manualEncrypt(pkgInfo);
        pkgInfoDao.save(pkgInfo);
    }

    @Override
    public List<PkgInfoVo> getPkgInfo(PkgInfoQueryParamDto queryParamDto) {
        return pkgInfoServiceDao.getPkgInfo(queryParamDto);
    }

    @Override
    public Map<String, Object> getPkgDetailInfo(String accidentId) {
        log.info("------------行李异常详情--查询参数[{}]",accidentId);
        Map<String, Object> dataMap = new HashMap<>();
        PkgInfoDetailInfoVo pkgInfoDetailInfoVo = pkgInfoServiceDao.getPkgDetailInfo(accidentId);
        pkgInfoDetailInfoVo.setAccidentId(accidentId);
        FlightInfoListForm form1 = new FlightInfoListForm();
        form1.setFlightNum(pkgInfoDetailInfoVo.getFlightNo());
        form1.setFlightDate(pkgInfoDetailInfoVo.getFlightDate().replaceAll("-", "/"));
        OriginalSegmentView originalSegmentView = flightInfoService.getOriginalSegment(form1);
        pkgInfoDetailInfoVo.setStd(originalSegmentView.getStd());
        pkgInfoDetailInfoVo.setSta(originalSegmentView.getSta());
        pkgInfoDetailInfoVo.setAcType(originalSegmentView.getAcType());
        pkgInfoDetailInfoVo.setSegment(originalSegmentView.getSegment().replaceAll(" ", ""));
        PkgOrderInfoVo pkgOrderInfoVo = pkgInfoServiceDao.getPkgOrderInfoByAccidentId(accidentId);
        ContentIdxForm contentIdxForm = new ContentIdxForm(pkgOrderInfoVo.getPaxId());
        Object object = traceService.getPsgByIdx(contentIdxForm);
        PaxParseDto parseObject = JSON.parseObject(object.toString(), PaxParseDto.class);
        PaxInfoParseVo paxInfoParseVo = parseObject.getDataList().get(0);
        if (StringUtils.isNotBlank(pkgInfoDetailInfoVo.getPkgNo())) {
            if(StringUtils.isNotBlank(paxInfoParseVo.getBagTag())&&!paxInfoParseVo.getBagTag().contains(pkgInfoDetailInfoVo.getPkgNo())){
                pkgInfoDetailInfoVo.setPaxPkgNo(paxInfoParseVo.getBagTag() + "," + pkgInfoDetailInfoVo.getPkgNo());
            }
            if(StringUtils.isNotBlank(paxInfoParseVo.getBagTag())&&paxInfoParseVo.getBagTag().contains(pkgInfoDetailInfoVo.getPkgNo())){
                pkgInfoDetailInfoVo.setPaxPkgNo(paxInfoParseVo.getBagTag());
            }
            if(StringUtils.isBlank(paxInfoParseVo.getBagTag())){
                pkgInfoDetailInfoVo.setPaxPkgNo(pkgInfoDetailInfoVo.getPkgNo());
            }

        } else {
            pkgInfoDetailInfoVo.setPaxPkgNo(paxInfoParseVo.getBagTag());
        }
        pkgInfoDetailInfoVo.setExitPkgNo(getPaxExitPkgInfo(pkgOrderInfoVo.getPaxId()));
        if (StringUtils.isNotBlank(pkgInfoDetailInfoVo.getPkgNo())) {
            if (pkgInfoDetailInfoVo.getPkgNo().contains(",")) {
                pkgInfoDetailInfoVo.setPkgNo(pkgInfoDetailInfoVo.getPkgNo().substring(0, pkgInfoDetailInfoVo.getPkgNo().lastIndexOf(",")));
            }
        }
        if (StringUtils.isNotBlank(pkgInfoDetailInfoVo.getPkgSize())) {
            if (pkgInfoDetailInfoVo.getPkgSize().contains(",")) {
                pkgInfoDetailInfoVo.setPkgSize(pkgInfoDetailInfoVo.getPkgSize().substring(0, pkgInfoDetailInfoVo.getPkgSize().lastIndexOf(",")));
            }
        }
        pkgInfoDetailInfoVo.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, pkgInfoDetailInfoVo.getTelephone()));
        dataMap.put("pkgInfoDetailInfo", pkgInfoDetailInfoVo);
        List<PkgOrderInfoVo> pkgOrderInfoVoList1 = new ArrayList<>();
        if (StringUtils.isNotBlank(pkgOrderInfoVo.getPkgSize()) && StringUtils.isNotBlank(pkgOrderInfoVo.getPayMoney())&&(Integer.valueOf(pkgOrderInfoVo.getPayMoney())>0)) {
            pkgOrderInfoVo.setPayType("2");
        }
        if (StringUtils.isBlank(pkgOrderInfoVo.getPkgSize()) && StringUtils.isNotBlank(pkgOrderInfoVo.getPayMoney())&&(Integer.valueOf(pkgOrderInfoVo.getPayMoney())>0)) {
            pkgOrderInfoVo.setPayType("0");
        }
        if (StringUtils.isNotBlank(pkgOrderInfoVo.getPkgSize()) && StringUtils.isBlank(pkgOrderInfoVo.getPayMoney())) {
            pkgOrderInfoVo.setPayType("1");
        }
        String orderId = pkgOrderInfoVo.getOrderId();
        if (StringUtils.isNotBlank(orderId)) {
            List<Map<String, Object>> orderList = flightCompensateDao.getPkgOrderInfo(orderId);
            List<Map<String, Object>> showOrderList=new ArrayList<>();
            int payMoney=0;
            for(Map<String,Object> map:orderList){
                if(null!=map.get("PAYMONEY")){
                    payMoney+=Integer.valueOf(map.get("PAYMONEY").toString());
                }
                map.put("ISSHOWREEDIT",orderAuditService.verifyIsShowReEdit(map.get("ORDERID").toString(),map.get("CREATEID").toString(),map.get("STATUS").toString()));
                showOrderList.add(map);
            }
            dataMap.put("orderList", showOrderList);
            pkgOrderInfoVo.setPayMoney(String.valueOf(payMoney));
        } else {
            dataMap.put("orderList", new ArrayList<>());
            pkgOrderInfoVo.setPayMoney("0");
        }
        pkgOrderInfoVo.setPkgSize(pkgInfoDetailInfoVo.getPkgSize());
        pkgOrderInfoVo.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, pkgOrderInfoVo.getTelephone()));
        pkgOrderInfoVoList1.add(pkgOrderInfoVo);
        dataMap.put("orderInfoList", pkgOrderInfoVoList1);
        return dataMap;
    }

    private List<Map<String, Object>> getPaxExitPkgInfo(String paxId) {
        List<Map<String, Object>> pkgInfoMapList = new ArrayList<>();
        List<PkgInfoVo> pkgInfoVoList = pkgInfoServiceDao.getPaxPkgInfo(paxId);
        if (pkgInfoVoList.size() > 0) {
            List<String> pkgNoList = new ArrayList<>();
            for (PkgInfoVo pkgInfoVo : pkgInfoVoList) {
                if (StringUtils.isNotBlank(pkgInfoVo.getPkgNo())) {
                    pkgNoList.add(pkgInfoVo.getPkgNo());
                }
            }
            //去掉重复的行李号
            HashSet hPkgNoListList = new HashSet(pkgNoList);
            pkgNoList.clear();
            pkgNoList.addAll(hPkgNoListList);
            for (int i = 0; i < pkgNoList.size(); i++) {
                Map<String, Object> map = new HashMap<String, Object>();
                String accidentType = "";
                for (int j = 0; j < pkgInfoVoList.size(); j++) {
                    if (pkgNoList.get(i).equals(pkgInfoVoList.get(j).getPkgNo())) {
                        accidentType += pkgInfoVoList.get(j).getAccidentType() + ",";
                    }
                }
                map.put("pkgNo", pkgNoList.get(i));
                map.put("accidentType", accidentType.substring(0, accidentType.length() - 1));
                map.put("disabled", "disabled");
                pkgInfoMapList.add(map);
            }
        }
        return pkgInfoMapList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pkgCase(String[] accidentIds) {
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String createUser = (String) authentication.getPrincipal();
        String serviceCity = "";
        try {
            Employee employee = userJpaDao.findByTuNo(createUser);
            if (null != employee && StringUtils.isNotBlank(employee.getAirport3code())) {
                serviceCity = employee.getAirport3code();
            }
            for (String accidentId : accidentIds) {
                PkgOrderInfoVo pkgOrderInfoVo = pkgInfoServiceDao.getPkgOrderInfoByAccidentId(accidentId);
                if (!serviceCity.equals(pkgOrderInfoVo.getServiceCity())) {
                    throw new BusinessException(MessageCode.PAX_PKG_WEB_OPERATE_LIMIT.getCode());
                }
            }
            pkgInfoServiceDao.pkgCase(accidentIds);
        } catch (Exception e) {
            log.error("web端异常行李事故单结案获取当前登录服务航站异常");
        }

    }

    @Override
    public QueryResults getPkgInfoPage(PkgInfoQueryParamDto queryParamDto) {
        QueryResults queryResults =pkgInfoServiceDao.getPkgInfoPage(queryParamDto);
        List<PkgPageInfoVo> pkgPageInfoVoList=(List<PkgPageInfoVo>)queryResults.getList();
        if(pkgPageInfoVoList.size()>0){
            List<PkgPageInfoVo> showPkgInfoList=new ArrayList<>();
            pkgPageInfoVoList.forEach(pkgPageInfoVo->{
                //1.判断赔付单是否为空
                if(StringUtils.isNotBlank(pkgPageInfoVo.getOrderId())){
                    String[] orderIds=pkgPageInfoVo.getOrderId().split(",");
                    if(orderIds.length>1){
                        String auditor="";
                        String grantUser="";
                        for(String orderId:orderIds){
                            //处理发放人
                            Map<String,Object> map=flightCompensateDao.getOrderIssueUserAndLastAuditor(orderId);
                            if(null!=map.get("AUDITOR")){
                                auditor+=map.get("AUDITOR").toString()+"("+orderId+")"+";";
                            }
                            if(null!=map.get("GRANTUSER")){
                                grantUser+=map.get("GRANTUSER").toString()+"("+orderId+")"+";";
                            }
                        }
                        if(StringUtils.isNotBlank(auditor)){
                            pkgPageInfoVo.setAuditor(auditor.substring(0,auditor.length()-1));
                        }
                        if(StringUtils.isNotBlank(grantUser)){
                            pkgPageInfoVo.setGrantUser(grantUser.substring(0,grantUser.length()-1));
                        }
                    }else{
                        for(String orderId:orderIds){
                            //处理发放人
                            Map<String,Object> map=flightCompensateDao.getOrderIssueUserAndLastAuditor(orderId);
                            if(null!=map.get("AUDITOR")){
                                pkgPageInfoVo.setAuditor(map.get("AUDITOR").toString());
                            }
                        }
                    }
                }
                //处理赔付次数和赔付金额(赔付金额不是导入的情况)
                pkgPageInfoVo.setPayCount(pkgInfoServiceDao.getPkgInfoPayCount(pkgPageInfoVo.getPaxId(),pkgPageInfoVo.getPkgNo()));
                if("否".equals(pkgPageInfoVo.getDataType())){
                    pkgPageInfoVo.setTotalPay(pkgInfoServiceDao.getPkgInfoTotalPay(pkgPageInfoVo.getPaxId(),pkgPageInfoVo.getFlightNo(),pkgPageInfoVo.getFlightDate(),"1"));
                }
                showPkgInfoList.add(pkgPageInfoVo);
            });
            queryResults.setList(showPkgInfoList);
        }
        return queryResults;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDraftPkg(String accidentIds) {
        pkgInfoServiceDao.deleteDraftPkg(accidentIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePkgInfoByExcel(PkgInfoDto pkgInfoDto, PaxInfoParseVo paxInfoParseVo) throws Exception {
        String  accidentId =PkgAccidentTypeEnum.build(pkgInfoDto.getAccidentType()).getValue()+ DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMMSS);
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String createUser = (String) authentication.getPrincipal();
        Date createTime = new Date();
        //1.旅客信息
        if (pkgInfoServiceDao.isExitPax(pkgInfoDto.getPaxId()).size() <= 0) {
            savePaxInfo(paxInfoParseVo, "", createTime, createUser, pkgInfoDto.getPayMoney());
        }
        FocFlightInfo flightInfo = getFlightInfo(paxInfoParseVo);
        //保存事故单信息
        savePkgInfo(pkgInfoDto, createUser, createTime, "", flightInfo.getFlightId(), accidentId, "1");
    }

    @Override
    public List<PkgExportInfoVo> getExportPkgInfo(PkgInfoQueryParamDto queryParamDto) {
        List<PkgExportInfoQueryVo> pkgExportInfoQueryVoList=pkgInfoServiceDao.getExportPkgInfo(queryParamDto);
        List<PkgExportInfoVo> pkgExportInfoVoListExport=new ArrayList<>();
        if(pkgExportInfoQueryVoList.size()>0){
            for(PkgExportInfoQueryVo pkgExportInfoQueryVo:pkgExportInfoQueryVoList){
                 //1.判断赔付单是否为空
                if(StringUtils.isNotBlank(pkgExportInfoQueryVo.getOrderId())){
                    String[] orderIds=pkgExportInfoQueryVo.getOrderId().split(",");
                    if(orderIds.length>1){
                        String auditor="";
                        String grantUser="";
                        for(String orderId:orderIds){
                            //处理发放人
                            Map<String,Object> map=flightCompensateDao.getOrderIssueUserAndLastAuditor(orderId);
                            if(null!=map.get("AUDITOR")){
                                auditor+=map.get("AUDITOR").toString()+"("+orderId+")"+";";
                            }
                            if(null!=map.get("GRANTUSER")){
                                grantUser+=map.get("GRANTUSER").toString()+"("+orderId+")"+";";
                            }
                        }
                        if(StringUtils.isNotBlank(auditor)){
                            pkgExportInfoQueryVo.setAuditor(auditor.substring(0,auditor.length()-1));
                        }
                        if(StringUtils.isNotBlank(grantUser)){
                            pkgExportInfoQueryVo.setGrantUser(grantUser.substring(0,grantUser.length()-1));
                        }

                    }else{
                        for(String orderId:orderIds){
                            //处理发放人
                            Map<String,Object> map=flightCompensateDao.getOrderIssueUserAndLastAuditor(orderId);
                            if(null!=map.get("AUDITOR")){
                                pkgExportInfoQueryVo.setAuditor(map.get("AUDITOR").toString());
                            }
                        }
                    }
                }
                //处理赔付次数和赔付金额(赔付金额不是导入的情况)
                pkgExportInfoQueryVo.setPayCount(pkgInfoServiceDao.getPkgInfoPayCount(pkgExportInfoQueryVo.getPaxId(),pkgExportInfoQueryVo.getPkgNo()));
                if("否".equals(pkgExportInfoQueryVo.getDataType())){
                    pkgExportInfoQueryVo.setTotalPay(pkgInfoServiceDao.getPkgInfoTotalPay(pkgExportInfoQueryVo.getPaxId(),pkgExportInfoQueryVo.getFlightNo(),pkgExportInfoQueryVo.getFlightDate(),"1"));
                }
                PkgExportInfoVo pkgExportInfoVo=new PkgExportInfoVo();
                BeanUtils.copyProperties(pkgExportInfoQueryVo, pkgExportInfoVo);
                pkgExportInfoVoListExport.add(pkgExportInfoVo);
            }
        }
        return pkgExportInfoVoListExport;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String auditPkgInfo(PkgAuditParamInfoDto pkgAuditParamInfoDto) throws Exception {
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String createUser = (String) authentication.getPrincipal();
        //String createUser ="262345661";
        Date createTime = new Date();
        Employee employee = userJpaDao.findByTuNo(createUser);
        String serviceCity = "";
        if (null != employee && StringUtils.isNotBlank(employee.getAirport3code())) {
            serviceCity = employee.getAirport3code();
        }
        String accidentId = pkgAuditParamInfoDto.getAccidentId();
        String orderId = pkgAuditParamInfoDto.getOrderId();
        String payMoney = pkgAuditParamInfoDto.getPayMoney();
        log.info("异常行李发起赔付，参数：{}",pkgAuditParamInfoDto.toString());
        PkgOrderInfoVo pkgInfoDto = pkgInfoServiceDao.getPkgOrderInfoByAccidentId(accidentId);
        if(StringUtils.isBlank(orderId)&&StringUtils.isBlank(payMoney)&&StringUtils.isBlank(pkgAuditParamInfoDto.getPkgSize())){
            payMoney=pkgInfoDto.getPayMoney();
        }

        String pkgSize ="";
        if(StringUtils.isNotBlank(pkgInfoDto.getPkgSize())){
            pkgSize=pkgInfoDto.getPkgSize();
        }
        if (StringUtils.isNotBlank(pkgAuditParamInfoDto.getPkgSize())) {
            if(StringUtils.isBlank(pkgSize)){
                pkgSize +=pkgAuditParamInfoDto.getPkgSize() + ",";
            }else{
                if(pkgSize.lastIndexOf(",")==-1){
                    pkgSize +=","+pkgAuditParamInfoDto.getPkgSize() + ",";
                }else{
                    pkgSize +=pkgAuditParamInfoDto.getPkgSize() + ",";
                }
            }
        }
        if (StringUtils.isNotBlank(payMoney) && Integer.valueOf(payMoney) > 0) {
            if (StringUtils.isNotBlank(orderId)) {
                // 删除关联的赔偿单信息
                flightCompensateDao.deleteOrderInfo(orderId);
            } else {
                orderId = setOrderId(orderId);
            }
            //1.获取旅客
            PaxInfoParseVo paxInfoParseVo = getPaxInfo(pkgInfoDto.getPaxId());
            List<PkgInfoVo> paxList = pkgInfoServiceDao.isExitPax(pkgInfoDto.getPaxId());
            //2.旅客创建
            if (paxList.size() > 0) {
                pkgInfoServiceDao.deletePkgPax(pkgInfoDto.getPaxId());
            }
            if(StringUtils.isNotBlank(pkgInfoDto.getTelephone())){
                paxInfoParseVo.setPhone(pkgInfoDto.getTelephone());
            }
            savePaxInfo(paxInfoParseVo, orderId, createTime, createUser, payMoney);
            FocFlightInfo focFlightInfo = getFlightInfo(paxInfoParseVo);
            saveFlightInfo(orderId, createTime, createUser, focFlightInfo, paxInfoParseVo.getSegment());
            saveOrderInfo(
                    createTime,
                    createUser,
                    orderId,
                    focFlightInfo,
                    payMoney,
                    serviceCity,
                    paxInfoParseVo.getSegment().replace(" ", ""),
                    accidentId,pkgAuditParamInfoDto.getRemark());
            orderAuditService.launchAuditProcess(createUser,orderAuditService.getAuditKeyByServiceCity(DpOrderConstant.STATUS_ABNORMAL_BAGGAGE,serviceCity), orderId, "","1",payMoney);
            //更新异常行李依赔付信息
            if (StringUtils.isNotBlank(pkgInfoDto.getOrderId())) {
                int payMoney1 = Integer.valueOf(pkgInfoDto.getPayMoney());
                if(!pkgInfoDto.getOrderId().contains(orderId)){
                    orderId += "," + pkgInfoDto.getOrderId() + ",";
                    if (StringUtils.isNotBlank(payMoney)) {
                        payMoney1 += Integer.valueOf(payMoney);
                    }
                }
                payMoney=String.valueOf(payMoney1);
            }
        }
        if(StringUtils.isBlank(orderId)){
            orderId=pkgInfoDto.getOrderId();
            payMoney=pkgInfoDto.getPayMoney();
        }
        if(StringUtils.isNotBlank(payMoney)){
            pkgInfoServiceDao.updatePkgInfo("0",String.valueOf(payMoney), pkgSize, orderId, accidentId);
        }else{
            pkgInfoServiceDao.updatePkgInfo("0","", pkgSize, orderId, accidentId);
        }
        return orderId;
    }

    @Override
    public Map<String, Object> pkgOrderDetailInfo(String orderId) {
        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(), new String[] { "赔偿单id" });
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        Map<String, Object> dataMap = new HashMap<>();
        // 包含赔偿单信息
        PaxCompensateDetailsVo orderDetailsVo = orderInfoDaoImpl.getOrderDetailsInfo(orderId, userId);
        CompensationProgressVo cpVo = orderAuditService.queryCpsProgressQuery(orderId);
        dataMap.put("cpsProgressInfo", cpVo);
        // 赔偿标准
        List<CompensateInfoVo> compensateInfo =
                compensateInfoDaoImpl.getCompensateInfoByOrderId(orderId);
        dataMap.put("orderDetailsInfo", orderDetailsVo);
        dataMap.put("compensateInfo", compensateInfo);
        return dataMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePkgInfoImg(String accidentId, String imgUrl) throws Exception {
        String[] imgs = imgUrl.split(",");
        String saveImgl = "";
        for (String img : imgs) {
            if(img.contains(IMG_TEMP_PATH)){
                img=uploadAndDownload.saveImg(img);
            }
            saveImgl += img + ",";
        }
        pkgInfoServiceDao.updatePkgInfoImg(accidentId,saveImgl);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invalidPkgInfo(String accidentId) throws Exception {
        pkgInfoServiceDao.invalidPkgInfo(accidentId);
    }

    @Override
    public int getPaxPkgHistoryByIdNo(String idNo) {
        return pkgInfoServiceDao.getPaxPkgHistoryByIdNo(idNo);
    }
}