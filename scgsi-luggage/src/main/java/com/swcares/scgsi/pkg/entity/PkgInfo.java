package com.swcares.scgsi.pkg.entity;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.pkg.entity <br>
 * Description：异常行李 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月20日 15:10 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "DP_PKG_INFO")
@Data
@EncryptionClassz
public class PkgInfo {
    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;
    /**
     * 赔付单ID
     */
    @Column(name = "ORDER_ID")
    private String orderId;
    /**
     * 航班ID
     */
    @Column(name = "FLIGHT_ID")
    private String flightId;
    /**
     * 旅客id
     */
    @Column(name = "PAX_ID")
    private String paxId;
    /**
     * 服务航站
     */
    @Column(name = "SERVICE_CITY")
    private String serviceCity;
    /**
     * 事故类型1破损行李,2少收行李，3多收行李，4异常行李
     */
    @Column(name = "ACCIDENT_TYPE")
    private String accidentType;
    /**
     * 行李类型
     */
    @Column(name = "PKG_TYPE")
    private String pkgType;
    /**
     * 行李航线
     */
    @Column(name = "PKG_SEGMENT")
    private String pkgSegment;
    /**
     * 逾重行李票号
     */
    @Column(name = "OVER_WEIGHT_TKTNO")
    private String overWeightTktNo;
    /**
     * 行李编号
     */
    @Column(name = "PKG_NO")
    private String pkgNo;
    /**
     * 破损类型
     */
    @Column(name = "DAMAGE_TYPE")
    private String damageType;
    /**
     * 破损类型备注
     */
    @Column(name = "DAMAGE_TYPE_REMARK")
    private String damageTypeRemark;
    /**
     * 破损部位
     */
    @Column(name = "DAMAGE_PART")
    private String damagePart;
    /**
     * 破损部位备注
     */
    @Column(name = "DAMAGE_PART_REMARK")
    private String damagePartRemark;

    /**
     * 破损程度
     */
    @Column(name = "DAMAGE_LEVEL")
    private String damageLevel;
    /**
     * 状态0未结案1已结案
     */
    @Column(name = "STATUS")
    private String status;
    /**
     * 行李品牌
     */
    @Column(name = "PKG_BRAND")
    private String pkgBrand;
    /**
     * 附件
     */
    @Column(name = "IMG_URL")
    private String imgUrl;
    /**
     * 备注说明
     */
    @Column(name = "REMARK")
    private String remark;
    /**
     * 赔偿行李箱尺寸
     */
    @Column(name = "PKG_SIZE")
    private String pkgSize;
    /**
     * 赔偿金额
     */
    @Column(name = "PAY_MONEY")
    private String payMoney;
    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    private String createUser;

    /**
     * 创建日期
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;
    /**
     * 行李经停航站（少收内件缺失才有）
     */
    @Column(name = "PKG_SEGMENT_STOP")
    private String pkgSegmentStop;
    /**
     * 丢失重量（少收内件缺失才有）
     */
    @Column(name = "LITTLE_PKG_WEIGHT")
    private String littlePkgWeight;
    /**
     * 少收类型（少收内件缺失才有）
     */
    @Column(name = "LITTLE_PKG_TYPE")
    private String littlePkgType;
    /**
     * 常规为21天，不可编辑
     * 事故提醒（少收内件缺失才有）
     */
    @Column(name = "REMIND_DAY")
    private String remindDay;
    /**
     * 航班号（多收）
     */
    @Column(name = "PKG_FLIGHT_NO")
    private String pkgFlightNo;
    /**
     * 航班日期（多收）
     */
    @Column(name = "PKG_FLIGHT_DATE")
    private String pkgFlightDate;

    /**
     * 数据来源0系统录入1导入
     */
    @Column(name = "DATA_TYPE")
    private String dataType;
    /**
     * 确认发放操作人
     */
    @Column(name = "GRANT_USER")
    private String grantUser;
    /**
     * 确认发放操作时间
     */
    @Column(name = "GRANT_DATE")
    private Date grantDate;
    /**
     * 事故单号
     */
    @Column(name = "ACCIDENT_ID")
    private String accidentId;
    /**
     * 联系电话
     */
    @Column(name = "TELEPHONE")
    @Encryption
    private String telephone;
    /**
     * 邮箱地址
     */
    @Column(name = "POST_ADDR")
    private String postAddr;
}