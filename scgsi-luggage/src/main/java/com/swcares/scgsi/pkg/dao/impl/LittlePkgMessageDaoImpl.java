package com.swcares.scgsi.pkg.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.pkg.vo.LittlePkgMessageVo;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.pkg.dao.impl <br>
 * Description：异常行李少收类型消息提醒Dao <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 04月09日 10:40 <br>
 * @version v1.0 <br>
 */
@Repository
public class LittlePkgMessageDaoImpl {
    @Resource
    private BaseDAO baseDAO;
    /**
     * Title：getLittlePkgMessage <br>
     * Description： 获取到期未处理结案少收行李信息<br>
     * author：王建文 <br>
     * date：2020-4-9 10:45 <br>
     * @param
     * @return
     */
    public List<LittlePkgMessageVo>  getLittlePkgMessage(){
        StringBuffer sql=new StringBuffer();
        sql.append(" SELECT PKG.ACCIDENT_ID AS accidentId,F.FLIGHT_NO AS flightNo, ");
        sql.append(" replace(F.FLIGHT_DATE,'/','-') AS flightDate,PKG.CREATE_USER AS createUser, ");
        sql.append(" DPI.PAX_NAME AS paxName,PKG.REMIND_DAY AS remindDay ");
        sql.append(" FROM DP_PKG_INFO PKG ");
        sql.append(" LEFT JOIN FOC_FLIGHT_INFO F ");
        sql.append(" ON F.FLIGHT_ID=PKG.FLIGHT_ID ");
        sql.append(" LEFT JOIN DP_PAX_INFO DPI ON ");
        sql.append(" DPI.PAX_ID=PKG.PAX_ID ");
        sql.append(" WHERE PKG.ACCIDENT_TYPE='2' AND PKG.STATUS='0' AND  PKG.SEND_STATUS is NULL ");
        sql.append(" AND TO_CHAR(PKG.CREATE_TIME+PKG.REMIND_DAY,'YYYY-MM-DD')=TO_CHAR(SYSDATE,'YYYY-MM-DD') ");
        sql.append(" GROUP BY PKG.ACCIDENT_ID,F.FLIGHT_NO,F.FLIGHT_DATE,PKG.CREATE_USER,DPI.PAX_NAME,PKG.REMIND_DAY ");
        return (List<LittlePkgMessageVo>)baseDAO.findBySQL_comm(sql.toString(),new HashMap<>(),LittlePkgMessageVo.class);
    }
    public void updateLittlePkgSendStatus(String accidentId){
        StringBuffer sql=new StringBuffer();
        sql.append(" UPDATE DP_PKG_INFO PKG ");
        sql.append(" SET PKG.SEND_STATUS='1' ");
        sql.append(" WHERE PKG.ACCIDENT_ID=? ");
        baseDAO.batchUpdate(sql.toString(),accidentId);
    }
}