package com.swcares.scgsi.pkg.vo;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.pkg.vo <br>
 * Description：后台异常行李列表显示 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月22日 17:09 <br>
 * @version v1.0 <br>
 */
@Data
public class PkgExportInfoVo {
    /**
     * 事故单号
     */
    private String accidentId;

    /**
     * 事故类型1破损行李,2少收行李，3多收行李，4内件缺失
     */
    private String accidentType;

    /**
     * 状态0未结案1已结案
     */
    private String status;

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班日期
     */
    private String flightDate;

    /**
     * 航段
     */
    private String segment;

    /**
     * 服务航站
     */
    private String serviceCity;

    /**
     * 旅客姓名
     */
    private String paxName;

    /**
     * 行李编号
     */
    private String pkgNo;

    /**
     * 总赔偿金额
     */
    private String totalPay;

    /**
     * 总赔偿次数
     */
    private String payCount;

    /**
     * 行李尺寸
     */
    private String pkgSize;
    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 发放人
     */
    private String grantUser;

    /**
     * 数据来源0系统录入1导入
     */
    private String dataType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 审核审人
     */
    private String auditor;
//    /**
//     * 事故单创建人
//     */
//    private String createUser;
}