package com.swcares.scgsi.pkg.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hum.employee.entity.Employee;
import com.swcares.scgsi.pkg.dto.PkgInfoQueryParamDto;
import com.swcares.scgsi.pkg.entity.PkgInfo;
import com.swcares.scgsi.pkg.vo.*;
import com.swcares.scgsi.user.dao.UserJpaDao;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.pkg.dao.impl <br>
 * Description：异常行李自定义sql <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月22日 9:10 <br>
 * @version v1.0 <br>
 */
@Repository
public class PkgInfoServiceDaoImpl {
    @Resource
    private BaseDAO baseDAO;
    @Resource
    private UserJpaDao userJpaDao;
    /**
     * Title：judgeIsExit <br>
     * Description： 同一行李号同一类型判断是否重复<br>
     * author：王建文 <br>
     * date：2020-3-22 9:19 <br>
     *
     * @param pkgNo   行李号
     * @param pkgType 行李类型
     * @return
     */
    public boolean judgeIsExit(String pkgNo, String pkgType,boolean flag) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("pkgNo", pkgNo);
        paramsMap.put("pkgType", pkgType);
        sql.append(" SELECT PKG.PKG_NO AS pkgNo ");
        sql.append(" FROM DP_PKG_INFO PKG ");
        sql.append(" WHERE  PKG.PKG_NO=:pkgNo AND PKG.STATUS!='3' ");
        if(flag){
            Authentication authentication = AuthenticationUtil.getAuthentication();
            String createUser = (String) authentication.getPrincipal();
            String serviceCity = "";
            Employee employee=null;
            try{
                employee= userJpaDao.findByTuNo(createUser);
                if (null != employee && StringUtils.isNotBlank(employee.getAirport3code())) {
                    serviceCity = employee.getAirport3code();
                }
            }catch (Exception e){
                e.printStackTrace();
            }
            paramsMap.put("serviceCity", serviceCity);
           sql.append(" AND PKG.SERVICE_CITY=:serviceCity ");

        }
        sql.append(" AND PKG.ACCIDENT_TYPE=:pkgType ");
        List<PkgInfo> pkgInfoList = (List<PkgInfo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, PkgInfo.class);
        if (pkgInfoList.size() > 0) {
            return true;
        }
        return false;
    }

    public void deleteExitPkg(String pkgNo, String pkgType) {
        StringBuffer sql = new StringBuffer();
        sql.append(" DELETE FROM DP_PKG_INFO PKG ");
        sql.append(" WHERE PKG.STATUS='2' AND PKG.PKG_NO=? ");
        sql.append(" AND PKG.ACCIDENT_TYPE=? ");
        baseDAO.batchUpdate(sql.toString(), pkgNo, pkgType);
    }
    public void deletePkgByAccidentId(String accidentId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" DELETE FROM DP_PKG_INFO PKG ");
        sql.append(" WHERE PKG.STATUS='2' AND PKG.ACCIDENT_ID=? ");
        baseDAO.batchUpdate(sql.toString(),accidentId);
    }
    public void deleteDraftPkg(String accidentIds) {
        String[] accidentId=accidentIds.split(",");
        for(String id:accidentId){
            StringBuffer sql = new StringBuffer();
            sql.append(" DELETE FROM DP_PKG_INFO PKG ");
            sql.append(" WHERE PKG.STATUS='2' ");
            sql.append(" AND PKG.ACCIDENT_ID=? ");
            baseDAO.batchUpdate(sql.toString(),id);
            StringBuffer orderSql = new StringBuffer();
            orderSql.append(" DELETE FROM DP_ORDER_INFO O ");
            orderSql.append(" WHERE O.ACCIDENT_ID=? ");
            baseDAO.batchUpdate(orderSql.toString(),id);
        }
    }

    /**
     * Title：getPkgInfo <br>
     * Description：异常行李列表查询<br>
     * author：王建文 <br>
     * date：2020-3-22 10:54 <br>
     *
     * @param queryParamDto 查询参数接收
     * @return
     */
    public List<PkgInfoVo> getPkgInfo(PkgInfoQueryParamDto queryParamDto) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT DISTINCT(PKG.ACCIDENT_ID) AS accidentId, ");
        sql.append(" PKG.ACCIDENT_TYPE AS accidentType,PKG.SERVICE_CITY AS serviceCity, ");
        sql.append(" DPI.PAX_NAME AS paxName,DPI.PAX_ID AS paxId, ");
        sql.append(" F.FLIGHT_NO AS flightNo,F.FLIGHT_DATE AS flightDate, ");
        sql.append(" F.DEPART_PORT ||'-'||F.ARRIVAL_PORT AS segment, ");
        sql.append(" PKG.ORDER_ID AS orderId,PKG.STATUS AS status ");
        sql.append(" FROM DP_PKG_INFO PKG ");
        sql.append(" LEFT JOIN DP_PAX_INFO DPI ON DPI.PAX_ID=PKG.PAX_ID ");
        sql.append(" LEFT JOIN FOC_FLIGHT_INFO F ON F.FLIGHT_ID=PKG.FLIGHT_ID ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ON O.ORDER_ID=PKG.ORDER_ID ");
        sql.append(" WHERE 1=1  ");
        String keySearch = queryParamDto.getKeySearch();
        if (StringUtils.isNotBlank(keySearch)) {
            paramsMap.put("keySearch", keySearch);
            sql.append(" AND (DPI.PAX_NAME=:keySearch OR PKG.PKG_NO=:keySearch) ");
        }
        String accidentId=queryParamDto.getAccidentId();
        if (StringUtils.isNotBlank(accidentId)) {
            paramsMap.put("accidentId", accidentId);
            sql.append(" AND PKG.ACCIDENT_ID=:accidentId  ");
        }
        String paxName = queryParamDto.getPaxName();
        if (StringUtils.isNotBlank(paxName)) {
            paramsMap.put("paxName", paxName);
            sql.append(" AND DPI.PAX_NAME=:paxName  ");
        }
        String pkgNo = queryParamDto.getPkgNo();
        if (StringUtils.isNotBlank(pkgNo)) {
            paramsMap.put("pkgNo", pkgNo);
            sql.append(" AND PKG.PKG_NO=:pkgNo  ");
        }
        String flightDate = queryParamDto.getFlightDate();
        if (StringUtils.isNotBlank(flightDate)) {
            paramsMap.put("flightDate", flightDate);
            sql.append(" AND replace(F.FLIGHT_DATE,'/','-')=:flightDate  ");
        }
        String flightNo = queryParamDto.getFlightNo();
        if (StringUtils.isNotBlank(flightNo)) {
            paramsMap.put("flightNo", flightNo);
            sql.append(" AND F.FLIGHT_NO=:flightNo ");
        }
        String accidentType = queryParamDto.getAccidentType();
        if (StringUtils.isNotBlank(accidentType)) {
            sql.append(" AND PKG.ACCIDENT_TYPE ");
            sql.append(" in (SELECT REGEXP_SUBSTR('"+accidentType+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+accidentType+"', '[^,]+', 1, level) is not null) ");
        }
        String draftStatus=queryParamDto.getDraftStatus();
        if(StringUtils.isNotBlank(draftStatus)){
            sql.append(" AND PKG.STATUS='2' ");
        }
        if(StringUtils.isBlank(draftStatus)){
            sql.append(" AND PKG.STATUS!='2' ");
        }
        String status = queryParamDto.getStatus();
        if (StringUtils.isNotBlank(status)) {
            sql.append(" AND PKG.STATUS ");
            sql.append(" in (SELECT REGEXP_SUBSTR('"+status+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+status+"', '[^,]+', 1, level) is not null) ");
        }
        String serviceCity = queryParamDto.getServiceCity();
        if (StringUtils.isNotBlank(serviceCity)) {
            paramsMap.put("serviceCity", serviceCity);
            sql.append(" AND PKG.SERVICE_CITY=:serviceCity ");
        }
        String receiveStatus = queryParamDto.getReceiveStatus();
        if (StringUtils.isNotBlank(receiveStatus)) {
            sql.append(" AND O.RECEIVE_STATUS ");
            sql.append(" in (SELECT REGEXP_SUBSTR('"+receiveStatus+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+receiveStatus+"', '[^,]+', 1, level) is not null) ");
        }
        String orderStatus = queryParamDto.getOrderStatus();
        if (StringUtils.isNotBlank(orderStatus)) {
            sql.append(" AND O.STATUS ");
            sql.append(" in (SELECT REGEXP_SUBSTR('"+orderStatus+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+orderStatus+"', '[^,]+', 1, level) is not null) ");
        }
        String startDate = queryParamDto.getStartDate();
        String endDate = queryParamDto.getEndDate();
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND replace(F.FLIGHT_DATE,'/','-')  BETWEEN :startDate and :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
            endDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND replace(F.FLIGHT_DATE,'/','-')  BETWEEN :startDate and :endDate ");
        }
        if (StringUtils.isNotBlank(endDate) && StringUtils.isBlank(startDate)) {
            paramsMap.put("endDate", endDate);
            sql.append(" AND replace(F.FLIGHT_DATE,'/','-') <= :endDate ");
        }

        return (List<PkgInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, PkgInfoVo.class);
    }
    /**
     * Title：getPkgDetailInfo <br>
     * Description： 获取异常行李详情<br>
     * author：王建文 <br>
     * date：2020-3-22 15:10 <br>
     * @param  accidentId 事故单号
     * @return
     */
    public PkgInfoDetailInfoVo getPkgDetailInfo(String accidentId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("accidentId",accidentId);
        sql.append(" SELECT F.FLIGHT_NO AS flightNo,replace(F.FLIGHT_DATE,'/','-') AS flightDate, ");
        sql.append(" replace(F.FLIGHT_DATE,'/','-') ||' ' || F.STD AS std, ");
        sql.append(" DPI.SEGMENT AS segment,DPI.PAX_NAME AS paxName,PKG.CREATE_USER AS accidentCreateId, ");
        sql.append(" DPI.SEX AS sex,DPI.WITH_BABY AS babyName, ");
        sql.append(" DPI.ID_NO AS idNo,DPI.ID_TYPE AS idType, ");
        sql.append(" DPI.IS_CHILD AS isChild,PKG.SERVICE_CITY AS serviceCity, ");
        sql.append(" DPI.TKT_NO AS tktNo,PKG.TELEPHONE AS telephone, ");
        sql.append(" DPI.MAIN_CLASS AS mainClass,DPI.SUB_CLASS AS subClass, ");
        sql.append(" PKG.PKG_TYPE AS pkgType,NVL(PKG.PKG_NO,' ') AS pkgNo,PKG.PKG_SIZE AS pkgSize, ");
        sql.append(" PKG.PKG_SEGMENT AS pkgSegment,PKG.OVER_WEIGHT_TKTNO AS overWeightTktNo, ");
        sql.append(" PKG.DAMAGE_TYPE AS damageType,PKG.DAMAGE_TYPE_REMARK AS damageTypeRemark, ");
        sql.append(" PKG.DAMAGE_PART AS damagePart,PKG.DAMAGE_PART_REMARK AS damagePartRemark, ");
        sql.append(" PKG.DAMAGE_LEVEL AS damageLevel,PKG.REMARK AS remark,PKG.PAX_ID AS paxId, ");
        sql.append(" substr(PKG.IMG_URL,0,length(PKG.IMG_URL)-1) AS imgUrl,PKG.ACCIDENT_TYPE AS accidentType,PKG.PAY_MONEY AS payMoney, ");
        sql.append(" PKG.LITTLE_PKG_WEIGHT AS littlePkgWeight,PKG.LITTLE_PKG_TYPE AS littlePkgType, ");
        sql.append(" PKG.REMIND_DAY AS remindDay,PKG.PKG_FLIGHT_NO AS pkgFlightNo,PKG.STATUS AS status, ");
        sql.append(" PKG.POST_ADDR AS postAddr, ");
        sql.append(" PKG.PKG_FLIGHT_DATE AS pkgFlightDate,PKG.PKG_BRAND AS pkgBrand,PKG.PKG_SEGMENT_STOP AS pkgSegmentStop ");
        sql.append(" FROM DP_PKG_INFO PKG ");
        sql.append(" LEFT JOIN DP_PAX_INFO DPI ON DPI.PAX_ID=PKG.PAX_ID ");
        sql.append(" LEFT JOIN FOC_FLIGHT_INFO F ON F.FLIGHT_ID=PKG.FLIGHT_ID ");
        sql.append(" WHERE PKG.ACCIDENT_ID=:accidentId ");
        sql.append(" order by DPI.CREATE_TIME DESC ");
        List<PkgInfoDetailInfoVo> pkgInfoDetailInfoVoList=(List<PkgInfoDetailInfoVo>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,PkgInfoDetailInfoVo.class);
        if(pkgInfoDetailInfoVoList.size()>0){
            return pkgInfoDetailInfoVoList.get(0);
        }
        return new PkgInfoDetailInfoVo();
    }
    public void updatePkgInfo(String status,String payMoney,String pkgSize,String orderId,String accidentId){
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_PKG_INFO SET  ");
        sql.append(" STATUS=?, ");
        sql.append(" PAY_MONEY=?, ");
        sql.append(" PKG_SIZE=?, ");
        sql.append(" ORDER_ID=? ");
        sql.append(" WHERE ACCIDENT_ID=? ");
        baseDAO.batchUpdate(sql.toString(),status,payMoney,pkgSize,orderId,accidentId);
    }
    /**
     * Title：getPkgOrderInfoByAccidentId <br>
     * Description： 根据事故单号获取赔付详情<br>
     * author：王建文 <br>
     * date：2020-3-22 15:36 <br>
     * @param  accidentId 事故单号
     * @return
     */
    public PkgOrderInfoVo getPkgOrderInfoByAccidentId(String accidentId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("accidentId",accidentId);
        sql.append(" SELECT PKG.PKG_SIZE AS pkgSize,get_user_name(PKG.CREATE_USER) AS applyUser,PKG.ORDER_ID AS orderId, ");
        sql.append(" PKG.PAY_MONEY AS payMoney,DPI.RECEIVE_STATUS AS receiveStatus,PKG.TELEPHONE AS telephone, ");
        sql.append(" O.STATUS AS status,PKG.PAX_ID AS paxId,PKG.SERVICE_CITY AS serviceCity ");
        sql.append(" FROM DP_PKG_INFO PKG ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ON O.ORDER_ID=PKG.ORDER_ID ");
        sql.append(" LEFT JOIN DP_PAX_INFO DPI ON DPI.ORDER_ID=PKG.ORDER_ID ");
        sql.append(" WHERE PKG.ACCIDENT_ID=:accidentId ");
        List<PkgOrderInfoVo> pkgOrderInfoVoList=(List<PkgOrderInfoVo>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,PkgOrderInfoVo.class);
        PkgOrderInfoVo pkgOrderInfoVo=null;
        if(pkgOrderInfoVoList.size()>0){
             pkgOrderInfoVo= pkgOrderInfoVoList.get(0);
        }
        return pkgOrderInfoVo;
    }
    /**
     * Title：pkgCase <br>
     * Description： 事故单结案<br>
     * author：王建文 <br>
     * date：2020-3-22 15:43 <br>
     * @param  accidentIds 事故单id
     * @return
     */
    public void pkgCase(String[] accidentIds){
        for(String accidentId:accidentIds ){
            StringBuffer sql = new StringBuffer();
            sql.append(" UPDATE DP_PKG_INFO SET  ");
            sql.append(" STATUS='1' ");
            sql.append(" WHERE ACCIDENT_ID=? ");
            baseDAO.batchUpdate(sql.toString(),accidentId);
        }
    }
    /**
     * Title：getPkgInfoPage <br>
     * Description：后台异常行李列表分页查询<br>
     * author：王建文 <br>
     * date：2020-3-22 17:16 <br>
     * @param  queryParamDto 查询参数
     * @return
     */
    public QueryResults getPkgInfoPage(PkgInfoQueryParamDto queryParamDto){
        Map<String,Object> sqlMap=queryPkgInfoSql(queryParamDto);
        return baseDAO.findBySQLPage_comm(sqlMap.get("sql").toString(),
                queryParamDto.getCurrent(),
                queryParamDto.getPageSize(),
                (Map<String, Object>) sqlMap.get("paramsMap"),
                PkgPageInfoVo.class);
    }
    private Map<String,Object> queryPkgInfoSql(PkgInfoQueryParamDto queryParamDto){
        Map<String,Object> dataMap=new HashMap<>();
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT DISTINCT(PKG.ACCIDENT_ID) AS accidentId, ");
        sql.append(" DECODE(PKG.ACCIDENT_TYPE,'1','破损行李','2','少收行李','3','多收行李','4','内件缺失行李') AS accidentType,");
        sql.append(" DECODE(PKG.STATUS,'0','处理中','1','已结案','2','草稿','3','作废') AS status,");
        sql.append(" PKG.STATUS AS pkgStatus,DPI.ID_NO AS idNo, ");
        sql.append(" F.FLIGHT_NO AS flightNo,replace(F.FLIGHT_DATE,'/','-') AS flightDate, ");
        sql.append(" REPLACE(DPI.SEGMENT,' ','') AS segment,C.CITY_CH_NAME AS serviceCity,PKG.PAX_ID AS paxId, ");
        sql.append(" DPI.PAX_NAME AS paxName,PKG.PKG_NO AS pkgNo,PKG.ORDER_ID AS orderId, ");
        sql.append(" PKG.PAY_MONEY AS totalPay,");
        //sql.append(" get_dp_pkg_pax_paycount(PKG.PAX_ID,PKG.PKG_NO) AS payCount, ");
        sql.append(" PKG.PKG_SIZE AS pkgSize,EMP.TU_CNAME AS createUser, ");
        sql.append(" TO_CHAR(PKG.CREATE_TIME,'YYYY-MM-DD hh24:mi:ss') AS createTime, ");
        sql.append(" EMP1.TU_CNAME AS grantUser, ");
        sql.append(" PKG.POST_ADDR AS postAddr, ");
        sql.append(" DECODE(PKG.DATA_TYPE,'0','否','1','是') AS dataType");
        sql.append(" FROM DP_PKG_INFO PKG  ");
        sql.append(" LEFT JOIN DP_PAX_INFO DPI ON DPI.PAX_ID=PKG.PAX_ID ");
        sql.append(" LEFT JOIN FOC_FLIGHT_INFO F ON F.FLIGHT_ID=PKG.FLIGHT_ID ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ON O.ORDER_ID=PKG.ORDER_ID ");
        sql.append(" LEFT JOIN CITY_CODE C ON C.AIRPORT_3CODE=UPPER(PKG.SERVICE_CITY) ");
        sql.append(" LEFT JOIN EMPLOYEE EMP ON EMP.TUNO=PKG.CREATE_USER ");
        sql.append(" LEFT JOIN EMPLOYEE EMP1 ON EMP1.TUNO=O.ISS_USER ");
        sql.append(" WHERE 1=1 ");
        String idNo=queryParamDto.getIdNo();
        if (StringUtils.isNotBlank(idNo)) {
            idNo= AesEncryptUtil.aesEncryptScgsi(idNo);
            paramsMap.put("idNo", idNo);
            sql.append(" AND DPI.ID_NO=:idNo  ");
        }
        String check=queryParamDto.getCheck();
        if(StringUtils.isNotBlank(check)){
            sql.append(" AND PKG.STATUS!='3' AND PKG.ACCIDENT_TYPE!='3'  ");
        }
        if(StringUtils.isBlank(check)){
            String paxName = queryParamDto.getPaxName();
            if (StringUtils.isNotBlank(paxName)) {
                paramsMap.put("paxName", paxName);
                sql.append(" AND DPI.PAX_NAME=:paxName  ");
            }

            String accidentId=queryParamDto.getAccidentId();
            if (StringUtils.isNotBlank(accidentId)) {
                paramsMap.put("accidentId", accidentId);
                sql.append(" AND PKG.ACCIDENT_ID=:accidentId  ");
            }
            String pkgNo = queryParamDto.getPkgNo();
            if (StringUtils.isNotBlank(pkgNo)) {
                paramsMap.put("pkgNo", pkgNo);
                sql.append(" AND PKG.PKG_NO=:pkgNo  ");
            }
            String flightDate = queryParamDto.getFlightDate();
            if (StringUtils.isNotBlank(flightDate)) {
                paramsMap.put("flightDate", flightDate);
                sql.append(" AND replace(F.FLIGHT_DATE,'/','-')=:flightDate  ");
            }
            String flightNo = queryParamDto.getFlightNo();
            if (StringUtils.isNotBlank(flightNo)) {
                paramsMap.put("flightNo", flightNo);
                sql.append(" AND F.FLIGHT_NO=:flightNo ");
            }

            String terminalLaunch=queryParamDto.getTerminalLaunch();
            if(StringUtils.isNotBlank(terminalLaunch)) {
                if(!"2".equals(terminalLaunch)){
                    Authentication authentication = AuthenticationUtil.getAuthentication();
                    String createUser = (String) authentication.getPrincipal();
                    if("1".equals(terminalLaunch)){
                        paramsMap.put("createUser", createUser);
                        sql.append(" AND  PKG.CREATE_USER=:createUser ");
                        sql.append(" AND PKG.STATUS !='2' ");
                    }
                    if("0".equals(terminalLaunch)){
                        try{
                            Employee employee = userJpaDao.findByTuNo(createUser);
                            paramsMap.put("airport3code", employee.getAirport3code());
                            sql.append(" AND  PKG.SERVICE_CITY=:airport3code ");
                            sql.append(" AND PKG.STATUS !='2' ");
                        }catch (Exception e){
                            e.printStackTrace();
                        }
                    }
                }
            }
            String queryType=queryParamDto.getQueryType();
            if (StringUtils.isNotBlank(queryType)) {
                Authentication authentication = AuthenticationUtil.getAuthentication();
                String createUser = (String) authentication.getPrincipal();
                paramsMap.put("createUser", createUser);
                if("0".equals(queryType)){
                    try{
                        Employee employee = userJpaDao.findByTuNo(createUser);
                        paramsMap.put("airport3code", employee.getAirport3code());
                        sql.append(" AND  PKG.CREATE_USER=:createUser ");
                        sql.append(" AND  PKG.SERVICE_CITY=:airport3code ");
                        sql.append(" AND PKG.STATUS !='2' ");
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
                if("1".equals(queryType)){
                    sql.append(" AND  PKG.CREATE_USER=:createUser ");
                    sql.append(" AND PKG.STATUS='2' ");
                }

            }
            String startDate = queryParamDto.getStartDate();
            String endDate = queryParamDto.getEndDate();
            if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
                endDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
                paramsMap.put("endDate", endDate);
                paramsMap.put("startDate", startDate);
                sql.append(" AND REPLACE(F.FLIGHT_DATE,'/','-')  BETWEEN :startDate and :endDate ");
            }
            if (StringUtils.isNotBlank(endDate) && StringUtils.isBlank(startDate)) {
                paramsMap.put("endDate", endDate);
                sql.append(" AND REPLACE(F.FLIGHT_DATE,'/','-') <= :endDate ");
            }
            if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
                paramsMap.put("endDate", endDate);
                paramsMap.put("startDate", startDate);
                sql.append(" AND REPLACE(F.FLIGHT_DATE,'/','-')  BETWEEN :startDate and :endDate ");
            }

            String keySearch = queryParamDto.getKeySearch();
            if (StringUtils.isNotBlank(keySearch)) {
                paramsMap.put("keySearch", "%" + keySearch + "%");
                paramsMap.put("keySearch1",  keySearch );
                sql.append(" AND (DPI.PAX_NAME LIKE:keySearch OR PKG.ACCIDENT_ID=:keySearch1 OR DPI.ID_NO=:keySearch1) ");
            }

            String draftStatus=queryParamDto.getDraftStatus();
            if (StringUtils.isNotBlank(draftStatus)) {
                Authentication authentication = AuthenticationUtil.getAuthentication();
                String createUser = (String) authentication.getPrincipal();
                paramsMap.put("createUser", createUser);
                sql.append(" AND  PKG.CREATE_USER=:createUser ");
                paramsMap.put("draftStatus", draftStatus);
                sql.append(" AND PKG.STATUS=:draftStatus ");
            }
            String serviceCity = queryParamDto.getServiceCity();
            if (StringUtils.isNotBlank(serviceCity)) {
                paramsMap.put("serviceCity", serviceCity);
                sql.append(" AND PKG.SERVICE_CITY=:serviceCity ");
            }
            String receiveStatus = queryParamDto.getReceiveStatus();
            if (StringUtils.isNotBlank(receiveStatus)) {
                paramsMap.put("receiveStatus", receiveStatus);
                sql.append(" AND DPI.RECEIVE_STATUS=:receiveStatus ");
            }
            String orderStatus = queryParamDto.getOrderStatus();
            if (StringUtils.isNotBlank(orderStatus)) {
                paramsMap.put("orderStatus", orderStatus);
                sql.append(" AND O.STATUS=:orderStatus ");
            }
            String compensateType=queryParamDto.getCompensateType();
            if(StringUtils.isNotBlank(compensateType)&&"0".equals(compensateType)){
                sql.append(" AND PKG.PKG_SIZE IS NOT NULL AND PKG.PAY_MONEY IS NULL");
            }
            if(StringUtils.isNotBlank(compensateType)&&"1".equals(compensateType)){
                sql.append(" AND PKG.PAY_MONEY IS NOT NULL AND PKG.PKG_SIZE IS NULL ");
            }
            if(StringUtils.isNotBlank(compensateType)&&"2".equals(compensateType)){
                sql.append(" AND PKG.PKG_SIZE IS NOT NULL AND PKG.PAY_MONEY IS NOT NULL ");
            }
            String accidentType = queryParamDto.getAccidentType();
            if (StringUtils.isNotBlank(accidentType)) {
                sql.append(" AND PKG.ACCIDENT_TYPE ");
                sql.append(" in (SELECT REGEXP_SUBSTR('"+accidentType+"','[^,]+', 1, LEVEL) FROM DUAL ");
                sql.append(" connect by regexp_substr('"+accidentType+"', '[^,]+', 1, level) is not null) ");
            }
            String status = queryParamDto.getStatus();
            if (StringUtils.isNotBlank(status)) {
                sql.append(" AND PKG.STATUS ");
                sql.append(" in (SELECT REGEXP_SUBSTR('"+status+"','[^,]+', 1, LEVEL) FROM DUAL ");
                sql.append(" connect by regexp_substr('"+status+"', '[^,]+', 1, level) is not null) ");
            }
            sql.append(" ORDER BY flightDate DESC,createTime DESC,pkgStatus ASC ");
        }
        dataMap.put("sql",sql);
        dataMap.put("paramsMap",paramsMap);
        return dataMap;
    }
    /**
     * Title：getExportPkgInfo <br>
     * Description： 异常行李数据导出<br>
     * author：王建文 <br>
     * date：2020-4-21 16:23 <br>
     * @param  queryParamDto 查询参数
     * @return
     */
    public List<PkgExportInfoQueryVo> getExportPkgInfo(PkgInfoQueryParamDto queryParamDto){
        Map<String,Object> sqlMap=queryPkgInfoSql(queryParamDto);
        return (List<PkgExportInfoQueryVo>)baseDAO.findBySQL_comm(sqlMap.get("sql").toString(),
                (Map<String, Object>) sqlMap.get("paramsMap"),
                PkgExportInfoQueryVo.class);
    }
    public List<PkgInfoVo> isExitPax(String paxId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT P.PAX_NAME AS paxName FROM DP_PAX_INFO P ");
        sql.append(" WHERE 1=1  ");
        paramsMap.put("paxId", paxId);
        sql.append(" AND P.PAX_ID=:paxId  ");
        sql.append(" AND P.ORDER_ID IS NULL ");
        return (List<PkgInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, PkgInfoVo.class);
    }
    public void deletePkgPax(String paxId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" DELETE FROM DP_PAX_INFO  ");
        sql.append(" WHERE PAX_ID=? ");
        sql.append(" AND ORDER_ID IS NULL ");
        baseDAO.batchUpdate(sql.toString(), paxId);
    }
    public List<PkgInfoVo> getPaxPkgInfo(String paxId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("paxId",paxId);
        sql.append(" SELECT DISTINCT(PKG.ACCIDENT_ID) AS accidentId, ");
        sql.append(" PKG.ACCIDENT_TYPE AS accidentType,PKG.PKG_NO AS pkgNo ");
        sql.append(" FROM DP_PKG_INFO PKG ");
        sql.append(" WHERE PKG.PAX_ID=:paxId AND PKG.PKG_NO IS NOT NULL AND PKG.STATUS !='2' AND PKG.STATUS !='3' ");
        return (List<PkgInfoVo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, PkgInfoVo.class);
    }
    /**
     * Title：updatePkgInfoImg <br>
     * Description：更新异常行李图片<br>
     * author：王建文 <br>
     * date：2020-8-26 11:28 <br>
     * @param  accidentId 事故单号
     * @param  imgUrl 图片地址
     * @return
     */
    public void updatePkgInfoImg(String accidentId, String imgUrl){
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_PKG_INFO SET  ");
        sql.append(" IMG_URL=? ");
        sql.append(" WHERE ACCIDENT_ID=? ");
        baseDAO.batchUpdate(sql.toString(),imgUrl,accidentId);
    }
    public void invalidPkgInfo(String accidentId){
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_PKG_INFO SET  ");
        sql.append(" STATUS='3' ");
        sql.append(" WHERE ACCIDENT_ID=? AND ORDER_ID IS NULL ");
        baseDAO.batchUpdate(sql.toString(),accidentId);
    }
    public int getPaxPkgHistoryByIdNo(String idNo){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        idNo= AesEncryptUtil.aesEncryptScgsi(idNo);
        paramsMap.put("idNo",idNo);
        sql.append(" SELECT DISTINCT(PKG.ACCIDENT_ID) AS accidentId FROM DP_PKG_INFO PKG ");
        sql.append(" LEFT JOIN DP_PAX_INFO PAX ON PAX.PAX_ID=PKG.PAX_ID ");
        sql.append(" WHERE PKG.STATUS!='3' AND PKG.ACCIDENT_TYPE!='3' ");
        sql.append(" AND PAX.ID_NO=:idNo ");
        List<PkgInfo> pkgInfoList = (List<PkgInfo>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, PkgInfo.class);
        if (pkgInfoList.size() > 0) {
            return pkgInfoList.size();
        }
        return 0;
    }
    public String getPkgInfoPayCount(String paxId,String pkgNo){
        StringBuffer sql = new StringBuffer();
        sql.append("  SELECT get_dp_pkg_pax_paycount('"+paxId+"','"+pkgNo+"') AS payCount FROM DUAL ");
        List<PkgPageInfoVo> pkgPageInfoVoList=(List<PkgPageInfoVo>)baseDAO.findBySQL_comm(sql.toString(),new HashMap<>(),PkgPageInfoVo.class);
        if(pkgPageInfoVoList.size()>0){
            return pkgPageInfoVoList.get(0).getPayCount();
        }
        return "0";
    }
    public String getPkgInfoTotalPay(String paxId,String flightNo,String flightDate,String payType){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("paxId",paxId);
        paramsMap.put("flightNo",flightNo);
        paramsMap.put("flightDate",flightDate);
        paramsMap.put("payType",payType);
        sql.append(" SELECT sum(p.CURRENT_AMOUNT) as totalPay  FROM DP_PAX_INFO P ");
        sql.append(" LEFT JOIN DP_ORDER_INFO O ON O.ORDER_ID=P.ORDER_ID ");
        sql.append(" WHERE O.PAY_TYPE=:payType ");
        sql.append(" AND O.FLIGHT_NO=:flightNo ");
        sql.append(" AND O.FLIGHT_DATE=:flightDate ");
        sql.append(" AND P.PAX_ID=:paxId ");
        sql.append(" AND (O.STATUS = '1'OR O.STATUS = '2'OR O.STATUS = '3'OR O.STATUS = '7'OR (O.STATUS='4' AND P.RECEIVE_STATUS='1'))   ");
        sql.append(" AND (p.RECEIVE_STATUS='1' OR P.SWITCH='0') ");
        List<PkgPageInfoVo> pkgPageInfoVoList=(List<PkgPageInfoVo>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,PkgPageInfoVo.class);
        if(pkgPageInfoVoList.size()>0){
            if(null!=pkgPageInfoVoList.get(0).getTotalPay()){
                return pkgPageInfoVoList.get(0).getTotalPay();
            }
            return "0";
        }
        return "0";
    }
}