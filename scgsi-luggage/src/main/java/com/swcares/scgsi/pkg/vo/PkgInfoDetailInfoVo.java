package com.swcares.scgsi.pkg.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.pkg.vo <br>
 * Description：异常行李详情 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月22日 14:42 <br>
 * @version v1.0 <br>
 */
@Data
@EncryptionClassz
public class PkgInfoDetailInfoVo {
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 航班日期
     */
    private String flightDate;
    /**
     * 航段
     */
    private String segment;
    /**
     * 旅客姓名
     */
    private String paxName;
    /**
     * 性别C儿童M男F女
     */
    private String sex;
    /**
     * 婴儿名字
     */
    private String babyName;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号
     */
    @Encryption
    private String idNo;
    /**
     * 票号
     */
    private String tktNo;
    /**
     * 联系电话
     */
    @Encryption
    private String telephone;
    /**
     * 行李类型
     */
    private String pkgType;
    /**
     * 行李编号
     */
    private String pkgNo;
    /**
     * 事故类型1破损行李,2少收行李，3多收行李，4异常行李
     */
    private String accidentType;
    /**
     * 行李航线
     */
    private String pkgSegment;
    /**
     * 逾重行李票号
     */
    private String overWeightTktNo;
    /**
     * 破损类型
     */
    private String damageType;
    /**
     * 破损类型备注
     */
    private String damageTypeRemark;
    /**
     * 破损部位
     */
    private String damagePart;
    /**
     * 破损部位备注
     */
    private String damagePartRemark;
    /**
     * 破损程度
     */
    private String damageLevel;
    /**
     * 备注说明
     */
    private String remark;
    /**
     * 附件
     */
    private String imgUrl;
    /**
     * 丢失重量（少收内件缺失才有）
     */
    private String littlePkgWeight;
    /**
     * 少收类型（少收内件缺失才有）
     */
    private String littlePkgType;
    /**
     * 常规为21天，不可编辑
     * 事故提醒（少收内件缺失才有）
     */
    private String remindDay;
    /**
     * 航班号（多收）
     */
    private String pkgFlightNo;
    /**
     * 航班日期（多收）
     */
    private String pkgFlightDate;
    /**
     * 计划起飞时间
     */
    private String std;
    /**
     * 计划到达时间
     */
    private String sta;
    /**
     * 主舱位
     */
    private String mainClass;
    /**
     * 子舱位
     */
    private String subClass;

    /**
     * 总金额
     */
    private String payMoney;
    /**
     * 儿童标识
     */
    private String isChild;
    /**
     * 状态0未结案1已结案2草稿3作废
     */
    private String status;

    /**
     * 创建人ID
     */
    private String accidentCreateId;

    /**
     * 机型
     */
    private String acType;
    /**
     * paxId
     */
    private String paxId;
    private String accidentId;
    private String pkgBrand;
    private String pkgSegmentStop;
    private String pkgSize;
    private List<Map<String,Object>> exitPkgNo;
    private String paxPkgNo;
    private String serviceCity;
    private String postAddr;
}