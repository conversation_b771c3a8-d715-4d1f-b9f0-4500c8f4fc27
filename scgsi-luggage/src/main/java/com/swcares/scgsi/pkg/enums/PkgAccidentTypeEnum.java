package com.swcares.scgsi.pkg.enums;

/**
 * ClassName：com.swcares.scgsi.pkg.enums <br>
 * Description：异常行李枚举 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月20日 15:51 <br>
 * @version v1.0 <br>
 */
public enum PkgAccidentTypeEnum {
    DAMAGE_PKG("1", "DMG"),
    LITTLE_PKG("2", "AHL"),
    MORE_PKG("3", "OHD"),
    INTERNAL_PKG("4", "LOST");

    private PkgAccidentTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static PkgAccidentTypeEnum build(String key) {
        return build(key, true);
    }

    public static PkgAccidentTypeEnum build(String key, boolean throwEx) {
        PkgAccidentTypeEnum typeEnum = null;
        for (PkgAccidentTypeEnum element : PkgAccidentTypeEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + PkgAccidentTypeEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
