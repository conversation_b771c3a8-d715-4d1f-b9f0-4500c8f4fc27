package com.swcares.scgsi.pkg.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.pkg.vo <br>
 * Description：异常行李赔付单详情 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月22日 15:32 <br>
 * @version v1.0 <br>
 */
@Data
@EncryptionClassz
public class PkgOrderInfoVo {
    /**
     * 赔偿行李尺寸
     */
    private String pkgSize;

    /**
     * 申请人
     */
    private String applyUser;

    /**
     * 赔偿金额
     */
    private String payMoney;

    /**
     * 领取状态(0未领取,1已领取,2处理中)
     */
    private String receiveStatus;

    /**
     * 赔付单状态
     */
    private String status;

    /**
     * 0现金1行李箱2行李箱加现金
     */
    private String payType;

    /**
     * 赔偿单号
     */
    private String orderId;

    /**
     * 旅客ID
     */
    private String paxId;

    /**
     * 服务航站
     */
    private String serviceCity;

    /**
     *
     */
    @Encryption
    private String telephone;
}