package com.swcares.scgsi.pkg.service;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.flight.vo.PaxInfoParseVo;
import com.swcares.scgsi.pkg.dto.PkgAuditParamInfoDto;
import com.swcares.scgsi.pkg.dto.PkgInfoDto;
import com.swcares.scgsi.pkg.dto.PkgInfoQueryParamDto;
import com.swcares.scgsi.pkg.vo.PkgExportInfoVo;
import com.swcares.scgsi.pkg.vo.PkgInfoVo;
import com.swcares.scgsi.pkg.vo.PkgPageInfoVo;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.pkg.service <br>
 * Description：异常行李service <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月20日 10:33 <br>
 * @version v1.0 <br>
 */
public interface PkgInfoService {
    /**
     * Title：authUserInfo <br>
     * Description：异常行李验证用户信息<br>
     * author：王建文 <br>
     * date：2020-3-20 10:36 <br>
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @param keySearch  证件号/行李号/票号
     * @return
     */
    public Map<String, Object> authUserInfo(String flightNo, String flightDate, String keySearch);

    /**
     * Title：savePkgInfo <br>
     * Description： 异常行李保存<br>
     * author：王建文 <br>
     * date：2020-3-20 16:00 <br>
     *
     * @param pkgInfoDto 参数接收
     * @return
     */
    public String savePkgInfo(PkgInfoDto pkgInfoDto) throws Exception;

   /**
    * Title：auditPkgInfo <br>
    * Description： 异常行李走审核流程<br>
    * author：王建文 <br>
    * date：2020-5-15 9:37 <br>
    * @param  pkgAuditParamInfoDto 参数接收
    * @return
    */
    public String auditPkgInfo(PkgAuditParamInfoDto pkgAuditParamInfoDto) throws Exception;
    /**
     * Title：getPkgInfo <br>
     * Description：异常行李列表查询<br>
     * author：王建文 <br>
     * date：2020-3-22 11:08 <br>
     *
     * @param queryParamDto 查询参数接收
     * @return
     */
    public List<PkgInfoVo> getPkgInfo(PkgInfoQueryParamDto queryParamDto);

    /**
     * Title：getPkgDetailInfo <br>
     * Description： 获取异常行李详情<br>
     * author：王建文 <br>
     * date：2020-3-22 15:10 <br>
     *
     * @param accidentId 事故单号
     * @return
     */
    public Map<String, Object> getPkgDetailInfo(String accidentId);

    /**
     * Title：pkgCase <br>
     * Description： 事故单结案<br>
     * author：王建文 <br>
     * date：2020-3-22 15:43 <br>
     *
     * @param accidentIds 事故单id
     * @return
     */
    public void pkgCase(String[] accidentIds);

    /**
     * Title：getPkgInfoPage <br>
     * Description：后台异常行李列表分页查询<br>
     * author：王建文 <br>
     * date：2020-3-22 17:16 <br>
     *
     * @param queryParamDto 查询参数
     * @return
     */
    public QueryResults getPkgInfoPage(PkgInfoQueryParamDto queryParamDto);
    /**
     * Title：deleteDraftPkg <br>
     * Description： 删除草稿箱行李<br>
     * author：王建文 <br>
     * date：2020-4-8 15:56 <br>
     * @param  accidentIds 事故单号
     * @return
     */
    public void deleteDraftPkg(String accidentIds);
    /**
     * Title：savePkgInfoByExcel <br>
     * Description： 异常行李数据导入存储<br>
     * author：王建文 <br>
     * date：2020-4-20 16:33 <br>
     * @param  pkgInfoDto 行李信息
     * @param  paxInfoParseVo 旅客信息
     */
    public void savePkgInfoByExcel(PkgInfoDto pkgInfoDto,PaxInfoParseVo paxInfoParseVo) throws Exception;
    /**
     * Title：getExportPkgInfo <br>
     * Description： 异常行李数据导出<br>
     * author：王建文 <br>
     * date：2020-4-21 16:23 <br>
     * @param  queryParamDto 查询参数
     * @return
     */
    public List<PkgExportInfoVo> getExportPkgInfo(PkgInfoQueryParamDto queryParamDto);
    /**
     * Title：pkgOrderDetailInfo <br>
     * Description： 异常行李赔付单号<br>
     * author：王建文 <br>
     * date：2020-5-20 15:52 <br>
     * @param  orderId 赔付单号
     * @return
     */
    public Map<String, Object> pkgOrderDetailInfo(String orderId);
    /**
     * Title：updatePkgInfoImg <br>
     * Description： 更新异常行李图片<br>
     * author：王建文 <br>
     * date：2020-8-26 11:02 <br>
     * @param  accidentId 事故单号
     * @param  imgUrl 图片地址
     * @return
     */
    public void updatePkgInfoImg(String accidentId,String imgUrl)throws Exception;
    /**
     * Title：invalidPkgInfo <br>
     * Description： 作废没有赔付单的事故单<br>
     * author：王建文 <br>
     * date：2020-11-26 9:33 <br>
     * @param  accidentId 事故单号
     * @return
     */
    public void invalidPkgInfo(String accidentId)throws Exception;
    /**
     * Title：getPaxPkgHistoryByIdNo <br>
     * Description：证件号下是否已有历史行李事故单且事故单类型不包含【多收】与【作废】状态的行李事故单<br>
     * author：王建文 <br>
     * date：2021-1-22 13:47 <br>
     * @param  idNo 证件号
     * @return
     */
    public int getPaxPkgHistoryByIdNo(String idNo);
}
