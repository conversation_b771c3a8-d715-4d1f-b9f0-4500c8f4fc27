package com.swcares.scgsi.pkg.dto;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.flight.dto <br>
 * Description：异常行李查询接收前端参数实体 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月22日 10:47 <br>
 * @version v1.0 <br>
 */
@Data
public class PkgInfoQueryParamDto {
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 航班日期
     */
    private String flightDate;

    /**
     * 航班开始日期
     */
    private String startDate;
    /**
     * 航班结束日期
     */
    private String endDate;
    /**
     * 服务航站
     */
    private String serviceCity;
    /**
     * 事故类型
     */
    private String accidentType;
    /**
     * 事故状态
     */
    private String status;

    /**
     * 旅客姓名,行李号搜索
     */
    private String keySearch;
    /**
     * 领取状态
     */
    private String receiveStatus;

    /**
     * 证件号
     */
    private String idNo;
    /**
     * 核实传值代表去核实旅客历史事故单
     */
    private String check;
    /**
     * 赔付单状态
     */
    private String orderStatus;
    /**
     * 旅客姓名
     */
    private String paxName;
    /**
     * 行李号
     */
    private String pkgNo;
    /**
     * 草稿状态
     */
    private String draftStatus;
    /**
     * 0行李箱1现金
     */
    private String compensateType;
    /**
     * 本站发起,1我的0本站发起
     */
    private String terminalLaunch;

    /**
     * 0本站1草稿
     */
    private String queryType;
    /**
     * 事故单号
     */
    private String accidentId;
    /** 当前页数，默认为第一页 **/
    private int current = 1;

    /** 每页显示记录数，默认为10条 **/
    private int pageSize = 10;
}