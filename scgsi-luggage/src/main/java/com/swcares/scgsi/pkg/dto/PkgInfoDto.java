package com.swcares.scgsi.pkg.dto;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.pkg.dto <br>
 * Description：破损行李新建接收参数 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月20日 14:15 <br>
 * @version v1.0 <br>
 */
@Data
public class PkgInfoDto {
    /**
     * 旅客id
     */
    private String paxId;
    /**
     * 联系电话
     */
    private String telephone;
    /**
     * 事故类型1破损行李,2少收行李，3多收行李，4异常行李
     */
    private String accidentType;
    /**
     * 行李类型
     */
    private String pkgType;
    /**
     * 行李航线
     */
    private String pkgSegment;
    /**
     * 逾重行李票号
     */
    private String overWeightTktNo;
    /**
     * 行李编号
     */
    private String pkgNo;
    /**
     * 破损类型
     */
    private String damageType;
    /**
     * 破损类型备注
     */
    private String damageTypeRemark;
    /**
     * 破损部位
     */
    private String damagePart;
    /**
     * 破损部位备注
     */
    private String damagePartRemark;

    /**
     * 破损程度
     */
    private String damageLevel;
    /**
     * 行李品牌
     */
    private String pkgBrand;
    /**
     * 附件
     */
    private String imgUrl;
    /**
     * 备注说明
     */
    private String remark;
    /**
     * 赔偿行李箱尺寸
     */
    private String pkgSize;
    /**
     * 赔偿金额
     */
    private String payMoney;
    /**
     * 行李经停航站（少收内件缺失才有）
     */
    private String pkgSegmentStop;
    /**
     * 丢失重量（少收内件缺失才有）
     */
    private String littlePkgWeight;
    /**
     * 少收类型（少收内件缺失才有）
     */
    private String littlePkgType;
    /**
     * 常规为21天，不可编辑
     * 事故提醒（少收内件缺失才有）
     */
    private String remindDay;
    /**
     * 航班号（多收）
     */
    private String pkgFlightNo;
    /**
     * 航班日期（多收）
     */
    private String pkgFlightDate;
    /**
     * 0草稿1提交
     */
    private String operateType;

    /**
     * 航班ID
     */
    private String flightId;
    /**
     * 事故单号
     */
    private String accidentId;
    /**
     * 服务航站针对导入功能
     */
    private String serviceCity;

    /**
     * 邮箱地址
     */
    private String postAddr;

}