<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.swcares</groupId>
		<artifactId>scgsi</artifactId>
        <version>1.1.20</version>
	</parent>

	<artifactId>scgsi-luggage</artifactId>
	

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-data</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-common</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-irregular-flight</artifactId>
		</dependency>
        <dependency>
            <groupId>com.swcares</groupId>
            <artifactId>scgsi-overbooking</artifactId>
        </dependency>
    </dependencies>
</project>
