package com.swcares.scgsi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.common.model.form.ContentIdxForm;
import com.swcares.scgsi.common.model.form.ContentTraceForm;
import com.swcares.scgsi.common.model.form.SystemTraceForm;
import com.swcares.scgsi.common.model.form.TraceDataForm;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.service.TraceService;
import com.swcares.scgsi.util.HttpConnectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Objects;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：TraceServiceImpl <br>
 * Package：com.swcares.scgsi.service.impl <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 03月12日 14:11 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class TraceServiceImpl implements TraceService {

    @Autowired
    private RedisService redisService;

    @Value("${trace.cont.url}")
    private String PSG_LIST;

    @Value("${trace.idx.url}")
    private String PSG_IDX;

    @Autowired
    private Environment environment;

    /**
     * Title：getPsgListByFilght() <br>
     * Description：通过航班查询旅客列表 <br>
     * author：于琦海 <br>
     * date：2020/3/12 14:03 <br>
     * @param form ContentTraceForm
     * @return Object
     */
    @Override
    public Object getPsgListByFilght(ContentTraceForm form) {
        log.info("H5航班监控，根据航班id查询旅客信息:查询参数为:{}",form);
        Object redisResult = redisService.get(String.valueOf(form.hashCode()));
        JSONObject jsonObject;
        if (Objects.isNull(redisResult)) {
            String httpResult = getHttpResult(form, PSG_LIST, this.SystemListForm(),true);
            jsonObject = JSONObject.parseObject(httpResult);
            log.info("H5航班监控，根据航班id查询旅客信息:从trace接口获取旅客数据结果为:{}",jsonObject);
            redisService.set(String.valueOf(form.hashCode()),httpResult,30L);
        } else {
            jsonObject = JSON.parseObject(String.valueOf(redisResult));
            log.info("H5航班监控，根据航班id查询旅客信息:从trace(redis)接口获取旅客数据结果为:{}",jsonObject);
        }
        if(ObjectUtils.isEmpty(jsonObject)){
            throw new BusinessException(MessageCode.TRACE_DATA_IS_EMPTY.getCode());
        }
        String result = jsonObject.getString("result");
        if (!"01".equals(result)) {
            return null;
        }
        return jsonObject.getJSONObject("response");
    }

    /**
     * Title：getPsgByIdx() <br>
     * Description：通过Idx查询旅客信息 <br>
     * author：于琦海 <br>
     * date：2020/3/12 14:03 <br>
     * @param form ContentIdxForm
     * @return Object
     */
    @Override
    public Object getPsgByIdx(ContentIdxForm form) {
        log.info("H5航班监控，根据航班idx查询旅客信息:查询参数为:{}",form);
        Object redisResult = redisService.get(form.getIdx());
        JSONObject jsonObject;
        if (Objects.isNull(redisResult)) {
            String httpResult = getHttpResult(form, PSG_IDX, this.SystemIdxForm(),false);
            jsonObject = JSONObject.parseObject(httpResult);
            log.info("H5航班监控，根据航班idx查询旅客信息:从trace接口获取旅客数据结果为:{}",jsonObject);
           redisService.set(form.getIdx(),httpResult,30L);
        }else {
            jsonObject = JSON.parseObject(String.valueOf(redisResult));
            log.info("H5航班监控，根据航班idx查询旅客信息:从trace(redis)接口获取旅客数据结果为:{}",jsonObject);
        }
        String result = jsonObject.getString("result");
        if (!"01".equals(result)){
            throw new BusinessException(MessageCode.FAIL.getCode());
        }
        return jsonObject.getJSONObject("response");
    }

    /**
     * Title：getHttpResult（） <br>
     * Description：发送请求得到返回值 <br>
     * author：于琦海 <br>
     * date：2020/3/31 11:30 <br>
     * @param form Object
     * @param url String
     * @param systemTraceForm SystemTraceForm
     * @param flag boolean（true为list，false为idx）
     * @return String
     */
    private String getHttpResult(Object form,String url,SystemTraceForm systemTraceForm,boolean flag) {
        TraceDataForm<Object> contentTraceFormTraceDataForm = this.setSystemTraceForm(form,systemTraceForm);
        String params = JSON.toJSONString(contentTraceFormTraceDataForm);
        String result = null;
        try {
            Header clientId = new BasicHeader("ClientId", "cn.sda.marketing.gssp");
            Header operationCode;
            if (flag){
                operationCode = new BasicHeader("OperationCode", "cn.sda.marketing.scsis.dpi1.getPsgListByFilght");
            }else{
                operationCode = new BasicHeader("OperationCode", "cn.sda.marketing.scsis.dpi2.getPsgByIdx");
            }
            result = HttpConnectionUtils.doPost(url, params, false, clientId,operationCode);
        } catch (Exception e) {
            log.error("调用trace接口报错，请求参数【{}】，异常：", JSONObject.toJSON(form), e);
        }
        return result;
    }

    /**
     * Title：setSystemTraceForm（） <br>
     * Description：组装参数 <br>
     * author：于琦海 <br>
     * date：2020/3/12 14:59 <br>
     * @param form ContentTraceForm
     * @return TraceDataForm<ContentTraceForm>
     */
    private TraceDataForm<Object> setSystemTraceForm(Object form,SystemTraceForm sysForm){
        TraceDataForm<Object> psgListForm = new TraceDataForm<>();
        psgListForm.setCont(form);
        SystemTraceForm systemTraceForm = new SystemTraceForm();
        systemTraceForm.setAppkey(sysForm.getAppkey());
        systemTraceForm.setMethod(sysForm.getMethod());
        systemTraceForm.setSubchannelcode(sysForm.getSubchannelcode());
        systemTraceForm.setSysid(sysForm.getSysid());
        psgListForm.setSys(systemTraceForm);
        return psgListForm;
    }

    /**
     * Title：SystemListForm() <br>
     * Description：组装航班列表请求参数 <br>
     * author：于琦海 <br>
     * date：2020/3/31 11:30 <br>
     * @return SystemTraceForm
     */
    private SystemTraceForm SystemListForm(){
        SystemTraceForm systemTraceForm = new SystemTraceForm();
        systemTraceForm.setAppkey(environment.getProperty("trace.appkey"));
        systemTraceForm.setMethod(environment.getProperty("trace.mothod"));
        systemTraceForm.setSysid(environment.getProperty("trace.cont.sysid"));
        systemTraceForm.setSubchannelcode(environment.getProperty("trace.cont.subchannelcode"));
        return systemTraceForm;
    }

    /**
     * Title：SystemIdxForm() <br>
     * Description：组装航班详情请求参数 <br>
     * author：于琦海 <br>
     * date：2020/3/31 11:31 <br>
     * @return SystemTraceForm
     */
    private SystemTraceForm SystemIdxForm(){
        SystemTraceForm systemTraceForm = new SystemTraceForm();
        systemTraceForm.setAppkey(environment.getProperty("trace.appkey"));
        systemTraceForm.setMethod(environment.getProperty("trace.mothod"));
        systemTraceForm.setSysid(environment.getProperty("trace.idx.sysid"));
        systemTraceForm.setSubchannelcode(environment.getProperty("trace.idx.subchannelcode"));
        return systemTraceForm;
    }

}
