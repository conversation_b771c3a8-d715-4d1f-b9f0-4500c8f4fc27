package com.swcares.scgsi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.enums.OriginalFlightEnum;
import com.swcares.scgsi.common.model.VO.FlightDetailVO;
import com.swcares.scgsi.common.model.VO.FlightListVO;
import com.swcares.scgsi.common.model.VO.FlightMonitoringVO;
import com.swcares.scgsi.common.model.VO.TraceInfoVO;
import com.swcares.scgsi.common.model.form.FlightInfoListForm;
import com.swcares.scgsi.common.model.form.FlightListForm;
import com.swcares.scgsi.common.model.form.MobileFlightInfoListForm;
import com.swcares.scgsi.common.model.view.FlightMonitoringView;
import com.swcares.scgsi.common.model.view.FocFlightInfoDetailView;
import com.swcares.scgsi.common.model.view.OriginalSegmentView;
import com.swcares.scgsi.common.model.view.TraceInfoView;
import com.swcares.scgsi.dao.FocFlightInfoDao;
import com.swcares.scgsi.dao.repository.FocFlightInfoRepository;
import com.swcares.scgsi.entity.FocFlightInfo;
import com.swcares.scgsi.service.FlightInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FlightInfoServiceImpl <br>
 * Package：com.swcares.scgsi.service.impl <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 03月20日 10:31 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class FlightInfoServiceImpl implements FlightInfoService {

    @Resource
    private FocFlightInfoRepository focFlightInfoRepository;

    @Resource
    private FocFlightInfoDao focFlightInfoDao;

    @Resource
    private BaseDAO baseDAO;

    /**
     * Title：getFlightInfo() <br>
     * Description：通过航班ID查询航班信息 <br>
     * author：于琦海 <br>
     * date：2020/3/20 10:37 <br>
     * @param flightId String
     * @return RenderResult<FocFlightInfo>
     */
    @Override
    public FocFlightInfo getFlightInfoById(String flightId) {
        return focFlightInfoRepository.findTById(flightId);
    }

    /**
     * Title：getDelayFlightInfo（） <br>
     * Description：补全延误信息 <br>
     * author：于琦海 <br>
     * date：2020/6/14 13:42 <br>
     * @param focFlightInfo FocFlightInfo
     * @return FlightDetailVO
     */
    public FlightDetailVO getDelayFlightInfo(FocFlightInfo focFlightInfo) {
        if (Objects.isNull(focFlightInfo)) {
            return null;
        }
        FlightDetailVO flightDetailVO = new FlightDetailVO();
        String originalFlightNo = this.getOriginalFlightNo(focFlightInfo.getFlightNo());
        BeanUtils.copyProperties(focFlightInfo, flightDetailVO,"bay");
        Map<String, Object> departMap = this.getFlightInfoCodeAndName(flightDetailVO.getPod());
        Map<String, Object> arrivalMap = this.getFlightInfoCodeAndName(flightDetailVO.getPoa());
        if (Objects.nonNull(departMap)) {
            flightDetailVO.setPod((String) departMap.get("AIRPORT_3CODE"));
            flightDetailVO.setDepartPort((String) departMap.get("CITY_CH_NAME"));
        } else {
            log.error("WEB航班监控查询航班详情:航班号->{},航班日期->{},四字码->{}查询三字码出错，需补全三字码", flightDetailVO.getFlightNo(),
                    flightDetailVO.getFlightDate(), flightDetailVO.getPoa());
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        }
        if (Objects.nonNull(arrivalMap)) {
            flightDetailVO.setPoa((String) arrivalMap.get("AIRPORT_3CODE"));
            flightDetailVO.setArrivalPort((String) arrivalMap.get("CITY_CH_NAME"));
        } else {
            log.error("WEB航班监控查询航班详情:航班号->{},航班日期->{},四字码->{}查询三字码出错，需补全三字码", flightDetailVO.getFlightNo(),
                    flightDetailVO.getFlightDate(), flightDetailVO.getPoa());
            throw new BusinessException(MessageCode.UN_KNOWN.getCode());
        }
        // 原始航班号
        flightDetailVO.setOriFlightNo(originalFlightNo);
        // 通过航班号,航班日期，起飞到达四字码查询前一天的航班数据
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        LocalDate parse = LocalDate.parse(focFlightInfo.getFlightDate(), dateTimeFormatter);
        LocalDate localDate = parse.plusDays(-1);
        String oriFlightDate = localDate.format(dateTimeFormatter);
        FocFlightInfo flightInfo = focFlightInfoDao.findFlightInfo(originalFlightNo, oriFlightDate,
                focFlightInfo.getPoa(), focFlightInfo.getPod());
        if (Objects.nonNull(flightInfo)) {
            // 原始航班状态
            flightDetailVO.setFlightStatus(this.getStatus(flightInfo));
            flightDetailVO.setOriFlightDate(flightInfo.getFlightDate());
            flightDetailVO.setOriDepartPort(flightInfo.getDepartPort());
            flightDetailVO.setOriArrivalPort(flightInfo.getArrivalPort());
            flightDetailVO.setBay(flightInfo.getBay());
            flightDetailVO.setDelay_reason(flightInfo.getDelay_reason());
        }
        return flightDetailVO;
    }

    /**
     * Title：getOriginalFlightNo() <br>
     * Description：获取航班的原始航班号 <br>
     * author：于琦海 <br>
     * date：2020/6/14 10:26 <br>
     * @param flightNo String
     * @return String
     */
    private String getOriginalFlightNo(String flightNo) {
        if (StringUtils.isBlank(flightNo)){
            return "";
        }
        // 判断最后一位是不是字符：SC876W
        if (!Character.isDigit(flightNo.charAt(flightNo.length()-1))){
            String value = OriginalFlightEnum.getValue(String.valueOf(flightNo.charAt(flightNo.length() - 1)));
            StringBuilder stringBuilder = new StringBuilder(flightNo);
            StringBuilder replace = stringBuilder.replace(stringBuilder.length() - 1, stringBuilder.length(), value);
            return replace.toString();
        }
        return "";
    }

    /**
     * Title：getFlightSegment() <br>
     * Description：通过航班号航班日期查询航班航段 <br>
     * author：于琦海 <br>
     * date：2020/3/20 10:37 <br>
     * @param flightNum String
     * @param flightDate String
     * @return Object
     */
    @Override
    public List<Map<String, String>> getFlightSegment(String flightNum, String flightDate) {
        return focFlightInfoDao.getFlightSegment(flightNum,flightDate);
    }

    /**
     * Title：getFlightInfoList（） <br>
     * Description：通过航班四要素查询航班信息列表 <br>
     * author：于琦海 <br>
     * date：2020/3/20 14:30 <br>
     * @param form FlightInfoListForm
     * @return RenderResult<List<FocFlightInfo>>
     */
    @Override
    public List<FocFlightInfo> getFlightInfoList(FlightInfoListForm form) {
        List<FocFlightInfo> focFlightInfoList = focFlightInfoDao.getFlightInfoList(form);
        // 起始航段都为空
        if (StringUtils.isEmpty(form.getOrig()) && StringUtils.isEmpty(form.getDest())) {
            return focFlightInfoList;
        }
        // 有一个为空需要抛出异常
        if (StringUtils.isEmpty(form.getOrig()) || StringUtils.isEmpty(form.getDest())) {
            throw new BusinessException(MessageCode.FAIL.getCode());
        }
        List<FocFlightInfo> result = new ArrayList<>();
        // 首先把通过航班号 航班日期 起飞机场 到达机场匹配的数据放入最终返回集合
        focFlightInfoList.stream().forEach(focFlightInfo -> {
            if (form.getOrig().equals(focFlightInfo.getPod()) && form.getDest().equals(focFlightInfo.getPoa())) {
                result.add(focFlightInfo);
            }
        });
        focFlightInfoList.removeAll(result);
        // 通过FOC返回的所有数据查询逻辑航段信息
        focFlightInfoList.forEach(focFlightInfo -> {
            if (!form.getOrig().equals(focFlightInfo.getPod())) {
                return;
            }
            List<FocFlightInfo> collect = focFlightInfoList.stream().filter(focFlight ->
                    focFlight.getPod().equals(focFlightInfo.getPoa())).collect(Collectors.toList());
            if (Objects.isNull(collect) || collect.size() <= 0) {
                return;
            }
            collect.forEach(focFlightInfoLogic -> {
                if (form.getDest().equals(form.getDest())) {
                    result.add(focFlightInfoLogic);
                    result.add(focFlightInfo);
                }
            });
        });
        return result;
    }

    /**
     * Title： getFlightList（）<br>
     * Description：web端根据条件查询航班列表<br>
     * author：于琦海 <br>
     * date：2020/3/30 15:00 <br>
     * @param form FlightListForm
     * @return QueryResults
     */
    @Override
    public QueryResults getFlightList(FlightListForm form) {
        return focFlightInfoDao.getFlightList(form);
    }

    /**
     * Title：getOriginalSegment() <br>
     * Description：获取原始航段 <br>
     * author：于琦海 <br>
     * date：2020/3/27 14:56 <br>
     * @param form FlightInfoListForm
     * @return Object
     */
    @Override
    public OriginalSegmentView getOriginalSegment(FlightInfoListForm form){
        List<FocFlightInfo> focFlightInfoList = focFlightInfoDao.getFlightInfoList(form);
        if (Objects.isNull(focFlightInfoList) || focFlightInfoList.size() <= 0) {
            return null;
        }
        focFlightInfoList.removeIf(focFlightInfo -> {
            // 逻辑段去除，取消标识的去除，
           /* if (focFlightInfo.getLogic() || StringUtils.isNotBlank(focFlightInfo.getFlgCs())) {
                return true;
            }*/

            return false;
        });
        if (focFlightInfoList.size() == 1) {
            FocFlightInfo focFlightInfo = focFlightInfoList.get(0);
            OriginalSegmentView originalSegmentView = new OriginalSegmentView();
            BeanUtils.copyProperties(focFlightInfo, originalSegmentView);
            originalSegmentView.setSegment(this.getSegment(focFlightInfo));
            return originalSegmentView;
        }
        for (FocFlightInfo focFlightInfo : focFlightInfoList) {
            for (FocFlightInfo info : focFlightInfoList) {
                if (focFlightInfo.getFlightId().equals(info.getFlightId())){
                    continue;
                }
                if (focFlightInfo.getPod().equals(info.getPoa())){
                    return getOriginalSegmentView(info, focFlightInfo.getSta(), focFlightInfo.getArrivalPort());
                }
                if (focFlightInfo.getPoa().equals(info.getPod())){
                    return getOriginalSegmentView(focFlightInfo, info.getSta(), info.getArrivalPort());
                }
            }
        }
        return null;
    }

    /**
     * Title：getH5FlgihtInfoList（） <br>
     * Description：获取H5端的航班列表 <br>
     * author：于琦海 <br>
     * date：2020/4/12 16:14 <br>
     * @param form MobileFlightInfoListForm
     * @return QueryResults
     */
    @Override
    public QueryResults getH5FlightInfoList(MobileFlightInfoListForm form) {
        // 航班状态，不填是全部 0起飞 1到达 2延误 3异常 4取消
        // 查询航班信息
        StringBuilder sql = new StringBuilder("SELECT FLIGHT_ID, AC_REG, " +
                "AC_TYPE, ADJUST_TYPE, ARRIVAL_PORT, ATA, ATD, BAY, BOOKSEAT, CLD_TIME, " +
                "COBT, CS_ABNORMAL_REASON, DELAY_ABNORMAL_REASON, DEPART_PORT, ETA, ETD, FLG_CS, " +
                "FLG_DELAY, FLG_VR, FLG_VR1,FLIGHT_DATE, FLIGHT_NO, FLIGHT_TYPE, GET_CITY3CODE_BY4CODE(POA) as POA, " +
                "GET_CITY3CODE_BY4CODE(POD) as POD, R_ABNORMAL_REASON, SEAT_NUM, STA, STD, UPDATE_TIME, " +
                "V_ABNORMAL_REASON, LOGIC,D_OR_I FROM FOC_FLIGHT_INFO where 1 = 1 ");
        QueryResults results = focFlightInfoDao.getFlightInfoList(sql, form, FocFlightInfo.class);
        // 转换航班状态 FlightMonitoringVO
        List<FocFlightInfo> focFlightInfos = (List<FocFlightInfo>) results.getList();
        List<FlightMonitoringVO> flightMonitoringVOS = this.switchFlightStatues(focFlightInfos,form.getStates());
        results.setList(flightMonitoringVOS);
        return results;
    }

    /**
     * Title：findFlightInfo() <br>
     * Description：通过航班号和航班日期查询航班信息 <br>
     * author：于琦海 <br>
     * date：2020/4/14 9:45 <br>
     * @param flightNo   String
     * @param flightDate String
     * @return List<FlightMonitoringVO>
     */
    @Override
    public List<FlightMonitoringVO> findFlightInfo(String flightNo, String flightDate) {
        List<FocFlightInfo> focFlightInfos = findFlightInfoByNumAndDate(flightNo, flightDate,null);
        return switchFlightStatues(focFlightInfos,null);
    }

    /**
     * Title：findFlightInfoByNumAndDate（） <br>
     * Description：根据航班号和航班日期查询航班信息 <br>
     * author：于琦海 <br>
     * date：2020/4/17 10:05 <br>
     * @param flightDate String
     * @param flightNo String
     * @param vr String
     * @return List<FocFlightInfo>
     */
    private List<FocFlightInfo> findFlightInfoByNumAndDate(String flightNo, String flightDate,String vr) {
        StringBuilder sql = new StringBuilder("SELECT * FROM FOC_FLIGHT_INFO WHERE 1 = 1 ");
        Map<String, Object> parameters = new HashMap<>();
        if (StringUtils.isNotBlank(flightNo)) {
            sql.append(" AND FLIGHT_NO LIKE :flightNo ");
            parameters.put("flightNo", "%" + flightNo + "%");
        }
        if (StringUtils.isNotBlank(flightDate)) {
            sql.append(" AND FLIGHT_DATE =:flightDate ");
            parameters.put("flightDate", flightDate);
        }
        if (StringUtils.isNotBlank(vr)){
            sql.append(" AND FLG_VR1 != 'VC' AND FLG_VR1 like '%V%' ");
        }
        return (List<FocFlightInfo>) baseDAO.findBySQL_comm(sql.toString(), parameters,
                FocFlightInfo.class);
    }

    /**
     * Title：getFlightInfoCodeAndName（） <br>
     * Description：根据四字码获取三字码和中文航站 <br>
     * author：于琦海 <br>
     * date：2020/4/14 16:55 <br>
     * @param code String
     * @return Map<String, Object>
     */
    @Override
    public Map<String, Object> getFlightInfoCodeAndName(String code) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("code", code);
        StringBuilder sql = new StringBuilder(" SELECT * FROM CITY_CODE WHERE AIRPORT_4CODE =:code");
        List<Map<String, Object>> bySQL_comm = (List<Map<String, Object>>) baseDAO.findBySQL_comm(sql.toString(),
                parameters, null);
        if(Objects.nonNull(bySQL_comm) && bySQL_comm.size()>0){
            return bySQL_comm.get(0);
        }
        return null;
    }

    /**
     * Title：passengerClassification() <br>
     * Description：统计旅客分类 <br>
     * author：于琦海 <br>
     * date：2020/4/14 17:32 <br>
     * @param psgListByFlight Object
     * @return TraceInfoView
     */
    @Override
    public TraceInfoView passengerClassification(Object psgListByFlight) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(psgListByFlight));
        Object dataList = jsonObject.get("dataList");
        List<TraceInfoVO> traceInfoVO = JSONArray.parseArray(dataList.toString(), TraceInfoVO.class);
        TraceInfoView traceInfoView = new TraceInfoView();
        if (traceInfoVO.size()>0){
            traceInfoView.setTotalAll(traceInfoVO.size());
            long totalCheckIn = traceInfoVO.stream().filter(traceInfo -> "AC".equalsIgnoreCase(traceInfo.getStatus())).count();
            traceInfoView.setTotalCheckIn(totalCheckIn);
            long fAll = traceInfoVO.stream().filter(traceInfo -> "C".equalsIgnoreCase(traceInfo.getMainClass())).count();
            traceInfoView.setFAll(fAll);
            long fCheckIn = traceInfoVO.stream().filter(traceInfo -> "C".equalsIgnoreCase(traceInfo.getMainClass()) && "AC".equalsIgnoreCase(traceInfo.getStatus())).count();
            traceInfoView.setFCheckIn(fCheckIn);
            long yAll = traceInfoVO.stream().filter(traceInfo -> "Y".equalsIgnoreCase(traceInfo.getMainClass())).count();
            traceInfoView.setYAll(yAll);
            long yCheckIn = traceInfoVO.stream().filter(traceInfo -> "Y".equalsIgnoreCase(traceInfo.getMainClass()) && "AC".equalsIgnoreCase(traceInfo.getStatus())).count();
            traceInfoView.setYCheckIn(yCheckIn);
            long childAll = traceInfoVO.stream().filter(traceInfo -> "1".equals(traceInfo.getIsInfant()) || "Y".equalsIgnoreCase(traceInfo.getIsChild())).count();
            traceInfoView.setChildAll(childAll);
            long childCheckIn = traceInfoVO.stream().filter(traceInfo -> ("1".equals(traceInfo.getIsInfant()) || "Y".equalsIgnoreCase(traceInfo.getIsChild())) && "AC".equalsIgnoreCase(traceInfo.getStatus())).count();
            traceInfoView.setChildCheckIn(childCheckIn);
            traceInfoView.setAdultAll(traceInfoVO.size() - childAll);
            traceInfoView.setAdultCheckIn(totalCheckIn - childCheckIn);
        }
        return traceInfoView;
    }

    /**
     * Title：findAlternateInfo（） <br>
     * Description：查询备降地 <br>
     * author：于琦海 <br>
     * date：2020/4/17 9:53 <br>
     * @param focFlightInfoDetailView FocFlightInfoDetailView
     * @return FocFlightInfo
     */
    @Override
    public FocFlightInfoDetailView findAlternateInfo(FocFlightInfoDetailView focFlightInfoDetailView) {
        //FLG_VR1列，里面的值如果是空是正常航班，如果是VC是备降的原始段，
        // 如果是V1代表备降后新增的第1段，V2是备降后新增的第2段。
        if ("VC".equalsIgnoreCase(focFlightInfoDetailView.getFlgVr1())) {
            // 通过(航班号，航班日期)查出FlgVr1包含V的数据，就是实际备降地
            List<FocFlightInfo> flightInfoByNumAndDate =
                    findFlightInfoByNumAndDate(focFlightInfoDetailView.getFlightNo(),
                            focFlightInfoDetailView.getFlightDate(), focFlightInfoDetailView.getFlgVr1());
           StringBuilder actualAlternateLand = new StringBuilder();
            flightInfoByNumAndDate.forEach(focFlightInfo -> {
                // 四字码转三字码
                Map<String, Object> arrivalMap = this.getFlightInfoCodeAndName(focFlightInfo.getPoa());
                log.info("H5航班监控查询航班详情:通过四字码:{}转换三字码:{}",focFlightInfo.getPoa(),arrivalMap);
                if (Objects.nonNull(arrivalMap)){
                    actualAlternateLand.append(focFlightInfo.getArrivalPort() + arrivalMap.get("AIRPORT_3CODE") + ",");
                }else {
                    // 查询不到相应的三字码，需要记录日志，同时抛出异常。
                    log.error("H5航班监控查询航班详情:航班号->{},航班日期->{},四字码->{}查询三字码出错，需补全三字码", focFlightInfo.getFlightNo(),
                            focFlightInfo.getFlightDate(), focFlightInfo.getPoa());
                   throw new BusinessException(MessageCode.UN_KNOWN.getCode());
                }
            });
            focFlightInfoDetailView.setActualAlternateLand(actualAlternateLand.toString().substring(0,
                    actualAlternateLand.toString().length() - 1));
        }
        if (StringUtils.isNotBlank(focFlightInfoDetailView.getFlgDelay())) {
            // 延误计算预计延误时间和实际延误时间,不考虑跨天
            focFlightInfoDetailView.setEdt(this.timeDifference(focFlightInfoDetailView.getEta(),
                    focFlightInfoDetailView.getStd()));
            focFlightInfoDetailView.setAtd(this.timeDifference(focFlightInfoDetailView.getAtd(),
                    focFlightInfoDetailView.getStd()));
        }
        focFlightInfoDetailView.setDepartures(focFlightInfoDetailView.getBay());
        return focFlightInfoDetailView;
    }

    /**
     * Title：getFlightCountInfo() <br>
     * Description：获取航班监控统计表头 <br>
     * author：于琦海 <br>
     * date：2020/4/17 14:33 <br>
     * @param form MobileFlightInfoListForm
     * @return FlightMonitoringView
     */
    @Override
    public FlightMonitoringView getFlightCountInfo(MobileFlightInfoListForm form) {
        // 统计航班状态，这里是不需要管航班状态的
        FlightMonitoringView flightMonitoringView = focFlightInfoDao.statisticsFlightStatus(form);
        StringBuilder sql = new StringBuilder("SELECT * FROM FOC_FLIGHT_INFO WHERE 1 = 1 ");
        form.setStates(null);
        QueryResults results = focFlightInfoDao.getFlightInfoList(sql, form, FocFlightInfo.class);
        flightMonitoringView.setAllCount(results.getPagination().getTotal());
        return flightMonitoringView;
    }

    /**
     * Title：timeDifference（） <br>
     * Description：计算延误的时间差 <br>
     * author：于琦海 <br>
     * date：2020/4/17 13:08 <br>
     * @param startTime String
     * @param endTime String
     * @return String
     */
    private String timeDifference(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return "-";
        }
        String[] start = startTime.split(":");
        int startInter = Integer.parseInt(start[0]) * 60 + Integer.parseInt(start[1]);
        String[] end = endTime.split(":");
        int endInter = Integer.parseInt(end[0]) * 60 + Integer.parseInt(end[1]);
        int abs = Math.abs(startInter - endInter);
        int hour = abs / 60;
        int min = abs % 60;
        return hour + ":" + min;
    }

    /**
     * Title：switchFlightStatues() <br>
     * Description：转换航班状态 <br>
     * author：于琦海 <br>
     * date：2020/4/13 21:22 <br>
     * @param list List<FocFlightInfo>
     * @return List<FlightMonitoringVO>
     */
    private List<FlightMonitoringVO> switchFlightStatues( List<FocFlightInfo> list,String flightState) {
        List<FlightMonitoringVO> result = new ArrayList<>();
        if (StringUtils.isBlank(flightState)) {
            list.forEach(focFlightInfo -> {
                FlightMonitoringVO flightMonitoringVO = new FlightMonitoringVO();
                BeanUtils.copyProperties(focFlightInfo, flightMonitoringVO);
                flightMonitoringVO.setStatus(this.getH5Status(focFlightInfo));
                result.add(flightMonitoringVO);
            });
        } else {
            list.forEach(focFlightInfo -> {
                FlightMonitoringVO flightMonitoringVO = new FlightMonitoringVO();
                BeanUtils.copyProperties(focFlightInfo, flightMonitoringVO);
                if (flightState.equals("0")) {
                    flightMonitoringVO.setStatus("起飞");
                } else if (flightState.equals("1")) {
                    flightMonitoringVO.setStatus("到达");
                } else if (flightState.equals("2")) {
                    flightMonitoringVO.setStatus("延误");
                } else if (flightState.equals("4")) {
                    flightMonitoringVO.setStatus("取消");
                } else if(flightState.equals("3")) {
                    flightMonitoringVO.setStatus("异常");
                } else {
                    flightMonitoringVO.setStatus("计划");
                }
                result.add(flightMonitoringVO);
            });
        }
        return result;
    }

    /**
     * Title：getH5Status <br>
     * Description：H5状态转换 <br>
     * author：于琦海 <br>
     * date：2020/5/18 14:11 <br>
     * @param focFlightInfo FocFlightInfo
     * @return String
     */
    private String getH5Status(FocFlightInfo focFlightInfo) {
        if (StringUtils.isNotBlank(focFlightInfo.getFlgCs()) || "0".equals(focFlightInfo.getAdjustType())) {
            return "取消";
        }
        if (StringUtils.isNotBlank(focFlightInfo.getFlgDelay())){
            return "延误";
        }
        // 备降返航补班都是异常
        if (StringUtils.isNotBlank(focFlightInfo.getFlightType()) && "补班".equals(focFlightInfo.getFlightType())
                || StringUtils.isNotBlank(focFlightInfo.getFlgVr1()) && focFlightInfo.getFlgVr1().contains("V")
                || StringUtils.isNotBlank(focFlightInfo.getFlgVr1()) && focFlightInfo.getFlgVr1().contains("R")) {
            return "异常";
        }
        // 起飞
        if (StringUtils.isNotBlank(focFlightInfo.getAtd()) || StringUtils.isNotBlank(focFlightInfo.getAta())){
            String x = getTakeOff(focFlightInfo);
            if (x != null) return x;
        }
        return "计划";
    }

    /**
     * Title：getStatus() <br>
     * Description：获取航班状态 <br>
     * author：于琦海 <br>
     * date：2020/4/13 21:33 <br>
     * @param focFlightInfo FocFlightInfo
     * @return String
     */
    public String getStatus(FocFlightInfo focFlightInfo) {
        if (StringUtils.isNotBlank(focFlightInfo.getFlgCs()) || "0".equals(focFlightInfo.getAdjustType())) {
            return "取消";
        }
        if (StringUtils.isNotBlank(focFlightInfo.getFlgDelay())){
            return "延误";
        }
        if (StringUtils.isNotBlank(focFlightInfo.getFlightType()) && "补班".equals(focFlightInfo.getFlightType())){
            return "补班";
        }
        //VC是备降的原始段，如果是V1代表备降后新增的第1段，V2是备降后新增的第2段。
        if (StringUtils.isNotBlank(focFlightInfo.getFlgVr1())&& focFlightInfo.getFlgVr1().contains("V")){
            return "备降";
        }
        //RC是返航的原始段，如果是R1代表返航后新增的第1段，R2是返航后新增的第2段。
        if (StringUtils.isNotBlank(focFlightInfo.getFlgVr1())&& focFlightInfo.getFlgVr1().contains("R")){
            return "返航";
        }
        // 实际起飞时间，实际到达时间
        if (StringUtils.isNotBlank(focFlightInfo.getAtd()) || StringUtils.isNotBlank(focFlightInfo.getAta())){
            String x = getTakeOff(focFlightInfo);
            if (x != null) return x;
        }
        if (StringUtils.isBlank(focFlightInfo.getAtd())){
            return "计划";
        }
        if (StringUtils.isNotBlank(focFlightInfo.getFlightType())&& focFlightInfo.getFlightType().contains("正班")){
            return "正班";
        }
        return "-";
    }

    private String getTakeOff(FocFlightInfo focFlightInfo) {
         //StringUtils.isNotBlank(focFlightInfo.getEta()
        if (StringUtils.isNotBlank(focFlightInfo.getAtd()) && StringUtils.isBlank(focFlightInfo.getAta())) {
            return "起飞";
        }
        if (StringUtils.isNotBlank(focFlightInfo.getAta())) {
            return "到达";
        }
        return null;
    }

    /**
     * Title：getOriginalSegementView() <br>
     * Description：获取原始航段信息 <br>
     * author：于琦海 <br>
     * date：2020/3/27 17:17 <br>
     * @param focFlightInfo FocFlightInfo
     * @param sta String
     * @param arrivalPort String
     * @return OriginalSegmentView
     */
    private OriginalSegmentView getOriginalSegmentView(FocFlightInfo focFlightInfo, String sta, String arrivalPort) {
        OriginalSegmentView originalSegmentView = new OriginalSegmentView();
        originalSegmentView.setFlightDate(focFlightInfo.getFlightDate());
        originalSegmentView.setFlightNum(focFlightInfo.getFlightNo());
        originalSegmentView.setStd(focFlightInfo.getStd());
        originalSegmentView.setSta(sta);
        originalSegmentView.setAcType(focFlightInfo.getAcType());
        originalSegmentView.setAcReg(focFlightInfo.getAcReg());
        originalSegmentView.setSegment(focFlightInfo.getDepartPort()+" - "+focFlightInfo.getArrivalPort()+" - "+ arrivalPort);
        return originalSegmentView;
    }

    /**
     * Title：getSegment() <br>
     * Description：获取普通航段信息 <br>
     * author：于琦海 <br>
     * date：2020/3/27 15:52 <br>
     * @param focFlightInfo FocFlightInfo
     * @return String
     */
    private String getSegment(FocFlightInfo focFlightInfo){
        String departPort = focFlightInfo.getDepartPort();
        String arrivalPort = focFlightInfo.getArrivalPort();
        return departPort + " - " + arrivalPort;
    }

}