package com.swcares.scgsi.service;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.VO.FlightMonitoringVO;
import com.swcares.scgsi.common.model.form.FlightInfoListForm;
import com.swcares.scgsi.common.model.form.FlightListForm;
import com.swcares.scgsi.common.model.form.MobileFlightInfoListForm;
import com.swcares.scgsi.common.model.view.FlightMonitoringView;
import com.swcares.scgsi.common.model.view.FocFlightInfoDetailView;
import com.swcares.scgsi.common.model.view.OriginalSegmentView;
import com.swcares.scgsi.common.model.view.TraceInfoView;
import com.swcares.scgsi.entity.FocFlightInfo;

import java.util.List;
import java.util.Map;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FlightInfoService <br>
 * Package：com.swcares.scgsi.service <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 03月20日 10:30 <br>
 * @version v1.0 <br>
 */
public interface FlightInfoService {

    /**
     * Title：getFlightInfo() <br>
     * Description：通过航班ID查询航班信息 <br>
     * author：于琦海 <br>
     * date：2020/3/20 10:37 <br>
     * @param flightId String
     * @return RenderResult<FocFlightInfo>
     */
    FocFlightInfo getFlightInfoById(String flightId);

    /**
     * Title：getFlightSegment() <br>
     * Description：通过航班号航班日期查询航班航段 <br>
     * author：于琦海 <br>
     * date：2020/3/20 10:37 <br>
     * @param flightNum String
     * @param flightDate String
     * @return Object
     */
    List<Map<String,String>> getFlightSegment(String flightNum, String flightDate);

    /**
     * Title：getFlightInfoList（） <br>
     * Description：通过航班四要素查询航班信息列表 <br>
     * author：于琦海 <br>
     * date：2020/3/20 14:30 <br>
     * @param form FlightInfoListForm
     * @return RenderResult<List<FocFlightInfo>>
     */
    List<FocFlightInfo> getFlightInfoList(FlightInfoListForm form);

    /**
     * Title： getFlightList（）<br>
     * Description：web端根据条件查询航班列表<br>
     * author：于琦海 <br>
     * date：2020/3/30 15:00 <br>
     * @param form FlightListForm
     * @return QueryResults
     */
    QueryResults getFlightList(FlightListForm form);
    /**
     * Title：getOriginalSegment <br>
     * Description：航班四要素获取原始航段信息<br>
     * author：王建文 <br>
     * date：2020-4-3 12:37 <br>
     * @param  form 参数
     * @return
     */
    public OriginalSegmentView getOriginalSegment(FlightInfoListForm form);

    /**
     * Title：getH5FlgihtInfoList（） <br>
     * Description：获取H5端的航班列表 <br>
     * author：于琦海 <br>
     * date：2020/4/12 16:14 <br>
     * @param form MobileFlightInfoListForm
     * @return QueryResults
     */
    QueryResults getH5FlightInfoList(MobileFlightInfoListForm form);

    /**
     * Title：findFlightInfo() <br>
     * Description：通过航班号和航班日期查询航班信息 <br>
     * author：于琦海 <br>
     * date：2020/4/14 9:45 <br>
     * @param flightNo String
     * @param flightDate String
     * @return List<FlightMonitoringVO>
     */
    List<FlightMonitoringVO> findFlightInfo(String flightNo, String flightDate);

    /**
     * Title：getFlightInfoCodeAndName（） <br>
     * Description：根据四字码获取三字码和中文航站 <br>
     * author：于琦海 <br>
     * date：2020/4/14 16:55 <br>
     * @param code String
     * @return Map<String, Object>
     */
    Map<String, Object> getFlightInfoCodeAndName(String code);

    /**
     * Title：passengerClassification() <br>
     * Description：统计旅客分类 <br>
     * author：于琦海 <br>
     * date：2020/4/14 17:32 <br>
     * @param psgListByFlight Object
     * @return TraceInfoView
     */
    TraceInfoView passengerClassification(Object psgListByFlight);

    /**
     * Title：findAlternateInfo（） <br>
     * Description：查询备降地 <br>
     * author：于琦海 <br>
     * date：2020/4/17 9:53 <br>
     * @param focFlightInfoDetailView FocFlightInfoDetailView
     * @return FocFlightInfoDetailView
     */
    FocFlightInfoDetailView findAlternateInfo(FocFlightInfoDetailView focFlightInfoDetailView);

    /**
     * Title：getFlightCountInfo() <br>
     * Description：获取航班监控统计表头 <br>
     * author：于琦海 <br>
     * date：2020/4/17 14:33 <br>
     * @param form MobileFlightInfoListForm
     * @return FlightMonitoringView
     */
    FlightMonitoringView getFlightCountInfo(MobileFlightInfoListForm form);
}
