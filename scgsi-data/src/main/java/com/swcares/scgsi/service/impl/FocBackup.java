package com.swcares.scgsi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.swcares.scgsi.dao.repository.FocBackupRepository;
import com.swcares.scgsi.entity.FocFlightInfoBackup;
import com.swcares.scgsi.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FocBackup <br>
 * Package：com.swcares.scgsi.service.impl <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 03月19日 10:09 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class FocBackup {

    @Autowired
    private RedisService redisService;

    @Resource
    private FocBackupRepository focBackupRepository;

    /**
     * Title：backupRedisToDb（） <br>
     * Description：将redis中前天的FOC历史数据入库 <br>
     * author：于琦海 <br>
     * date：2020/3/19 10:11 <br>
     */
    public void backupRedisToDb(){
        // Map<field,value> key为getTheDayBefore()
        LocalDateTime start = LocalDateTime.now();
        String data = getTheDayBefore();
        Map<Object, Object> hmget = redisService.hmget(data);
        log.info("FOC历史数据入库定时任务开始，航班日期为:{} ,redis返回的数据为：{}", data, hmget);
        if (Objects.isNull(hmget) && hmget.size()<=0){
            return;
        }
        // 拿到所有数据 存库并且删除redis中的值
        List<Object> values = hmget.values().stream().collect(Collectors.toList());
        List<FocFlightInfoBackup> backupList = new ArrayList<>();
        for (Object value : values) {
            backupList.add(JSONObject.parseObject(JSON.toJSONString(value), FocFlightInfoBackup.class));
        }
        focBackupRepository.saveAll(backupList);
        Set<Object> objects = hmget.keySet();
        if (Objects.nonNull(objects) && objects.size()>0){
            redisService.hdel(data,hmget.keySet().toArray());
        }
        log.info("线程{}:开启时间:{} 消耗的时间为:{}毫秒", "FOC备份定时任务", start,
                Duration.between(start, LocalDateTime.now()).toMillis());
        // 尽快回收
        values = null;
        backupList = null;
        hmget = null;
    }

    /**
     * Title：getTheDayBefore（） <br>
     * Description：获取前一天的时间<br>
     * author：于琦海 <br>
     * date：2020/3/19 10:20 <br>
     * @param: null
     * @return:
     */
    private static String getTheDayBefore(){
        LocalDate localDate = LocalDate.now();
        LocalDate yesterday = localDate.plusDays(-2);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        return yesterday.format(dateTimeFormatter);
    }

}
