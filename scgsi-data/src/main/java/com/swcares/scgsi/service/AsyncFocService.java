package com.swcares.scgsi.service;

import com.swcares.scgsi.common.model.VO.FocFlightsVO;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：AsyncFocService <br>
 * Package：com.swcares.scgsi.service <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 02月24日 12:17 <br>
 * @version v1.0 <br>
 */
public interface AsyncFocService {

    /**
     * Title：Warehouse() <br>
     * Description：解析数据入库 <br>
     * author：于琦海 <br>
     * date：2020/2/24 12:23 <br>
     * @param flights  FocFlightsVO.Flight
     */
    void Warehouse(FocFlightsVO.Flight flights);
}
