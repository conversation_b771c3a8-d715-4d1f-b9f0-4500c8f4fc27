package com.swcares.scgsi.service;

import com.swcares.scgsi.common.model.form.ContentIdxForm;
import com.swcares.scgsi.common.model.form.ContentTraceForm;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：TraceService <br>
 * Package：com.swcares.scgsi.service <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 03月12日 14:11 <br>
 * @version v1.0 <br>
 */
public interface TraceService {

    /**
     * Title：getPsgListByFilght() <br>
     * Description：通过航班查询旅客列表 <br>
     * author：于琦海 <br>
     * date：2020/3/12 14:03 <br>
     * @param form ContentTraceForm
     * @return Object
     */
    Object getPsgListByFilght(ContentTraceForm form);

    /**
     * Title：getPsgByIdx() <br>
     * Description：通过Idx查询旅客信息 <br>
     * author：于琦海 <br>
     * date：2020/3/12 14:03 <br>
     * @param form ContentIdxForm
     * @return Object
     */
    Object getPsgByIdx(ContentIdxForm form);
}
