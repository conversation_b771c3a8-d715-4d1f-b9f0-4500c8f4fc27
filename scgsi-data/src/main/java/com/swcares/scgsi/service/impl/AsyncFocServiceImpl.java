package com.swcares.scgsi.service.impl;

import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.SpringUtil;
import com.swcares.scgsi.common.model.VO.FocFlightsVO;
import com.swcares.scgsi.dao.FocFlightInfoDao;
import com.swcares.scgsi.entity.FocFlightInfo;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.service.AsyncFocService;
import com.swcares.scgsi.util.Asserts;
import com.swcares.scgsi.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：AsyncFocServiceImpl <br>
 * Package：com.swcares.scgsi.service.impl <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 02月24日 12:19 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class AsyncFocServiceImpl implements AsyncFocService {

    @Autowired
    private IdWorker idWorker;

    /**
     * Title：Warehouse() <br>
     * Description：解析数据入库 <br>
     * author：于琦海 <br>
     * date：2020/2/24 12:23 <br>
     * @param flights FocFlightsVO.Flight
     */
    @Override
    public void Warehouse(FocFlightsVO.Flight flights) {
        if (Objects.isNull(flights)) {
            return;
        }
        List<FocFlightsVO.FlightInfo> flightsList = flights.getList();
        if (flightsList == null || flightsList.size() <= 0) {
            log.info("解析FOC数据，其航班信息为空！");
            return;
        }
        ExecutorService executorService = Executors.newFixedThreadPool(4);
        // 根据航班日期、航班号分组
        Map<String, Map<String, List<FocFlightsVO.FlightInfo>>> groupsMap =
                flightsList.stream().collect(Collectors.groupingBy(FocFlightsVO.FlightInfo::getFlightDate,
                        Collectors.groupingBy(FocFlightsVO.FlightInfo::getFlightNo)));
        // 分析：假定当前这个线程为main线程，遍历四个航班日期会产生四个以日期结尾的线程。
        // main线程会在forEach完‘所有’后才往下走，而此时value值已经都在日期结尾的线程中了
        // 所以后续直接释放map的值来加快GC回收是可行的并且安全的
        groupsMap.forEach((key, value) -> executorService.execute(() ->{
            String threadName = "Thread-pool-" + key;
            Thread.currentThread().setName(threadName);
            LocalDateTime start = LocalDateTime.now();
            this.asyncInit(value);
            log.info("线程{}:开启时间:{} 消耗的时间为:{}毫秒", threadName, start,
                    Duration.between(start, LocalDateTime.now()).toMillis());
        }));
        // 优雅的关闭通道
        executorService.shutdown();
        groupsMap = null;
    }

    /**
     * Title：asyncInit() <br>
     * Description：异步线程的开始 <br>
     * author：于琦海 <br>
     * date：2020/3/2 20:43 <br>
     * @param map Map<航班号，航班信息列表>
     */
    private void asyncInit(Map<String, List<FocFlightsVO.FlightInfo>> map) {
        map.forEach((key, value) -> {
            // 航班号对应的值只有一条，不需要拼逻辑航段直接copy属性设置不为逻辑段
            if (value.size() == 1) {
                FocFlightInfo focFlightInfo = new FocFlightInfo();
                focFlightInfo.setLogic(false);
                this.copyProperties(value.get(0), focFlightInfo);
                log.debug("接收FOC的航班号为:{}  --> 全量航班数据为:{}",focFlightInfo.getFlightNo(),focFlightInfo);
                this.updateFocFlightInfo(focFlightInfo);
            } else {
                // 有逻辑段处理
                this.assembleFlight(value);
            }
        });
    }

    /**
     * Title：assembleFlight() <br>
     * Description：解析正常航段，组装逻辑段 <br>
     * author：于琦海 <br>
     * date：2020/3/2 20:46 <br>
     * @param value FOC返回航班信息集合
     */
    private void assembleFlight(List<FocFlightsVO.FlightInfo> value) {
        List<FocFlightInfo> result = new ArrayList<>();
        List<FocFlightInfo> focFlightInfos = new ArrayList<>();
        for (FocFlightsVO.FlightInfo flightInfo : value) {
            FocFlightInfo focFlightInfo = new FocFlightInfo();
            this.copyProperties(flightInfo, focFlightInfo);
            focFlightInfo.setLogic(false);
            stringBlankToNull(focFlightInfo);
            log.debug("接收FOC的航班号为:{}  --> 全量航班数据为:{}",focFlightInfo.getFlightNo(),focFlightInfo);
            focFlightInfos.add(focFlightInfo);
        }
        // 取消的航班不参与拼接
       /* List<FocFlightInfo> cancelFlightInfo =
                focFlightInfos.stream().filter(flightInfo -> !StringUtils.isEmpty(flightInfo.getFlgCs())
                        || "0".equals(flightInfo.getAdjustType())).collect(Collectors.toList());*/
        //FLG_VR1列，里面的值如果是空是正常航班，如果是VC是备降的原始段，
        // 如果是V1代表备降后新增的第1段，V2是备降后新增的第2段。
        // RC是返航的原始段，如果是R1代表返航后新增的第1段，R2是返航后新增的第2段。
       /* if (cancelFlightInfo != null && cancelFlightInfo.size() > 0) {
            focFlightInfos.removeAll(cancelFlightInfo);
            result.addAll(cancelFlightInfo);
        }*/
        List<FocFlightInfo> vr =
                focFlightInfos.stream().filter(focFlightInfo -> !StringUtils.isEmpty(focFlightInfo.getFlgVr1())).collect(Collectors.toList());
        if (Objects.nonNull(vr) && vr.size()>0){
            result.addAll(vr);
        }
        // 查询出正常航班进行拼接，其他情况直接存库
        List<FocFlightInfo> normalFlightInfo =
                focFlightInfos.stream().filter(focFlightInfo -> StringUtils.isEmpty(focFlightInfo.getFlgVr1())).collect(Collectors.toList());
        // 此处不使用Lambda，因为要快速回收result中的对象和List对象
        for (FocFlightInfo flightInfo : normalFlightInfo) {
            for (FocFlightInfo info : normalFlightInfo) {
                // 排除自己和防止A-B-A航段造成 A-A B-B航段（可能没有这种情况）
                if (info.getFlightId().equals(flightInfo.getFlightId())) {
                    continue;
                }
                // 生成逻辑航段
                if (flightInfo.getPoa().equals(info.getPod()) && !info.getPoa().equals(flightInfo.getPod())) {
                    result.add(this.getFlightInfoEntity(flightInfo, info));
                }
            }
            result.add(flightInfo);
        }
       // cancelFlightInfo = null;
        focFlightInfos = null;
        result.parallelStream().forEach(focFlightInfo -> updateFocFlightInfo(focFlightInfo));
        result = null;
    }

    /**
     * Title：updateFocFlightInfo() <br>
     * Description：更新数据 <br>
     * author：于琦海 <br>
     * date：2020/3/6 10:44 <br>
     * @param focFlightInfo
     */
    private void updateFocFlightInfo(FocFlightInfo focFlightInfo) {
        RedisService redisService = SpringUtil.getBean(RedisService.class);
        FocFlightInfoDao focFlightInfoDao = SpringUtil.getBean(FocFlightInfoDao.class);
        // 后续可以通过values(key)获取值存历史表
        String hashKey = focFlightInfo.getFlightDate();
        // 如果为true，存在说明数据入库过了，并且不需要更新
        if (redisService.hHasKey(hashKey, String.valueOf(focFlightInfo.hashCode()))) {
            return;
        }
        redisService.hset(hashKey, String.valueOf(focFlightInfo.hashCode()), focFlightInfo);
        // 通过航班四要素查询 航班号 航班日期 起始航段
        FocFlightInfo flightInfo = focFlightInfoDao.findFlightInfo(focFlightInfo.getFlightNo(),
                focFlightInfo.getFlightDate(), focFlightInfo.getPoa(), focFlightInfo.getPod());
        if (Objects.isNull(flightInfo)) {
            // insert DB
            focFlightInfoDao.saveFlightInfo(focFlightInfo);
            return;
        }
        if (!focFlightInfo.equals(flightInfo)){
            BeanUtils.copyProperties(focFlightInfo,flightInfo,"flightId");
            focFlightInfoDao.saveFlightInfo(flightInfo);
        }
    }

    /**
     * Title：getFlightInfoEntity() <br>
     * Description：获取逻辑段航段 <br>
     * author：于琦海 <br>
     * date：2020/3/2 16:16 <br>
     * @param flightInfo 起飞航班拼接信息
     * @param flight     到达航段拼接信息
     * @return FocFlightInfo
     */
    private FocFlightInfo getFlightInfoEntity(FocFlightInfo flightInfo, FocFlightInfo flight) {
        //组装对象
        FocFlightInfo createFlight = new FocFlightInfo();
        createFlight.setFlightId(idWorker.nextId().toString());
        // 航班号，机号，机型，日期都会保持一致
        createFlight.setFlightDate(flightInfo.getFlightDate());
        createFlight.setFlightNo(flightInfo.getFlightNo());
        createFlight.setAcReg(flightInfo.getAcReg());
        createFlight.setAcType(flightInfo.getAcType());
        // 到达机场
        createFlight.setArrivalPort(flight.getArrivalPort());
        createFlight.setSta(flight.getSta());
        createFlight.setEta(flight.getEta());
        createFlight.setAta(flight.getAta());
        createFlight.setPoa(flight.getPoa());
        // 起飞机场
        createFlight.setDepartPort(flightInfo.getDepartPort());
        createFlight.setStd(flightInfo.getStd());
        createFlight.setEtd(flightInfo.getEtd());
        createFlight.setAtd(flightInfo.getAtd());
        createFlight.setPod(flightInfo.getPod());
        // 标识为逻辑段
        createFlight.setLogic(true);
        if ("0".equals(flight.getAdjustType()) || "0".equals(flightInfo.getAdjustType())){
            createFlight.setAdjustType("0");
        }
        if("I".equalsIgnoreCase(flight.getDOrI()) || "I".equalsIgnoreCase(flightInfo.getDOrI())){
            createFlight.setDOrI("I");
        }else{
            createFlight.setDOrI("D");
        }
        return createFlight;
    }

    /**
     * Title：copyProperties() <br>
     * Description：将一个VO的属性值copy到另一个VO属性中 <br>
     * author：于琦海 <br>
     * date：2020/3/4 14:07 <br>
     * @param source Object
     * @param target Object
     */
    private void copyProperties(@Nullable Object source, @Nullable Object target) {
        Asserts.notNull(source, MessageCode.PARAM_IS_NULL.getCode());
        Asserts.notNull(target, MessageCode.PARAM_IS_NULL.getCode());
        Class<?> actualEditable = source.getClass();
        Field[] declaredFields = actualEditable.getDeclaredFields();
        Class<?> targetClass = target.getClass();
        for (Field declaredField : declaredFields) {
            String getMethodName =
                    "get" + declaredField.getName().substring(0, 1).toUpperCase() + declaredField.getName().substring(1);
            String setMethodName =
                    "set" + declaredField.getName().substring(0, 1).toUpperCase() + declaredField.getName().substring(1);
            String genericType = declaredField.getType().getTypeName();
            try {
                Method method = actualEditable.getMethod(getMethodName, null);
                Object invoke = method.invoke(source, null);
                if (Integer.class.getName().equals(genericType)){
                    Method methodFiled = targetClass.getMethod(setMethodName, Integer.class);
                    methodFiled.invoke(target,invoke);
                }
                if (Boolean.class.getName().equals(genericType)){
                    Method methodFiled = targetClass.getMethod(setMethodName, Boolean.class);
                    methodFiled.invoke(target,invoke);
                }
                if (Long.class.getName().equals(genericType)){
                    Method methodFiled = targetClass.getMethod(setMethodName, Long.class);
                    methodFiled.invoke(target,invoke);
                }
                if (Double.class.getName().equals(genericType)){
                    Method methodFiled = targetClass.getMethod(setMethodName, Double.class);
                    methodFiled.invoke(target,invoke);
                }
                if (String.class.getName().equals(genericType)){
                    Method methodFiled = targetClass.getMethod(setMethodName, String.class);
                    methodFiled.invoke(target,invoke);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
      Title：stringBlankToNull() <br>
     * Description：把空格变成null <br>
     * author：于琦海 <br>
     * date：2020/3/26 20:30 <br>
    * @param t T
     */
    public static <T> void stringBlankToNull(T t) {
        if (t == null) {
            return;
        }
        Field[] declaredFields = t.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            if (field.getType().equals(String.class)) {
                // 将属性的首字母大写
                String methodName = field.getName().replaceFirst(field.getName().substring(0, 1), field.getName().substring(0, 1).toUpperCase());
                try {
                    Method methodGet = t.getClass().getMethod("get" + methodName);
                    // 调用getter方法获取属性值
                    String str = (String) methodGet.invoke(t);
                    if (StringUtils.isEmpty(str)) {
                        field.set(t, null);
                    }
                } catch (Exception e) {
                    log.error("方法stringBlankToNull转换异常", e);
                    e.printStackTrace();
                }
            }
        }
    }
}