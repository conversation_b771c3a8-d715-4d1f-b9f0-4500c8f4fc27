package com.swcares.scgsi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.swcares.exception.GenericException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.common.model.VO.FocFlightsVO;
import com.swcares.scgsi.common.utils.FocHttpClient;
import com.swcares.scgsi.service.AsyncFocService;
import lombok.extern.slf4j.Slf4j;
import org.json.XML;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FocAnalyze <br>
 * Package：com.swcares.scgsi.service <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 02月24日 9:52 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class FocAnalyze {

    @Autowired
    private AsyncFocService asyncFocService;

    // FOC的地址，经过了nginx反向代理
    @Value("${foc.url}")
    private String FOC_URL;

    // FOC日期格式
    private static final String FOC_DATE_FORMATE = "yyyyMMdd";

    /**
     * Title：parseContent <br>
     * Description：解析FOC接口返回的内容 <br>
     * author：于琦海 <br>
     * date：2020/5/20 9:40 <br>
     * @param result String
     */
    public void parseContent(String result){
        // 根据业务逻辑此处根据节点获取的值不会为空
        org.json.JSONObject xmlJSONObj = XML.toJSONObject(result);
        xmlJSONObj = xmlJSONObj.getJSONObject("soap:Envelope");
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(xmlJSONObj.toString());
        JSONObject body = jsonObject.getJSONObject("soap:Body");
        JSONObject dynamicResponse = body.getJSONObject("getFlight_dynamicResponse");
        JSONObject dynamicResult = dynamicResponse.getJSONObject("getFlight_dynamicResult");
        dynamicResult = JSON.parseObject(dynamicResult.toString());
        FocFlightsVO listFlight = JSON.parseObject(dynamicResult.toString(), FocFlightsVO.class);
        // 解析为对象后，判断返回内容正确与否。再进行下一步比对入库操作
        FocFlightsVO.Ri ri = listFlight.getRi();
        if ("S".equalsIgnoreCase(ri.getResultFlag())){
            // 多线程处理，提高性能。
            asyncFocService.Warehouse(listFlight.getFlights());
        }else{
            log.info("FOC返回的错误结果标志为:{},错误消息为:{}", ri.getResultFlag(), ri.getResultMessage());
            throw new GenericException(MessageCode.UN_KNOWN.getCode(), "FOC返回的错误结果标志为:" + ri.getResultFlag() + "FOC" +
                    "错误消息为" + ri.getResultMessage());
        }
    }

    /**
     * Title：linkFocClient() <br>
     * Description：与FOC接口建立连接 <br>
     * author：于琦海 <br>
     * date：2020/2/24 9:58 <br>
     */
    public void linkFocClient() {
        String result;
        try {
            result = FocHttpClient.doPost(FOC_URL,
                    this.assemblyParameters(getRequestFocDate(-1), getRequestFocDate(2)));
        } catch (IOException e) {
            log.error("定时任务获取航班数据:[URI:{},startDate:{},startDate:{}]", FOC_URL, getRequestFocDate(0),
                    getRequestFocDate(3), e);
            throw new GenericException(MessageCode.UN_KNOWN.getCode(), "FOC接口起始日期为:" + getRequestFocDate(0) + "FOC" +
                    "接口结束日期为" + getRequestFocDate(3));
        }
        this.parseContent(result);
    }

    /**
     * Title：assemblyParameters() <br>
     * Description：组装参数，传入开始和截至日期 <br>
     * author：于琦海 <br>
     * date：2020/2/24 10:37 <br>
     * @param beginDate 开始日期
     * @param endDate 截至日期
     * @return String
     */
    private String assemblyParameters(String beginDate, String endDate) {
        StringBuilder parameters = new StringBuilder("<soapenv:Envelope xmlns:soapenv=\"http://schemas" +
                ".xmlsoap.org/soap/envelope/\" xmlns:wsy=\"http://WsYdpt.shandongair.com.cn/\">\n");
        parameters.append("<soapenv:Header/>\n");
        parameters.append("<soapenv:Body>\n");
        parameters.append("<wsy:getFlight_dynamic>\n");
        parameters.append("<wsy:getFlight_dynamic_c_1>ydpt</wsy:getFlight_dynamic_c_1>\n");
        parameters.append("<wsy:getFlight_dynamic_c_2>sdasdasda</wsy:getFlight_dynamic_c_2>\n");
        parameters.append("<wsy:getFlight_dynamic_begin_date>{0}</wsy:getFlight_dynamic_begin_date>\n");
        parameters.append("<wsy:getFlight_dynamic_end_date>{1}</wsy:getFlight_dynamic_end_date>\n");
        parameters.append("<wsy:getFlight_dynamic_flight_no></wsy:getFlight_dynamic_flight_no>\n");
        parameters.append("<wsy:getFlight_dynamic_ac_reg></wsy:getFlight_dynamic_ac_reg>\n");
        parameters.append("<wsy:getFlight_dynamic_pod_cn></wsy:getFlight_dynamic_pod_cn>\n");
        parameters.append("<wsy:getFlight_dynamic_poa_cn></wsy:getFlight_dynamic_poa_cn>\n");
        parameters.append("</wsy:getFlight_dynamic>\n");
        parameters.append("</soapenv:Body>\n");
        parameters.append("</soapenv:Envelope>");
        return MessageFormat.format(parameters.toString(), beginDate, endDate);
    }

    /**
     * Title：getRequestFocDate() <br>
     * Description：获取当前日期转化为yyyyMMDD格式，0为当天，-3为三天前，3为三天后 <br>
     * author：于琦海 <br>
     * date：2020/2/24 10:32 <br>
     * @param days 日期
     * @return : String
     */
    private String getRequestFocDate(int days) {
        LocalDate now = LocalDate.now().plusDays(days);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(FOC_DATE_FORMATE);
        return now.format(dateTimeFormatter);
    }

}
