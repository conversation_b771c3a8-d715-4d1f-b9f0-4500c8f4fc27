package com.swcares.scgsi.common.model.view;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：TraceInfoView <br>
 * Package：com.swcares.scgsi.common.model.view <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月14日 18:02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "H5航班分类展示")
public class TraceInfoView {

    //全部数量'
    @ApiModelProperty(value = "全部总数")
    private long totalAll;
    //全部值机
    @ApiModelProperty(value = "全部值机")
    private long totalCheckIn;

    // F舱全部
    @ApiModelProperty(value = "F舱总数")
    private long fAll;

    @ApiModelProperty(value = "F舱值机")
    private long fCheckIn;

    @ApiModelProperty(value = "Y舱总数")
    private long yAll;

    @ApiModelProperty(value = "Y舱值机")
    private long yCheckIn;

    @ApiModelProperty(value = "儿童婴儿总数")
    private long childAll;

    @ApiModelProperty(value = "儿童婴儿值机")
    private long childCheckIn;

    @ApiModelProperty(value = "成人总数")
    private long adultAll;

    @ApiModelProperty(value = "成人值机")
    private long adultCheckIn;
}
