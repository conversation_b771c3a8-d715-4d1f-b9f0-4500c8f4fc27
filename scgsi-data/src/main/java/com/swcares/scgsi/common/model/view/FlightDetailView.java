package com.swcares.scgsi.common.model.view;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FlightDetailView <br>
 * Package：com.swcares.scgsi.common.model.view <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月14日 16:14 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "H5航班详情展示")
public class FlightDetailView {

    /** 起飞机场 */
    @ApiModelProperty(value = "起飞机场")
    private String departPort;

    /** 到达机场 */
    @ApiModelProperty(value = "到达机场")
    private String arrivalPort;

    /** 起飞机场 */
    @ApiModelProperty(value = "起飞机场三字码")
    private String pod;

    /** 到达机场 */
    @ApiModelProperty(value = "到达机场三字码")
    private String poa;



}
