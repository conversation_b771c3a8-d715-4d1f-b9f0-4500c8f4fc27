package com.swcares.scgsi.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：MobileFlightInfoListForm <br>
 * Package：com.swcares.scgsi.common.model.form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月12日 15:49 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "H5端查询航班列表表单")
public class MobileFlightInfoListForm {

    @ApiModelProperty(value = "航班日期")
    @NotNull(message = "航班日期为必填项!")
    private String flightDate;

    @ApiModelProperty(value = "排序方式，不传为默认 0:从早到晚 1：从晚到早")
    private String sort;

    @ApiModelProperty(value = "机场三字码")
    private String airport;

    @ApiModelProperty(value = "进出港，0进港 1出港")
    private String harbor;

    @ApiModelProperty(value = "航班状态，不填是全部 0起飞 1到达 2延误 3异常 4取消")
    private String states;

    // 分页相关
    @ApiModelProperty(value = "当前页码")
    @Min(value = 1,message = "当前页码必须从1开始!")
    @NotNull(message = "当前页码为必填项!")
    private Integer current;

    @ApiModelProperty(value = "每页显示记录数")
    @NotNull(message = "每页显示记录数为必填项!")
    private Integer pageSize;
}
