package com.swcares.scgsi.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：TraceQueryPassengerForm <br>
 * Package：com.swcares.scgsi.common.model.form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月14日 17:22 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "旅客分类查询表单")
public class TraceQueryPassengerForm {

    @ApiModelProperty(value = "航班日期", required = true)
    @NotBlank(message = "航班日期不能为空!")
    private String flightDate;

    @ApiModelProperty(value = "航班号", required = true)
    @NotBlank(message = "航班号不能为空!")
    private String flightNum;

    @ApiModelProperty(value = "始发地三字码")
    @NotBlank(message = "始发地三字码不能为空!")
    private String orig;

    @ApiModelProperty(value = "到达地三字码")
    @NotBlank(message = "到达地三字码不能为空!")
    private String dest;
}
