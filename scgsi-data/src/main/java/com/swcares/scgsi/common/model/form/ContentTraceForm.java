package com.swcares.scgsi.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ContentTraceForm <br>
 * Package：com.swcares.scgsi.common.model.form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 03月12日 14:39 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "通过航班获取旅客信息集合表单")
public class ContentTraceForm {
    @ApiModelProperty(value = "航班日期", required = true)
    @NotBlank(message = "航班日期不能为空!")
    private String flightDate;

    @ApiModelProperty(value = "航班号", required = true)
    @NotBlank(message = "航班号不能为空!")
    private String flightNum;

    @ApiModelProperty(value = "始发地三字码")
    private String orig;

    @ApiModelProperty(value = "到达地三字码")
    private String dest;

    @ApiModelProperty(value = "支持旅客姓名/票号/证件号/行李号 多字段")
    private String keySearch;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "值机状态")
    private String checkStatus;

    @ApiModelProperty(value = "是否取消")
    private String isCancel;

    @ApiModelProperty(value = "订票开始时间")
    private String tktStartDate;

    @ApiModelProperty(value = "订票结束时间")
    private String tktEndDate;

    @ApiModelProperty(value = "不含含N舱")
    private String notContainsN;

    @ApiModelProperty(value = "是否出票")
    private String isPrintTktNo;

    @ApiModelProperty(value = "取消时间")
    private String cancelDate;

}
