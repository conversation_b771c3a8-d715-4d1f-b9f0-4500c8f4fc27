package com.swcares.scgsi.common.model.VO;

import lombok.Data;

import java.util.Objects;

@Data
public class TraceInfoDistinctVO{
    private String  bagTag;
    private String  bagWht;
    private String  orig;
    private String  dest;
    private String  etNum;
    private String  flightDate;
    private String  flightNum;
    private String  gender;
    private String  idNum;
    private String  idType;
    private String  idx;
    private String  infantIdx;
    private String  infantName;
    private String  isChild;
    private String  isInfant;
    private String  mainClass;
    private String  sellClass;
    private String  phone;
    private String  printTicketTime;
    private String  psgName;
    private String  segment;
    private String  status;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TraceInfoDistinctVO that = (TraceInfoDistinctVO) o;
        return Objects.equals(idNum, that.idNum);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(idNum);
    }
}
