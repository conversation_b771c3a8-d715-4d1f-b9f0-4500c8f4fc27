package com.swcares.scgsi.common.model.view;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FocFlightInfoDetailView <br>
 * Package：com.swcares.scgsi.common.model.view <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月17日 10:25 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "H5航班详情展示")
public class FocFlightInfoDetailView {

    /** 主键航班id */
    @ApiModelProperty(value = "主键航班id")
    private String flightId;

    /** 航班日期 */
    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    /** 航班号 */
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    /** 机号 */
    @ApiModelProperty(value = "机号")
    private String acReg;

    /** 机型 */
    @ApiModelProperty(value = "机型")
    private String acType;

    /** 起飞机场 */
    @ApiModelProperty(value = "起飞机场")
    private String departPort;

    /** 到达机场 */
    @ApiModelProperty(value = "到达机场")
    private String arrivalPort;

    /** 计划起飞时间 */
    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    /** 预计起飞时间 */
    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    /** 实际起飞时间 */
    @ApiModelProperty(value = "实际起飞时间")
    private String atd;

    /** 计划到达时间 */
    @ApiModelProperty(value = "计划到达时间")
    private String sta;

    /** 预计到达时间 */
    @ApiModelProperty(value = "预计到达时间")
    private String eta;

    /** 实际到达时间 */
    @ApiModelProperty(value = "实际到达时间")
    private String ata;

    /** 航班类型 */
    @ApiModelProperty(value = "航班类型")
    private String flightType;

    /** 延误标识 */
    @ApiModelProperty(value = "延误标识")
    private String flgDelay;

    /** 取消标志 */
    @ApiModelProperty(value = "取消标志")
    private String flgCs;

    /** 备降返航 */
    @ApiModelProperty(value = "备降返航")
    private String flgVr;

    /** 机位 */
    @ApiModelProperty(value = "机位")
    private String bay;

    /** cobt时间 */
    @ApiModelProperty(value = "cobt时间")
    private String cobt;

    /** 订座数 */
    @ApiModelProperty(value = "订座数")
    private String bookSeat;

    /** 总座数 */
    @ApiModelProperty(value = "总座数")
    private String seatNum;

    /** 起飞机场 */
    @ApiModelProperty(value = "起飞机场")
    private String pod;

    /** 到达机场 */
    @ApiModelProperty(value = "到达机场")
    private String poa;

    /** 撤轮档时间 */
    @ApiModelProperty(value = "撤轮档时间")
    private String cldTime;

    /** 无(文档中无此字段含义) */
    @ApiModelProperty(value = "无(文档中无此字段含义)")
    private String adjustType;

    /**备降返航标识*/
    @ApiModelProperty(value = "备降返航标识")
    private String flgVr1;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    /** 延误 */
    @ApiModelProperty(value = "延误原因")
    private String delay_reason;

    /** 取消 */
    @ApiModelProperty(value = "取消原因")
    private String cs_reason;

    /** 备降 */
    @ApiModelProperty(value = "备降原因")
    private String r_reason;

    /** 返航 */
    @ApiModelProperty(value = "返航")
    private String v_reason;

    /** 是否为逻辑航段 */
    @ApiModelProperty(value = "是否为逻辑航段")
    private Boolean logic;

    /** 实际备降地 */
    @ApiModelProperty(value = "实际备降地")
    private String actualAlternateLand;

    /** 预计延误时间 */
    @ApiModelProperty(value = "预计延误时间")
    private String edt;

    /** 实际延误时间 */
    @ApiModelProperty(value = "实际延误时间")
    private String adt;

    /** 出港机位 */
    @ApiModelProperty(value = "出港机位")
    private String departures;

    /** 出港登机口 */
    @ApiModelProperty(value = "出港登机口")
    private String  departureGate;

    /** 到港机位 */
    @ApiModelProperty(value = "到港机位")
    private String arrivals;

    /** 到港登机口 */
    @ApiModelProperty(value = "到港登机口")
    private String arrivalsGate;

}
