package com.swcares.scgsi.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FlightInfoListForm <br>
 * Package：com.swcares.scgsi.common.model.form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 03月20日 12:02 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "通过航班四要素查询航班信息列表表单")
public class FlightInfoListForm{

    @ApiModelProperty(value = "航班日期", required = true)
    private String flightDate;

    @ApiModelProperty(value = "航班号", required = true)
    private String flightNum;

    @ApiModelProperty(value = "始发地三字码")
    private String orig;

    @ApiModelProperty(value = "到达地三字码")
    private String dest;
}
