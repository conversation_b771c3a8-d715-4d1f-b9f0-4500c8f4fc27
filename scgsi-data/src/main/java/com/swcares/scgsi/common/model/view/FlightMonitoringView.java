package com.swcares.scgsi.common.model.view;

import com.swcares.scgsi.common.model.VO.FlightMonitoringVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FlightMonitoringView <br>
 * Package：com.swcares.scgsi.common.model.view <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月13日 13:39 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "H5航班监控列表展示")
public class FlightMonitoringView {

    @ApiModelProperty(value = "取消总数")
    private int flgCsCount;

    @ApiModelProperty(value = "延误总数")
    private int flgTypeCount;

    @ApiModelProperty(value = "异常总数")
    private int abnormalCount;

    @ApiModelProperty(value = "起飞总数")
    private int takeOffCount;

    @ApiModelProperty(value = "到达总数")
    private int arrivalsCount;

    @ApiModelProperty(value = "全部总数")
    private int allCount;

}
