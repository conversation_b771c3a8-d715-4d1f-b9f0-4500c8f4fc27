package com.swcares.scgsi.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Objects;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FocFlightInfo,重写了equals和hashCode方法 <br>
 * Package：com.swcares.scgsi.entity <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 02月25日 11:07 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "FOC_FLIGHT_INFO")
public class FocFlightInfo {

    /** 主键航班id */
    @Id
    @Column(name = "FLIGHT_ID")
    private String flightId;

    /** 航班日期 */
    @Column(name = "FLIGHT_DATE")
    private String flightDate;

    /** 航班号 */
    @Column(name = "FLIGHT_NO")
    private String flightNo;

    /** 机号 */
    @Column(name = "AC_REG")
    private String acReg;

    /** 机型 */
    @Column(name = "AC_TYPE")
    private String acType;

    /** 起飞机场 */
    @Column(name = "DEPART_PORT")
    private String departPort;

    /** 到达机场 */
    @Column(name = "ARRIVAL_PORT")
    private String arrivalPort;

    /** 计划起飞时间 */
    @Column(name = "STD")
    private String std;

    /** 预计起飞时间 */
    @Column(name = "ETD")
    private String etd;

    /** 实际起飞时间 */
    @Column(name = "ATD")
    private String atd;

    /** 计划到达时间 */
    @Column(name = "STA")
    private String sta;

    /** 预计到达时间 */
    @Column(name = "ETA")
    private String eta;

    /** 实际到达时间 */
    @Column(name = "ATA")
    private String ata;

    /** 国内国际 */
    @Column(name="D_OR_I")
    private String dOrI;

    /** 航班类型 */
    @Column(name = "FLIGHT_TYPE")
    private String flightType;

    /** 延误标识 */
    @Column(name = "FLG_DELAY")
    private String flgDelay;

    /** 取消标志 */
    @Column(name = "FLG_CS")
    private String flgCs;

    /** 备降返航 */
    @Column(name = "FLG_VR")
    private String flgVr;

    /** 机位 */
    @Column(name = "BAY")
    private String bay;

    /** cobt时间 */
    @Column(name = "COBT")
    private String cobt;

    /** 订座数 */
    @Column(name = "BOOKSEAT")
    private String bookSeat;

    /** 总座数 */
    @Column(name = "SEAT_NUM")
    private String seatNum;

    /** 起飞机场 */
    @Column(name = "POD")
    private String pod;

    /** 到达机场 */
    @Column(name = "POA")
    private String poa;

    /** 撤轮档时间 */
    @Column(name = "CLD_TIME")
    private String cldTime;

    /** 等于0为计划取消 */
    @Column(name = "ADJUST_TYPE")
    private String adjustType;

    /**备降返航标识*/
    @Column(name = "FLG_VR1")
    private String flgVr1;

    /** 更新时间 */
    @Column(name = "UPDATE_TIME")
    private String updateTime;

    /** 延误 */
    @Column(name = "DELAY_ABNORMAL_REASON")
    private String delay_reason;

    /** 取消 */
    @Column(name = "CS_ABNORMAL_REASON")
    private String cs_reason;

    /** 备降 */
    @Column(name = "R_ABNORMAL_REASON")
    private String r_reason;

    /** 返航 */
    @Column(name = "V_ABNORMAL_REASON")
    private String v_reason;

    /** 是否为逻辑航段 */
    @Column(name = "LOGIC")
    private Boolean logic;


    /**
     * Title：hashCode() <br>
     * Description：重写hashcode，排除航班ID和更新时间 <br>
     * author：于琦海 <br>
     * date：2020/3/27 10:06 <br>
     * @return int
     */
    @Override
    public int hashCode() {
        int result = flightDate != null ? flightDate.hashCode() : 0;
        result = 31 * result + (flightNo != null ? flightNo.hashCode() : 0);
        result = 31 * result + (acReg != null ? acReg.hashCode() : 0);
        result = 31 * result + (acType != null ? acType.hashCode() : 0);
        result = 31 * result + (departPort != null ? departPort.hashCode() : 0);
        result = 31 * result + (arrivalPort != null ? arrivalPort.hashCode() : 0);
        result = 31 * result + (std != null ? std.hashCode() : 0);
        result = 31 * result + (etd != null ? etd.hashCode() : 0);
        result = 31 * result + (atd != null ? atd.hashCode() : 0);
        result = 31 * result + (sta != null ? sta.hashCode() : 0);
        result = 31 * result + (eta != null ? eta.hashCode() : 0);
        result = 31 * result + (ata != null ? ata.hashCode() : 0);
        result = 31 * result + (flightType != null ? flightType.hashCode() : 0);
        result = 31 * result + (flgDelay != null ? flgDelay.hashCode() : 0);
        result = 31 * result + (flgCs != null ? flgCs.hashCode() : 0);
        result = 31 * result + (flgVr != null ? flgVr.hashCode() : 0);
        result = 31 * result + (bay != null ? bay.hashCode() : 0);
        result = 31 * result + (cobt != null ? cobt.hashCode() : 0);
        result = 31 * result + (bookSeat != null ? bookSeat.hashCode() : 0);
        result = 31 * result + (seatNum != null ? seatNum.hashCode() : 0);
        result = 31 * result + (pod != null ? pod.hashCode() : 0);
        result = 31 * result + (poa != null ? poa.hashCode() : 0);
        result = 31 * result + (cldTime != null ? cldTime.hashCode() : 0);
        result = 31 * result + (adjustType != null ? adjustType.hashCode() : 0);
        result = 31 * result + (flgVr1 != null ? flgVr1.hashCode() : 0);
        result = 31 * result + (delay_reason != null ? delay_reason.hashCode() : 0);
        result = 31 * result + (cs_reason != null ? cs_reason.hashCode() : 0);
        result = 31 * result + (r_reason != null ? r_reason.hashCode() : 0);
        result = 31 * result + (v_reason != null ? v_reason.hashCode() : 0);
        result = 31 * result + (logic != null ? logic.hashCode() : 0);
        return result;
    }

    /**
     * Title：equals() <br>
     * Description：重写equals方法 <br>
     * author：于琦海 <br>
     * date：2020/3/27 10:07 <br>
     * @param o Object
     * @return boolean
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        FocFlightInfo that = (FocFlightInfo) o;

        if (!Objects.equals(flightDate, that.flightDate)) return false;
        if (!Objects.equals(flightNo, that.flightNo)) return false;
        if (!Objects.equals(acReg, that.acReg)) return false;
        if (!Objects.equals(acType, that.acType)) return false;
        if (!Objects.equals(departPort, that.departPort)) return false;
        if (!Objects.equals(arrivalPort, that.arrivalPort)) return false;
        if (!Objects.equals(std, that.std)) return false;
        if (!Objects.equals(etd, that.etd)) return false;
        if (!Objects.equals(atd, that.atd)) return false;
        if (!Objects.equals(sta, that.sta)) return false;
        if (!Objects.equals(eta, that.eta)) return false;
        if (!Objects.equals(ata, that.ata)) return false;
        if (!Objects.equals(flightType, that.flightType)) return false;
        if (!Objects.equals(flgDelay, that.flgDelay)) return false;
        if (!Objects.equals(flgCs, that.flgCs)) return false;
        if (!Objects.equals(flgVr, that.flgVr)) return false;
        if (!Objects.equals(bay, that.bay)) return false;
        if (!Objects.equals(cobt, that.cobt)) return false;
        if (!Objects.equals(bookSeat, that.bookSeat)) return false;
        if (!Objects.equals(seatNum, that.seatNum)) return false;
        if (!Objects.equals(pod, that.pod)) return false;
        if (!Objects.equals(poa, that.poa)) return false;
        if (!Objects.equals(cldTime, that.cldTime)) return false;
        if (!Objects.equals(adjustType, that.adjustType)) return false;
        if (!Objects.equals(flgVr1, that.flgVr1)) return false;
        if (!Objects.equals(delay_reason, that.delay_reason)) return false;
        if (!Objects.equals(cs_reason, that.cs_reason)) return false;
        if (!Objects.equals(r_reason, that.r_reason)) return false;
        if (!Objects.equals(v_reason, that.v_reason)) return false;
        return Objects.equals(logic, that.logic);
    }
}
