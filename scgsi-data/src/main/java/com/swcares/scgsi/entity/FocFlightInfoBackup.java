package com.swcares.scgsi.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FocFlightInfoBackup <br>
 * Package：com.swcares.scgsi.entity <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 03月19日 9:49 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "FOC_FLIGHT_INFO_BACKUP")
public class FocFlightInfoBackup {

    /** 主键 */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    /** 航班id */
    @Column(name = "FLIGHT_ID")
    private String flightId;

    /** 航班日期 */
    @Column(name = "FLIGHT_DATE")
    private String flightDate;

    /** 航班号 */
    @Column(name = "FLIGHT_NO")
    private String flightNo;

    /** 机号 */
    @Column(name = "AC_REG")
    private String acReg;

    /** 国内国际 */
    @Column(name="D_OR_I")
    private String dOrI;

    /** 机型 */
    @Column(name = "AC_TYPE")
    private String acType;

    /** 起飞机场 */
    @Column(name = "DEPART_PORT")
    private String departPort;

    /** 到达机场 */
    @Column(name = "ARRIVAL_PORT")
    private String arrivalPort;

    /** 计划起飞时间 */
    @Column(name = "STD")
    private String std;

    /** 预计起飞时间 */
    @Column(name = "ETD")
    private String etd;

    /** 实际起飞时间 */
    @Column(name = "ATD")
    private String atd;

    /** 计划到达时间 */
    @Column(name = "STA")
    private String sta;

    /** 预计到达时间 */
    @Column(name = "ETA")
    private String eta;

    /** 实际到达时间 */
    @Column(name = "ATA")
    private String ata;

    /** 航班类型 */
    @Column(name = "FLIGHT_TYPE")
    private String flightType;

    /** 延误标识 */
    @Column(name = "FLG_DELAY")
    private String flgDelay;

    /** 取消标志 */
    @Column(name = "FLG_CS")
    private String flgCs;

    /** 备降返航 */
    @Column(name = "FLG_VR")
    private String flgVr;

    /** 机位 */
    @Column(name = "BAY")
    private String bay;

    /** cobt时间 */
    @Column(name = "COBT")
    private String cobt;

    /** 订座数 */
    @Column(name = "BOOKSEAT")
    private String bookSeat;

    /** 总座数 */
    @Column(name = "SEAT_NUM")
    private String seatNum;

    /** 起飞机场 */
    @Column(name = "POD")
    private String pod;

    /** 到达机场 */
    @Column(name = "POA")
    private String poa;

    /** 撤轮档时间 */
    @Column(name = "CLD_TIME")
    private String cldTime;

    /** 无(文档中无此字段含义) */
    @Column(name = "ADJUST_TYPE")
    private String adjustType;

    /**备降返航标识*/
    @Column(name = "FLG_VR1")
    private String flgVr1;

    /** 更新时间 */
    @Column(name = "UPDATE_TIME")
    private String updateTime;

    /** 延误 */
    @Column(name = "DELAY_ABNORMAL_REASON")
    private String delay_reason;

    /** 取消 */
    @Column(name = "CS_ABNORMAL_REASON")
    private String cs_reason;

    /** 备降 */
    @Column(name = "R_ABNORMAL_REASON")
    private String r_reason;

    /** 返航 */
    @Column(name = "V_ABNORMAL_REASON")
    private String v_reason;

    /** 是否为逻辑航段 */
    @Column(name = "LOGIC")
    private Boolean logic;
}
