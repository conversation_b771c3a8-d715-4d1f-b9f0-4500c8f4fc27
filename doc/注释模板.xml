<?xml version="1.0" encoding="UTF-8" standalone="no"?><templates><template autoinsert="false" context="filecomment_context" deleted="false" description="Comment for created Java files" enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.filecomment" name="filecomment">
/**  
 * All rights Reserved, Designed By http://xnky.travelsky.net/ &lt;br&gt;
 * Title：${file_name} &lt;br&gt;
 * Package：${package_name} &lt;br&gt; 
 * Description：${todo}(用一句话描述该文件做什么) &lt;br&gt;
 * Copyright © ${year} xnky.travelsky.net Inc. All rights reserved. &lt;br&gt;
 * Company：Aviation Cares Of Southwest Chen Du LTD  &lt;br&gt;
 * <AUTHOR> &lt;br&gt;
 * date ${date} ${time} &lt;br&gt;
 * @version v1.0 &lt;br&gt;
 */ </template><template autoinsert="false" context="fieldcomment_context" deleted="false" description="Comment for fields" enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.fieldcomment" name="fieldcomment">
/**   
 * ${field}：${todo}(用一句话描述这个变量表示什么) &lt;br&gt; 
 * @since v1.0 &lt;br&gt;
 */   
</template><template autoinsert="false" context="methodcomment_context" deleted="false" description="Comment for non-overriding methods" enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.methodcomment" name="methodcomment">
/**   
 * Title：${enclosing_method} &lt;br&gt;
 * Description：${todo}(这里用一句话描述这个方法的作用) &lt;br&gt;
 * author：夏阳 &lt;br&gt;
 * date：${date} ${time} &lt;br&gt;
 * ${tags} &lt;br&gt;
 */ </template><template autoinsert="false" context="overridecomment_context" deleted="false" description="Comment for overriding methods" enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.overridecomment" name="overridecomment">
/**   
 * Title：${enclosing_method} &lt;br&gt;
 * Description：&lt;br&gt;
 * <AUTHOR> &lt;br&gt;
 * date ${date} ${time} &lt;br&gt;
 * ${tags} &lt;br&gt;
 * ${see_to_overridden} &lt;br&gt;
 */ </template><template autoinsert="true" context="settercomment_context" deleted="false" description="Comment for setter method" enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.settercomment" name="settercomment">
/**  
 * Title：${enclosing_method} &lt;br&gt;
 * Description：please write your description &lt;br&gt; 
 * @return ${field_type} &lt;br&gt;  
 */</template><template autoinsert="false" context="typecomment_context" deleted="false" description="Comment for created types" enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.typecomment" name="typecomment">
/**   
 * ClassName：${package_name}.${type_name} &lt;br&gt;
 * Description：${todo}(这里用一句话描述这个类的作用) &lt;br&gt;
 * Copyright © ${year} xnky.travelsky.net Inc. All rights reserved. &lt;br&gt;
 * Company：Aviation Cares Of Southwest Chen Du LTD  &lt;br&gt;
 * <AUTHOR> &lt;br&gt;
 * date ${date} ${time} &lt;br&gt;
 * @version v1.0 &lt;br&gt;
 * ${tags}  
 */  </template><template autoinsert="false" context="constructorcomment_context" deleted="false" description="Comment for created constructors" enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.constructorcomment" name="constructorcomment">  
/**   
 * Title：${enclosing_type} &lt;br&gt;
 * Description：${todo}(这里用一句话描述这个方法的作用) &lt;br&gt;
 * author：夏阳 &lt;br&gt;
 * date：${date} ${time} &lt;br&gt;
 * ${tags} &lt;br&gt;
 */</template><template autoinsert="true" context="delegatecomment_context" deleted="false" description="Comment for delegate methods" enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.delegatecomment" name="delegatecomment">
/**   
 * ${tags}   
 * ${see_to_target}   
 */</template><template autoinsert="false" context="gettercomment_context" deleted="false" description="Comment for getter method" enabled="true" id="org.eclipse.jdt.ui.text.codetemplates.gettercomment" name="gettercomment">
/**  
 * Title：${enclosing_method} &lt;br&gt;
 * Description：please write your description &lt;br&gt;
 * @return ${field_type} &lt;br&gt;  
 */ 
</template></templates>