package com.swcares.scgsi.overbook.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.audit.service.OrderAuditService;
import com.swcares.scgsi.audit.vo.CompensationProgressVo;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.form.ContentIdxForm;
import com.swcares.scgsi.common.model.form.ContentTraceForm;
import com.swcares.scgsi.common.model.form.FlightInfoListForm;
import com.swcares.scgsi.common.model.view.OriginalSegmentView;
import com.swcares.scgsi.entity.FocFlightInfo;
import com.swcares.scgsi.fileuploadanddownload.UploadAndDownload;
import com.swcares.scgsi.flight.dao.impl.FlightCompensateDaoImpl;
import com.swcares.scgsi.flight.service.FlightCompensateService;
import com.swcares.scgsi.flight.vo.CityCodeVo;
import com.swcares.scgsi.overbook.dao.*;
import com.swcares.scgsi.overbook.dao.impl.*;
import com.swcares.scgsi.overbook.dto.*;
import com.swcares.scgsi.overbook.entity.*;
import com.swcares.scgsi.overbook.service.OverBookService;
import com.swcares.scgsi.overbook.vo.*;
import com.swcares.scgsi.service.FlightInfoService;
import com.swcares.scgsi.service.TraceService;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.util.Asserts;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * ClassName：com.swcares.scgsi.audit.service.impl <br>
 * Description：H5-旅客超售 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月15日 13:14 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class OverBookServiceImpl implements OverBookService {

    @Resource
    private FlightInfoDao flightInfoDao;
    @Resource
    private OrderInfoDao orderInfoDao;
    
    @Resource
    private PaxInfoDao paxInfoDao;
    @Resource
    private PaxInfoDaoImpl paxInfoDaoImpl;
    @Resource
    private FlightInfoDaoImpl flightInfoDaoImpl;
    @Resource
    private OrderInfoDaoImpl orderInfoDaoImpl;

    @Resource
    private CompensateInfoDao compensateInfoDao;
    @Resource
    private CompensateInfoDaoImpl compensateInfoDaoImpl;

    @Resource
    private OverInfoDao overInfoDao;
    @Resource
    private OverInfoDaoImpl overInfoDaoImpl;
    @Resource
    private OverBookConfigDao overBookConfigDao;
    @Resource
    private OverBookConfigDaoImpl overBookConfigDaoImpl;
    @Resource
    private FlightCompensateDaoImpl flightCompensateDao;
    @Resource
    private UploadAndDownload uploadAndDownload;

    @Resource
    private OrderAuditService orderAuditService;
    @Resource
    private TraceService traceService;
    @Resource
    private FlightInfoService flightInfoService;
    @Resource
    private FlightCompensateService flightCompensateService;



    DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");


    // 超售改签
    private static final String TYPE_REBOOK = "0";
    // 超售退票
    private static final String TYPE_REFUND = "1";
    // 最低赔付
    private static final String PAY_TYPE_LOWEST = "1";
    // 比例赔付
    private static final String PAY_TYPE_RATIO = "0";

    // 经济舱
    private static final String CLASS_TYPE_J = "1";
    // 公务舱
    private static final String CLASS_TYPE_G = "2";


    // 赔偿单
    private static final String PAY_TYPE_IRREGULAR = "0";
    // 超售
    private static final String PAY_TYPE_OVER = "2";
    // 保存提交
    private static final String SAVE_SUBMIT = "1";
    // 保存草稿
    private static final String SAVE_DRAFT = "0";
    // 状态 0-草稿
    private static final String ORDER_STATUS_DRAFT = "0";
    //状态3-发放
    private static final String ORDER_STATUS_GRANT = "3";
    //赔偿类型 不正常航班赔偿0
    private static final String STATUS_IRREGULAR = "0";

    @Override
    public QueryResults overBookWebList(OverBookListQueryDto overBookListQueryDto) {
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        //三字码转中文
        if(StringUtils.isNotBlank(overBookListQueryDto.getServiceCity())){
            CityCodeVo cityCodeVo = flightCompensateDao.getCityCodeInfoByCityCode3(overBookListQueryDto.getServiceCity());
            Asserts.notNull(cityCodeVo,MessageCode.OVER_SERVICE_CITY_IS_NULL.getCode(), new String[] {overBookListQueryDto.getServiceCity()});
            overBookListQueryDto.setServiceCity(cityCodeVo.getCityChName());
        }

        log.info("web-超售列表【前端请求参数】"+overBookListQueryDto.toString()+";获取当前登录人："+userId);
        return overInfoDaoImpl.findOverBookWebList(overBookListQueryDto, userId);
    }

    @Override
    public List<OverBookListVo> excelOverBookList(OverBookListQueryDto overBookListQueryDto) {
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        //三字码转中文
        if(StringUtils.isNotBlank(overBookListQueryDto.getServiceCity())){
            CityCodeVo cityCodeVo = flightCompensateDao.getCityCodeInfoByCityCode3(overBookListQueryDto.getServiceCity());
            Asserts.notNull(cityCodeVo,MessageCode.OVER_SERVICE_CITY_IS_NULL.getCode(), new String[] {overBookListQueryDto.getServiceCity()});
            overBookListQueryDto.setServiceCity(cityCodeVo.getAirport3code());
        }
        return overInfoDaoImpl.excelOverBookList(overBookListQueryDto, userId);
    }

    @Override
    public OverBookDetailsVo overBookWebDetails(String overId) {
        // 验空
        Asserts.isNotEmpty(overId, MessageCode.PARAM_EXCEPTION.getCode(), new String[] {"超售id"});
        OverBookDetailsVo vo = overInfoDaoImpl.findWebOverDetailsById(overId);
        if(ObjectUtils.isNotEmpty(vo)){
            vo.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, vo.getTelephone()));
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOverBookConfig(List<TimeDifferenceInfoVo> timeDifferenceInfo,
            LowestStandInfo lowestStandInfo, RefundInfoVo refundInfo) {
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        List<OverBookConfigInfo> configList = new ArrayList<>();
        Date createTime = new Date();
        for (TimeDifferenceInfoVo tObj : timeDifferenceInfo) {
            OverBookConfigInfo overBookConfigInfo = new OverBookConfigInfo();
            BeanUtils.copyProperties(tObj, overBookConfigInfo);
            getParamIsUpd(overBookConfigInfo, createTime, userId);
            overBookConfigInfo.setType(TYPE_REBOOK);
            overBookConfigInfo.setPayType(PAY_TYPE_RATIO);
            configList.add(overBookConfigInfo);
        }

        if (null != lowestStandInfo) {
            OverBookConfigInfo configInfo = new OverBookConfigInfo();
            BeanUtils.copyProperties(lowestStandInfo, configInfo);
            getParamIsUpd(configInfo, createTime, userId);
            configInfo.setType(TYPE_REBOOK);
            configInfo.setPayType(StringUtils.isEmpty(lowestStandInfo.getPayType())?PAY_TYPE_RATIO:lowestStandInfo.getPayType());
            configList.add(configInfo);
        }
        if (null != refundInfo) {
            //1最低赔付 0比例赔付
            if ("0".equals(refundInfo.getPayType())) {
                OverBookConfigInfo configInfo1 =
                        getRefundSaveInfo(refundInfo, true, createTime, userId);
                configInfo1.setType(TYPE_REFUND);
                configList.add(configInfo1);
            } else {
                if (StringUtils.isNotBlank(refundInfo.getConditionMoney())) {
                    OverBookConfigInfo configInfo2 =
                            getRefundSaveInfo(refundInfo, false, createTime, userId);
                    configInfo2.setType(TYPE_REFUND);
                    configList.add(configInfo2);
                }
            }

        }
        log.info("---【web-超售配置更新-】--最新配置：" + JSONObject.toJSONString(configList));
        overBookConfigDao.deleteAll();
        overBookConfigDao.saveAll(configList);
    }


    /**
     * Title：getRefundSaveInfo <br>
     * Description： 封装退票保存对象参数<br>
     * author：傅欣荣 <br>
     * date：2020/3/23 14:17 <br>
     * @param
     * @return
     */
    private OverBookConfigInfo getRefundSaveInfo(RefundInfoVo refundInfo, boolean type,
            Date createTime, String createUser) {
        OverBookConfigInfo configInfo = new OverBookConfigInfo();
        BeanUtils.copyProperties(refundInfo, configInfo);
        if (type) {// 比例
            configInfo.setDamagePart(refundInfo.getDamagePart());
            getParamIsUpd(configInfo, createTime, createUser);
            configInfo.setPayType(PAY_TYPE_RATIO);
            return configInfo;
        }
        configInfo.setConditionMoney(refundInfo.getConditionMoney());
        configInfo.setPayType(StringUtils.isEmpty(refundInfo.getPayType())?PAY_TYPE_RATIO:refundInfo.getPayType());
        configInfo.setPayMoney(refundInfo.getPayMoney());
        getParamIsUpd(configInfo, createTime, createUser);
        return configInfo;
    }


    private void getParamIsUpd(OverBookConfigInfo overBookConfigInfo, Date createTime,
            String createUser) {
        overBookConfigInfo.setCreateTime(createTime);
        overBookConfigInfo.setCreateUser(createUser);
        overBookConfigInfo.setStatus("0");

    }

    @Override
    public QueryResults findOverBookList(OverBookQueryDto overBookQueryDto) {
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        log.info("H5-超售列表查询【请求参数】"+overBookQueryDto.toString()+" 【userId】"+userId);
        return overInfoDaoImpl.findOverBookList(overBookQueryDto, userId);
    }

    @Override
    public OverBookVerftnVo overBookVerification(OverBookVerificationDto overBookVerificationDto) {
        String keySearch = overBookVerificationDto.getKeySearch();
        String flightNo = overBookVerificationDto.getFlightNo();
        String flightDate = overBookVerificationDto.getFlightDate();
        String serviceCity = overBookVerificationDto.getServiceCity();
        // 验空
        Asserts.isNotEmpty(keySearch, MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"模糊查询字段"});
        Asserts.isNotEmpty(flightNo, MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"航班号"});
        Asserts.isNotEmpty(flightDate, MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"航班日期"});
        Asserts.isNotEmpty(serviceCity, MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"服务航站三字码"});
        List<TracePaxInfoVo> paxInfoList =
                this.getPaxInfoByPaxFlight(flightNo, flightDate, keySearch);
        if (paxInfoList.size() > 0) {
            TracePaxInfoVo tracePaxInfoVo = paxInfoList.get(0);
            FlightInfoListForm flightForm = new FlightInfoListForm();
            flightForm.setFlightNum(overBookVerificationDto.getFlightNo());
            flightForm.setFlightDate(overBookVerificationDto.getFlightDate().replaceAll("-", "/"));
            //服务航站与旅客起始站不匹配
            if(!serviceCity.equals(tracePaxInfoVo.getOrig())){
                throw new BusinessException(MessageCode.OVER_VERIFICATION_ORG_ERROR.getCode());
            }
            String org = flightCompensateDao.getCityCodeInfoByCityCode3(tracePaxInfoVo.getOrig())
                    .getAirport4code();
            String dst = flightCompensateDao.getCityCodeInfoByCityCode3(tracePaxInfoVo.getDest())
                    .getAirport4code();
            flightForm.setOrig(org);
            flightForm.setDest(dst);
            List<FocFlightInfo> flightList = flightInfoService.getFlightInfoList(flightForm);
            if (flightList.size() > 0) {
                return getReturnVerftnVo(flightList.get(0), tracePaxInfoVo);
            }
        }
        return new OverBookVerftnVo();
    }

    /**
     * Title：getPaxInfoByFlight <br>
     * Description：根据航班四要素查旅客信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/22 11:35 <br>
     * @param  flightNo, flightDate, org, dst
     * @return com.alibaba.fastjson.JSONArray
     */
    private List<TracePaxInfoVo> getPaxInfoByFlight(String flightNo, String flightDate, String org,
            String dst) {
        ContentTraceForm tForm = new ContentTraceForm();
        tForm.setFlightDate(flightDate);
        tForm.setFlightNum(flightNo);
        tForm.setOrig(org);
        tForm.setDest(dst);
        JSONObject obj = (JSONObject) traceService.getPsgListByFilght(tForm);
        com.alibaba.fastjson.JSONArray json =
                com.alibaba.fastjson.JSONArray.parseArray(obj.getString("dataList"));

        return JSONObject.parseArray(json.toJSONString(), TracePaxInfoVo.class);
    }

    /**
     * Title：getPaxInfoByFlight <br>
     * Description：根据航班信息和旅客信息查旅客信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/22 11:35 <br>
     * @param  flightNo, flightDate, org, dst
     * @return com.alibaba.fastjson.JSONArray
     */
    private List<TracePaxInfoVo> getPaxInfoByPaxFlight(String flightNo, String flightDate,
            String keySearch) {
        ContentTraceForm form = new ContentTraceForm();
        form.setFlightNum(flightNo);
        form.setFlightDate(flightDate.replaceAll("-", "/"));
        form.setKeySearch(keySearch);
        form.setIsCancel("Y");
        JSONObject obj = (JSONObject) traceService.getPsgListByFilght(form);
        com.alibaba.fastjson.JSONArray json =
                com.alibaba.fastjson.JSONArray.parseArray(obj.getString("dataList"));

        return JSONObject.parseArray(json.toJSONString(), TracePaxInfoVo.class);
    }

    /**
     * Title：getPaxInfoByFlight <br>
     * Description：根据id查询旅客信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/22 11:35 <br>
     * @param
     * @return com.alibaba.fastjson.JSONArray
     */
    private TracePaxInfoVo getPaxInfoByFlight(String paxId) {
        ContentIdxForm form = new ContentIdxForm(paxId);
        JSONObject obj = (JSONObject) traceService.getPsgByIdx(form);
        com.alibaba.fastjson.JSONArray json =
                com.alibaba.fastjson.JSONArray.parseArray(obj.getString("dataList"));
        return JSONObject.parseArray(json.toJSONString(), TracePaxInfoVo.class).get(0);
    }

    /**
     * Title：getReturnVerftnVo <br>
     * Description： 封装验证返回参数对象<br>
     * author：傅欣荣 <br>
     * date：2020/3/22 11:56 <br>
     * @param
     * @return
     */
    private OverBookVerftnVo getReturnVerftnVo(FocFlightInfo flight, TracePaxInfoVo paxInfo) {
        OverBookVerftnVo verftnVo = new OverBookVerftnVo();
        verftnVo.setFlightId(flight.getFlightId());
        verftnVo.setFlightNo(flight.getFlightNo());
        verftnVo.setFlightDate(flight.getFlightDate().replaceAll("/", "-"));
        verftnVo.setAcType(flight.getAcType());
        verftnVo.setSta(flight.getSta());
        verftnVo.setStd(flight.getStd());
        verftnVo.setPaxId(paxInfo.getIdx());
        verftnVo.setPaxName(paxInfo.getPsgName());
        verftnVo.setSex(paxInfo.getGender());
        verftnVo.setBabyName(paxInfo.getInfantName());
        verftnVo.setIsInfant(paxInfo.getIsInfant());
        verftnVo.setIsChild(paxInfo.getIsChild());
        verftnVo.setIdType(paxInfo.getIdType());
        verftnVo.setTktNo(paxInfo.getEtNum());
        verftnVo.setIdNo(paxInfo.getIdNum());
        verftnVo.setSegment(paxInfo.getSegment());
        verftnVo.setDOrI(flight.getDOrI());
        return verftnVo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOverBookInfo(OverBookInfoDto overBookInfoDto) throws Exception {

        log.info("H5-【超售-保存接口】，前端传入参数：{}", overBookInfoDto.toString());
        // 旅客信息查询
        TracePaxInfoVo paxInfo = getPaxInfoByFlight(overBookInfoDto.getPaxId());
        Asserts.notNull(paxInfo, MessageCode.OVER_PAX_ID_QUERY_IS_NULL.getCode());
        // 验证赔偿金额
        try {
            if (overBookInfoDto.getIsCommit().equals(SAVE_SUBMIT)) {
                Asserts.isNotEmpty(overBookInfoDto.getPayMoney(),
                        MessageCode.OVER_PAY_MONEY_IS_NULL.getCode());
                if(TYPE_REBOOK.equals(overBookInfoDto.getType())) Asserts.isNotEmpty(overBookInfoDto.getPlaneDate(),
                        MessageCode.OVER_PLANEDATE_IS_NULL.getCode());
                if (Integer.valueOf(overBookInfoDto.getPayMoney()) < 1) {
                    throw new BusinessException(MessageCode.OVER_PAY_MONEY_IS_NULL.getCode());
                }
                inspectPayMoney(overBookInfoDto);
            }
        } catch (ScriptException e) {
            throw new ScriptException("H5保存超售数据-验证赔偿金额计算异常！");
        } catch (ParseException e2) {
            throw new ParseException("H5保存超售数据-验证赔偿金额-航班计划起飞时间转换异常", e2.getErrorOffset());
        }
        overBookInfoDto.setSegment(paxInfo.getSegment());
        // 查原航班详细信息
        FocFlightInfo focFlightInfo =
                flightInfoService.getFlightInfoById(overBookInfoDto.getFlightId());
        overBookInfoDto.setFlightDate(focFlightInfo.getFlightDate().replaceAll("/", "-"));
        overBookInfoDto.setOldPlaneDate(focFlightInfo.getStd());

        //查询航班原始航段
        FlightInfoListForm form1 = new FlightInfoListForm();
        form1.setFlightNum(focFlightInfo.getFlightNo());
        form1.setFlightDate(focFlightInfo.getFlightDate().replaceAll("-", "/"));
        OriginalSegmentView originalSegmentView = flightInfoService.getOriginalSegment(form1);

        // 草稿保存或提交 删除旧数据，保存最新数据
        String orderId = overBookInfoDto.getOrderId();
        String overId = overBookInfoDto.getOverId();
        String createUser = (String) AuthenticationUtil.getAuthentication().getPrincipal();

        if (ObjectUtils.isNotEmpty(overBookInfoDto.getOverId())) {
            delDraftInfo(overBookInfoDto.getOrderId());
        } else {
            orderId = handleServiceOrderId(PAY_TYPE_IRREGULAR);
            overId = handleServiceOrderId(PAY_TYPE_OVER);
        }
        Date createTime = new Date();// payType 不正常航班赔偿0、异常行李1、超售旅客2
        overBookInfoDto.setOrderId(orderId);
        overBookInfoDto.setOverId(overId);

        log.info("H5保存超售数据-[保存业务数据表]，orderId[{}],overId[{}],实体信息[{}]",orderId,overId,overBookInfoDto.toString());
        // 保存超售表信息
        saveOverInfo(createTime, createUser, overBookInfoDto, false);
        // 保存赔付标准
        saveCompensateInfo(createTime, createUser, overBookInfoDto);
        // 保存旅客信息
        savePaxInfo(createTime, createUser, orderId, focFlightInfo, overBookInfoDto, paxInfo);
        // 保存赔付单信息
        saveOrderInfo(createTime, createUser, focFlightInfo, overBookInfoDto);

        //以下数据保存航班原始航段。以上保存旅客航段
        overBookInfoDto.setSegment(originalSegmentView.getSegment());
        // 保存航延航班信息 原旅客航班信息
        saveCompensateFlightInfo(createTime, createUser, focFlightInfo, overBookInfoDto);



        if (overBookInfoDto.getIsCommit().equals(SAVE_SUBMIT)) {
            // 进入审核，默认进入二级审核
            return orderId;
        }
        return new String();

    }


    @Override
    public Map<String, Object> findOverBookConfig() {
        return getConfigInfoByType(false);
    }

    @Override
    public Map<String, Object> getConfigInfo() {
        return getConfigInfoByType(true);
    }

    @Override
    public QueryResults findOverDraftList(OverDraftListQueryDto overDraftListQueryDto) {
        return overInfoDaoImpl.findOverDraftList(overDraftListQueryDto);
    }

    @Override
    public OverBookDraftVo findOverDraftDetails(String overId) {
        Asserts.isNotEmpty(overId, MessageCode.PARAM_EXCEPTION.getCode(),new String[]{"超售id"});
        OverBookDraftVo vo = overInfoDaoImpl.findOverDraftDetails(overId);
        if(StringUtils.isNotEmpty(vo.getTelephone())){
            vo.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, vo.getTelephone()));
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delOverDraftInfo(String[] ids) {
        log.info("-----H5-【旅客超售草稿箱-删除】，请求参数：超售id{}",ids.toString());
        for (String id : ids) {
            OverInfo overInfo = overInfoDao.findTById(id);
            delDraftInfo(overInfo.getOrderId());
        }
    }

    /**
     * Title：delDraftInfo <br>
     * Description：根据服务单删除草稿信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/31 14:55 <br>
     * @param
     * @return
     */
    private void delDraftInfo(String orderId) {
        overInfoDaoImpl.deleteByOrderId(orderId);
        compensateInfoDaoImpl.deleteByOrderId(orderId);
        flightInfoDaoImpl.deleteByOrderId(orderId);
        orderInfoDaoImpl.deleteByOrderId(orderId);
        paxInfoDaoImpl.deleteByOrderId(orderId);

    }

    @Override
    public Map<String, Object> findOverDetails(String orderId) throws Exception {
        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"赔偿单id"});
        Map<String, Object> dataMap = new HashMap<>();
        // 赔偿单信息 类型 赔偿金额 客票价格 审核状态
        OverAndOrderInfo overAndOrderInfo = overInfoDaoImpl.findOverInfoAndOrder(orderId);
        dataMap.put("overBookInfo", overAndOrderInfo);
        //赔偿进度信息
        CompensationProgressVo cpVo = orderAuditService.queryCpsProgressQuery(orderId);
        dataMap.put("cpsProgressInfo", cpVo);

        // 乘机人信息
        PaxInfoVo paxInfoVo = overInfoDaoImpl.findPaxInfo(orderId);
        if(StringUtils.isNotEmpty(paxInfoVo.getTelephone())){
            paxInfoVo.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, paxInfoVo.getTelephone()));
        }
        dataMap.put("overPaxInfo", paxInfoVo);

        // 根据航班id 查航班信息
        FlightInfoVo flightInfoVo = overInfoDaoImpl.getFlightInfoVo(orderId);
        dataMap.put("flightInfo", flightInfoVo);
        if (overAndOrderInfo.getType().equals(TYPE_REBOOK)) {
            // 原航班信息 根据旅客id 查 航班信息
            OverInfo overInfo = overInfoDao.findTById(overAndOrderInfo.getOverId());
            // 改签航班信息 根据id查对象 ，航班信息 查航班详情
            FlightInfoVo flightInfoVo2 = new FlightInfoVo();
            BeanUtils.copyProperties(overInfo, flightInfoVo2);

            String timeDiff;
            // 拼接时间 ，计算时差
            String startTime = flightInfoVo.getFlightDate() + " "
                    + (flightInfoVo.getPlanDate() == null ? "00:00" : flightInfoVo.getPlanDate());
            String endTime = flightInfoVo2.getFlightDate() + " "
                    + (flightInfoVo2.getPlanDate() == null ? "00:00" : flightInfoVo2.getPlanDate());
            try {
                timeDiff = getHoursMin(startTime, endTime);
            } catch (ParseException e) {
                throw new ParseException(MessageCode.OVER_TIME_DIFFERENCE_ERROR.getCode(),
                        e.getErrorOffset());
            }
            flightInfoVo.setTimeDiff(timeDiff);
            dataMap.put("overBookFlight", flightInfoVo2);
        }
        return dataMap;
    }



    @Override
    public OverOrderDetailsVo findOrderDetails(String orderId) {
        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"赔偿单id"});

        // 赔偿单号 、 赔偿类型 、 航段 、申请人 、 申请时间 、 终审人 审核时间
        OverOrderDetailsVo overOrderDetailsVo = overInfoDaoImpl.getOrderAuditInfo(orderId);
        // 赔偿标准查询
        List<CompensateInfoVo> cList = compensateInfoDaoImpl.getCompensateInfoByOrderId(orderId);
        overOrderDetailsVo.setCompensateInfo(cList);
        return overOrderDetailsVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatus(OverOrderStatusSaveDto statusSaveDto) {
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        log.info("H5-【去发放请求参数：】{} ; userId:{}",statusSaveDto.toString(),userId);
        int num = 0;
        num = overInfoDaoImpl.updOrderStatus(statusSaveDto.getOrderIds(), statusSaveDto.getStatus(),userId);
        if(num < 1 ){
            throw new BusinessException(MessageCode.OVER_ORDER_GRANT_ERROR.getCode());
        }
        if(num > 0 && ORDER_STATUS_GRANT.equals(statusSaveDto.getStatus()) &&
                (!STATUS_IRREGULAR.equals(statusSaveDto.getPayType()))){
            log.info("H5-【去发放请求参数：】{},发起发放通知短信{}",statusSaveDto.toString());
            //发送短信
            flightCompensateService.sendMessageToPaxByPayType(statusSaveDto.getOrderIds(),statusSaveDto.getPayType());
        }
    }

    @Override
    public Map<String, Object> getRebookFlightInfo(String flightNo, String flightDate,
            String paxId) {

        Asserts.isNotEmpty(flightNo, MessageCode.PARAM_EXCEPTION.getCode(),new String[]{"航班号"});
        Asserts.isNotEmpty(flightDate, MessageCode.PARAM_EXCEPTION.getCode(),new String[]{"航班时间"});
        Asserts.isNotEmpty(paxId, MessageCode.PARAM_EXCEPTION.getCode(),new String[]{"旅客id"});

        // 旅客id查询旅客航班信息
        TracePaxInfoVo paxInfo = getPaxInfoByFlight(paxId);
        Map<String, Object> data = new HashMap<>();
        // 航班信息查改签航班
        FocFlightInfo flight = overInfoDaoImpl.getFlightInfo(flightNo,
                flightDate.replaceAll("-", "/"), paxInfo.getOrig(), paxInfo.getDest());
        if (StringUtils.isNotBlank(flight.getFlightNo())
                && StringUtils.isNotBlank(flight.getFlightDate())) {
            data.put("flightNo", flight.getFlightNo());
            data.put("flightDate", flight.getFlightDate().replaceAll("/", "-"));
            data.put("std", flight.getStd());
        }
        return data;
    }


    // ----------------分割 以下是私有的---------------------------------------------------

    /**
     * 验证前端传入赔偿金额，返回赔偿金额
     */
    public String inspectPayMoney(OverBookInfoDto overBookInfoDto)
            throws ScriptException, ParseException {
        Object payMoney = "0";
        if (TYPE_REBOOK.equals(overBookInfoDto.getType())) {

                // 根据规则判断赔偿金额 四舍五入取整
            payMoney = getRebookMoney(overBookInfoDto);
            if (String.valueOf(payMoney).indexOf(".") != -1) {
                payMoney = String.valueOf((int) Math.round((Double) payMoney));
            }
            // 最低赔付标准
            LowestStandInfo lowestStandInfo = overBookConfigDaoImpl.getLowestStandInfo(true);
            if (ObjectUtils.isNotEmpty(lowestStandInfo)
                    && Integer.parseInt(String.valueOf(payMoney)) < Integer.parseInt(lowestStandInfo.getConditionMoney())) {
                payMoney = lowestStandInfo.getPayMoney();
            }

            //成人携带婴儿
            if (StringUtils.isBlank(overBookInfoDto.getIsChild()) && StringUtils.isNotBlank(overBookInfoDto.getIsInfant())) {
                payMoney = new BigDecimal(String.valueOf(payMoney)).multiply(new BigDecimal("2")).stripTrailingZeros().toPlainString();
            }

        }

        if (TYPE_REFUND.equals(overBookInfoDto.getType())) {
            payMoney = getRefundMoney(overBookInfoDto);
            if(String.valueOf(payMoney).indexOf(".") != -1 ){
                payMoney =String.valueOf((int) Math.round((Double) payMoney));
            }
        }

        // 后台计算 比较 前端传入赔偿金额
        if (!(Integer.parseInt(overBookInfoDto.getPayMoney()) == Integer.parseInt(String.valueOf(payMoney)))) {
            log.info("赔偿金额计算有误，请核对！前端计算{}，后台计算{}", overBookInfoDto.getPayMoney(), payMoney);
            throw new BusinessException(MessageCode.OVER_PAY_MONEY_ATYPISM.getCode());
        }
        return String.valueOf(payMoney);
    }

    /**
     * 保存超售信息表
     */
    private void saveOverInfo(Date createTime, String createUser, OverBookInfoDto overBookInfoDto,
            Boolean isUpd) throws Exception {
        OverInfo overInfo = new OverInfo();
        overInfo.setId(overBookInfoDto.getOverId());
        overInfo.setOrderId(overBookInfoDto.getOrderId());
        overInfo.setType(overBookInfoDto.getType());
        overInfo.setFlightNo(overBookInfoDto.getOverBookFlightNo());
        overInfo.setFlightDate(overBookInfoDto.getOverBookFlightDate());
        overInfo.setPlanDate(overBookInfoDto.getPlaneDate());
        overInfo.setPrice(overBookInfoDto.getPrice());
        overInfo.setPriceSpread(overBookInfoDto.getPriceSpread());
        String imgUrlStr = "";
        if (StringUtils.isNotBlank(overBookInfoDto.getImgUrl())) {
            String[] imgUrls = overBookInfoDto.getImgUrl().split(",");
            String tempStr = "";
            for (String imgUrl : imgUrls) {
                tempStr = imgUrl;
                if(imgUrl.contains("/temp/")){
                    tempStr = uploadAndDownload.saveImg(imgUrl);
                }
                imgUrlStr += tempStr;
                imgUrlStr +=",";
            }
            overInfo.setAttachment(imgUrlStr.substring(0,imgUrlStr.length()-1));
        }
        if (!isUpd) {
            overInfo.setCreateUser(createUser);
            overInfo.setCreateTime(createTime);
        }
        overInfoDao.save(overInfo);

    }

    /**
     * 保存赔付标准
     */
    private void saveCompensateInfo(Date createTime, String createUser,
            OverBookInfoDto overBookInfoDto) {
        List<CompensateInfo> cList = new ArrayList<>();
        cList.add(getCompensateInfo(createTime, createUser, CLASS_TYPE_J, overBookInfoDto));
        cList.add(getCompensateInfo(createTime, createUser, CLASS_TYPE_G, overBookInfoDto));
        compensateInfoDao.saveAll(cList);
    }

    /**
     * 赔偿标准封装
     */
    private CompensateInfo getCompensateInfo(Date createTime, String createUser, String classType,
            OverBookInfoDto overBookInfoDto) {
        CompensateInfo compensateInfo = new CompensateInfo();
        String payMoney = "0";
        compensateInfo.setOrderId(overBookInfoDto.getOrderId());
        compensateInfo.setAccidentId(overBookInfoDto.getOverId());
        if (StringUtils.isNotBlank(overBookInfoDto.getPayMoney())) {
            payMoney = overBookInfoDto.getPayMoney();
        }
        if (TYPE_REFUND.equals(overBookInfoDto.getType()) && !"0".equals(payMoney) && !"0".equals(overBookInfoDto.getPriceSpread())
            && StringUtils.isNotBlank(payMoney) && StringUtils.isNotBlank(overBookInfoDto.getPriceSpread())) {
            // 根据比例计算赔偿金
            payMoney = String.valueOf(Integer.parseInt(payMoney) - Integer.parseInt(overBookInfoDto.getPriceSpread()));
            log.info("H5超售保存-退票赔偿标准= 赔偿金额 - 客票差价 ；计算结果：{}",payMoney);
        }

        compensateInfo.setCpsNum(Integer.parseInt(payMoney));
        // 1-经济舱，2-公务舱
        compensateInfo.setClassType(classType);
        // 婴儿 、儿童补偿标准默认
        compensateInfo.setBabyStd(100);
        compensateInfo.setChildStd(100);
        compensateInfo.setCreateId(createUser);
        compensateInfo.setCreateTime(createTime);
        return compensateInfo;
    }


    /**
     * 服务单保存
     */
    private void saveOrderInfo(Date createTime, String createUser, FocFlightInfo focFlightInfo,
            OverBookInfoDto overBookInfoDto) {

        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setPayType(Integer.valueOf(PAY_TYPE_OVER));// 赔偿类型 不正常航班赔偿0、异常行李1、超售旅客2、
        orderInfo.setOrderId(overBookInfoDto.getOrderId());
        orderInfo.setAccidentId(overBookInfoDto.getOverId());
        orderInfo.setFlightId(focFlightInfo.getFlightId());
        orderInfo.setFlightNo(focFlightInfo.getFlightNo());
        orderInfo.setFlightDate(overBookInfoDto.getFlightDate());
        orderInfo.setCreateTime(createTime);
        orderInfo.setCreateId(createUser);
        orderInfo.setStatus(ORDER_STATUS_DRAFT);// 草稿
        orderInfo.setServiceCity(overBookInfoDto.getServiceCity()); // 服务航站
        String payMoney = "0";
        if (StringUtils.isNotBlank(overBookInfoDto.getPayMoney())) {
            payMoney = overBookInfoDto.getPayMoney();
        }
        orderInfo.setSumMoney(Integer.parseInt(payMoney));
        orderInfo.setChoiceSegment(overBookInfoDto.getSegment().replace(" ", ""));
        orderInfoDao.save(orderInfo);

    }

    /**
     * 保存航班信息
     */
    private void saveCompensateFlightInfo(Date createTime, String createUser,
            FocFlightInfo focFlightInfo, OverBookInfoDto overBookInfoDto) {

        FlightInfo flightInfo = new FlightInfo();
        flightInfo.setOrderId(overBookInfoDto.getOrderId());
        flightInfo.setFlightNo(focFlightInfo.getFlightNo());
        flightInfo.setFlightDate(overBookInfoDto.getFlightDate());
        flightInfo.setSegment(overBookInfoDto.getSegment().replace(" ", ""));
        flightInfo.setCreateTime(createTime);
        flightInfo.setCreateId(createUser);
        flightInfo.setFlightId(focFlightInfo.getFlightId());
        flightInfo.setStd(overBookInfoDto.getOldPlaneDate());
        flightInfo.setSta(focFlightInfo.getSta());
        flightInfo.setAcType(focFlightInfo.getAcType());
        flightInfo.setDOrI(focFlightInfo.getDOrI());
        flightInfoDao.save(flightInfo);
    }

    /**
     * 保存旅客信息
     */
    private void savePaxInfo(Date createTime, String createUser, String orderId,
            FocFlightInfo focFlightInfo, OverBookInfoDto overBookInfoDto, TracePaxInfoVo tPaxInfo) {

        PaxInfo paxInfo = new PaxInfo();
        paxInfo.setOrderId(orderId);
        paxInfo.setPaxId(overBookInfoDto.getPaxId());
        String phone = AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_PHONE_KEY, overBookInfoDto.getTelephone());
        paxInfo.setTelephone(AesEncryptUtil.aesEncryptScgsi(phone));

        paxInfo.setPaxName(tPaxInfo.getPsgName());
        paxInfo.setIdNo(AesEncryptUtil.aesEncryptScgsi(tPaxInfo.getIdNum()));
        paxInfo.setIdType(tPaxInfo.getIdType());
        paxInfo.setSex(tPaxInfo.getGender());
        paxInfo.setSegment(tPaxInfo.getSegment().replace(" ", ""));
        paxInfo.setPaxStatus(tPaxInfo.getStatus());
        paxInfo.setTktNo(tPaxInfo.getEtNum());
        paxInfo.setBabyName(tPaxInfo.getInfantName());
        paxInfo.setPkgNo(tPaxInfo.getBagTag());
        paxInfo.setPkgWeight(tPaxInfo.getBagWht());
        paxInfo.setIsChild(tPaxInfo.getIsChild());
        paxInfo.setWithBaby(tPaxInfo.getIsInfant());
        paxInfo.setPnr(tPaxInfo.getPnr());
        if (ObjectUtils.isNotEmpty(tPaxInfo.getPrintTicketTime())) {
            paxInfo.setTktDate(tPaxInfo.getPrintTicketTime());
        }
        paxInfo.setOrgCityAirp(tPaxInfo.getOrig());
        paxInfo.setDstCityAirp(tPaxInfo.getDest());
        paxInfo.setReceiveStatus(0);
        String payMoney = "0";
        if (StringUtils.isNotBlank(overBookInfoDto.getPayMoney())) {
            payMoney = overBookInfoDto.getPayMoney();

        }
        paxInfo.setCurrentAmount(Integer.parseInt(payMoney));
        paxInfo.setIsFlag(0);
        paxInfo.setSwitchOff(0);
        paxInfo.setCreateId(createUser);
        paxInfo.setCreateTime(createTime);
        paxInfoDao.save(paxInfo);
    }


    private Object getRefundMoney(OverBookInfoDto overBookInfoDto) throws ScriptException {
        String payMoney = "0";
        RefundInfoVo refundInfo = overBookConfigDaoImpl.getRefundInfo(true);

        if ("D".equals(overBookInfoDto.getDOrI())) {
            payMoney = refundInfo.getDMoney();

        } else if ("I".equals(overBookInfoDto.getDOrI())) {
            payMoney = refundInfo.getIMoney();
        }

        // 是否最低赔付
        if (PAY_TYPE_LOWEST.equals(refundInfo.getPayType())
                && (int) Math.round(Double.valueOf(payMoney)) < (int) Math.round(Double.valueOf(refundInfo.getConditionMoney()))) {
            payMoney = refundInfo.getPayMoney();
        }

        //成人携带婴儿
        if (StringUtils.isBlank(overBookInfoDto.getIsChild()) && StringUtils.isNotBlank(overBookInfoDto.getIsInfant())) {
            payMoney =new BigDecimal(payMoney).multiply(new BigDecimal("2")).stripTrailingZeros().toPlainString();
        }
        // 总赔偿金额 = 赔偿金额+ 客票差价
        String priceSpread = StringUtils.isBlank(overBookInfoDto.getPriceSpread()) ? "0"
                : overBookInfoDto.getPriceSpread();
        //int sumPayMoney = Integer.parseInt(payMoney) + Integer.parseInt(priceSpread);

        // 根据比例计算赔偿金
        String ruleStr = payMoney + "+" + priceSpread;
        return getdamagePart(ruleStr);
    }

    /**
     * 改签赔偿金额计算
     */
    private Object getRebookMoney(OverBookInfoDto overBookInfoDto)
            throws ScriptException, ParseException {
        String result = "";
        Object money;
        List<TimeDifferenceInfoVo> configList = new ArrayList<>();
        StringBuffer str = new StringBuffer();
        List<TimeDifferenceInfoVo> bookList =
                overBookConfigDaoImpl.getTimeDifferenceInfo(TYPE_REBOOK);

        // 拼接时间
        String startTime =
                overBookInfoDto.getFlightDate() + " " + overBookInfoDto.getOldPlaneDate();
        String endTime =
                overBookInfoDto.getOverBookFlightDate() + " " + overBookInfoDto.getPlaneDate();
        // 计算时差
        String time = getHours(startTime, endTime);
        // 规则成立并返回规则比例值
        for (TimeDifferenceInfoVo tdi : bookList) {
            if(StringUtils.isBlank(tdi.getOverWeightTktno()) || StringUtils.isBlank(tdi.getConditionValue())
                    || StringUtils.isBlank(time)) {
                continue;
            }
            String overWeightTktno = "=".equals(tdi.getOverWeightTktno().trim())?"==":tdi.getOverWeightTktno().trim();
            str.setLength(0);
            str.append(time);
            str.append(overWeightTktno);
            str.append(tdi.getConditionValue());
            if ((Boolean) getdamagePart(str.toString())) {
                configList.add(tdi);
                continue;
            }
        }
        if (configList.size() < 1) {
            log.info("改签航班时差{}，查无对应规则！请联系管理员配置！", time);
            throw new BusinessException(MessageCode.OVER_TIME_DIFFERENCE_RULE_IS_NULL.getCode());
        }

        //区分国内国际
        if ("D".equals(overBookInfoDto.getDOrI())) {
            result = configList.get(0).getDMoney();
            //若满足两个以上条件、默认取第一条  ，第一条是> 取最后一条 <取第一条
            if (configList.get(0).getOverWeightTktno().indexOf("<") != -1) {
                result = configList.get(0).getDMoney();
            }
            if (configList.get(0).getOverWeightTktno().indexOf(">") != -1) {
                result = configList.get(configList.size() - 1).getDMoney();
            }

        } else if ("I".equals(overBookInfoDto.getDOrI())) {
            result = configList.get(0).getIMoney();
            //若满足两个以上条件、默认取第一条  ，第一条是> 取最后一条 <取第一条
            if (configList.get(0).getOverWeightTktno().indexOf("<") != -1) {
                result = configList.get(0).getIMoney();
            }
            if (configList.get(0).getOverWeightTktno().indexOf(">") != -1) {
                result = configList.get(configList.size() - 1).getIMoney();
            }
        }

        return result;
    }

    /**
     * 得到两个时间的时间差
     */
    private String getHours(String startTime, String endTime) throws ParseException {
        Date time = df.parse(startTime);
        Date time2 = df.parse(endTime);
        long timeout = time2.getTime() - time.getTime();
        double quot = 0.00;
        quot = ((double) timeout) / (1000 * 60 * 60);
        DecimalFormat formater = new DecimalFormat();
        formater.setMaximumFractionDigits(2);
        formater.setGroupingSize(0);
        formater.setRoundingMode(RoundingMode.FLOOR);
        return formater.format(quot);
    }

    /**
     * 得到两个时间的时间差 天 小时 分
     */
    private String getHoursMin(String startTime, String endTime) throws ParseException {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        Date time = df.parse(startTime);
        Date time2 = df.parse(endTime);
        long diff = time2.getTime() - time.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        System.out.println("" + day + "day" + hour + "hour" + min + "min");
        StringBuffer strTime = new StringBuffer();
        if (day != 0) {
            strTime.append(day).append("day ");
        }
        return strTime.append(hour).append("h ").append(min).append("m").toString();
    }

    /**
     * 传入表达式返回计算结果
     */
    private Object getdamagePart(String ruleStr) throws ScriptException {
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("js");
        Object result = engine.eval(ruleStr);
        log.info("方法：表达式字符串计算 ，计算结果类型{}，计算结果{} ", result.getClass().getName(), result);
        return result;
    }

    /**
     * Title：handleServiceOrderId <br>
     * Description：处理服务单号 18位（年月日时分+6位序列号 ）最后一位区分单子类型<br>
     * author：王建文 <br>
     * date：2020-3-3 16:17 <br>
     *
     * @param payType 不正常航班赔偿0、异常行李1、超售旅客2
     * @return String
     */
    private String handleServiceOrderId(String payType) {
        // 精确时间到分12位
        String orderId = DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMM);
        // 随机5位数
        int random5 = (int) ((Math.random() * 9 + 1) * 10000);
        orderId += String.valueOf(random5);
        // 加上赔偿类型
        orderId += payType;
        return orderId;
    }

    // H5 WEB 查询超售配置信息
    /**
     * Title：getConfigInfoByType <br>
     * Description： 查询超售配置（web 、 h5）<br>
     * author：傅欣荣 <br>
     * date：2020/3/23 10:43 <br>
     * @param  isLowest true 只查询当前配置有应执行的规则、 false 所有规则包含关闭的
     * @return
     */
    private Map<String, Object> getConfigInfoByType(boolean isLowest) {

        Map<String, Object> dataMap = new HashMap<>();
        // 查询改签比例
        // 查询改签最低赔付
        // 退票比例 最低赔付
        dataMap.put("timeDifferenceInfo", overBookConfigDaoImpl.getTimeDifferenceInfo(TYPE_REBOOK));
        dataMap.put("lowestStandInfo", overBookConfigDaoImpl.getLowestStandInfo(isLowest));
        dataMap.put("refundInfo", overBookConfigDaoImpl.getRefundInfo(isLowest));
        return dataMap;
    }
}
