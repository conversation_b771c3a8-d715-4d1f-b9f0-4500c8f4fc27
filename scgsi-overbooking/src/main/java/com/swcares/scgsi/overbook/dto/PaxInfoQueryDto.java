package com.swcares.scgsi.overbook.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.dto <br>
 * Description：H5获取赔偿单旅客列表<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月30日 11:01 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "旅客赔偿-赔偿单旅客列表查询")
public class PaxInfoQueryDto {

    /**
     * 服务单id
     */
    @ApiModelProperty(value = " 赔偿单id")
    private String orderId;

    /**
     * 模糊搜索 旅客姓名
     */
    @ApiModelProperty(value = "模糊搜索")
    private String keySearch;
    /**
     * 航段
     */
    @ApiModelProperty(value = "航段")
    private String segment;

    /**
     * 领取状态 空全部 0未领取,1已领取 2领取中
     */
    @ApiModelProperty(value = "领取状态")
    private String receiveStatus;
    /**
     * 冻结状态 旅客申领资格开关(默认0有资格，1取消领取资格)
     */
    @ApiModelProperty(value = "冻结状态")
    private String isSwitch;

    /** 当前页数，默认为第一页 **/
    private int current = 1;

    /** 每页显示记录数，默认为10条 **/
    private int pageSize = 10;
}
