package com.swcares.scgsi.overbook.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.vo <br>
 * Description：超售事故单-旅客验证 -查询返回对象 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月15日 13:38 <br>
 * @version v1.0 <br>
 */
@Data
@EncryptionClassz
public class OverBookVerftnVo {

    /**
     * 航班id
     */
    private String flightId;

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班日期
     */
    private String flightDate;

    /**
     * 机型
     */
    private String acType;

    /**
     * 航段
     */
    private String segment;
    /**
     * 计划起飞
     */
    private String std;
    /**
     * 计划到达
     */
    private String sta;

    /**
     * 旅客id
     */
    private String paxId;

    /**
     * 旅客性别C儿童M男F女
     */
    private String sex;

    /**
     * 婴儿姓名不为空代表携带婴儿
     */
    private String babyName;
    /**
     * 是否婴儿
     */
    private String isInfant;
    /**
     * 是否是儿童	Y-是
     */
    private String isChild;

    /**
     * 旅客姓名
     */
    private String paxName;

    /**
     * 票号
     */
    private String tktNo;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号
     */
    @Encryption
    private String idNo;

    /**
     * 国内国际
     */
    private String dOrI;

}
