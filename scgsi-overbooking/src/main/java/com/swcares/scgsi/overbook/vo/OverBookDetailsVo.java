package com.swcares.scgsi.overbook.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.vo <br>
 * Description：超售列表 - 数据详情展示 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月19日 10:01 <br>
 * @version v1.0 <br>
 */
@Data
@EncryptionClassz
public class OverBookDetailsVo {

    /**
     * 超售单号
     */
    private String overId;

    /**
     * 赔偿单号
     */
    private String orderId;

    /**
     * 领取状态
     */
    private String receiveStatus;

    /**
     * 总金额
     */
    private String payMoney;

    /**
     * 赔偿金额
     */
    private String sumMoney;

    /**
     * 客票差价 退票
     */
    private String priceSpread;

    /**
     * 超售类型
     */
    private String type;


    /**
     * imgUrl
     */
    private String imgUrl;

    //------旅客信息-------

    /**
     * 旅客姓名
     */
    private String paxName;

    /**
     * 票号
     */
    private String tktNo;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号
     */
    @Encryption
    private String idNo;

    /**
     * 乘机人手机号
     */
    @Encryption
    private String telephone;


    //-------原航班信息-------
    /**
     * 原航班号
     */
    private String flightNo;

    /**
     * 原航班日期
     */
    private String flightDate;
    /**
     * 航段
     */
    private String segment;

    /**
     * 旅客原航班票价（不含机建燃油费）
     */
    private String price;

    /**
     * 原航班计划起飞时间
     */
    private String oldPlaneDate;

    //-------改签航班信息 四要素关联查-------
    /**
     * 改签航班号
     */
    private String overBookFlightNo;

    /**
     * 改签航班日期
     */
    private String overBookFlightDate;

    /**
     * 改签航班的计划起飞时间
     */
    private String planeDate;

    /**
     * 客票差价
     */
    private String overBookPrice;


}
