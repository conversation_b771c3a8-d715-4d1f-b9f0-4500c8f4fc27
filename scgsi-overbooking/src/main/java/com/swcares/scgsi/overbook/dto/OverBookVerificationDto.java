package com.swcares.scgsi.overbook.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.dto <br>
 * Description：超售事故单-旅客验证 -H5 前端请求参数 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月15日 13:31 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "H5超售-新建旅客验证")
public class OverBookVerificationDto {

    /**
     * 航班号
     */
    @ApiModelProperty(value = "航班号", required = true)
    private String flightNo;

    /**
     * 航班日期
     */
    @ApiModelProperty(value = "航班日期", required = true)
    private String flightDate;

    /**
     *  证件号、 行李号、票号
     */
    @ApiModelProperty(value = "证件号、 行李号、票号", required = true)
    private String keySearch;


    /**
     * 服务航站 三字码
     */
    @ApiModelProperty(value = "服务航站")
    private String serviceCity;
}
