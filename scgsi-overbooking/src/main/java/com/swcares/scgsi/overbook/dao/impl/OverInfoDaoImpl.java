package com.swcares.scgsi.overbook.dao.impl;


import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.entity.FocFlightInfo;
import com.swcares.scgsi.overbook.dto.OverBookListQueryDto;
import com.swcares.scgsi.overbook.dto.OverBookQueryDto;
import com.swcares.scgsi.overbook.dto.OverDraftListQueryDto;
import com.swcares.scgsi.overbook.vo.*;
import com.swcares.scgsi.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.audit.dao.impl <br>
 * Description：超售dao实现 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月19日 10:32 <br>
 * @version v1.0 <br>
 */
@Repository
public class OverInfoDaoImpl{

    @Resource
    BaseDAO baseDAO;

    //全部
    private static final String DEFAULT = "0";
    //本人发起
    private static final String USER_LAUNCH = "1";
    //草稿
    private static final String OVER_DRAFT="2";


    /**
     * Title：deleteByOrderId <br>
     * Description： 根据服务单id删除信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/26 21:07 <br>
     * @param
     * @return
     */
    public void deleteByOrderId(String orderId){
        StringBuffer sql = new StringBuffer();
        sql.append("DELETE DP_OVER_INFO WHERE ORDER_ID = ? ");
        baseDAO.batchUpdate(sql.toString(),orderId);
    }

    /**
     * Title：getOrderAuditInfo <br>
     * Description：获取赔偿单及审核信息 审核人+审核时间<br>
     * author：傅欣荣 <br>
     * date：2020/4/5 17:25 <br>
     * @param  orderId
     * @return com.swcares.scgsi.overBook.vo.OverOrderDetailsVo
     */
    public OverOrderDetailsVo getOrderAuditInfo(String orderId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT OI.ORDER_ID,OI.PAY_TYPE ,OI.CHOICE_SEGMENT ,");
        sql.append(" OI.CREATE_ID,OI.CLOSE_TIME,");
        sql.append(" OT.AUDITOR ,OT.AUDIT_TIME ");
        sql.append(" FROM DP_ORDER_INFO OI");
        sql.append(" LEFT JOIN DP_ACT_MIDDLE AM ON OI.ORDER_ID = AM.ORDER_ID");
        sql.append(" LEFT JOIN DP_ORDER_AUDIT OT ON OT.ID = AM.RECORD_ID");
        sql.append(" WHERE OI.ORDER_ID =:orderId ");
        paramsMap.put("orderId",orderId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,OverOrderDetailsVo.class);
    }


    /**
     * Title：findOverInfoAndOrder <br>
     * Description： 查超售信息 + 赔偿单信息 -[H5超售单详情]<br>
     * author：傅欣荣 <br>
     * date：2020/4/5 17:25 <br>
     * @param  orderId 超售id
     * @return com.swcares.scgsi.overBook.vo.OverAndOrderInfo
     */
    public OverAndOrderInfo findOverInfoAndOrder(String orderId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT O.ID overId,O.ORDER_ID orderId,");//,null as dept
        sql.append(" OI.STATUS status,O.TYPE type,O.ATTACHMENT imgUrl,");
        sql.append(" NVL(OI.SUM_MONEY,0) sumMoney,(nvl(OI.SUM_MONEY,0) -nvl(O.PRICE_SPREAD,0)) payMoney,");
        sql.append(" NVL(O.PRICE_SPREAD,0) priceSpread , OI.CREATE_ID createUser");
        sql.append(" FROM DP_OVER_INFO O");
        sql.append(" LEFT JOIN DP_ORDER_INFO OI ON O.ORDER_ID = OI.ORDER_ID");
        sql.append(" WHERE OI.ORDER_ID = :orderId");
        paramsMap.put("orderId",orderId);

        return baseDAO.findOneBySql(sql.toString(),paramsMap,OverAndOrderInfo.class);
    }

    /**
     * Title：findPaxInfo <br>
     * Description：超售id查旅客信息-[超售详情-乘机人信息]<br>
     * author：傅欣荣 <br>
     * date：2020/4/5 17:24 <br>
     * @param  orderId
     * @return com.swcares.scgsi.overBook.vo.PaxInfoVo
     */
    public PaxInfoVo findPaxInfo(String orderId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT ");
        sql.append(" PI.PAX_ID paxId,PI.PAX_NAME paxName,PI.ID_NO idNo,PI.ID_TYPE idType,PI.TKT_NO tktNo,");
        sql.append(" PI.TELEPHONE telephone,PI.SEX sex,PI.WITH_BABY AS isInfant,PI.IS_CHILD isChild,PI.BABY_PAX_NAME babyName ");
        sql.append(" FROM DP_OVER_INFO O ");
        sql.append(" LEFT JOIN DP_PAX_INFO PI ON PI.ORDER_ID = O.ORDER_ID ");
        sql.append(" WHERE O.ORDER_ID = :orderId ");
        paramsMap.put("orderId",orderId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,PaxInfoVo.class);
    }

    /**
     * Title：findOverBookList <br>
     * Description：h5-超售列表查询<br>
     * author：傅欣荣 <br>
     * date：2020/4/5 17:23 <br>
     * @param  overBookQueryDto, createUser
     * @return com.swcares.scgsi.base.QueryResults
     */
    public QueryResults findOverBookList(OverBookQueryDto overBookQueryDto, String createUser) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT");
        sql.append(" O.ID overId ,O.ORDER_ID orderId,O.TYPE type,");
        sql.append(" OI.FLIGHT_NO flightNo,OI.FLIGHT_DATE flightDate,OI.SERVICE_CITY serviceCity,");
        sql.append(" OI.STATUS orderStatus ,PI.SEGMENT segment,PI.PAX_ID paxId,PI.PAX_NAME paxName,");
        sql.append(" PI.RECEIVE_STATUS receiveStatus");
        sql.append(" FROM DP_OVER_INFO O");
        sql.append(" LEFT JOIN DP_ORDER_INFO oi on o.ORDER_ID = OI.ORDER_ID");
        sql.append(" LEFT JOIN DP_PAX_INFO pi on o.ORDER_ID = pi.order_id");
        sql.append(" WHERE 1=1  AND OI.STATUS <>0 ");

        if(StringUtils.isNotBlank(overBookQueryDto.getServiceCity())){
            sql.append(" AND OI.SERVICE_CITY = :serviceCity ");
            paramsMap.put("serviceCity", overBookQueryDto.getServiceCity());
        }
        if (StringUtils.isNotBlank(overBookQueryDto.getDataType())) {
            sql.append(" AND O.CREATE_USER = :createUser");
            paramsMap.put("createUser", createUser);
        }
        String flightNo = overBookQueryDto.getFlightNo();
        if (StringUtils.isNotBlank(flightNo)) {
            paramsMap.put("flightNo", flightNo);
            sql.append(" AND OI.FLIGHT_NO= :flightNo ");
        }
        String startDate = overBookQueryDto.getStartDate();
        String endDate = overBookQueryDto.getEndDate();
        if (StringUtils.isNotBlank(endDate) && StringUtils.isBlank(startDate)) {
            paramsMap.put("endDate", endDate);
            sql.append(" AND OI.FLIGHT_DATE <= :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
            endDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND OI.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND OI.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }

        String keySearch = overBookQueryDto.getKeySearch();
        if (StringUtils.isNotBlank(keySearch)) {
            paramsMap.put("keySearch", "%" + keySearch + "%");
            sql.append(" AND (PI.PAX_NAME LIKE:keySearch OR O.ID LIKE:keySearch) ");
        }

        String type = overBookQueryDto.getType();
        if (StringUtils.isNotBlank(type)) {
            sql.append(" AND (O.TYPE ");
            sql.append(" in (SELECT REGEXP_SUBSTR('"+type+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+type+"', '[^,]+', 1, level) is not null))");

        }

        String receiveStatus = overBookQueryDto.getReceiveStatus();
        if (StringUtils.isNotBlank(receiveStatus)) {
            sql.append(" AND (PI.RECEIVE_STATUS  ");
            sql.append(" in (SELECT REGEXP_SUBSTR('"+receiveStatus+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+receiveStatus+"', '[^,]+', 1, level) is not null))");
        }
        String orderStatus = overBookQueryDto.getOrderStatus();
        if (StringUtils.isNotBlank(orderStatus)) {
            sql.append(" AND (OI.STATUS  ");
            sql.append(" in (SELECT REGEXP_SUBSTR('"+orderStatus+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+orderStatus+"', '[^,]+', 1, level) is not null))");
        }
        sql.append(" ORDER BY o.CREATE_TIME DESC ");

        return baseDAO.findBySQLPage_comm(sql.toString(),
                overBookQueryDto.getCurrent(),
                overBookQueryDto.getPageSize(),
                paramsMap, OverBookInfoVo.class);

    }

    /**
     * Title：findOverDraftList <br>
     * Description： 草稿箱列表-查询<br>
     * author：傅欣荣 <br>
     * date：2020/4/5 17:23 <br>
     * @param  overDraftListQueryDto
     * @return com.swcares.scgsi.base.QueryResults
     */
    public QueryResults findOverDraftList(OverDraftListQueryDto overDraftListQueryDto) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("  SELECT i.ID overId ,i.TYPE type,oi.FLIGHT_NO flightNo ,");
        sql.append("  oi.FLIGHT_DATE flightDate,PI.PAX_NAME paxName,PI.SEGMENT segment ");
        sql.append("  FROM DP_OVER_INFO i ");
        sql.append("  LEFT JOIN DP_ORDER_INFO oi on i.ORDER_ID = OI.ORDER_ID ");
        sql.append("  LEFT JOIN DP_PAX_INFO PI ON i.ORDER_ID = PI.ORDER_ID ");
        sql.append("  WHERE OI.STATUS = 0 AND oi.SERVICE_CITY = :serviceCity ");
        sql.append("   ORDER BY i.CREATE_TIME DESC ");
        paramsMap.put("serviceCity",overDraftListQueryDto.getServiceCity());
        return baseDAO.findBySQLPage_comm(sql.toString(),
                overDraftListQueryDto.getCurrent(),
                overDraftListQueryDto.getPageSize(),
                paramsMap,OverBookDraftDetailsVo.class);
    }

    /**
     * Title：findOverDraftDetails <br>
     * Description： 草稿箱列表数据-详情数据<br>
     * author：傅欣荣 <br>
     * date：2020/4/5 17:22 <br>
     * @param  overId
     * @return com.swcares.scgsi.overBook.vo.OverBookDraftVo
     */
    public OverBookDraftVo findOverDraftDetails(String overId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        //根据事故单查详情 包含 航班信息 旅客信息 改签航班信息
        sql.append(" SELECT DISTINCT O.ID overId ,O.ORDER_ID orderId ,O.TYPE type ,O.PRICE price ,");
        sql.append(" O.FLIGHT_NO overBookFlightNo,O.FLIGHT_DATE overBookFlightDate,O.PLAN_DATE planeDate,");
        sql.append(" O.PRICE_SPREAD priceSpread,O.ATTACHMENT imgUrl,PI.CURRENT_AMOUNT payMoney,");
        sql.append(" PI.PAX_ID paxId,PI.PAX_NAME paxName ,PI.ID_TYPE idType ,PI.ID_NO idNo,");
        sql.append(" pi.TELEPHONE telephone,PI.TKT_NO tktNo,");
        sql.append(" PI.SEX sex,PI.WITH_BABY AS isInfant,PI.IS_CHILD isChild,PI.BABY_PAX_NAME babyName ,");
        sql.append(" T.FLIGHT_ID flightId,T.FLIGHT_NO flightNo, T.FLIGHT_DATE flightDate,");
        sql.append(" T.SEGMENT segment ,T.STD std,T.STA sta,T.AC_TYPE acType,T.D_OR_I dOrI ");
        sql.append(" FROM DP_OVER_INFO O ");
        sql.append(" LEFT JOIN DP_ORDER_INFO OI ON O.ORDER_ID = OI.ORDER_ID");
        sql.append(" LEFT JOIN DP_FLIGHT_INFO T ON OI.ORDER_ID = T.ORDER_ID");
        sql.append(" LEFT JOIN DP_PAX_INFO PI ON o.ORDER_ID = PI.ORDER_ID");
        sql.append(" WHERE O.ID = :overId ");
        paramsMap.put("overId", overId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,OverBookDraftVo.class);
    }


    /**
     * Title：updOrderStatus <br>
     * Description： 更新赔偿单-状态 3发放-4关闭 ,只能由发起人发放<br>
     * author：傅欣荣 <br>
     * date：2020/4/5 17:21 <br>
     * @param  orderId, status
     * @return void
     */
    public int updOrderStatus(String orderId ,String status,String userId){
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE DP_ORDER_INFO SET STATUS=? ,ISS_USER = ? WHERE ORDER_ID = ? and CREATE_ID = ? ");
        return baseDAO.batchUpdate(sql.toString(),status,userId,orderId,userId);
    }

    /**
     * Title：getFlightInfoVo <br>
     * Description： 根据超售单号-查询原航班 + 改签航班<br>
     * author：傅欣荣 <br>
     * date：2020/4/5 17:21 <br>
     * @param  orderId 超售单号
     * @return com.swcares.scgsi.overBook.vo.FlightInfoVo
     */
    public FlightInfoVo getFlightInfoVo(String orderId){

        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT ");
        sql.append(" OI.FLIGHT_NO flightNo,OI.FLIGHT_DATE flightDate,");
        sql.append(" OI.CHOICE_SEGMENT segment,o.PRICE price,FTI.FLIGHT_ID flightId,FTI.STD planDate");
        sql.append(" FROM DP_OVER_INFO O");
        sql.append(" LEFT JOIN DP_ORDER_INFO OI ON O.ORDER_ID = OI.ORDER_ID");
        sql.append(" LEFT JOIN DP_FLIGHT_INFO FTI ON  OI.ORDER_ID = FTI.ORDER_ID");
        sql.append(" WHERE O.ORDER_ID = :orderId");
        paramsMap.put("orderId",orderId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,FlightInfoVo.class);
    }

    /**
     * Title：getFlightInfo <br>
     * Description：根据航班四要素查航班信息<br>
     * author：傅欣荣 <br>
     * date：2020/4/5 17:20 <br>
     * @param  flightNum, flightDate, orig, dest
     * @return com.swcares.scgsi.entity.FocFlightInfo
     */
    public FocFlightInfo getFlightInfo(String flightNum,String flightDate ,String orig,String dest){

        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT t.* FROM FOC_FLIGHT_INFO t");
        sql.append(" LEFT JOIN CITY_CODE C1 ON  T.POD = C1.AIRPORT_4CODE");
        sql.append(" LEFT JOIN CITY_CODE C2 ON  T.POA = C2.AIRPORT_4CODE");
        sql.append(" WHERE 1 = 1 and t.FLIGHT_NO = :flightNum");
        sql.append(" AND t.FLIGHT_DATE = :flightDate");
        sql.append(" AND c1.AIRPORT_3CODE = :orig ");
        sql.append(" AND c2.AIRPORT_3CODE = :dest ");
        paramsMap.put("flightNum",flightNum);
        paramsMap.put("flightDate",flightDate);
        paramsMap.put("orig",orig);
        paramsMap.put("dest",dest);
        List<FocFlightInfo> data = (List<FocFlightInfo>) baseDAO.findBySQL_comm(sql.toString(),paramsMap,FocFlightInfo.class);
        if(data.size() > 0){
            return data.get(0);
        }
        return new FocFlightInfo();
    }




    /**
     * Title：findOverBookWebList <br>
     * Description：WEB后台-超售列表查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/22 17:00 <br>
     * @param
     * @return
     */
    public QueryResults findOverBookWebList(OverBookListQueryDto overBookDto, String userId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT o.ID overId,O.ORDER_ID orderId,OI.FLIGHT_NO flightNo, OI.FLIGHT_DATE flightDate,");
        sql.append("  PI.RECEIVE_STATUS receiveStatus, o.TYPE type,  OI.STATUS status ,");
        sql.append(" OI.CHOICE_SEGMENT segment,PI.PAX_NAME paxName,PI.TKT_NO tktNo,");
        sql.append(" NVL(O.PRICE_SPREAD,0) priceSpread,NVL(OI.SUM_MONEY,0) sumMoney,");
        sql.append(" (nvl(OI.SUM_MONEY,0) -nvl(O.PRICE_SPREAD,0)) payMoney,");
        sql.append(" OI.SERVICE_CITY serviceCity,FT.STD oldPlanDate,O.FLIGHT_NO overBookFlightNo,");
        sql.append(" O.FLIGHT_DATE overBookFlightDate,O.PLAN_DATE planDate,get_user_name(O.CREATE_USER) createUser,");
        sql.append(" get_user_name(OI.ISS_USER) grantUser,get_user_name(OT.AUDITOR) auditor ");
        sql.append(" FROM DP_OVER_INFO O");
        sql.append(" LEFT JOIN DP_ORDER_INFO OI ON O.ORDER_ID = OI.ORDER_ID");
        sql.append(" LEFT JOIN DP_PAX_INFO PI ON o.ORDER_ID = PI.ORDER_ID");
        sql.append(" LEFT JOIN DP_ACT_MIDDLE AM ON O.ORDER_ID = AM.ORDER_ID");
        sql.append(" LEFT JOIN DP_ORDER_AUDIT OT ON AM.RECORD_ID = OT.ID");
        sql.append(" LEFT JOIN DP_FLIGHT_INFO FT ON OI.ORDER_ID = FT.ORDER_ID");
        sql.append(" WHERE 1=1");

        String flightNo = overBookDto.getFlightNo();
        if (StringUtils.isNotBlank(flightNo)) {
            paramsMap.put("flightNo", flightNo);
            sql.append(" AND OI.FLIGHT_NO= :flightNo ");
        }
        String startDate = overBookDto.getStartDate();
        String endDate = overBookDto.getEndDate();
        if (StringUtils.isNotBlank(endDate) && StringUtils.isBlank(startDate)) {
            paramsMap.put("endDate", endDate);
            sql.append(" AND OI.FLIGHT_DATE <= :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
            endDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND OI.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND OI.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }

        if(StringUtils.isNotBlank(overBookDto.getServiceCity())){
            sql.append(" AND OI.SERVICE_CITY = :serviceCity ");
            paramsMap.put("serviceCity", overBookDto.getServiceCity());
        }

        String type = overBookDto.getType();
        if (StringUtils.isNotBlank(type)) {
            paramsMap.put("type", type);
            sql.append(" AND O.TYPE = :type ");
        }
        String queryType = overBookDto.getQueryType();
        if(StringUtils.isNotBlank(queryType) && queryType.equals("1")){//我发起的
            paramsMap.put("userId", userId);
            sql.append(" AND O.CREATE_USER = :userId ");
        }
        if(StringUtils.isNotBlank(queryType) && queryType.equals("2")){//草稿
            paramsMap.put("status", "0");
            sql.append(" AND OI.STATUS = :status ");
        }
        String orderStatus = overBookDto.getOrderStatus();
        if(StringUtils.isNotBlank(orderStatus)){
            paramsMap.put("status", orderStatus);
            sql.append(" AND OI.STATUS = :status ");
        }
        String receiveStatus = overBookDto.getReceiveStatus();
        if(StringUtils.isNotBlank(receiveStatus)){
            paramsMap.put("receiveStatus", receiveStatus);
            sql.append(" AND  PI.RECEIVE_STATUS= :receiveStatus ");
        }
        String payType = overBookDto.getPayType();
        if(StringUtils.isNotBlank(payType)){
            paramsMap.put("payType", payType);
            sql.append(" AND PI.RECEIVE_WAY = :payType ");
        }
        String keySearch = overBookDto.getKeySearch();
        if(StringUtils.isNotBlank(keySearch)){
            paramsMap.put("keySearch", "%" + keySearch + "%");
            sql.append(" AND (PI.PAX_NAME LIKE :keySearch OR PI.TKT_NO  LIKE :keySearch OR o.ID LIKE :keySearch) ");
        }

        sql.append(" ORDER BY O.CREATE_TIME DESC ");
        return baseDAO.findBySQLPage_comm(sql.toString(),
                overBookDto.getCurrent(),
                overBookDto.getPageSize(),
                paramsMap,OverBookListVo.class);
    }

    public List<OverBookListVo> excelOverBookList(OverBookListQueryDto overBookDto,String userId){

        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT o.ID overId,O.ORDER_ID orderId,OI.FLIGHT_NO flightNo, OI.FLIGHT_DATE flightDate,");
        sql.append(" DECODE(OI.status,'0','草稿','1','审核中','2','通过','3','生效','4','关闭','5','未通过','6','驳回','7','待审核') status,");
        sql.append(" DECODE(o.TYPE,'0','改签','1','退票') type, ");
        sql.append(" DECODE(PI.RECEIVE_STATUS,'0','未领取','1','已领取','2','领取中') receiveStatus, ");
        sql.append(" OI.CHOICE_SEGMENT segment,PI.PAX_NAME paxName,PI.TKT_NO tktNo,");
        sql.append(" NVL(O.PRICE_SPREAD,0) priceSpread,NVL(OI.SUM_MONEY,0) sumMoney,");
        sql.append(" (nvl(OI.SUM_MONEY,0) -nvl(O.PRICE_SPREAD,0)) payMoney,");
        sql.append(" OI.SERVICE_CITY serviceCity,FT.STD oldPlanDate,");
        sql.append(" O.FLIGHT_NO overBookFlightNo,O.FLIGHT_DATE overBookFlightDate, O.PLAN_DATE planDate,");
        sql.append(" get_user_name(O.CREATE_USER) createUser,get_user_name(OI.ISS_USER) grantUser ,get_user_name(OT.AUDITOR) auditor ");
        sql.append(" FROM DP_OVER_INFO O");
        sql.append(" LEFT JOIN DP_ORDER_INFO OI ON O.ORDER_ID = OI.ORDER_ID");
        sql.append(" LEFT JOIN DP_PAX_INFO PI ON o.ORDER_ID = PI.ORDER_ID");
        sql.append(" LEFT JOIN DP_ACT_MIDDLE AM ON O.ORDER_ID = AM.ORDER_ID");
        sql.append(" LEFT JOIN DP_ORDER_AUDIT OT ON AM.RECORD_ID = OT.ID");
        sql.append(" LEFT JOIN DP_FLIGHT_INFO FT ON OI.ORDER_ID = FT.ORDER_ID");
        sql.append(" WHERE 1=1");

        String flightNo = overBookDto.getFlightNo();
        if (StringUtils.isNotBlank(flightNo)) {
            paramsMap.put("flightNo", flightNo);
            sql.append(" AND OI.FLIGHT_NO= :flightNo ");
        }
        String startDate = overBookDto.getStartDate();
        String endDate = overBookDto.getEndDate();
        if (StringUtils.isNotBlank(endDate) && StringUtils.isBlank(startDate)) {
            paramsMap.put("endDate", endDate);
            sql.append(" AND OI.FLIGHT_DATE <= :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
            endDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND OI.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND OI.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }

        String type = overBookDto.getType();
        if (StringUtils.isNotBlank(type)) {
            paramsMap.put("type", type);
            sql.append(" AND O.TYPE = :type ");
        }
        String queryType = overBookDto.getQueryType();//0全部
        if(StringUtils.isNotBlank(queryType) && queryType.equals(USER_LAUNCH)){//我发起的
            paramsMap.put("userId", userId);
            sql.append(" AND O.CREATE_USER = :userId ");
        }
        if(StringUtils.isNotBlank(queryType) && queryType.equals(OVER_DRAFT)){//草稿
            paramsMap.put("status", "0");
            sql.append(" AND OI.STATUS = :status ");
        }
        String orderStatus = overBookDto.getOrderStatus();
        if(StringUtils.isNotBlank(orderStatus)){
            paramsMap.put("status", orderStatus);
            sql.append(" AND OI.STATUS = :status ");
        }
        String receiveStatus = overBookDto.getReceiveStatus();
        if(StringUtils.isNotBlank(receiveStatus)){
            paramsMap.put("receiveStatus", receiveStatus);
            sql.append(" AND  PI.RECEIVE_STATUS= :receiveStatus ");
        }
        String payType = overBookDto.getPayType();
        if(StringUtils.isNotBlank(payType)){
            paramsMap.put("payType", payType);
            sql.append(" AND PI.RECEIVE_WAY = :payType ");
        }
        String keySearch = overBookDto.getKeySearch();
        if(StringUtils.isNotBlank(keySearch)){
            paramsMap.put("keySearch", "%" + keySearch + "%");
            sql.append(" AND (PI.PAX_NAME LIKE:keySearch OR PI.TKT_NO  LIKE:keySearch) ");
        }
        sql.append(" ORDER BY O.CREATE_TIME DESC ");
        return (List<OverBookListVo>)baseDAO.findBySQL_comm(sql.toString(),
                paramsMap,OverBookListVo.class);

    }


    public OverBookDetailsVo findWebOverDetailsById(String overId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT DISTINCT O.ID overId ,O.ORDER_ID orderId ,O.TYPE type ,O.PRICE price ,");
        sql.append(" O.FLIGHT_NO overBookFlightNo,O.FLIGHT_DATE overBookFlightDate,O.PLAN_DATE planeDate,");
        sql.append(" NVL(O.PRICE_SPREAD,0) priceSpread,O.ATTACHMENT imgUrl,");
        sql.append(" PI.PAX_NAME paxName ,PI.ID_TYPE idType ,PI.ID_NO idNo,");
        sql.append(" PI.TELEPHONE telephone,PI.TKT_NO tktNo,");
        sql.append(" T.FLIGHT_NO flightNo, T.FLIGHT_DATE flightDate,");
        sql.append(" T.SEGMENT segment ,T.STD oldPlaneDate,pi.RECEIVE_STATUS receiveStatus ,");
        sql.append(" OI.SUM_MONEY sumMoney,(nvl(OI.SUM_MONEY,0) -nvl(O.PRICE_SPREAD,0)) payMoney");

        sql.append(" FROM DP_OVER_INFO O");
        sql.append(" LEFT JOIN DP_ORDER_INFO OI ON O.ORDER_ID = OI.ORDER_ID");
        sql.append(" LEFT JOIN DP_FLIGHT_INFO T ON OI.ORDER_ID = T.ORDER_ID");
        sql.append(" LEFT JOIN DP_PAX_INFO PI ON o.ORDER_ID = PI.ORDER_ID");
        sql.append(" WHERE O.ID = :overId ");
        paramsMap.put("overId",overId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,OverBookDetailsVo.class);
    }


}
