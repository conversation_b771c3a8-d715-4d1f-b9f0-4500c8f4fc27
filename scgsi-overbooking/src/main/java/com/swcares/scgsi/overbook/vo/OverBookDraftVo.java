package com.swcares.scgsi.overbook.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.vo <br>
 * Description：H5-草稿箱列表对象 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月17日 16:32 <br>
 * @version v1.0 <br>
 */
@Data
@EncryptionClassz
public class OverBookDraftVo {

    /**
     * 事故单ID
     */
    private String overId;

    /**
     * 类型 0改签,1退票
     */
    private String type;

    /**
     * 原航班号
     */
    private String flightNo;

    /**
     * 原航班日期
     */
    private String flightDate;
    /**
     * 航段
     */
    private String segment;

    /**
     * 旅客姓名
     */
    private String paxName;

//---------------以下是详情--------------------------------
    /**
     * 服务单id 草稿-提交 需要
     */
    private String orderId;
    /**
     * 原航班id
     */
    private String flightId;

    /**
     * 机型
     */
    private String acType;

    /**
     * 计划起飞
     */
    private String std;
    /**
     * 计划到达
     */
    private String sta;

    /**
     * 旅客id
     */
    private String paxId;

    /**
     * 旅客性别C儿童M男F女
     */
    private String sex;

    /**
     * 婴儿姓名不为空代表携带婴儿
     */
    private String babyName;
    /**
     * 是否是婴儿	1-是
     */
    private String isInfant;

    /**
     * 是否是儿童	Y-是
     */
    private String isChild;

    /**
     * 票号
     */
    private String tktNo;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号
     */
    @Encryption
    private String idNo;

    /**
     * 乘机人手机号
     */
    @Encryption
    private String telephone;

    /**
     * 改签航班号
     */
    private String overBookFlightNo;

    /**
     * 改签航班日期
     */
    private String overBookFlightDate;

    /**
     * 改签航班的计划起飞时间
     */
    private String planeDate;

    /**
     * 旅客原航班票价（不含机建燃油费）
     */
    private String price;

    /**
     * 附件URL
     */
    private String imgUrl;

    /**
     * 赔偿金额
     */
    private String payMoney;

    /**
     * 客票价 退票才有
     */
    private String priceSpread;

    /**
     * 国内国际
     */
    private String dOrI;

}
