package com.swcares.scgsi.overbook.vo;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.vo <br>
 * Description：web后台-超售管理列表查询返回对象 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月22日 15:27 <br>
 * @version v1.0 <br>
 */
@Data
public class OverBookListVo {

    /**
     * 赔偿单状态
     */
    private String status;

    /**
     * 类型 0改签 1退票
     */
    private String type;

    /**
     * 事故单id
     */
    private String overId;

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班日期
     */
    private String flightDate;

    /**
     * 航班计划起飞时间
     */
    private String oldPlanDate;

    /**
     * 旅客姓名
     */
    private String paxName;

    /**
     * 票号
     */
    private String tktNo;

    /**
     * 航段
     */
    private String segment;

    /**
     * 赔偿总金额
     */
    private String sumMoney;

    /**
     * 赔偿金额
     */
    private String payMoney;

    /**
     * 客票价 退票才有
     */
    private String priceSpread;

    /**
     * 领取状态(0未领取,1已领取,2领取中)
     */
    private String receiveStatus;

    /**
     * 服务单id
     */
    private String orderId;

    /**
     * 服务航站
     */
    private String serviceCity;

    /**
     * 改签航班号
     */
    private String overBookFlightNo;

    /**
     * 改签航班日期
     */
    private String overBookFlightDate;

    /**
     * 改签航班计划起飞时间
     */
    private String planDate;

    /**
     * 申请人
     */
    private String createUser;

    /**
     * 审核人姓名 状态为审核类型时有值
     */
    private String auditor;

    /**
     * 发放人
     */
    private String grantUser;

}
