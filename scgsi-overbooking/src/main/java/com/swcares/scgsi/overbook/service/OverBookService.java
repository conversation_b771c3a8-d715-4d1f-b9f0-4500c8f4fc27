package com.swcares.scgsi.overbook.service;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.overbook.dto.*;
import com.swcares.scgsi.overbook.vo.*;

import javax.script.ScriptException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.audit.service <br>
 * Description：旅客超售 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月13日 16:03 <br>
 * @version v1.0 <br>
 */
public interface OverBookService {

    /**
     * Title：overBookWebList <br>
     * Description： WEB后台-超售列表查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/22 17:29 <br>
     * @param
     * @return
     */
    QueryResults overBookWebList(OverBookListQueryDto overBookListQueryDto);


    /**
     * Title：excelOverBookList<br>
     * Description： 超售列表-导出功能<br>
     * author：傅欣荣 <br>
     * date：2020/3/25 15:43 <br>
     * @param
     * @return
     */
    List<OverBookListVo> excelOverBookList(OverBookListQueryDto overBookListQueryDto);

    /**
     * Title：overBookWebListDetails <br>
     * Description：WEB后台-超售列表详情<br>
     * author：傅欣荣 <br>
     * date：2020/3/22 17:30 <br>
     * @param
     * @return
     */
    OverBookDetailsVo overBookWebDetails(String overId);


    /**
     * Title：saveOverBookConfig <br>
     * Description： WEB-保存超售配置<br>
     * author：傅欣荣 <br>
     * date：2020/3/31 17:20 <br>
     * @param  timeDifferenceInfo
     * @param  lowestStandInfo
     * @param refundInfo
     * @return
     */
    void saveOverBookConfig(List<TimeDifferenceInfoVo> timeDifferenceInfo,
                            LowestStandInfo lowestStandInfo, RefundInfoVo refundInfo);

    /**
     * Title： findOverBookConfig<br>
     * Description：WEB - 查询超售配置回显<br>
     * author：傅欣荣 <br>
     * date：2020/3/31 17:21 <br>
     * @param
     * @return
     */
    Map<String, Object> findOverBookConfig();


    /**
     * Title：findOverBookList <br>
     * Description： H5 - 超售列表查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:25 <br>
     * @param  overBookQueryDto 请求参数对象
     * @return
     */
    QueryResults findOverBookList(OverBookQueryDto overBookQueryDto);

    /**
     * Title：findOverDetails <br>
     * Description：H5 - 超售列表数据详情查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:26 <br>
     * @param  orderId 赔偿单号
     * @return
     */
    Map<String, Object> findOverDetails(String orderId) throws Exception;

    /**
     * Title：findOrderDetails <br>
     * Description： H5 - 查看超售对应赔偿单信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:27 <br>
     * @param  orderId 服务单id
     * @return
     */
    OverOrderDetailsVo findOrderDetails(String orderId);

    /**
     * Title：updateOrderStatus <br>
     * Description： 去发放 ，修改赔偿单状态<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:27 <br>
     * @param  statusSaveDto 服务单信息
     * @return
     */
    void updateOrderStatus(OverOrderStatusSaveDto statusSaveDto);

    /**
     * Title：overBookVerification <br>
     * Description： H5 - 旅客航班信息查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:28 <br>
     * @param  overBookVerificationDto
     * @return
     */
    OverBookVerftnVo overBookVerification(OverBookVerificationDto overBookVerificationDto);


    /**
     * Title： inspectPayMoney<br>
     * Description： 验证超售赔偿金额<br>
     * author：傅欣荣 <br>
     * date：2020/4/20 10:06 <br>
     * @param
     * @return
     */
    String inspectPayMoney(OverBookInfoDto overBookInfoDto) throws ScriptException, ParseException;
    /**
     * Title：saveOverBookInfo <br>
     * Description：H5 - 保存旅客超售 改签 <br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:29 <br>
     * @param  overBookInfoDto 保存数据参数

     * @return
     */
    String saveOverBookInfo(OverBookInfoDto overBookInfoDto) throws Exception;

    /**
     * Title：getConfigInfo <br>
     * Description： H5 - 查询超售配置信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:31 <br>
     * @return
     */
    Map<String, Object> getConfigInfo();

    /**
     * Title：findOverDraftList <br>
     * Description： H5- 超售草稿箱列表数据查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:32 <br>
     * @param
     * @return
     */
    QueryResults findOverDraftList(OverDraftListQueryDto overDraftListQueryDto);

    /**
     * Title：findOverDraftDetails <br>
     * Description： 根据超售单号，查询超售详情<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:32 <br>
     * @param  overId 超售单号
     * @return
     */
    OverBookDraftVo findOverDraftDetails(String overId);

    /**
     * Title：delOverDraftInfo <br>
     * Description： H5 - 删除超售草稿数据<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:33 <br>
     * @param
     * @return
     */
    void delOverDraftInfo(String[] ids);


    /**
     * Title：getRebookFlightInfo <br>
     * Description： 获取改签航班信息【计划起飞时间等】<br>
     * author：傅欣荣 <br>
     * date：2020/3/30 18:04 <br>
     * @param  flightNo, flightDate, paxId
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    Map<String, Object> getRebookFlightInfo(String flightNo,String flightDate,String paxId);


}
