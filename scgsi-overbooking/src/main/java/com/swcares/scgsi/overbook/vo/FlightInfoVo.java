package com.swcares.scgsi.overbook.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.vo <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月19日 15:59 <br>
 * @version v1.0 <br>
 */
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class FlightInfoVo {

    /**
     * 航班id
     */
    private String flightId;

    /**
     * 原航班号
     */
    private String flightNo;

    /**
     * 原航班日期
     */
    private String flightDate;
    /**
     * 航段
     */
    private String segment;

    /**
     * 航班计划起飞时间
     */
    private String planDate;

    /**
     * 时差
     */
    private String timeDiff;

    /**
     * 票价
     */
    private String price;

    /**
     * 机型
     */
 /*   private String acType;*/


}
