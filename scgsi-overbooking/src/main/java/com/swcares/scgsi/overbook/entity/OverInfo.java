package com.swcares.scgsi.overbook.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.audit.entity <br>
 * Description：旅客超售信息表 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月17日 13:31 <br>
 * @version v1.0 <br>
 */
@Entity
@Data
@Table(name = "DP_OVER_INFO")
public class OverInfo {

    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
   /* @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")*/
    private String id;

    /**
     * 服务单id
     */
    @Column(name = "ORDER_ID")
    private String orderId;

    /**
     * 0改签,1退票
     */
    @Column(name = "TYPE")
    private String type;

    /**
     * 航班号
     */
    @Column(name = "FLIGHT_NO")
    private String flightNo;

    /**
     * 航班日期
     */
    @Column(name = "FLIGHT_DATE")
    private String flightDate;
    /**
     * 航班计划起飞时间
     */
    @Column(name = "PLAN_DATE")
    private String planDate;

    /**
     * 航班票价
     */
    @Column(name = "PRICE")
    private String price;

    /**
     * 附件
     */
    @Column(name = "ATTACHMENT")
    private String attachment;


    /**
     * 客票价 退票才有
     */
    @Column(name = "PRICE_SPREAD")
    private String priceSpread;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 是否有效 0默认 1删除
     */
    @Column(name = "IS_EFFECTIVE")
    private String isEffective;



}
