package com.swcares.scgsi.overbook.service;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.overbook.dto.PaxCompensateListQueryDto;
import com.swcares.scgsi.overbook.dto.PaxInfoQueryDto;

import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.audit.service <br>
 * Description：H5-旅客赔偿模块 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月27日 10:36 <br>
 * @version v1.0 <br>
 */
public interface PaxCompensateService {

    /**
     * Title：getPaxCompensateList <br>
     * Description： H5-旅客赔偿-列表查询 <br>
     * author：傅欣荣 <br>
     * date：2020/3/27 10:37 <br>
     * @param
     * @return
     */
    QueryResults getPaxCompensateList(PaxCompensateListQueryDto paxCompensateListQueryDto);

    /**
     * Title：queryOrderPayDetailsInfo <br>
     * Description：H5-旅客赔偿-赔偿单信息 <br>
     * author：傅欣荣 <br>
     * date：2020/3/27 15:43 <br>
     * @param
     * @return
     */
    Map<String, Object> getPaxCompensateDetails(String orderId);

    /**
     * Title：getOrderPaxInfo <br>
     * Description： H5-获取服务单旅客列表数据<br>
     * author：傅欣荣 <br>
     * date：2020/3/30 13:42 <br>
     * @param
     * @return
     */
    Map<String, Object> getOrderPaxInfo(PaxInfoQueryDto paxInfoQueryDto);

    /**
     * Title： getOrderPaxSegment<br>
     * Description： 旅客列表获取旅客航段信息（赔偿单已选航段）<br>
     * author：傅欣荣 <br>
     * date：2020/5/9 9:44 <br>
     * @param
     * @return
     */
    String getOrderPaxSegment(String orderId) throws Exception;

    /**
     * Title：freezeOrderPax <br>
     * Description： 冻结旅客<br>
     * author：傅欣荣 <br>
     * date：2020/3/30 14:59 <br>
     * @param  orderId
     * @param  paxIds
     * @return
     */
    void freezeOrderPax(String orderId,String[] paxIds );

    /**
     * Title：freezeOrderPax <br>
     * Description： 解除冻结旅客<br>
     * author：傅欣荣 <br>
     * date：2020/3/30 14:59 <br>
     * @param  orderId
     * @param  paxIds
     * @return
     */
    void unfreezeOrderPax(String orderId,String[] paxIds );



    /**
     * Title：queryOrderAuditDetailsInfo <br>
     * Description： H5-审核-赔偿单详情信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/27 15:41 <br>
     * @param
     * @return
     */
    Map<String, Object> queryOrderAuditDetailsInfo(String orderId);
}
