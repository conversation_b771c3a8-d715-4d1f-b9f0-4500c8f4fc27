package com.swcares.scgsi.overbook.dao;


import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.overbook.entity.OrderInfo;
import org.springframework.data.repository.query.Param;

/**
 * ClassName：com.swcares.scgsi.flight.dao <br>
 * Description：赔付单单信息dao <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月03日 13:16 <br>
 * @version v1.0 <br>
 */
public interface OrderInfoDao extends BaseJpaDao<OrderInfo,String> {


    /**
     * Title： findByOrderId<br>
     * Description： 根据orderid查询订单信息<br>
     * author：傅欣荣 <br>
     * date：2020/5/9 9:50 <br>
     * @param
     * @return
     */
    OrderInfo findByOrderId(@Param("orderId") String orderId) throws Exception;
}
