package com.swcares.scgsi.overbook.vo;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.vo <br>
 * Description：H5-旅客赔偿-赔偿单详情 【公共】 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月27日 14:38 <br>
 * @version v1.0 <br>
 */
@Data
public class PaxCompensateDetailsVo {

    /**
     * 服务单号
     */
    private String orderId;

    /**
     * 流程任务id 待审核有值
     */
    private String taskId;

    /**
     * 事故单id
     */
    private String accidentId;

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班日期
     */
    private String flightDate;

    /**
     * 服务航站
     */
    private String serviceCity;

    /**
     * 赔偿类型 不正常航班赔偿0、异常行李1、超售旅客2、
     */
    private String  payType;

    /**
     * 航段
     */
    private String segment;

    /**
     *  备注
     */
    private String remark;

    /**
     * 客票价 退票才有
     */
    private String priceSpread;

    /**
     * 赔偿人数
     */
    private String paxTotalCount;

    /**
     * 赔偿总金额
     */
    private String totalMoney;

    /**
     * 服务单状态(0草稿、1审核中、2通过、3生效、4关闭,5未通过6驳回7待审核8逾期)
     */
    private String status;

    /**
     * 审批节点 1-aoc 2-值班经理
     */
    private String processNode;
    /**
     * 赔付单创建人
     */
    private String createUser;

    private Boolean autoCreate;

}
