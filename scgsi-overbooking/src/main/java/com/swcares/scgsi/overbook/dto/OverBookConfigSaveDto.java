package com.swcares.scgsi.overbook.dto;

import com.swcares.scgsi.overbook.vo.LowestStandInfo;
import com.swcares.scgsi.overbook.vo.RefundInfoVo;
import com.swcares.scgsi.overbook.vo.TimeDifferenceInfoVo;
import lombok.Data;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.audit.vo <br>
 * Description：超售配置 保存<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月15日 20:12 <br>
 * @version v1.0 <br>
 */
@Data
public class OverBookConfigSaveDto {

    //赔付比例 list
    private List<TimeDifferenceInfoVo> timeDifferenceInfo;
    //最低赔付 obj
    private LowestStandInfo lowestStandInfo;
    //退票规则 obj
    private RefundInfoVo refundInfo;

}
