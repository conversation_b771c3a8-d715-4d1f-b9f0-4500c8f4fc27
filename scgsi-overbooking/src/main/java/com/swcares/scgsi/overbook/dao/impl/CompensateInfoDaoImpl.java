package com.swcares.scgsi.overbook.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.overbook.vo.CompensateInfoVo;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.audit.dao.impl <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月20日 11:29 <br>
 * @version v1.0 <br>
 */
@Repository
public class CompensateInfoDaoImpl {
    @Resource
    BaseDAO baseDAO;

    public List<CompensateInfoVo> getCompensateInfoByOrderId(String orderId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT CI.CLASS_TYPE classType,CI.CHILD_STD childStd, ");
        sql.append(" CI.CPS_NUM cpsNum,CI.BABY_STD babyStd");
        sql.append(" FROM DP_COMPENSATE_INFO CI");
        sql.append(" WHERE CI.CLASS_TYPE IS NOT NULL AND CI.ORDER_ID = :orderId");
        paramsMap.put("orderId",orderId);
        return (List<CompensateInfoVo>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,CompensateInfoVo.class);
    }

    /**
     * Title：deleteByOrderId <br>
     * Description： 根据服务单id删除信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/26 21:07 <br>
     * @param
     * @return
     */
    public void deleteByOrderId(String orderId){
        StringBuffer sql = new StringBuffer();
        sql.append("DELETE DP_COMPENSATE_INFO WHERE ORDER_ID = ? ");
        baseDAO.batchUpdate(sql.toString(),orderId);
    }
}
