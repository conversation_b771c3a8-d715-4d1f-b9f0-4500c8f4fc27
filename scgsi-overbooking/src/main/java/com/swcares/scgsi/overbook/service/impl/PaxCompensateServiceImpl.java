package com.swcares.scgsi.overbook.service.impl;


import com.alibaba.fastjson.JSON;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.audit.service.OrderAuditService;
import com.swcares.scgsi.audit.vo.OrderAuditProgressVo;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.overbook.dao.OrderInfoDao;
import com.swcares.scgsi.overbook.dao.impl.CompensateInfoDaoImpl;
import com.swcares.scgsi.overbook.dao.impl.OrderInfoDaoImpl;
import com.swcares.scgsi.overbook.dto.PaxCompensateListQueryDto;
import com.swcares.scgsi.overbook.dto.PaxInfoQueryDto;
import com.swcares.scgsi.overbook.entity.OrderInfo;
import com.swcares.scgsi.overbook.service.PaxCompensateService;
import com.swcares.scgsi.overbook.vo.CompensateInfoVo;
import com.swcares.scgsi.overbook.vo.PaxCompensateDetailsVo;
import com.swcares.scgsi.overbook.vo.PaxInfoQueryResultVo;
import com.swcares.scgsi.util.Asserts;
import com.swcares.scgsi.util.AuthenticationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.audit.service.impl <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月27日 10:38 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class PaxCompensateServiceImpl implements PaxCompensateService {

    @Resource
    OrderInfoDao orderInfoDao;
    @Resource
    OrderInfoDaoImpl orderInfoDaoImpl;
    @Resource
    OrderAuditService orderAuditService;
    @Resource
    CompensateInfoDaoImpl compensateInfoDaoImpl;
    /* 冻结 */
    private static final String SWITCH_YES = "1";
    /* 未冻结 */
    private static final String SWITCH_NO = "0";

    @Override
    public QueryResults getPaxCompensateList(PaxCompensateListQueryDto paxCompensateListQueryDto) {
        log.info("旅客列表查询参数："+paxCompensateListQueryDto.toString());
        return orderInfoDaoImpl.queryPaxComensateList(paxCompensateListQueryDto);
    }

    @Override
    public Map<String, Object> getPaxCompensateDetails(String orderId) {

        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"赔偿单id"});
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        Map<String, Object> dataMap = new HashMap<>();
        // 包含赔偿单信息
        PaxCompensateDetailsVo orderDetailsVo = orderInfoDaoImpl.getOrderDetailsInfo(orderId,userId);
        // 审核进度信息
        List<OrderAuditProgressVo> auditList = orderAuditService.getAuditProgressInfo(orderId);
        // 赔偿标准
        List<CompensateInfoVo> compensateInfo =
                compensateInfoDaoImpl.getCompensateInfoByOrderId(orderId);
        dataMap.put("orderDetailsInfo", orderDetailsVo);
        dataMap.put("auditProgressInfo", auditList);
        dataMap.put("compensateInfo", compensateInfo);
        return dataMap;
    }

    @Override
    public Map<String, Object> getOrderPaxInfo(PaxInfoQueryDto paxInfoQueryDto) {
        Asserts.isNotEmpty(paxInfoQueryDto.getOrderId(), MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"赔偿单id"});
        Map<String, Object> dataMap = new HashMap<>();
        PaxInfoQueryResultVo membersCount =
                orderInfoDaoImpl.getOrderMembersCount(paxInfoQueryDto.getOrderId());
        QueryResults paxList = orderInfoDaoImpl.getOrderPaxInfo(paxInfoQueryDto);
        dataMap.put("membersCount", membersCount.getMembersCount());
        dataMap.put("paxList", paxList);
        return dataMap;
    }

    @Override
    public String getOrderPaxSegment(String orderId) throws Exception {
        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"赔偿单id"});
        OrderInfo orderInfo = orderInfoDao.findByOrderId(orderId);
        Asserts.notNull(orderInfo,MessageCode.ORDER_INFO_IS_NULL.getCode(),new String[] {orderId});
        return orderInfo.getChoiceSegment();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void freezeOrderPax(String orderId, String[] paxIds) {
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"赔偿单id"});
        log.info("H5旅客赔偿-冻结旅客，操作人：" + userId + ", 参数orderId={}，paxIds={}", orderId, JSON.toJSONString(paxIds));
        orderInfoDaoImpl.updOrderPaxFreeze(orderId, paxIds, SWITCH_YES);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unfreezeOrderPax(String orderId, String[] paxIds) {
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"赔偿单id"});
        log.info("H5旅客赔偿-解除冻结旅客，操作人：" + userId + ", 参数orderId={}，paxIds={}", orderId, JSON.toJSONString(paxIds));
        orderInfoDaoImpl.updOrderPaxFreeze(orderId, paxIds, SWITCH_NO);
    }

    @Override
    public Map<String, Object> queryOrderAuditDetailsInfo(String orderId) {
        Asserts.isNotEmpty(orderId, MessageCode.PARAM_EXCEPTION.getCode(),new String[] {"赔偿单id"});
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        Map<String, Object> dataMap = new HashMap<>();
        // 包含赔偿单信息
        PaxCompensateDetailsVo orderDetailsVo = orderInfoDaoImpl.getOrderDetailsInfo(orderId,userId);
        // 审核进度信息
        List<OrderAuditProgressVo> auditList = orderAuditService.getAuditProgressInfo(orderId);
        // 赔偿标准
        List<CompensateInfoVo> compensateInfo =
                compensateInfoDaoImpl.getCompensateInfoByOrderId(orderId);
        dataMap.put("orderDetailsInfo", orderDetailsVo);
        dataMap.put("auditProgressInfo", auditList);
        dataMap.put("compensateInfo", compensateInfo);
        return dataMap;
    }
}
