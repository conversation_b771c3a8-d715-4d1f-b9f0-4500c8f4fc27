package com.swcares.scgsi.overbook.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.overBook.dto <br>
 * Description：H5旅客赔偿-旅客冻结状态请求参数 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 04月09日 14:12 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "旅客赔偿-旅客冻结状态")
public class OrderPaxFreezeDto {

    @ApiModelProperty(value = " 赔偿单id", required = true)
    private String orderId;

    @ApiModelProperty(value = " 旅客id", required = true)
    private String paxIds;

}
