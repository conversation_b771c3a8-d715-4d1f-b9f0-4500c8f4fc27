package com.swcares.scgsi.overbook.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * ClassName：com.swcares.scgsi.audit.dao.impl <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月26日 21:09 <br>
 * @version v1.0 <br>
 */
@Repository
public class PaxInfoDaoImpl {
    @Resource
    BaseDAO baseDAO;

    /**
     * Title：deleteByOrderId <br>
     * Description： 根据服务单id删除信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/26 21:07 <br>
     * @param
     * @return
     */
    public void deleteByOrderId(String orderId){
        StringBuffer sql = new StringBuffer();
        sql.append("DELETE DP_PAX_INFO WHERE ORDER_ID = ? ");
        baseDAO.batchUpdate(sql.toString(),orderId);
    }
}
