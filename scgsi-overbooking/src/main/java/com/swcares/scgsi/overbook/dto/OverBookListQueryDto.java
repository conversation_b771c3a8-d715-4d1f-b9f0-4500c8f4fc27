package com.swcares.scgsi.overbook.dto;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.dto <br>
 * Description：web后台-超售列表查询参数请求对象 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月22日 15:59 <br>
 * @version v1.0 <br>
 */
@Data
public class OverBookListQueryDto {

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班起始日期
     */
    private String startDate;

    /**
     * 航班结束日期
     */
    private String endDate;

    /**
     * 类型
     */
    private String type;

    /**
     * 服务航站
     */
    private String serviceCity;

    /**
     * 查询类型 0 全部 1我发起的 2草稿
     */
    private String queryType;

    /**
     * 模糊搜索 支持旅客姓名，事故单号
     */
    private String keySearch;

    /**
     * 审核状态 0草稿、1审核中、2通过、3生效、4关闭,5未通过,6驳回,7待审核
     */
    private String orderStatus;

    /**
     * 领取状态 1未领取,2已领取,3已逾期
     */
    private String receiveStatus;

    /**
     * 支付方式	下拉选项		全部、银联、微信、现金
     */
    private String payType;


    /** 当前页数，默认为第一页 **/
    private int current = 1;

    /** 每页显示记录数，默认为10条 **/
    private int pageSize = 10;

}
