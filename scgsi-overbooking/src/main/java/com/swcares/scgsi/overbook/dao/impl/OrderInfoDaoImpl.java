package com.swcares.scgsi.overbook.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.overbook.dto.PaxCompensateListQueryDto;
import com.swcares.scgsi.overbook.dto.PaxInfoQueryDto;
import com.swcares.scgsi.overbook.vo.PaxCompensateDetailsVo;
import com.swcares.scgsi.overbook.vo.PaxCompensateInfoVo;
import com.swcares.scgsi.overbook.vo.PaxInfoQueryResultVo;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.util.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.audit.dao.impl <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月26日 21:12 <br>
 * @version v1.0 <br>
 */
@Repository
public class OrderInfoDaoImpl {

    @Resource
    BaseDAO baseDAO;
    //本人发起
    private static final String USER_LAUNCH = "1";

    @Resource
    private UserUtil userUtil;
    /**
     * Title：deleteByOrderId <br>
     * Description： 根据服务单id删除信息<br>
     * author：傅欣荣 <br>
     * date：2020/3/26 21:07 <br>
     * @param
     * @return
     */
    public void deleteByOrderId(String orderId){
        StringBuffer sql = new StringBuffer();
        sql.append("DELETE DP_ORDER_INFO WHERE ORDER_ID = ? ");
        baseDAO.batchUpdate(sql.toString(),orderId);
    }


    /**
     * Title：queryPaxComensateList <br>
     * Description： H5-旅客赔偿查询列表<br>
     * author：傅欣荣 <br>
     * date：2020/3/27 13:54 <br>
     * @param  paxQueryDto
     * @return
     */
    public QueryResults queryPaxComensateList(PaxCompensateListQueryDto paxQueryDto){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT         ");
        sql.append(" O.ORDER_ID orderId,O.ACCIDENT_ID accidentId,");
        sql.append(" CASE WHEN O.AUTO_CREATE IS NULL AND O.APPLY_USER IS NULL THEN get_user_name(O.CREATE_ID) ELSE get_user_name(O.APPLY_USER) END AS applyUser,");
        sql.append(" TO_CHAR(O.APPLY_DATE,'YYYY-MM-DD hh24:mi:ss') AS applyTime,O.CREATE_ID AS createId, ");
        sql.append(" get_user_name(O.CREATE_ID) AS createUser,TO_CHAR(O.CREATE_TIME,'YYYY-MM-DD hh24:mi:ss') AS createDate,O.AUTO_CREATE AS autoCreate, ");
        sql.append(" O.SERVICE_CITY serviceCity,O.FLIGHT_NO flightNo,O.FLIGHT_DATE flightDate,");
        sql.append(" O.STATUS orderStatus,O.PAY_TYPE payType");
        sql.append(" FROM DP_ORDER_INFO O");
        sql.append(" WHERE 1=1 ");
        String flightNo = paxQueryDto.getFlightNo();
        if (StringUtils.isNotBlank(flightNo)) {
            paramsMap.put("flightNo", flightNo);
            sql.append(" AND O.FLIGHT_NO= :flightNo ");
        }
        String startDate = paxQueryDto.getStartDate();
        String endDate = paxQueryDto.getEndDate();
        if (StringUtils.isNotBlank(endDate) && StringUtils.isBlank(startDate)) {
            paramsMap.put("endDate", endDate);
            sql.append(" AND O.FLIGHT_DATE <= :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
            endDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND O.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND O.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }

        String queryType = paxQueryDto.getQueryType();//空全部
        String userIds = paxQueryDto.getUserId();
        if(StringUtils.isNotBlank(userIds) && StringUtils.isNotBlank(queryType)
                && queryType.equals(USER_LAUNCH)){//我发起的
            paramsMap.put("userId", userIds);
            sql.append(" AND O.CREATE_ID = :userId ");
        }

        String serviceCity = paxQueryDto.getServiceCity();
        if(StringUtils.isNotBlank(serviceCity)){
            paramsMap.put("serviceCity", serviceCity);
            sql.append(" AND O.SERVICE_CITY = :serviceCity ");
        }
        String orderId = paxQueryDto.getOrderId();
        if(StringUtils.isNotBlank(orderId)){
            paramsMap.put("orderId","%" + orderId+"%" );
            sql.append(" AND O.ORDER_ID LIKE :orderId ");
        }

        List<String> payTypeLIst = new ArrayList<>();
        String queryPayType = "";
        payTypeLIst = userUtil.findBussiTypeByEmpId(userIds);
        if(payTypeLIst.size()>0) {
            for (String payType1 : payTypeLIst) {
                queryPayType += payType1 + ",";
            }
            sql.append(" AND O.PAY_TYPE ");
            sql.append(" in (SELECT REGEXP_SUBSTR('"+queryPayType+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+queryPayType+"', '[^,]+', 1, level) is not null)");
        }

        String payType = paxQueryDto.getPayType();
        if (StringUtils.isNotBlank(payType)) {
            sql.append(" AND O.PAY_TYPE ");
            sql.append(" in (SELECT REGEXP_SUBSTR('"+payType+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+payType+"', '[^,]+', 1, level) is not null)");
        }
        String orderStatus = paxQueryDto.getOrderStatus();
        if (StringUtils.isNotBlank(orderStatus)) {
            sql.append(" AND O.STATUS ");
            sql.append(" in (SELECT REGEXP_SUBSTR('"+orderStatus+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+orderStatus+"', '[^,]+', 1, level) is not null)");
        }
        sql.append(" ORDER BY O.CREATE_TIME DESC ");
        return baseDAO.findBySQLPage_comm(sql.toString(),
                paxQueryDto.getCurrent(),
                paxQueryDto.getPageSize(),
                paramsMap,PaxCompensateInfoVo.class);
    }

    /**
     * Title：getOrderDetailsInfo <br>
     * Description： 查询赔偿单详情
     *          旅客赔偿-赔偿单详情 、审核-赔偿单详情 公共方法<br>
     * author：傅欣荣 <br>
     * date：2020/3/27 15:46 <br>
     * @param
     * @return
     */
    public PaxCompensateDetailsVo getOrderDetailsInfo(String orderId,String userId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT");
        sql.append("  ACTS.taskId AS taskId,get_task_node(ACTS.taskId) processNode, O.AUTO_CREATE AS autoCreate, ");
        sql.append(" O.ORDER_ID orderId,O.ACCIDENT_ID accidentId, ");
        sql.append(" O.SERVICE_CITY serviceCity,O.FLIGHT_NO flightNo,O.FLIGHT_DATE flightDate,");
        sql.append(" O.PAY_TYPE payType,O.REMARK,o.CHOICE_SEGMENT segment,");
        sql.append(" to_number(get_dp_order_receivecount(O.ORDER_ID,NULL))");
        sql.append(" AS paxTotalCount,O.CREATE_ID AS createUser,");
        sql.append(" o.SUM_MONEY totalMoney ,o.STATUS status,");
        sql.append(" decode(o.PAY_TYPE,'0',cf.CPS_NUM,OI.PRICE_SPREAD)  priceSpread ");
        sql.append(" FROM DP_ORDER_INFO O");
        sql.append(" LEFT JOIN DP_OVER_INFO OI on O.ORDER_ID = OI.ORDER_ID");
        sql.append(" LEFT JOIN DP_ACT_MIDDLE ae ON O.ORDER_ID = ae.ORDER_ID    ");
        sql.append(" LEFT JOIN DP_COMPENSATE_INFO cf on O.ORDER_ID  = cf.ORDER_ID and  cf.CLASS_TYPE is null ");
        sql.append(" LEFT JOIN ").append(getActUserTaskInfo()).append(" on ACTS.actPinId = ae.ACT_PIN_ID ");
        sql.append(" WHERE 1=1");
        sql.append(" AND o.ORDER_ID = :orderId ");
        paramsMap.put("orderId",orderId);
        paramsMap.put("userId",userId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,PaxCompensateDetailsVo.class);
    }

    /**
     * Title： getActUserTaskInfo<br>
     * Description： 查询用户审核任务task，流程id sql<br>
     * author：傅欣荣 <br>
     * date：2020/5/14 10:50 <br>
     * @param
     * @return
     */
    private String getActUserTaskInfo(){
        StringBuffer sql = new StringBuffer();
        sql.append(" (SELECT RT.ID_ AS taskId,RT.PROC_INST_ID_ AS actPinId ");
        sql.append(" FROM ACT_RU_TASK rt where rt.ASSIGNEE_ = :userId  and instr(rt.NAME_,'提交') != 1");
        sql.append(" UNION ");
        sql.append(" SELECT RT.ID_ AS taskId,RT.PROC_INST_ID_ AS actPinId from ACT_RU_TASK rt ");
        sql.append(" LEFT JOIN ACT_RU_IDENTITYLINK rk on RT.ID_ = rk.TASK_ID_  and rk.TYPE_ != 'starter'");
        sql.append(" WHERE rk.USER_ID_ = :userId ) ACTS ");
        return sql.toString();
    }


    /**
     * Title： getOrderMembersCount <br>
     * Description： 根据订单号查询成人/儿童/婴儿人数<br>
     * author：傅欣荣 <br>
     * date：2020/4/9 9:37 <br>
     * @param
     * @return
     */
    public PaxInfoQueryResultVo getOrderMembersCount(String orderId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();

        sql.append("SELECT");
        sql.append(" get_dp_order_receivecount(O.ORDER_ID,NULL) - GET_DP_ORDER_CHILDCOUNT(O.ORDER_ID)-GET_DP_ORDER_BABYCOUNT(O.ORDER_ID)||");
        sql.append(" '/'||GET_DP_ORDER_CHILDCOUNT(O.ORDER_ID)||");
        sql.append(" '/'||GET_DP_ORDER_BABYCOUNT(O.ORDER_ID) AS membersCount");
        sql.append(" FROM DP_ORDER_INFO O");
        sql.append(" WHERE O.ORDER_ID = :orderId ");
        paramsMap.put("orderId",orderId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,PaxInfoQueryResultVo.class);
    }

    /**
     * Title：getOrderPaxInfo <br>
     * Description： H5-获取服务单旅客列表数据<br>
     * author：傅欣荣 <br>
     * date：2020/3/30 13:42 <br>
     * @param
     * @return
     */
    public QueryResults getOrderPaxInfo(PaxInfoQueryDto paxInfoQueryDto){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("SELECT");
        sql.append(" O.ORDER_ID orderId,O.ACCIDENT_ID accidentId,replace(PI.SEGMENT ,' ','') segment,PI.SWITCH isSwitch,");
        sql.append(" PI.PAX_ID paxId,PI.PAX_NAME paxName,PI.CURRENT_AMOUNT payMoney,PI.RECEIVE_STATUS receiveStatus,");
        sql.append(" PI.WITH_BABY isInfant,");
        sql.append(" PI.IS_CHILD isChild,");
        sql.append(" get_dp_order_receivecount(O.ORDER_ID,NULL) - GET_DP_ORDER_CHILDCOUNT(O.ORDER_ID)-GET_DP_ORDER_BABYCOUNT(O.ORDER_ID)||");
        sql.append(" '/'||GET_DP_ORDER_CHILDCOUNT(O.ORDER_ID)||");
        sql.append(" '/'||GET_DP_ORDER_BABYCOUNT(O.ORDER_ID) AS membersCount");
        sql.append(" FROM DP_ORDER_INFO O");
        sql.append(" LEFT JOIN DP_PAX_INFO PI ON o.ORDER_ID = pi.ORDER_ID");
        sql.append(" WHERE O.ORDER_ID = :orderId ");

        paramsMap.put("orderId",paxInfoQueryDto.getOrderId());
        String keySearch = paxInfoQueryDto.getKeySearch();
        // String segment = paxInfoQueryDto.getSegment().replace(" ", "");
        String receiveStatus = paxInfoQueryDto.getReceiveStatus();
        String isSwitch = paxInfoQueryDto.getIsSwitch();
        if(StringUtils.isNotBlank(keySearch)){
            paramsMap.put("keySearch", "%" + keySearch + "%");
            sql.append(" AND PI.PAX_NAME LIKE :keySearch ");
        }
        // 这样写不是有注入的风险么，此处直接删掉这个条件，其实只需要orderid都可以查询出对应的信息。
    //    if(StringUtils.isNotBlank(segment)){
    //        sql.append(" AND  replace(PI.SEGMENT ,' ','') ");
    //        sql.append(" in (SELECT REGEXP_SUBSTR('"+segment+"','[^,]+', 1, LEVEL) FROM DUAL ");
    //        sql.append(" connect by regexp_substr('"+segment+"', '[^,]+', 1, level) is not null)");
    //    }


        if(StringUtils.isNotBlank(receiveStatus)){
            paramsMap.put("receiveStatus", receiveStatus);
            sql.append(" AND  PI.RECEIVE_STATUS= :receiveStatus ");
        }
        if(StringUtils.isNotBlank(isSwitch)){
            paramsMap.put("switch", isSwitch);
            sql.append(" AND PI.SWITCH= :switch ");
        }
        sql.append(" ORDER BY PI.RECEIVE_STATUS,NLSSORT(PI.PAX_NAME,'NLS_SORT = SCHINESE_PINYIN_M')");
        sql.append(" ,PI.SEGMENT,PI.CURRENT_AMOUNT");

        return baseDAO.findBySQLPage_comm(sql.toString(),
                paxInfoQueryDto.getCurrent(),
                paxInfoQueryDto.getPageSize(),
                paramsMap,PaxInfoQueryResultVo.class);
    }



    /**
     * Title：freezeOrderPax <br>
     * Description： 冻结服务单旅客<br>
     * author：傅欣荣 <br>
     * date：2020/3/30 14:18 <br>
     * @param orderId 服务单id
     * @param paxIds 旅客id
     * @param isSwitch 冻结状态 默认0有资格，1取消领取资格
     * @return
     */
    public void updOrderPaxFreeze(String orderId, String[] paxIds,String isSwitch) {
        for (String paxId:paxIds){
            StringBuffer sql = new StringBuffer();
            sql.append("UPDATE DP_PAX_INFO SET SWITCH = ? ");
            sql.append("WHERE ORDER_ID = ? AND PAX_ID = ? ");
            baseDAO.batchUpdate(sql.toString(),isSwitch,orderId,paxId);
        }
    }
}
