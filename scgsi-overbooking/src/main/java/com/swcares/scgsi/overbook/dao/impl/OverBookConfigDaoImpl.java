package com.swcares.scgsi.overbook.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.overbook.vo.LowestStandInfo;
import com.swcares.scgsi.overbook.vo.RefundInfoVo;
import com.swcares.scgsi.overbook.vo.TimeDifferenceInfoVo;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.audit.dao.impl <br>
 * Description：超售配置 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月19日 10:55 <br>
 * @version v1.0 <br>
 */
@Repository
public class OverBookConfigDaoImpl {

    @Resource
    BaseDAO baseDAO;


    /**
     * 超售改签|退票 - 配置比例集合查询
     */
    public List<TimeDifferenceInfoVo> getTimeDifferenceInfo(String type){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT O.ID id,O.OVER_WEIGHT_TKTNO overWeightTktno,");
        sql.append(" O.CONDITION_VALUE conditionValue,");
        sql.append(" O.D_MONEY dMoney,");
        sql.append(" O.I_MONEY iMoney,");
        sql.append(" O.DAMAGE_PART damagePart");
        sql.append(" FROM DP_OVERBOOK_CONFIG_INFO O");
        sql.append(" WHERE O.STATUS = 0 AND O.TYPE = :type");
        sql.append(" AND O.CONDITION_MONEY IS NULL ");
        sql.append(" ORDER BY O.OVER_WEIGHT_TKTNO,O.CONDITION_VALUE ASC");
        paramsMap.put("type",type);
        return (List<TimeDifferenceInfoVo>) baseDAO.findBySQL_comm(sql.toString(),paramsMap, TimeDifferenceInfoVo.class);
    }

    /**
     * 改签最低赔付 isLowest true 只查最低赔付
     */
    public LowestStandInfo getLowestStandInfo(boolean isLowest){
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT CONDITION_MONEY conditionMoney,PAY_MONEY payMoney,PAY_TYPE payType");
        sql.append(" FROM DP_OVERBOOK_CONFIG_INFO");
        sql.append(" WHERE  STATUS = 0 AND CONDITION_MONEY IS NOT NULL");
        if(isLowest){
            sql.append(" AND PAY_TYPE = 1  ");
        }
        sql.append(" AND TYPE = 0");
        return baseDAO.findOneBySql(sql.toString() ,null,LowestStandInfo.class);
    }

    /**
     * 退票最低赔付 isLowest true 只查最低赔付
     */
    public RefundInfoVo getRefundInfo(boolean isLowest){
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT O.DAMAGE_PART damagePart ,nvl2(O2.payType,O2.payType,O.PAY_TYPE) payType,");
        sql.append(" O.D_MONEY dMoney,");
        sql.append(" O.I_MONEY iMoney,");
        sql.append("  O2.CONDITION_MONEY conditionMoney,O2.PAY_MONEY payMoney");
        sql.append(" FROM DP_OVERBOOK_CONFIG_INFO O ");
        sql.append(" LEFT JOIN(SELECT ID RID ,CONDITION_MONEY,PAY_MONEY ,PAY_TYPE payType,TYPE ");
        sql.append(" FROM DP_OVERBOOK_CONFIG_INFO ");
        sql.append(" WHERE  STATUS = 0  ");
        //最低赔付
        if(isLowest){
            sql.append(" AND PAY_TYPE = 1  ");
        }
        sql.append(" AND CONDITION_MONEY IS NOT NULL)O2 ");
        sql.append(" ON O2.TYPE = O.TYPE  ");
        sql.append(" WHERE O.TYPE = 1 AND O.CONDITION_MONEY IS NULL AND O.STATUS = 0 ");
        return baseDAO.findOneBySql(sql.toString() ,null, RefundInfoVo.class);
    }
}
