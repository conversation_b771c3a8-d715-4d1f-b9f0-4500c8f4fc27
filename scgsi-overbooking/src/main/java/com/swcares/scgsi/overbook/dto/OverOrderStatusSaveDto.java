package com.swcares.scgsi.overbook.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.overBook.dto <br>
 * Description：去发放 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 04月07日 11:44 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "H5赔偿单-去发放")
public class OverOrderStatusSaveDto {

    @ApiModelProperty(value = "赔偿单id", required = true)
    private String orderIds;
    /**
     * 3确认发放4关闭
     */
    @ApiModelProperty(value = "3确认发放,4关闭", required = true)
    private String status;

    /**
     * 不正常航班赔偿0、异常行李1、超售旅客2、
     */
    private String payType;
}
