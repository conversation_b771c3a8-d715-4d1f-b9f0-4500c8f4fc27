package com.swcares.scgsi.overbook.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.dto <br>
 * Description：查询旅客超售列表信息-前端查询参数 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月13日 15:57 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "H5超售列表查询")
public class OverBookQueryDto {

    /**
     * 航班号
     */
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    /**
     * 航班起始日期
     */
    @ApiModelProperty(value = "航班起始日期")
    private String startDate;

    /**
     * 航班结束日期
     */
    @ApiModelProperty(value = "航班结束日期")
    private String endDate;

    /**
     * 类型 1改签,2退票
     */
    @ApiModelProperty(value = "超售类型")
    private String type;

    /**
     * 模糊搜索 支持旅客姓名，事故单号
     */
    @ApiModelProperty(value = "模糊搜索")
    private String keySearch;

    /**
     * 领取状态 1未领取,2已领取,3已逾期
     */
    @ApiModelProperty(value = "领取状态")
    private String receiveStatus;

    /**
     * 服务航站
     */
    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    /**
     * 审核状态 0草稿、1审核中、2通过、3生效、4关闭,5未通过,6驳回,7待审核
     */
    @ApiModelProperty(value = "审核状态")
    private String orderStatus;

    /**
     * 数据类型 0 全部 1本人发起
     */
    @ApiModelProperty(value = "数据类型")
    private String dataType;

    /** 当前页数，默认为第一页 **/
    @ApiModelProperty(value = "当前页数")
    private int current = 1;

    /** 每页显示记录数，默认为10条 **/
    @ApiModelProperty(value = "每页显示记录数")
    private int pageSize = 10;
}
