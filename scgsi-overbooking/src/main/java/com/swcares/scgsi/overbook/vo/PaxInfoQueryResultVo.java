package com.swcares.scgsi.overbook.vo;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.vo <br>
 * Description：H5-服务单旅客信息查询返回对象 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月30日 11:11 <br>
 * @version v1.0 <br>
 */
@Data
public class PaxInfoQueryResultVo {

    /**
     * 服务单id
     */
    private String orderId;
    /**
     * 事故单号
     */
    private String accidentId;

    /**
     * 旅客id
     */
    private String paxId;

    /**
     * 旅客姓名
     */
    private String paxName;

    /**
     * 航段
     */
    private String segment;

    /**
     * 赔偿金额
     */
    private String payMoney;

    /**
     * 领取状态 1未领取,2已领取,3已逾期
     */
    private String receiveStatus;
    /**
     * 冻结状态 旅客申领资格开关(默认0有资格，1取消领取资格)
     */
    private String isSwitch;

    /**
     * 服务单旅客数量 xx成人/xx儿童/xx婴儿
     */
    private String membersCount;

    /**
     * 携带婴儿标识
     */
    private String isInfant;

    /**
     * 儿童标识
     */
    private String isChild;
}
