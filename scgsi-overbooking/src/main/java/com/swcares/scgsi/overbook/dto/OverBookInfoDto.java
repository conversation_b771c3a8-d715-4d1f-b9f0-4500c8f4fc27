package com.swcares.scgsi.overbook.dto;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.dto <br>
 * Description：旅客改签信息 - 旅客退票信息 提交参数<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月15日 14:30 <br>
 * @version v1.0 <br>
 */
@Data
public class OverBookInfoDto {

    /**
     * 服务单id 草稿-提交 需要
     */
    private String orderId;
    /**
     * 事故单 草稿-提交 需要
     */
    private String overId;

    /**
     * 0提交默认 1保存
     */
    private String isCommit;
    ///----------提交--------------
    /**
     * 原航班id
     */
    private String flightId;

    /**
     * 旅客id
     */
    private String paxId;

    /**
     * 是否婴儿
     */
    private String isInfant;

    /**
     * 是否是儿童	Y-是
     */
    private String isChild;

    /**
     * 类型 0改签,1退票
     */
    private String type;

    /**
     * 改签航班号
     */
    private String overBookFlightNo;

    /**
     * 改签航班日期
     */
    private String overBookFlightDate;

    /**
     * 改签航班的计划起飞时间
     */
    private String planeDate;

    /**
     * 旅客原航班票价（不含机建燃油费）
     */
    private String price;

    /**
     * 附件URL
     */
    private String imgUrl;

    /**
     * 赔偿金额
     */
    private String payMoney;

    /**
     * 乘机人手机号
     */
    private String telephone;

    /**
     * 服务航站
     */
    private String serviceCity;


    //-----------退票
    /**
     * 客票价 退票才有
     */
    private String priceSpread;
//---------------以下参数不必传---------------------------------------

    /**
     * 原航班日期 不必传
     */
    private String flightDate;
    /**
     * 原航班计划起飞时间 不必传
     */
    private String oldPlaneDate;


    /**
     * 原航班航段  不必传
     */
    private String segment;

    /**
     * 国内国际
     */
    private String dOrI;

}
