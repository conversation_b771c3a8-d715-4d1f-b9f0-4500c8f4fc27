package com.swcares.scgsi.overbook.vo;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.vo <br>
 * Description：调用Trace接口返回对象 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月22日 12:14 <br>
 * @version v1.0 <br>
 */
@Data
public class TracePaxInfoVo {

    /**
     * 航班号
     */
    private String flightNum;

    /**
     * 航班日期	2020-01-10
     */
    private String flightDate;

    /**
     * 始发地三字码
     */
    private String orig;

    /**
     * 航段
     */
    private String segment;

    /**
     * 达到地三字码
     */
    private String dest;

    /**
     * 主舱位
     */
    private String mainClass;

    /**
     * 销售舱位
     */
    private String sellClass;

    /**
     * 旅客id（唯一）
     */
    private String idx;

    /**
     * 旅客姓名
     */
    private String psgName;

    /**
     * 性别	M-男，F-女，C-儿童，仅供参考
     */
    private String gender;

    /**
     * 证件类型	1表示身份证，仅供参考
     */
    private String idType;

    /**
     * 旅客状态 AC-值机，CL-订座取消，XR-直接取消等
     */
    private String status;

    /**
     * 证件号
     */
    private String idNum;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 票号
     */
    private String etNum;

    /**
     * 出票时间
     */
    private String printTicketTime;

    /**
     * 是否是儿童	Y-是
     */
    private String isChild;
    /**
     * 是否是婴儿	1-是
     */
    private String isInfant;

    /**
     * 婴儿姓名
     */
    private String infantName;

    /**
     * 是婴儿时为婴儿的编号
     */
    private String infantIdx;

    /**
     * 行李编号	null-无
     * 3324627256/SZX-只有一个
     * 3324428308/SZX,3324708656/SZX-多个
     */
    private String bagTag;

    /**
     * 行李重量	10
     */
    private String bagWht;

    /**
     * pnr
     */
    private String pnr;

}
