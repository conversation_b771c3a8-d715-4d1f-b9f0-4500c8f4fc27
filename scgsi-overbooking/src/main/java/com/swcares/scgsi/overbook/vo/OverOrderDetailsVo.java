package com.swcares.scgsi.overbook.vo;

import lombok.Data;

import java.util.List;

/**
 * ClassName：com.swcares.scgsi.audit.vo <br>
 * Description：超售列表-超售信息 - 赔偿单详情 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月20日 10:37 <br>
 * @version v1.0 <br>
 */
@Data
public class OverOrderDetailsVo {

    /**
     * 赔偿单号
     */
    private String orderId;

    /**
     * 赔偿单类型
     */
    private String type;

    /**
     * 航段
     */
    private String segment;

    /**
     * 申请人
     */
    private String applyUser;

    /**
     * 申请时间
     */
    private String applyTime;


    /**
     * 终审核人姓名
     */
    private String auditor;

    /**
     * 审核时间
     */
    private String auditorTime;

    // 补偿标准
    private List<CompensateInfoVo> compensateInfo;

}
