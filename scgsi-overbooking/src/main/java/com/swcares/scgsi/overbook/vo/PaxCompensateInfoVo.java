package com.swcares.scgsi.overbook.vo;

import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.vo <br>
 * Description：H5模块-旅客赔偿 列表查询返回参数对象 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月27日 10:16 <br>
 * @version v1.0 <br>
 */
@Data
public class PaxCompensateInfoVo {

    /**
     * 赔偿单号
     */
    private String orderId;

    /**
     * 事故单号
     */
    private String accidentId;

    /**
     * 服务航站
     */
    private String serviceCity;

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班日期
     */
    private String flightDate;

    /**
     * 状态0草稿、1审核中、2通过、3生效、4关闭,5未通过 6驳回 7待审核
     */
    private String orderStatus;

    /**
     * 赔偿类型 不正常航班赔偿0、异常行李1、超售旅客2、
     */
    private String  payType;

    /**
     * 发起人
     */
    private String applyUser;
    private String createUser;
    private String createId;
    private String applyTime;
    private String createDate;
    private String autoCreate;

}
