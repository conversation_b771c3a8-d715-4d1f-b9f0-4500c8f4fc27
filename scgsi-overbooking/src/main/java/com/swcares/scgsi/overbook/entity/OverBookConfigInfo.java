package com.swcares.scgsi.overbook.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.audit.entity <br>
 * Description：超售规则配置表<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月15日 16:00 <br>
 * @version v1.0 <br>
 */
@Entity
@Data
@Table(name = "DP_OVERBOOK_CONFIG_INFO")
public class OverBookConfigInfo {

    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    /**
     * 航班类型，D国内 赔偿金额
     */
    @Column(name = "D_MONEY")
    private String dMoney;

    /**
     * 航班类型，I国际 赔偿金额
     */
    @Column(name = "I_MONEY")
    private String iMoney;

    /**
     * 类型0改签,1退票
     */
    @Column(name = "TYPE")
    private String type;

    /**
     * 赔付标准类型： 1最低赔付 0比例赔付
     */
    @Column(name = "PAY_TYPE")
    private String payType;

    /**
     * 条件金额
     */
    @Column(name = "CONDITION_MONEY")
    private String conditionMoney;

    /**
     * 应赔付金额
     */
    @Column(name = "PAY_MONEY")
    private String payMoney;

    /**
     * 表达式
     */
    @Column(name = "OVER_WEIGHT_TKTNO")
    private String overWeightTktno;

    /**
     * 条件值 针对比例赔付
     */
    @Column(name = "CONDITION_VALUE")
    private String  conditionValue;

    /**
     * 赔付比例整数
     */
    @Column(name = "DAMAGE_PART")
    private String damagePart;

    /**
     * 状态0默认值 1已作废
     */
    @Column(name = "STATUS")
    private String status;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 修改人
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

}
