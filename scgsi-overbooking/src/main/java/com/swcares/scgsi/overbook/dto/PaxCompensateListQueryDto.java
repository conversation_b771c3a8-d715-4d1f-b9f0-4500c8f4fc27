package com.swcares.scgsi.overbook.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.audit.dto <br>
 * Description：H5-旅客赔偿-列表查询请求参数 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月27日 10:22 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "消息发送表单对象")
public class PaxCompensateListQueryDto {

    /**
     * 数据类型  1本人发起
     */
    @ApiModelProperty(value = "数据类型")
    private String queryType;

    /**
     * 用户
     */
    @ApiModelProperty(value = "用户")
    private String userId;

    /**
     * 服务航站
     */
    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    /**
     * 赔偿单号
     */
    @ApiModelProperty(value = "赔偿单号")
    private String orderId;

    /**
     * 赔偿类型 不正常航班赔偿0、异常行李1、超售旅客2、
     */
    @ApiModelProperty(value = "赔偿类型")
    private String  payType;

    /**
     * 赔偿单状态 0草稿、1审核中、2通过、3生效、4关闭,5未通过,6驳回,7待审核
     */
    @ApiModelProperty(value = "赔偿单状态")
    private String orderStatus;

    /**
     * 航班号
     */
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    /**
     * 航班起始日期
     */
    @ApiModelProperty(value = "startDate")
    private String startDate;

    /**
     * 航班结束日期
     */
    @ApiModelProperty(value = "endDate")
    private String endDate;

    /** 当前页数，默认为第一页 **/
    @ApiModelProperty(value = "current")
    private int current = 1;

    /** 每页显示记录数，默认为10条 **/
    @ApiModelProperty(value = "pageSize")
    private int pageSize = 10;
}
