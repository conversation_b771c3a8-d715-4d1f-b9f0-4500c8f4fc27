<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<name>scgsi</name>
	<description>Ground support of Shandong Airlines</description>

	<groupId>com.swcares</groupId>
	<artifactId>scgsi</artifactId>
    <version>1.1.20</version>
	<packaging>pom</packaging>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.5.14</version>
	</parent>

	<!-- 模块列表 -->
	<modules>
		<!--三方模块（比如同步人力、短接接口、登录接口等） -->
		<module>scgsi-third</module>
		<!--数据模块（foc\trace） -->
		<module>scgsi-data</module>
		<!--支付模块 -->
		<module>scgsi-pay</module>
		<!--公共模块 -->
		<module>scgsi-common</module>
		<!--审核模块 -->
		<module>scgsi-activiti</module>
		<!--不正常航班模块 -->
		<module>scgsi-irregular-flight</module>
		<!--行李模块 -->
		<module>scgsi-luggage</module>
		<!--超售模块 -->
		<module>scgsi-overbooking</module>
		<!--异常模块 -->
		<module>scgsi-exception</module>
		<!--后台应用 -->
		<module>scgsi-web</module>
		<!--对客应用 -->
		<module>scgsi-web-pssn</module>
		<!--权限验证 -->
		<module>scgsi-authority</module>
		<!--定时任务 -->
		<module>scgsi-quartz</module>
		<!--消息 -->
		<module>scgsi-message</module>
		<!--用户 -->
		<module>scgsi-user</module>
		<!--监控 -->
		<module>scgsi-admin</module>
		<!--山航的航延模块-->
		<module>scgsi-hotel-sda</module>
		<!--航延酒店 -->
		<module>scgsi-hotel</module>
	</modules>

	<properties>
		<sonar.projectKey>2025-IPRD-O-0002</sonar.projectKey>
		<sonar.projectName>山航地服系统2025年升级项目</sonar.projectName>
		<sonar.qualitygate.wait>true</sonar.qualitygate.wait>
		<tomcat.version>9.0.98</tomcat.version>
		<java.version>1.8</java.version>
		<skipTests>true</skipTests>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
		<jwt.version>0.9.0</jwt.version>
		<swagger2.version>2.8.0</swagger2.version>
		<thymeleaf.version>3.0.11.RELEASE</thymeleaf.version>
		<kaptcha.version>2.3.2</kaptcha.version>
		<alibaba.fastjson>1.2.62</alibaba.fastjson>
		<xerces.version>2.11.0</xerces.version>
		<hutool.all>5.3.2</hutool.all>
		<poi.version>3.17</poi.version>
		<commons.net>3.6</commons.net>
		<junit-version>3.8.2</junit-version>
	</properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- oracel连接驱动 -->
        <!--		<dependency>-->
        <!--			<groupId>com.oracle</groupId>-->
        <!--			<artifactId>ojdbc6</artifactId>-->
        <!--			<version>11.2.0.4.0</version>-->
        <!--		</dependency>-->
        <dependency>
            <groupId>cn.easyproject</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>11.2.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.1.10</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <version>1.18.28</version><!--$NO-MVN-MAN-VER$ -->
		</dependency>
		<!-- spring boot 由2.2.4升级到2.5.17后，默认不包含这个类了，需要单独引入 -->
		<dependency>
			 <groupId>org.springframework.boot</groupId>
			  <artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter</artifactId>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-exception</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-common</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-authority</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-data</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-message</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-third</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-user</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-quartz</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-irregular-flight</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-luggage</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-overbooking</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-pay</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-activiti</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-hotel-sda</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.swcares</groupId>
				<artifactId>scgsi-hotel</artifactId>
				<version>${project.version}</version>
			</dependency>
			<!-- JWT -->
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt</artifactId>
				<version>${jwt.version}</version>
			</dependency>
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger2</artifactId>
				<version>${swagger2.version}</version>
			</dependency>
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger-ui</artifactId>
				<version>${swagger2.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${alibaba.fastjson}</version>
			</dependency>
			<!-- Hutool工具类 -->
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-all</artifactId>
				<version>${hutool.all}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
			    <groupId>commons-net</groupId>
			    <artifactId>commons-net</artifactId>
			    <version>${commons.net}</version>
			</dependency>
			<!-- Hutool工具类结束 -->
			<!--验证码 -->
			<dependency>
				<groupId>com.github.penggle</groupId>
				<artifactId>kaptcha</artifactId>
				<version>${kaptcha.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

</project>

