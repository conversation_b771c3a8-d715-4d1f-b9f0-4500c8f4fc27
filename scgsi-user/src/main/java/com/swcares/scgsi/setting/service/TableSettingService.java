package com.swcares.scgsi.setting.service;

import java.util.List;
import java.util.Map;
import com.swcares.scgsi.setting.form.TableColumnsForm;

/**
 * ClassName：com.swcares.scgsi.setting.service.TableSettingService <br>
 * Description：提供给前端设置表格信息的服务 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年5月19日 上午11:41:53 <br>
 * @version v1.0 <br>
 */
public interface TableSettingService {
    
    /**
     * Title：saveTableColumns <br>
     * Description：存储当前用户的表格设置信息<br>
     * author：夏阳 <br>
     * date：2020年5月19日 下午1:13:40 <br>
     * @param tableColumnsForm <br>
     */
    void saveTableColumns(TableColumnsForm tableColumnsForm);
    
    /**
     * Title：getTableColumns <br>
     * Description：根据当前用户获取所有的table信息 <br>
     * author：夏阳 <br>
     * date：2020年5月19日 下午2:08:05 <br>
     * @return <br>
     */
    Map<String, List<String>> getTableColumns();
}
