package com.swcares.scgsi.setting.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.setting.form.TableColumnsForm;
import com.swcares.scgsi.setting.service.TableSettingService;
import com.swcares.scgsi.util.Asserts;
import com.swcares.scgsi.util.AuthenticationUtil;

/**
 * ClassName：com.swcares.scgsi.setting.service.impl.TableSettingServiceImpl <br>
 * Description：用于前端表格处理的实现类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年5月19日 下午1:15:17 <br>
 * @version v1.0 <br>
 */
@Service
public class TableSettingServiceImpl implements TableSettingService {

    @Autowired
    private RedisService redisService;

    /* 存储在redis中的table前缀，列入： TableColumns:2520585 **/
    private String TableColumnsPrefix = "TableSetting:Columns";

    @Override
    @SuppressWarnings("unchecked")
    public void saveTableColumns(TableColumnsForm tableColumnsForm) {
        // 参数校验
        Asserts.notNull(tableColumnsForm, MessageCode.PARAM_IS_NULL.getCode(),  new String[] {"表设置对象"});
        Asserts.notNull(tableColumnsForm.getTableColumns(), MessageCode.PARAM_IS_NULL.getCode(), new String[] {"表字段集合"});
        Asserts.notNull(tableColumnsForm.getTableName(), MessageCode.PARAM_IS_NULL.getCode(), new String[] {"表名"});

        // 获取当前用户的工号
       // String tuNo = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        String tuNo = "2520585";
        Asserts.notNull(tuNo, MessageCode.NOT_FIND_USER_INFO.getCode());

        // 从redis获取当前的存储信息
        Map<String, List<String>> map =
                (Map<String, List<String>>) redisService.hget(TableColumnsPrefix, tuNo);
        if (ObjectUtils.isEmpty(map)) {
            map = new HashMap<String, List<String>>();
        }

        map.put(tableColumnsForm.getTableName(), tableColumnsForm.getTableColumns());

        // 入缓存
        redisService.hset(TableColumnsPrefix, tuNo, map);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, List<String>> getTableColumns() {
        // 获取当前用户的工号
       // String tuNo = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        String tuNo = "2520585";
        Asserts.notNull(tuNo, MessageCode.NOT_FIND_USER_INFO.getCode());
        
        return (Map<String, List<String>>) redisService.hget(TableColumnsPrefix, tuNo);
    }

}
