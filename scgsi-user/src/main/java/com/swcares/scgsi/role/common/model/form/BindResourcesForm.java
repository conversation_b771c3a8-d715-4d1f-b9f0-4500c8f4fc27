package com.swcares.scgsi.role.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：BindResources <br>
 * Package：com.swcares.scgsi.role.common.model.form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月22日 14:18 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "资源绑定表单")
public class BindResourcesForm {

    @ApiModelProperty(value = "角色ID")
    @NotNull(message = "角色ID不能为空")
    private String roleId;

    @ApiModelProperty(value = "资源ID集合")
    private List<String> resource;

}
