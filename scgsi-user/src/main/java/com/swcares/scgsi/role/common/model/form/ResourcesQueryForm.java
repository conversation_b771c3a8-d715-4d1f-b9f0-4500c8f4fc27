package com.swcares.scgsi.role.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ResourcesQueryForm <br>
 * Package：com.swcares.scgsi.role.common.model.form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月08日 11:08 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "资源列表查询表单")
public class ResourcesQueryForm {

    @ApiModelProperty(value = "通过key查询资源信息")
    private String key;

    @ApiModelProperty(value = "模块名称")
    private String name;

    @ApiModelProperty(value = "模块类型:0组件 1页面 2菜单")
    private Integer types;

    // 分页相关
    @ApiModelProperty(value = "当前页码")
    @Min(value = 1,message = "当前页码必须从1开始!")
    @NotNull(message = "当前页码为必填项!")
    private Integer current;

    @ApiModelProperty(value = "每页显示记录数")
    @NotNull(message = "每页显示记录数为必填项!")
    private Integer pageSize;
}
