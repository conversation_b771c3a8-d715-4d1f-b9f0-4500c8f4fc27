package com.swcares.scgsi.role.common.model.view;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：RoleListView <br>
 * Package：com.swcares.scgsi.role.common.model.view <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月07日 18:03 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "角色列表查询前端显示信息")
public class RoleListView {

    /** 人数 */
    @ApiModelProperty(value = "人数")
    private String counts;

    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String id;

    /** 角色代码 */
    @ApiModelProperty(value = "角色代码")
    private String code;

    /** 角色名称 */
    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "角色类型")
    private String competence;

    /** 角色描述 */
    @ApiModelProperty(value = "角色描述")
    private String description;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    private String status;

    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String founder;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    /** 最后修改人 */
    @ApiModelProperty(value = "最后修改人")
    @Column(name="MODIFY_PERSON")
    private String modifyPerson;

    /** 最后修改时间 */
    @ApiModelProperty(value = "最后修改时间")
    @Column(name="MODIFY_TIME")
    private String modifyTime;

    @ApiModelProperty(value = "角色类型名称")
    private String competenceName;

    /** 状态 */
    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "部门ID")
    @Column(name = "DEPARTMENT_ID")
    private String departmentId;


    public String getCompetenceName() {
        if ("0".equals(this.competence)){
            return this.competenceName = "管理员";
        }
        return this.competenceName = "普通";
    }

    public String getStatusName() {
        if ("0".equals(this.status)) {
            return this.statusName = "停用";
        }
        return this.statusName = "启用";
    }
}
