package com.swcares.scgsi.role.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：RoleAddForm <br>
 * Package：com.swcares.scgsi.role.common.model.form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月09日 15:13 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "新增角色表单")
public class RoleAddForm {

    @ApiModelProperty(value = "第一步")
    private roleForm role;

    @ApiModelProperty(value = "功能权限ID")
    private List<String> resourceIds;

    @ApiModelProperty(value = "用户ID")
    private List<String> userIds;

    @Data
    @ApiModel(value = "新增角色第一步表单")
    public static class roleForm{

        @ApiModelProperty(value = "角色ID")
        private String roleId;

        @ApiModelProperty(value = "角色名称")
        private String name;

        @ApiModelProperty(value = "状态")
        private String status;

        @ApiModelProperty(value = "角色类型:0管理员 1普通用户")
        private String competence;

        @ApiModelProperty(value = "部门toId")
        private String departmentId;

        @ApiModelProperty(value = "角色描述")
        private String description;
    }

}
