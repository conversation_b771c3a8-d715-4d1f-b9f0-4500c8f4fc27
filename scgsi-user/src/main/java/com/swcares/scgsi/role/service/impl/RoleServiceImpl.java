package com.swcares.scgsi.role.service.impl;

import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.vo.ModuleTreeVO;
import com.swcares.scgsi.dao.ResourcesDao;
import com.swcares.scgsi.entity.Resources;
import com.swcares.scgsi.entity.Role;
import com.swcares.scgsi.entity.RoleResources;
import com.swcares.scgsi.entity.UserRole;
import com.swcares.scgsi.hum.department.entity.Department;
import com.swcares.scgsi.hum.employee.entity.Employee;
import com.swcares.scgsi.role.common.enums.RoleTypes;
import com.swcares.scgsi.role.common.model.form.BindResourcesForm;
import com.swcares.scgsi.role.common.model.form.RoleAddForm;
import com.swcares.scgsi.role.common.model.form.RoleListQueryForm;
import com.swcares.scgsi.role.common.model.form.RoleUpdateForm;
import com.swcares.scgsi.role.common.model.view.RoleManagerUserView;
import com.swcares.scgsi.role.common.model.vo.RoleManagerUserVO;
import com.swcares.scgsi.role.common.model.vo.RoleResourceTreeVo;
import com.swcares.scgsi.role.dao.ResourcesRepository;
import com.swcares.scgsi.role.dao.RoleDao;
import com.swcares.scgsi.role.dao.RoleRepository;
import com.swcares.scgsi.role.dao.UserRepository;
import com.swcares.scgsi.role.service.ResourceService;
import com.swcares.scgsi.role.service.RoleService;
import com.swcares.scgsi.user.common.model.view.UsersInfoView;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.util.IdWorker;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：RoleServiceImpl <br>
 * Package：com.swcares.scgsi.role.service.impl <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月07日 14:19 <br>
 * @version v1.0 <br>
 */
@Service
public class RoleServiceImpl implements RoleService {

    @Resource
    private RoleDao roleDao;

    @Resource
    private ResourcesDao resourcesDao;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private UserRepository userRepository;

    @Autowired
    private IdWorker idWorker;

    @Autowired
    private ResourceService resourceService;

    /**
     * Title：getRoleList() <br>
     * Description：web端根据条件查询角色列表 <br>
     * author：于琦海 <br>
     * date：2020/4/7 14:23 <br>
     * @param form RoleListQueryForm
     * @return List<Role>
     */
    @Override
    public QueryResults getRoleList(RoleListQueryForm form,Authentication authentication) {
        StringBuilder sql = new StringBuilder("select GET_ROLE_USER_COUNT(t.ID) as COUNTS,t.* from  ROLE t WHERE 1 = 1 ");
        Map<String, Object> parameters = new HashMap<>();
        if (StringUtils.isNotBlank(form.getCode())) {
            sql.append("AND t.CODE like :code ");
            parameters.put("code", "%" + form.getCode() + "%");
        }
        if (StringUtils.isNotBlank(form.getName())) {
            sql.append("AND t.NAME like :name ");
            parameters.put("name", "%" + form.getName() + "%");
        }
        if (StringUtils.isNotBlank(form.getFounder())) {
            sql.append("AND t.FOUNDER like :founder ");
            parameters.put("founder", "%" + form.getFounder() + "%");
        }
        if (StringUtils.isNotBlank(form.getStatus())) {
            sql.append("AND t.STATUS =:status ");
            parameters.put("status", form.getStatus());
        }
        if (StringUtils.isNotBlank(form.getCompetence())) {
            sql.append("AND t.COMPETENCE =:competence ");
            parameters.put("competence", form.getCompetence());
        }
        if (StringUtils.isNotBlank(form.getModifyPerson())) {
            sql.append("AND t.MODIFY_PERSON like :modifyPerson ");
            parameters.put("modifyPerson", "%" + form.getModifyPerson() + "%");
        }
        String id = (String) authentication.getCredentials();
        if (StringUtils.isNotBlank(id)){
            StringBuilder departmentIdSql = new StringBuilder("SELECT D.TOID FROM DEPARTMENT D START WITH D.TOID in (" +
                    "SELECT R.DEPARTMENT_ID FROM ROLE R LEFT JOIN USER_ROLE UR ON R.ID = UR.ROLE_ID " +
                    "WHERE R.COMPETENCE='0' AND UR.EMPLOYEE_ID =:id ) CONNECT BY PRIOR D.TOID = D.TOPID");
            sql.append("AND t.DEPARTMENT_ID in ( ").append(departmentIdSql).append(")");
            parameters.put("id", id);
        }
        // 还得排除掉自己的管理员角色信息
        List<Role> roles = roleDao.getCompetenceRoleList(id);
        if (Objects.nonNull(roles) || roles.size() > 0) {
            sql.append("AND t.ID NOT IN ( SELECT R.ID FROM ROLE R LEFT JOIN USER_ROLE UR ON R.ID = UR.ROLE_ID " +
                    "WHERE UR.EMPLOYEE_ID =:userId and R.COMPETENCE = '0') ");
            parameters.put("userId",id);
        }
        sql.append(" ORDER BY t.MODIFY_TIME DESC,t.CREATETIME DESC");
        // 获取当前用户的管理员角色的部门ID
        return roleDao.getRoleList(sql.toString(), parameters, form.getCurrent(), form.getPageSize());
    }

    /**
     * Title：getUserByRoleID() <br>
     * Description：根据角色ID查询用户集合 <br>
     * author：于琦海 <br>
     * date：2020/4/7 18:28 <br>
     * @param roleId String
     * @return List<User>
     */
    @Override
    public List<RoleManagerUserView.View> getUserByRoleID(String roleId) {
        StringBuilder sql =
                new StringBuilder(
                        "select e.ID, e.TU_CNAME as TUCNAME, d.TOFNAME as TOFNAME ,d.ID as DEPARTMENT "
                                + "from EMPLOYEE e left join DEPARTMENT d on e.TOID = d.TOID "
                                + "where e.ID IN (select u.EMPLOYEE_ID from USER_ROLE u where ROLE_ID =:roleId)");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("roleId", roleId);
        List<RoleManagerUserVO> result = roleDao.getUserByRoleID(sql.toString(), parameters);
        return this.changeToUserView(result);
    }

    /**
     * Title：getResourceByRoleID() <br>
     * Description：通过RoleID查询资源信息 <br>
     * author：于琦海 <br>
     * date：2020/4/9 10:29 <br>
     * @param roleId String
     * @return ModuleTreeView
     */
    @Override
    public List<ModuleTreeVO> getResourceByRoleID(String roleId) {
        // 通过roleId查询到role—resource中间表的resourceID，通过resourceID查询所有resouces，内存转换为树行结构
        List<ModuleTreeVO> resources = resourcesDao.getResourcesByRoleID(roleId);
        return getResourceTree(resources);
    }

    /**
     * Title：getResourceTree <br>
     * Description：获取资源树 <br>
     * author：于琦海 <br>
     * date：2020/4/29 14:30 <br>
     * @param resources List<ModuleTreeVO>
     * @return List<ModuleTreeVO>
     */ 
    public List<ModuleTreeVO> getResourceTree(List<ModuleTreeVO> resources) {
        Map<String, List<ModuleTreeVO>> sub =
                resources.stream().filter(node -> !node.getPid().equals("@ROOT"))
                        .collect(Collectors.groupingBy(node -> node.getPid()));
        resources.forEach(node -> node.setChildren(sub.get(node.getKey())));
        List<ModuleTreeVO> moduleTreeVOList = resources.stream().filter(node -> node.getPid().equals("@ROOT"))
                .collect(Collectors.toList());
        Map<String, List<ModuleTreeVO>> collect =
                moduleTreeVOList.stream().collect(Collectors.groupingBy(ModuleTreeVO::getPort));
        ModuleTreeVO web = new ModuleTreeVO();
        ModuleTreeVO h5 = new ModuleTreeVO();
        collect.forEach((key,value)->{
            if ("W".equals(key)) {
                web.setKey("W");
                web.setTitle("WEB端");
                web.setChildren(value);
            }
            if ("H".equals(key)) {
                h5.setKey("H");
                h5.setTitle("H5端");
                h5.setChildren(value);
            }
        });
        List<ModuleTreeVO> result = new ArrayList<>();
        if (StringUtils.isNotBlank(web.getTitle())){
            result.add(web);
        }
        if (StringUtils.isNotBlank(h5.getTitle())){
            result.add(h5);
        }
        return result;
    }

    /**
     * Title：isDisabled（） <br>
     * Description：角色停用启用 <br>
     * author：于琦海 <br>
     * date：2020/4/9 10:35 <br>
     * @param roleId String
     */
    @Override
    public void isDisabled(String roleId,Authentication authentication) {
        String userId = (String) authentication.getCredentials();
        // 如果是普通用户，就是当前角色的部门ID
        Employee tById = userRepository.findTById(userId);
        Role role = roleRepository.findTById(roleId);
        if ("1".equals(role.getStatus())) {
            role.setStatus("0");
        }else {
            role.setStatus("1");
        }
        role.setModifyPerson(tById.getTuNo()+tById.getTuCname());
        role.setModifyTime(DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS));
        roleRepository.save(role);
    }

    /**
     * Title：getFunctional() <br>
     * Description：新增角色中功能权限展示 <br>
     * author：于琦海 <br>
     * date：2020/4/9 13:32 <br>
     * @param: authentication Authentication
     * @return List<ModuleTreeVO>
     */
    @Override
    public List<ModuleTreeVO> getFunctional(String competence,Authentication authentication) {
        String id = (String) authentication.getCredentials();
        List<ModuleTreeVO> resources = resourcesDao.findRolesResourceByUid(competence,id);
        if (Objects.isNull(resources) || resources.size() <= 0) {
            throw new BusinessException(MessageCode.RESOURCE_NULL.getCode());
        }
        return getResourceTree(resources);
    }

    /**
     * Title：角色新增并且绑定用户和功能权限 <br>
     * Description：addRoleInfo() <br>
     * author：于琦海 <br>
     * date：2020/4/9 15:29 <br>
     * @param addForm RoleAddForm
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addRoleInfo(RoleAddForm addForm, Authentication authentication) {
        RoleAddForm.roleForm roleInfo = addForm.getRole();
        if (StringUtils.isNotBlank(roleInfo.getRoleId())){
            // 删除中间表role_resource
            resourceService.removeRolesBind(roleInfo.getRoleId());
            BindResourcesForm bindResourcesForm = new BindResourcesForm();
            bindResourcesForm.setRoleId(roleInfo.getRoleId());
            this.removeUsersBind(bindResourcesForm);
        }
        // 查看个人角色，看是否可以添加对应的角色权限
        String userId = (String) authentication.getCredentials();
        // 0管理员（限制1个员工），1普通(不限制)
        String competence = addForm.getRole().getCompetence();
        List<String> userIds = addForm.getUserIds();
        if (Objects.nonNull(userIds) && (competence.equals(RoleTypes.ADMIN.getCode()))) {
            if (userIds.size() > 1) {
                throw new BusinessException(MessageCode.ROLE_USER_EXCESS.getCode());
            }
            if (userIds.size() == 1) {
                // 通过用户ID查询角色，看是否是管理员角色类型
                List<Role> roleList = roleDao.getRoleList(userIds.get(0));
                if (Objects.nonNull(roleList) && roleList.size() > 0) {
                    roleList.forEach(role -> {
                        if (RoleTypes.ADMIN.getCode().equals(role.getCompetence())) {
                            throw new BusinessException(MessageCode.ROLE_USER_EXCESS.getCode());
                        }
                    });
                }
            }
        }
        // 不管怎么样都保存role信息
        Role role = this.putRoleEntity(addForm,userId);
        this.putRoleResources(addForm, role);
        // 如果没有UserId那么就不需要绑定user role表
        if(userIds.size()>0){
            this.putUserRole(addForm, role);
        }
    }

    /**
     * Title：findDepartmentById <br>
     * Description：通过用户ID查询角色中的部门ID集合 <br>
     * author：于琦海 <br>
     * date：2020/4/20 9:47 <br>
     * @param id String
     * @return List<String>
     */
    @Override
    public List<String> findDepartmentById(String id) {
        return roleDao.findDepartmentById(id);
    }

    @Override
    public List<RoleResourceTreeVo> findRoleResourceTreeVoByToId(String toId) {
        List<RoleResourceTreeVo> list = roleDao.findRoleResourceTreeVoByToId(toId);
        List<RoleResourceTreeVo> newList = new ArrayList<RoleResourceTreeVo>();
        for (RoleResourceTreeVo roleResourceTreeVo : list) {
            if(roleResourceTreeVo.getDepartmentId().equals(toId) && roleResourceTreeVo.getCompetence().equals(RoleTypes.ADMIN.getCode())){
//                list.remove(roleResourceTreeVo);
            }else{
                roleResourceTreeVo.setChildren((getResourceByRoleID(roleResourceTreeVo.getKey())));
                newList.add(roleResourceTreeVo);
            }
        }
        return newList;
    }

    @Override
    public List<RoleResourceTreeVo> findRoleResourceTreeVoByUserId(String userId) {
        List<RoleResourceTreeVo> list = roleDao.findRoleResourceTreeVoByUserId(userId);
        for (RoleResourceTreeVo roleResourceTreeVo : list) {
            roleResourceTreeVo.setChildren((getResourceByRoleID(roleResourceTreeVo.getKey())));
        }
        return list;
    }

    /**
     * Title：deleteRole <br>
     * Description：根据角色ID删除角色 <br>
     * author：于琦海 <br>
     * date：2020/4/21 15:47 <br>
     * @param roleId List<String>
     */
    @Override
    public void deleteRole(List<String> roleId) {
        if (roleId.size()>0){
            roleDao.deleteRole(roleId);
        }
        return;
    }

    /**
     * Title：updateRole <br>
     * Description：更新角色信息 <br>
     * author：于琦海 <br>
     * date：2020/4/22 15:06 <br>
     * @param authentication Authentication
     * @param form RoleAddForm.roleForm
     */
    @Override
    public void updateRole(RoleUpdateForm form, Authentication authentication) {
        String userId = (String) authentication.getCredentials();
        // 如果是普通用户，就是当前角色的部门ID
        Employee tById = userRepository.findTById(userId);
        Role role = roleRepository.findTById(form.getId());
        role.setModifyPerson(tById.getTuNo()+tById.getTuCname());
        role.setModifyTime(DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS));
        role.setName(form.getName());
        role.setStatus(form.getStatus());
        role.setCompetence(form.getCompetence());
        if (RoleTypes.COMMON.getCode().equals(form.getCompetence())){
            role.setDepartmentId(tById.getToId());
        }else {
            role.setDepartmentId(form.getDepartmentId());
        }
        role.setDescription(form.getDescription());
        roleRepository.save(role);
    }

    /**
     * Title：findUserByRoleId <br>
     * Description：通过roleId查询用户信息 <br>
     * author：于琦海 <br>
     * date：2020/4/22 15:38 <br>
     * @param roleId String
     * @return List<UsersInfoView>
     */
    @Override
    public List<UsersInfoView> findUserByRoleId(String roleId) {
        return roleDao.findUserByRoleId(roleId);
    }

    /**
     * Title：removeUsersBind <br>
     * Description：解除用户绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 15:56 <br>
     * @param form BindResourcesForm
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeUsersBind(BindResourcesForm form) {
        StringBuilder sql = new StringBuilder("DELETE FROM USER_ROLE WHERE ROLE_ID = ?");
        resourcesDao.removeRolesBind(sql.toString(),form.getRoleId());
        List<String> resource = form.getResource();
        if (Objects.nonNull(resource) && resource.size() > 0) {
            this.bindUsers(form);
        }
    }

    /**
     * Title：removeUsersBind <br>
     * Description：用户绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 15:56 <br>
     * @param form BindResourcesForm
     */
    @Transactional
    @Override
    public void bindUsers(BindResourcesForm form) {
        Role role = roleRepository.findTById(form.getRoleId());
        // ------------以下代码注释为部门过滤----------------
        //String departmentId = role.getDepartmentId();
        // 查询用户的部门和当前部门的所有子部门
        //List<Department> departments = roleDao.findSubDepartmentInfo(departmentId);
        /*List<String> toIds =
                departments.stream().map(department -> department.getToId()).collect(Collectors.toList());*/
        // 要区分普通和管理员角色，管理员不能有多个，存在的都不行
        List<UserRole> resources = new ArrayList<>();
        List<String> resource = form.getResource();
        // 查看用户是不是在当前的部门或者子部门中
       /* resource.forEach(str -> {
            Employee tById = userRepository.findTById(str);
            if (Objects.isNull(tById) || !toIds.contains(tById.getToId())) {
                throw new BusinessException(MessageCode.DEPARTMENT_ERROR.getCode());
            }
        });*/
        // 当前的用户如果是管理员权限
        if (RoleTypes.ADMIN.getCode().equals(role.getCompetence())) {
            // 管理员只能有一个
            if (Objects.nonNull(resource) && resource.size() == 1) {
                // 根据用户ID查询当前用户的管理员角色
                Role roleInfo = roleDao.findOneByUserId(resource.get(0), RoleTypes.ADMIN.getCode());
                if (Objects.nonNull(roleInfo)) {
                    throw new BusinessException(MessageCode.USER_BIND_ERROR.getCode());
                }
            } else {
                throw new BusinessException(MessageCode.ROLE_USER_EXCESS.getCode());
            }
        }
        resource.forEach(userRole -> {
            UserRole entity = new UserRole();
            entity.setRoleId(form.getRoleId());
            entity.setEmployeeId(userRole);
            resources.add(entity);
        });
        if (Objects.nonNull(resources) && resources.size() > 0) {
            resourcesDao.bindUsers(resources);
        }
    }

    /**
     * Title：putUserRole() <br>
     * Description：更新中间表 <br>
     * author：于琦海 <br>
     * date：2020/4/9 15:51 <br>
     * @param addForm RoleAddForm
     * @param role Role
     */
    private void putUserRole(RoleAddForm addForm, Role role) {
        List<String> userIds = addForm.getUserIds();
        List<UserRole> roles = new ArrayList<>();
        userIds.forEach(userId->{
            UserRole userRole = new UserRole();
            userRole.setEmployeeId(userId);
            userRole.setRoleId(role.getId());
            roles.add(userRole);
        });
        resourcesDao.bindUsers(roles);
    }

    /**
     * Title：putRoleResources() <br>
     * Description：更新中间表 <br>
     * author：于琦海 <br>
     * date：2020/4/9 15:51 <br>
     * @param addForm RoleAddForm
     * @param role Role
     */
    @Resource
    private ResourcesRepository resourcesRepository;
    private void putRoleResources(RoleAddForm addForm, Role role) {
        List<String> resourceIds = addForm.getResourceIds();
        resourceIds.removeIf(s -> "H".equals(s) || "W".equals(s));
        Set<String> setResources = new HashSet<>(resourceIds);
        Set<String> result = new HashSet<>();
        setResources.forEach(resourceStr->{
            Resources tById = resourcesRepository.findTById(resourceStr);
            if(Objects.isNull(tById)){
                return;
            }
            if ("@ROOT".equals(tById.getPid())){
                return;
            }
            List<Resources> subResources = resourcesDao.getRootResource(tById.getPid(),tById.getId());
            if (Objects.nonNull(subResources) && subResources.size()>0){
                subResources.forEach(resources -> result.add(resources.getId()));
            }
        });
        if (result.size()>0){
            setResources.addAll(result);
        }
        List<RoleResources> roleResourcesList = new ArrayList<>();
        setResources.forEach(resourceId ->{
            RoleResources roleResources = new RoleResources();
            roleResources.setResourcesID(resourceId);
            roleResources.setRoleID(role.getId());
            roleResourcesList.add(roleResources);
        });
        resourcesDao.bindResources(roleResourcesList);
    }

    /**
     * Title：putRoleEntity() <br>
     * Description：角色信息入库 <br>
     * author：于琦海 <br>
     * date：2020/4/9 15:51 <br>
     * @param addForm RoleAddForm
     * @param userId String
     * @return Role
     */
    @Transactional(rollbackFor = Exception.class)
    public Role putRoleEntity(RoleAddForm addForm, String userId) {
        // 如果是普通用户，就是当前角色的部门ID
        Employee tById = userRepository.findTById(userId);
        Role role = new Role();
        RoleAddForm.roleForm roleInfo = addForm.getRole();
        if (StringUtils.isNotBlank(roleInfo.getRoleId())){
            Role oldRoleInfo = roleRepository.findTById(roleInfo.getRoleId());
            role.setCode(oldRoleInfo.getCode());
            role.setModifyPerson(tById.getTuNo()+tById.getTuCname());
            role.setModifyTime(DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS));
            roleInfo.setRoleId(null);
            roleRepository.delete(oldRoleInfo);
        }else {
            role.setCode("R" + idWorker.nextId());
            role.setModifyPerson(tById.getTuNo()+tById.getTuCname());
            role.setModifyTime(DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS));
            role.setFounder(tById.getTuNo()+tById.getTuCname());
            role.setCreateTime(DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
        BeanUtils.copyProperties(roleInfo, role);
        if (RoleTypes.COMMON.getCode().equals(addForm.getRole().getCompetence())){
            Role oneByUserId = roleDao.findOneByUserId(userId,RoleTypes.ADMIN.getCode());
            role.setDepartmentId(oneByUserId.getDepartmentId());
        }
        return roleRepository.save(role);
    }

    /**
     * Title：changeToUserView <br>
     * Description：转换为树型结构 <br>
     * author：于琦海 <br>
     * date：2020/4/7 20:10 <br>
     * @param result List<RoleManagerUserVO>
     * @return:
     */
    private List<RoleManagerUserView.View> changeToUserView(List<RoleManagerUserVO> result) {
        Map<String, List<RoleManagerUserVO>> collect =
                result.stream().collect(Collectors.groupingBy(RoleManagerUserVO::getToFname));
        List<RoleManagerUserView.View> views = new ArrayList<>();
        collect.forEach((key,value)->{
            RoleManagerUserView.View view = new RoleManagerUserView.View();
            view.setTitle(key);
            RoleManagerUserVO roleManagerUserVO = value.get(0);
            view.setKey(roleManagerUserVO.getDepartment());
            views.add(view);
        });
        views.forEach(view -> {
            List<RoleManagerUserVO> roleManagerUserVOS = collect.get(view.getTitle());
            List<RoleManagerUserView.View> subViews = new ArrayList<>();
            roleManagerUserVOS.forEach(roleManagerUserVO -> {
                RoleManagerUserView.View subView = new RoleManagerUserView.View();
                subView.setKey(roleManagerUserVO.getId());
                subView.setTitle(roleManagerUserVO.getTuCname());
                subViews.add(subView);
            });
            view.setChildren(subViews);
        });
        return views;
    }
}
