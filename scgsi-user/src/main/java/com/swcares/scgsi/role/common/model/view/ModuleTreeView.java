package com.swcares.scgsi.role.common.model.view;

import com.swcares.scgsi.common.model.vo.ModuleTreeVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ModuleTreeView <br>
 * Package：com.swcares.scgsi.role.common.model.view <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月08日 16:48 <br>
 * @version v1.0 <br>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModuleTreeView {

    private String key;

    private String title;

    private String pid;

    private List<ModuleTreeVO> children;
}
