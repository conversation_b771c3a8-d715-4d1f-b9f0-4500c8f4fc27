package com.swcares.scgsi.role.service.impl;

import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.view.ResourcesView;
import com.swcares.scgsi.common.model.vo.ModuleTreeVO;
import com.swcares.scgsi.dao.ResourcesDao;
import com.swcares.scgsi.entity.Resources;
import com.swcares.scgsi.entity.RoleResources;
import com.swcares.scgsi.role.common.enums.ModelTypes;
import com.swcares.scgsi.role.common.model.form.BindResourcesForm;
import com.swcares.scgsi.role.common.model.form.ResourceAddForm;
import com.swcares.scgsi.role.common.model.form.ResourceUpdateForm;
import com.swcares.scgsi.role.common.model.form.ResourcesQueryForm;
import com.swcares.scgsi.role.dao.ResourcesRepository;
import com.swcares.scgsi.role.service.ResourceService;
import com.swcares.scgsi.role.service.RoleService;
import com.swcares.scgsi.util.IdWorker;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ResourceServiceImpl <br>
 * Package：com.swcares.scgsi.role.service.impl <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月08日 11:06 <br>
 * @version v1.0 <br>
 */
@Service
public class ResourceServiceImpl implements ResourceService {

    @Resource
    private ResourcesDao resourcesDao;

    @Resource
    private ResourcesRepository resourcesRepository;

    @Resource
    private RoleServiceImpl roleService;

    @Autowired
    private IdWorker idWorker;
    /**
     * Title：getResources() <br>
     * Description：获取资源列表 <br>
     * author：于琦海 <br>
     * date：2020/4/8 11:16 <br>
     * @param form ResourcesQueryForm
     * @return:
     */
    @Override
    public QueryResults getResources(ResourcesQueryForm form) {
        StringBuffer sql = new StringBuffer("select (CASE R.PORT WHEN 'H' THEN 'H5端' WHEN 'W' THEN 'WEB端' END )AS PORTNAME,R.PORT," +
                "R.ID, R.NAME, R.TYPES, R.STATUS, R.RESOURCE_URL, R.CONTAIN_URL, R.ICON, R.RESOURCE_CODE, R.PID, " +
                "R.REMARKS, R.COMPONENTS_ID,R.FUNCTIONAL FROM RESOURCES_SCGSI R WHERE 1 = 1 ");
        Map<String, Object> parameters = new HashMap<>();
        if (StringUtils.isNotBlank(form.getKey())) {
            if (form.getKey().equals("H")) {
                sql.append("AND R.PORT = 'H' ");
            } else if (form.getKey().equals("W")) {
                sql.append("AND R.PORT = 'W' ");
            } else {
                sql.append("AND R.PID =:key ");
                parameters.put("key", form.getKey());
            }
        }
        if (StringUtils.isNotBlank(form.getName())) {
            sql.append("AND R.NAME LIKE :name ");
            parameters.put("name", "%" + form.getName() + "%");
        }
        if (form.getTypes() != null) {
            sql.append("AND R.TYPES =:types");
            parameters.put("types", form.getTypes());
        }
        QueryResults queryResults = resourcesDao.getResources(sql.toString(), parameters, form.getCurrent(),
                form.getPageSize());
        List<ResourcesView> resources = (List<ResourcesView>) queryResults.getList();
        queryResults.setList(getResourcesViews(resources));
        return queryResults;
    }

    /**
     * Title：getResourcesViews <br>
     * Description：将数据转换为前端所需要的格式 <br>
     * author：于琦海 <br>
     * date：2020/5/7 16:19 <br>
     * @param resources List<ResourcesView>
     * @return List<ResourcesView>
     */
    private List<ResourcesView> getResourcesViews(List<ResourcesView> resources) {
        List<ResourcesView> resourceViews = new ArrayList<>();
        resources.forEach(resourceInfo -> {
            ResourcesView resourceView = new ResourcesView();
            BeanUtils.copyProperties(resourceInfo, resourceView);
            if (resourceInfo.getStatus()) {
                resourceView.setStatusName("启用");
            } else {
                resourceView.setStatusName("停用");
            }
            if ("0".equals(resourceInfo.getTypes())) {
                resourceView.setStatusName("组件");
            }
            if ("1".equals(resourceInfo.getTypes())) {
                resourceView.setStatusName("页面");
            }
            if ("2".equals(resourceInfo.getTypes())) {
                resourceView.setStatusName("菜单");
            }
            resourceView.setPidName(resourcesDao.getResourcesName(resourceInfo.getPid()));
            resourceViews.add(resourceView);
        });
        return resourceViews;
    }

    /**
     * Title：addResource() <br>
     * Description：新增资源 <br>
     * author：于琦海 <br>
     * date：2020/4/8 13:24 <br>
     * @param form ResourceAddForm
     * @param authentication Authentication
     */
    @Override
    public void addResource(ResourceAddForm form, Authentication authentication) {
        // 通过用户查询角色，
       // String userId = (String) authentication.getCredentials();
        Resources resources = new Resources();
        BeanUtils.copyProperties(form,resources);
        resources.setResourceCode(form.getResourceCode());
        if (ModelTypes.MENU.getCode().equals(form.getTypes())){
            resources.setPid("@ROOT");
        }
        if (!ModelTypes.MENU.getCode().equals(form.getTypes())){
            if (StringUtils.isBlank(form.getPid())){
                throw new BusinessException(MessageCode.BIND_RESOURCE_PID.getCode());
            }
        }
        resourcesRepository.save(resources);
    }

    /**
     * Title：updateResource() <br>
     * Description：修改资源 <br>
     * author：于琦海 <br>
     * date：2020/4/8 13:24 <br>
     * @param form ResourceUpdateForm
     */
    @Override
    public void updateResource(ResourceUpdateForm form) {
        Resources resources = new Resources();
        Resources tById = resourcesRepository.findTById(form.getId());
        BeanUtils.copyProperties(form,resources);
        resources.setPid(tById.getPid());
        resourcesRepository.save(resources);
    }

    /**
     * Title：batchDisabled（） <br>
     * Description：批量启用停用 <br>
     * author：于琦海 <br>
     * date：2020/4/8 14:24 <br>
     * @param id List<String>
     * @param status Integer
     */
    @Override
    public void batchDisabled(List<String> id, Boolean status) {
       resourcesDao.batchDisabled(id.toArray(),status);
    }

    /**
     * Title：deleteResource() <br>
     * Description：删除资源 <br>
     * author：于琦海 <br>
     * date：2020/4/8 16:14 <br>
     * @param id List<String>
     */
    @Override
    public void deleteResource(List<String> id) {
        resourcesRepository.deleteInBatch(resourcesRepository.findAllById(id));
    }

    /**
     * Title：getModuleTree（） <br>
     * Description：获取模块树 <br>
     * author：于琦海 <br>
     * date：2020/4/8 17:24 <br>
     * @return  List<ModuleTreeVO>
     */
    @Override
    public List<ModuleTreeVO> getModuleTree() {
        List<ModuleTreeVO> roots = resourcesDao.getModuleRoot();
        return roleService.getResourceTree(roots);
    }

    /**
     * Title：findResourcesByRoleId <br>
     * Description：通过角色ID获取资源集合 <br>
     * author：于琦海 <br>
     * date：2020/4/22 14:01 <br>
     * @param roleId String
     * @return  List<String>
     */
    @Override
    public List<String> findResourcesByRoleId(String roleId) {
        List<String> result = new ArrayList<>();
        List<ModuleTreeVO> resources = resourcesDao.getResourcesByRoleID(roleId);
        if (Objects.isNull(resources) || resources.size() <= 0) {
            throw new BusinessException(MessageCode.RESOURCE_NULL.getCode());
        }
        for (ModuleTreeVO resource : resources) {
            result.add(resource.getKey());
        }
        return result;
    }

    /**
     * Title：removeRolesBind <br>
     * Description：去除角色的资源绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 14:25 <br>
     * @param roleId String
     */
    @Transactional
    @Override
    public void removeRolesBind(String roleId) {
        StringBuilder sql = new StringBuilder("DELETE FROM ROLE_RESOURCES WHERE ROLE_ID = ?");
        resourcesDao.removeRolesBind(sql.toString(),roleId);
    }

    /**
     * Title：bindResources <br>
     * Description：资源绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 14:34 <br>
     * @param form BindResourcesForm
     */
    @Transactional
    @Override
    public void bindResources(BindResourcesForm form) {
        List<String> resourceString = form.getResource();
        if (resourceString.size() <= 0) {
            return;
        }
        resourceString.removeIf(s -> "H".equals(s) || "W".equals(s));
        Set<String> setResources = new HashSet<>(resourceString);
        Set<String> result = new HashSet<>();
        setResources.forEach(resourceStr->{
            Resources tById = resourcesRepository.findTById(resourceStr);
            if(Objects.isNull(tById)){
                return;
            }
            if ("@ROOT".equals(tById.getPid())){
                return;
            }
            List<Resources> subResources = resourcesDao.getRootResource(tById.getPid(),tById.getId());
            if (Objects.nonNull(subResources) && subResources.size()>0){
                subResources.forEach(resources -> {
                    result.add(resources.getId());
                });
            }
        });
        if (result.size()>0){
            setResources.addAll(result);
        }
        List<RoleResources> resources = new ArrayList<>();
        setResources.forEach(resourceId->{
            RoleResources roleResources = new RoleResources();
            roleResources.setRoleID(form.getRoleId());
            roleResources.setResourcesID(resourceId);
            resources.add(roleResources);
        });
        resourcesDao.bindResources(resources);
    }

}
