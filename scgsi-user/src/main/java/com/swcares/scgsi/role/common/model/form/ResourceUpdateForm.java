package com.swcares.scgsi.role.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ResourceUpdateForm <br>
 * Package：com.swcares.scgsi.role.common.model.form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月08日 13:32 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "资源新增表单")
public class ResourceUpdateForm {

    @ApiModelProperty(value = "资源ID")
    @NotBlank(message = "资源ID不能为空")
    private String id;

    /** 模块名称 */
    @ApiModelProperty(value = "模块名称")
    private String name;

    /** 模块类型，页面-菜单-资源 */
    @ApiModelProperty(value = "模块类型，0组件-1页面-2菜单")
    private Integer types;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    private Boolean status;

    /** URL */
    @ApiModelProperty(value = "URL")
    private String resourceUrl;

    /** 包含URL */
    @ApiModelProperty(value = "包含URL")
    private String containUrl;

    /** 图标ICON */
    @ApiModelProperty(value = "图标ICON")
    private String icon;

    @ApiModelProperty(value = "API路径")
    @NotBlank(message = "API路径不能为空")
    private String resourceCode;

    /** 端口 */
    @ApiModelProperty(value = "端口")
    private String port;

    /** 父亲ID:如果为根节点则为@ROOT */
    @ApiModelProperty(value = "父亲ID:如果为菜单则为空")
    private String pid;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "功能权限")
    private String functional;

    @ApiModelProperty(value = "组件ID")
    private String componentsId;
}
