package com.swcares.scgsi.role.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：RoleListQueryForm <br>
 * Package：com.swcares.scgsi.role.common.model <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月07日 14:12 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "角色列表查询表单")
public class RoleListQueryForm {

    @ApiModelProperty(value = "角色代码")
    private String code;

    /** 角色名称 */
    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "创建人")
    private String founder;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "角色类型 0管理员 1普通")
    private String competence;

    @ApiModelProperty(value = "最后修改人")
    private String modifyPerson;

    // 分页相关
    @ApiModelProperty(value = "当前页码")
    @Min(value = 1,message = "当前页码必须从1开始!")
    @NotNull(message = "当前页码为必填项!")
    private Integer current;

    @ApiModelProperty(value = "每页显示记录数")
    @NotNull(message = "每页显示记录数为必填项!")
    private Integer pageSize;
}
