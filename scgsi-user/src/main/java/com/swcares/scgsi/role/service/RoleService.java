package com.swcares.scgsi.role.service;

import java.util.List;

import com.swcares.scgsi.user.common.model.view.UsersInfoView;
import org.springframework.security.core.Authentication;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.vo.ModuleTreeVO;
import com.swcares.scgsi.role.common.model.form.BindResourcesForm;
import com.swcares.scgsi.role.common.model.form.RoleAddForm;
import com.swcares.scgsi.role.common.model.form.RoleListQueryForm;
import com.swcares.scgsi.role.common.model.form.RoleUpdateForm;
import com.swcares.scgsi.role.common.model.view.RoleManagerUserView;
import com.swcares.scgsi.role.common.model.vo.RoleResourceTreeVo;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：RoleService <br>
 * Package：com.swcares.scgsi.role.service <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月07日 14:19 <br>
 * @version v1.0 <br>
 */
public interface RoleService {

    /**
     * Title：getRoleList() <br>
     * Description：web端根据条件查询角色列表 <br>
     * author：于琦海 <br>
     * date：2020/4/7 14:23 <br>
     * @param form RoleListQueryForm
     * @return List<Role>
     */
    QueryResults getRoleList(RoleListQueryForm form,Authentication authentication);

    /**
     * Title：getUserByRoleID() <br>
     * Description：根据角色ID查询用户集合 <br>
     * author：于琦海 <br>
     * date：2020/4/7 18:28 <br>
     * @param roleId String
     * @return RoleManagerUserView
     */
    List<RoleManagerUserView.View> getUserByRoleID(String roleId);


    /**
     * Title：getResourceByRoleID() <br>
     * Description：通过RoleID查询资源信息 <br>
     * author：于琦海 <br>
     * date：2020/4/9 10:29 <br>
     * @param roleId String
     * @return ModuleTreeView
     */
    List<ModuleTreeVO> getResourceByRoleID(String roleId);

    /**
     * Title：isDisabled（） <br>
     * Description：角色停用启用 <br>
     * author：于琦海 <br>
     * date：2020/4/9 10:35 <br>
     * @param roleId String
     * @param authentication Authentication
     */
    void isDisabled(String roleId,Authentication authentication);

    /**
     * Title：getFunctional() <br>
     * Description：新增角色中功能权限展示 <br>
     * author：于琦海 <br>
     * date：2020/4/9 13:32 <br>
     * @param authentication Authentication
     * @return List<ModuleTreeVO>
     */
    List<ModuleTreeVO> getFunctional(String competence,Authentication authentication);

    /**
     * Title：角色新增并且绑定用户和功能权限 <br>
     * Description：addRoleInfo() <br>
     * author：于琦海 <br>
     * date：2020/4/9 15:29 <br>
     * @param addForm RoleAddForm
     */
    void addRoleInfo(RoleAddForm addForm,Authentication authentication);

    /**
     * Title：findDepartmentById <br>
     * Description：通过用户ID查询角色中的部门ID集合 <br>
     * author：于琦海 <br>
     * date：2020/4/20 9:47 <br>
     * @param id String
     * @return  List<String>
     */
    List<String> findDepartmentById(String id);

    /**
     * Title：deleteRole <br>
     * Description：根据角色ID删除角色 <br>
     * author：于琦海 <br>
     * date：2020/4/21 15:47 <br>
     * @param roleIds List<String>
     */
    void deleteRole(List<String> roleIds);

    /**
     * Title：updateRole <br>
     * Description：更新角色信息 <br>
     * author：于琦海 <br>
     * date：2020/4/22 15:06 <br>
     * @param form RoleUpdateForm
     * @param authentication Authentication
     */
    void updateRole(RoleUpdateForm form, Authentication authentication);


    /**
     * Title：findUserByRoleId <br>
     * Description：通过roleId查询用户信息 <br>
     * author：于琦海 <br>
     * date：2020/4/22 15:38 <br>
     * @param roleId String
     * @return  List<UsersInfoView>
     */
    List<UsersInfoView> findUserByRoleId(String roleId);

    /**
     * Title：removeUsersBind <br>
     * Description：解除用户绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 15:56 <br>
     * @param roleId BindResourcesForm
     */
    void removeUsersBind(BindResourcesForm roleId);

    /**
     * Title：removeUsersBind <br>
     * Description：用户绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 15:56 <br>
     * @param form BindResourcesForm
     */
    void bindUsers(BindResourcesForm form);
	
	    /**
     * Title：findRoleResourceTreeVoByToId <br>
     * Description：通过组织机构代码获取角色资源树 <br>
     * author：王磊 <br>
     * date：2020年4月23日 下午1:58:10 <br>
     * @param toId 组织机构代码
     * @return <br>
     */
    List<RoleResourceTreeVo> findRoleResourceTreeVoByToId(String toId);

    /**
     * Title：findRoleResourceTreeVoByUserId <br>
     * Description：通过用户ID获取角色集合 <br>
     * author：王磊 <br>
     * date：2020年4月23日 下午2:57:04 <br>
     * @param userId
     * @return <br>
     */
    List<RoleResourceTreeVo> findRoleResourceTreeVoByUserId(String userId);

}
