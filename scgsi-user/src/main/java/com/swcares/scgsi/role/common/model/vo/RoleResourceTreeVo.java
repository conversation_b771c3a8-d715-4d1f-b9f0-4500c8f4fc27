package com.swcares.scgsi.role.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import com.swcares.scgsi.common.model.vo.ModuleTreeVO;

/**
 * ClassName：com.swcares.scgsi.role.common.model.vo.RoleResourceTreeVo <br>
 * Description：角色资源树 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月23日 上午11:05:38 <br>
 * @version v1.0 <br>
 */
@ApiModel("用户详情角色资源树")
@Data
public class RoleResourceTreeVo {

    /** 主键 */
    @ApiModelProperty("角色ID")
    private String key;

    /** 角色代码 */
    @ApiModelProperty("角色代码")
    private String code;

    /** 角色名称 */
    @ApiModelProperty("角色名称")
    private String title;

    /** 角色类型 */
    @ApiModelProperty("角色类型(1管理,0非管理)")
    private String competence;

    /** 部门ID */
    @ApiModelProperty("部门toid")
    private String departmentId;

    /**是否选中**/
    @ApiModelProperty("是否选中(1是,0否)")
    private String isChecked = "0";

    /**资源树**/
    @ApiModelProperty("资源树")
    private List<ModuleTreeVO> children;

}
