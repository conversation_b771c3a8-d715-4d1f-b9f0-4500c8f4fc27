package com.swcares.scgsi.role.service;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.vo.ModuleTreeVO;
import com.swcares.scgsi.role.common.model.form.BindResourcesForm;
import com.swcares.scgsi.role.common.model.form.ResourceAddForm;
import com.swcares.scgsi.role.common.model.form.ResourceUpdateForm;
import com.swcares.scgsi.role.common.model.form.ResourcesQueryForm;
import org.springframework.security.core.Authentication;

import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ResourceService <br>
 * Package：com.swcares.scgsi.role.service <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月08日 11:06 <br>
 * @version v1.0 <br>
 */
public interface ResourceService {

    /**
     * Title：getResources() <br>
     * Description：获取资源列表 <br>
     * author：于琦海 <br>
     * date：2020/4/8 11:16 <br>
     * @param form ResourcesQueryForm
     * @return QueryResults
     */
    QueryResults getResources(ResourcesQueryForm form);

    /**
     * Title：addResource() <br>
     * Description：新增资源 <br>
     * author：于琦海 <br>
     * date：2020/4/8 13:24 <br>
     * @param form ResourceAddForm
     * @param authentication Authentication
     */
    void addResource(ResourceAddForm form,Authentication authentication);

    /**
     * Title：updateResource() <br>
     * Description：修改资源 <br>
     * author：于琦海 <br>
     * date：2020/4/8 13:24 <br>
     * @param form ResourceUpdateForm
     */
    void updateResource(ResourceUpdateForm form);

    /**
     * Title：batchDisabled（） <br>
     * Description：批量启用停用 <br>
     * author：于琦海 <br>
     * date：2020/4/8 14:24 <br>
     * @param id List<String>
     * @param status Integer
     */
    void batchDisabled(List<String> id,Boolean status);

    /**
     * Title：deleteResource() <br>
     * Description：删除资源 <br>
     * author：于琦海 <br>
     * date：2020/4/8 16:14 <br>
     * @param id List<String>
     */
    void deleteResource(List<String> id);

    /**
     * Title：getModuleTree（） <br>
     * Description：获取模块树 <br>
     * author：于琦海 <br>
     * date：2020/4/8 17:24 <br>
     * @return  List<ModuleTreeVO>
     */
    List<ModuleTreeVO> getModuleTree();

    /**
     * Title：findResourcesByRoleId <br>
     * Description：通过角色ID获取资源集合 <br>
     * author：于琦海 <br>
     * date：2020/4/22 14:01 <br>
     * @param roleId String
     * @return   List<String>
     */
    List<String> findResourcesByRoleId(String roleId);

    /**
     * Title：removeRolesBind <br>
     * Description：去除角色的资源绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 14:25 <br>
     * @param roleId String
     */
    void removeRolesBind(String roleId);

    /**
     * Title：bindResources <br>
     * Description：资源绑定 <br>
     * author：于琦海 <br>
     * date：2020/4/22 14:34 <br>
     * @param resource BindResourcesForm
     */
    void bindResources(BindResourcesForm resource);
}
