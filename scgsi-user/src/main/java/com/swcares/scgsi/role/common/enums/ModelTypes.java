package com.swcares.scgsi.role.common.enums;

import java.util.Arrays;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ModelTypes <br>
 * Package：com.swcares.scgsi.role.common.enums <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月08日 10:37 <br>
 * @version v1.0 <br>
 */
public enum ModelTypes {

    MODULE(0,"组件"),

    PAGE(1,"页面"),

    MENU(2,"菜单");

    private Integer code;

    private String name;

    /**
     * Title：ModelTypes（） <br>
     * Description：construct <br>
     * author：于琦海 <br>
     * date：2020/4/8 11:04 <br>
     * @param code Integer
     * @param name String
     */
    ModelTypes(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode(){
        return this.code;
    }
    /**
     * Title：getName（） <br>
     * Description：通过code获取名称 <br>
     * author：于琦海 <br>
     * date：2020/4/8 11:03 <br>
     * @param code Integer
     * @return String
     */
    public String getName(Integer code) {
        ModelTypes[] values = ModelTypes.values();
        List<ModelTypes> modelTypes = Arrays.asList(values);
        String result = null;
        for (ModelTypes modelType : modelTypes) {
            if (code.equals(modelType.code)) {
                result = modelType.name;
                break;
            }
        }
        return result;
    }

}
