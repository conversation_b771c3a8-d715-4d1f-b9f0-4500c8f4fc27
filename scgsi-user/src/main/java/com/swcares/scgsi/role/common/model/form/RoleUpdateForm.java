package com.swcares.scgsi.role.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：RoleUpdateForm <br>
 * Package：com.swcares.scgsi.role.common.model.form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月22日 15:11 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "角色信息更新表单")
public class RoleUpdateForm {

    @ApiModelProperty(value = "角色ID")
    @NotBlank(message = "ID不能为空")
    private String id;

    @ApiModelProperty(value = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    private String name;

    @ApiModelProperty(value = "状态")
    @NotBlank(message = "状态不能为空")
    private String status;

    @ApiModelProperty(value = "角色类型:0管理员 1普通用户")
    @NotBlank(message = "角色类型不能为空")
    private String competence;

    @ApiModelProperty(value = "部门toId")
    private String departmentId;

    @ApiModelProperty(value = "角色描述")
    private String description;
}
