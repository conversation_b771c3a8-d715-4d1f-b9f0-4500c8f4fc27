package com.swcares.scgsi.role.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.scgsi.role.common.model.vo.RoleResourceTreeVo <br>
 * Description：角色资源树 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月23日 上午11:05:38 <br>
 * @version v1.0 <br>
 */
@ApiModel("用户详情角色资源树")
@Data
@EqualsAndHashCode(callSuper=false)
public class RoleResourceTreeExtendVo extends RoleResourceTreeVo {
    @ApiModelProperty("禁用还是启用 true禁用(此字段主要使用于管理员角色已关联用户)")
    private boolean disabled = false;
}
