package com.swcares.scgsi.user.service;

import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * ClassName：com.swcares.scgsi.login.service.VerifyCodeService <br>
 * Description：验证码服务 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月24日 下午4:29:47 <br>
 * @version v1.0 <br>
 */
public interface KaptchaService {

    /**
     * Title：generateVerifyCodeImage <br>
     * Description：获取图片验证码 <br>
     * author：王磊 <br>
     * date：2020年3月24日 下午4:30:08 <br>
     * @param request 
     * @param response
     * @throws IOException <br>
     */
    void generateVerifyCodeImage(HttpServletRequest request, HttpServletResponse response) throws IOException;
}
