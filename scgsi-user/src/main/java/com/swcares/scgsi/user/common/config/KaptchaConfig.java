package com.swcares.scgsi.user.common.config;

import java.util.Properties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;

/**
 * ClassName：com.swcares.scgsi.login.common.config.KaptchaConfig <br>
 * Description：验证码配置类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月24日 下午4:24:44 <br>
 * @version v1.0 <br>
 */
@Configuration
public class KaptchaConfig {

  @Bean
  public DefaultKaptcha defaultKaptcha() {
    DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
    Properties properties = new Properties();
    properties.setProperty("kaptcha.border", "yes");
    properties.setProperty("kaptcha.border.color", "0,0,0");
    properties.setProperty("kaptcha.textproducer.font.color", "black");
    properties.setProperty("kaptcha.image.width", "100");
    properties.setProperty("kaptcha.image.height", "45");
    properties.setProperty("kaptcha.session.key", "code");
    properties.setProperty("kaptcha.noise.impl", "com.google.code.kaptcha.impl.DefaultNoise");
    properties.setProperty("kaptcha.textproducer.char.length", "4");
    properties.setProperty("kaptcha.textproducer.font.names", "宋体,楷体,微软雅黑");
    Config config = new Config(properties);
    defaultKaptcha.setConfig(config);
    return defaultKaptcha;
  }
}
