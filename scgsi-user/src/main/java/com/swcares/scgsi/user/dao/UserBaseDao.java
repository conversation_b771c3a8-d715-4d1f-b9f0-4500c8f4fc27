package com.swcares.scgsi.user.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.user.common.model.form.UserListForm;
import com.swcares.scgsi.user.common.model.vo.UserDetailVo;
import com.swcares.scgsi.user.common.model.vo.UserListVo;

/**
 * 
 * ClassName：com.swcares.scgsi.user.dao.UserDao <br>
 * Description：UserDao <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月11日 下午11:24:31 <br>
 * @version v1.0 <br>
 */
@Repository
public class UserBaseDao{
    @Autowired
    private BaseDAO baseDao;

    /**
     * Title：getUserList <br>
     * Description：分页查询用户数据 <br>
     * author：王磊 <br>
     * date：2020年4月11日 下午11:25:36 <br>
     * @param pageable
     * @param form
     * @return <br>
     */
    public QueryResults getUserList(Pageable pageable, UserListForm form) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append("select t.id as id,t.photo as photo,t.tuno as tuNo,t.tu_cname as tuCname,t2.tofname as deptName,t.tu_ext2 as position,t.tu_mobile as tuMobile,t.tu_email as tuEmail,t.user_state as userState,t.lock_out_state as userLockState,");
        sql.append(" to_char(t.login_date,'yyyy-mm-dd hh24:mi:ss') as loginDate,to_char(t.modifi_date,'yyyy-mm-dd hh24:mi:ss') as modifiDate from EMPLOYEE t");
        sql.append(" inner join (select t3.id, t3.toid,t3.tofname from department t3 start with t3.id = :deptId connect by prior t3.toid = t3.topid) t2 on t.toid = t2.toid");
        sql.append(" where 1 = 1");
        String userName = form.getTuCname();
        String userTuNo = form.getTuNo();
        Integer userState = form.getUserState();
        String deptId = form.getDeptId();
        if (StringUtils.isNotBlank(deptId)) {
            paramsMap.put("deptId", deptId);
        }
        if (StringUtils.isNotBlank(userName)) {
            paramsMap.put("userName", "%"+userName+"%");
            sql.append(" and t.tu_cname like :userName");
        }
        if (StringUtils.isNotBlank(userTuNo)) {
            paramsMap.put("tuNo", "%"+userTuNo+"%");
            sql.append(" and t.tuno like :tuNo");
        }
        if (userState != null) {
            paramsMap.put("userState", userState);
            sql.append(" and t.user_state = :userState");
        }
        sql.append(" order by t.login_date desc nulls last,t.tuno desc");
        QueryResults queryResults =
                baseDao.findBySQLPage_comm(sql.toString(), form.getCurrent(), form.getPageSize(),
                        paramsMap, UserListVo.class);
        return queryResults;
    }

    @SuppressWarnings("unchecked")
    public List<UserDetailVo> getUserDetail(String userId) {
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" select t.id,t.photo,t.tuno,t.tu_cname as tuCname,t2.tofname as deptName,t.tu_bdate as tuBdate,t.tu_mobile as tuMobile,t.tu_email as tuEmail,(case when t.user_state = '0' then '启用' else '禁用' end) as userState,");
        sql.append(" (select t3.airport_3code||t3.city_ch_name from CITY_CODE t3 where t3.city_3code = t.city_3code) as airport,t.PWD_UPD_DATE pwdUpdDate,");
        sql.append(" to_char(t.login_date,'yyyy-mm-dd hh24:mi:ss') as loginDate,to_char(t.modifi_date,'yyyy-mm-dd hh24:mi:ss') as modifiDate ,");
        sql.append(" (select t4.tuno||t4.tu_cname from EMPLOYEE t4 where t4.id = t.modifi_user) as modifiUser, t.is_on_duty as isOnDuty , t.tu_sex as tuSex from EMPLOYEE t left join");
        sql.append(" Department t2 on t.toid = t2.toid where t.id = :userId");
        paramsMap.put("userId", userId);
        // TODO %CodeTemplates.methodstub.tododesc return null;
        return (List<UserDetailVo>) baseDao.findBySQL_comm(sql.toString(), paramsMap, UserDetailVo.class);
    }
    
}
