package com.swcares.scgsi.user.common.model.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Clob;
import java.sql.SQLException;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.user.common.model.vo.UserDetailVo <br>
 * Description：用户详情 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月11日 下午11:32:45 <br>
 * @version v1.0 <br>
 */
@ApiModel("用户详情vo")
@Data
@EncryptionClassz
public class UserDetailVo {
    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private String id;
    /**
     * 人员照片
     */
    @ApiModelProperty("用户照片")
    private Clob photo;
    
    /**
     * 中文名
     */
    @ApiModelProperty("员工姓名")
    private String tuCname;
    
    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String deptName;
    
    /**
     * 员工编号
     */
    @ApiModelProperty("员工编号")
    private String tuNo;
    
    /**
     * 手机
     */
    @ApiModelProperty("联系电话")
    @Encryption
    private String tuMobile;
    
    /**
     * 邮件地址
     */
    @ApiModelProperty("邮箱地址")
    private String tuEmail;
    
    /**
     * 生日
     */
    @ApiModelProperty("生日")
    private String tuBdate;
    
    /**
     * 工作航站(机场3字码)
     */
    @ApiModelProperty("工作航站")
    private String airport;
    
    /**
     * 本系统用户状态((0 启用、1 禁用))
     */
    @ApiModelProperty("用户状态")
    private String userState;
    
    /**
     * 最后登录时间
     */
    @ApiModelProperty("最后登录时间")
    private String loginDate;
    
    /**
     * 最后修改时间
     */
    @ApiModelProperty("最后修改时间")
    private String modifiDate;
    
    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人")
    private String modifiUser;
    
    /**
     * 值班状态(0 未值班、1 值班)
     */
    @ApiModelProperty("值班状态")
    private Integer isOnDuty;
    
    /**
     * 性别
     */
    @ApiModelProperty("性别")
    private String tuSex;

    /**
     * 密码更新时间
     */
    @ApiModelProperty("密码更新时间")
    private String pwdUpdDate;
    
    /**
     * Title：getPhoto <br>
     * Description：重写get方法转换clob <br>
     * author：王磊 <br>
     * date：2020年4月13日 下午4:43:36 <br>
     * @return <br>
     */
    public String getPhoto(){
        String value = null;
        try {
            if(photo != null){
                value = photo.getSubString((long)1, (int)photo.length());//把clob数据转换为string
            }
        } catch (SQLException e) {
            // TODO %CodeTemplates.catchblock.tododesc e.printStackTrace();
        }
        return value;
    }
}
