package com.swcares.scgsi.user.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.user.common.model.form.UserListForm <br>
 * Description：用户列表form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月11日 下午11:18:38 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "用户列表form")
public class UserListForm {
    /** 部门id */
    @ApiModelProperty(value = "部门id", required = true)
    @NotNull(message = "部门不能为空")
    private String deptId;
    /** 用户ID */
    @ApiModelProperty(value = "用户姓名")
    private String tuCname;
    /** 用户工号 */
    @ApiModelProperty(value = "用户工号")
    private String tuNo;
    /** 用户状态(-1全部、0 启用、1 禁用) */
    @ApiModelProperty(value = "用户状态(0 启用、1 禁用)")
    private Integer userState;
    /**当前页码*/
    @Min(value = 1, message = "页码最小为1")
    @NotNull(message = "页码不能为空")
    @ApiModelProperty(value = "当前页码", required = true)
    private Integer current;
    /** 每页条数 */
    @Min(value = 1, message = "每页条数最小为1")
    @NotNull(message = "每页条数不能为空")
    @ApiModelProperty(value = "每页条数", required = true)
    private Integer pageSize;
}
