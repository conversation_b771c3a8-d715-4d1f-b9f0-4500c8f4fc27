package com.swcares.scgsi.user.service.impl;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import javax.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.swcares.scgsi.common.userenum.UserEnum;
import com.swcares.scgsi.hum.service.HumResourceDataService;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.user.service.SynchronizationUserService;

/**
 * ClassName：com.swcares.scgsi.user.service.impl.SynchronizationUserServiceImpl <br>
 * Description：增量用户更新异步调用类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年6月8日 下午1:29:35 <br>
 * @version v1.0 <br>
 */
@Service
@Getter
@Setter
@Slf4j
public class SynchronizationUserServiceImpl implements SynchronizationUserService {


    @Resource
    private HumResourceDataService humResourceDataService;

    @Autowired
    private RedisService redisService;

    private static final Long MANUAL_SYNC_EXECUTION_RELEASE_TIME = 60L;// 同步标识过期时间

    @Override
    @Async
    public void synchronizationUser() {
        Calendar cal = Calendar.getInstance();
        Long startTime = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.info("异步增量更新用户开启,时间:{}", sdf.format(cal.getTime()));
        log.info("异步增量更新用户执行redis标识存入");
        //设置过期时间为3小时
        redisService.set(UserEnum.USER_MANUAL_SYNC_EXECUTION_REDIS.getValue(),
                UserEnum.USER_MANUAL_SYNC_EXECUTION_REDIS.getValue(),
                MANUAL_SYNC_EXECUTION_RELEASE_TIME * 10 * 6 * 3);
        //人为休眠
        if (StringUtils.isNotBlank((String) redisService.get("synchronization_user_sleep"))) {
            try {
                Thread.sleep(30000);
            } catch (InterruptedException e) {
                // TODO %CodeTemplates.catchblock.tododesc e.printStackTrace();
            }
        }
        humResourceDataService.employeeIncrementalData();
        Long endTime = System.currentTimeMillis();
        cal = Calendar.getInstance();
        redisService.deleteKey(UserEnum.USER_MANUAL_SYNC_EXECUTION_REDIS.getValue());
        log.info("异步增量更新用户执行redis标识释放");
        log.info("异步增量更新用户结束,时间:{},总耗时:{}毫秒", sdf.format(cal.getTime()), endTime - startTime);
    }

}
