package com.swcares.scgsi.user.service;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.data.domain.Pageable;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.view.LoginView;
import com.swcares.scgsi.department.common.model.vo.DepartmentTreeVO;
import com.swcares.scgsi.role.common.model.vo.RoleResourceTreeExtendVo;
import com.swcares.scgsi.role.common.model.vo.RoleResourceTreeVo;
import com.swcares.scgsi.user.common.model.form.UpdatePasswordForm;
import com.swcares.scgsi.user.common.model.form.UpdateUserForm;
import com.swcares.scgsi.user.common.model.form.UserListForm;
import com.swcares.scgsi.user.common.model.view.UsersInfoView;
import com.swcares.scgsi.user.common.model.vo.UserDetailVo;
import com.swcares.scgsi.user.common.model.vo.UserListVo;


/**
 * ClassName：com.swcares.scgsi.login.service.LoginService <br>
 * Description：用户登录接口 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月3日 上午11:29:54 <br>
 * @version v1.0 <br>
 */
public interface UserService {

    /**
     * Title：authTokenForWS <br>
     * Description：通过ws接口验证token值 <br>
     * author：王磊 <br>
     * date：2020年3月12日 下午6:17:29 <br>
     * @param LoginView
     * @return <br>
     */
    public LoginView<Object> authTokenForWS(String token) throws Exception;

    /**
     * Title：authTokenForH5 <br>
     * Description：H5验证token <br>
     * author：王磊 <br>
     * date：2020年3月23日 下午2:43:34 <br>
     * @param LoginView
     * @return <br>
     */
    public LoginView<Object> authTokenForH5(String token) throws Exception;

    /**
     * Title：updatePassword <br>
     * Description：修改密码 <br>
     * author：王磊 <br>
     * date：2020年3月25日 下午11:46:20 <br>
     * @param updatePasswordForm <br>
     */
    public void updatePassword(@Valid UpdatePasswordForm updatePasswordForm) throws Exception;

    /**
     * Title：resetPasswords <br>
     * Description：重置密码 <br>
     * author：王磊 <br>
     * date：2020年3月26日 上午10:43:22 <br>
     * @param userId <br>
     * @param modifiUserId 
     * @return 
     */
    public String resetPasswords(String userId, String modifiUserId);

    /**
     * Title：updateUserLockState <br>
     * Description：解锁用户 <br>
     * author：王磊 <br>
     * date：2020年3月26日 下午12:15:23 <br>
     * @param userIds <br>
     * @param modifiUserId 
     */
    public void updateUserLockState(String[] userIds, String modifiUserId);

    /**
     * Title：updateUserState <br>
     * Description：改变用户状态 <br>
     * author：王磊 <br>
     * date：2020年3月26日 下午12:15:33 <br>
     * @param userIds
     * @param modifiUserId 
     * @param state(0启用,1禁用) <br>
     */
    public void updateUserState(String[] userIds, String state, String modifiUserId);

    /**
     * Title：forgetPassword <br>
     * Description：用户密码找回 <br>
     * author：王磊 <br>
     * date：2020年3月26日 下午5:02:34 <br>
     * @param request request对象
     * @param userName 用户账号
     * @param phoneNum 手机号
     * @param smsCode 短信验证码
     * @param password 新密码
     * @param type 找回的类型1短信2邮箱
     * @return <br>
     * @throws Exception 
     */
    public void forgetPassword(HttpServletRequest request,String userName, String phoneNum,String smsCode, String password,String type) throws Exception;

    /**
     * Title：cacheToken <br>
     * Description：统一登录平台传入token后暂存redis并返回key <br>
     * author：王磊 <br>
     * date：2020年4月10日 下午3:21:06 <br>
     * @param token
     * @return <br>
     * @throws Exception 
     */
    public String cacheToken(String token) throws Exception;

    /**
     * Title：updateOnDutyState <br>
     * Description：用户值班状态修改 <br>
     * author：王磊 <br>
     * date：2020年4月10日 下午4:55:39 <br>
     * @param userId
     * @param onDutyState
     * @throws Exception <br>
     */
    public void updateOnDutyState(String userId, String onDutyState)throws Exception;

    /**
     * Title：getUserList <br>
     * Description：获取用户列表 <br>
     * author：王磊 <br>
     * date：2020年4月12日 上午12:02:34 <br>
     * @param pageable
     * @param form
     * @return
     * @throws Exception <br>
     */
    public QueryResults getUserList(Pageable pageable, UserListForm form) throws Exception;

    /**
     * Title：getUserDetail <br>
     * Description：获取用户详情 <br>
     * author：王磊 <br>
     * date：2020年4月12日 下午5:26:27 <br>
     * @param userId <br>
     * @return 
     */
    public UserDetailVo getUserDetail(String userId) throws Exception;

    /**
     * Title：updateUser <br>
     * Description：修改用户 <br>
     * author：王磊 <br>
     * date：2020年4月12日 下午9:29:37 <br>
     * @param updateUserForm
     * @throws Exception <br>
     */
    public void updateUser(UpdateUserForm updateUserForm) throws Exception;
    
    /**
     * Title：getDepartmentTreeByRole <br>
     * Description：通过用户角色部门ID获取部门树 <br>
     * author：王磊 <br>
     * date：2020年4月13日 下午8:19:42 <br>
     * @param userId
     * @return
     * @throws Exception <br>
     */
    public List<DepartmentTreeVO> getDepartmentTreeByRole(String userId) throws Exception;

    /**
     * Title：logOut <br>
     * Description：登出功能 <br>
     * author：王磊 <br>
     * date：2020年4月20日 上午9:59:45 <br>
     * @param userId <br>
     */
    public void logOut(String userId);

    /**
     * Title：findUserByDepartmentId <br>
     * Description：通过部门唯一标识和角色类型查询用户信息 <br>
     * author：于琦海 <br>
     * date：2020/4/20 15:28 <br>
     * @param competence String
     * @param toId String
     * @return List<UsersInfoView>
     */
    List<UsersInfoView> findUserByDepartmentId(String competence, String toId);
    
    /**
     * Title：queryUserByNameAndNo <br>
     * Description：根据姓名和工号模糊查询用户信息 <br>
     * author：夏阳 <br>
     * date：2020年4月21日 上午10:37:50 <br>
     * @param keySearch
     * @return <br>
     */
    List<UserListVo> queryUserByNameAndNo(String keySearch);

    /**
     * Title：findRoleByUserIdAndModifiUserId <br>
     * Description：通过组织机构代码和userid获取角色资源树并构建,编辑时使用 <br>
     * author：王磊 <br>
     * date：2020年4月23日 下午2:23:07 <br>
     * @param modifiUserId
     * @param userId
     * @return <br>
     */
    List<RoleResourceTreeExtendVo> findRoleByUserIdAndModifiUserId(String modifiUserId,String userId);

    /**
     * Title：findRoleByUserId <br>
     * Description：通过UserID获取角色资源树,展示时使用 <br>
     * author：王磊 <br>
     * date：2020年4月23日 下午2:27:39 <br>
     * @param userId
     * @return <br>
     */
    List<RoleResourceTreeVo> findRoleByUserId(String userId);
    
    /**
     * Title：manualSync <br>
     * Description：手动增量同步用户数据 <br>
     * author：王磊 <br>
     * date：2021年6月2日 上午11:26:13 <br> <br>
     */
    void manualSync();

    /***
     * @title handleUserSensitiveInfo
     * @description 员工敏感信息加密
     * <AUTHOR>
     * @date 2024/9/24 10:20

     * @return void
     */
    void handleUserSensitiveInfo();
}
