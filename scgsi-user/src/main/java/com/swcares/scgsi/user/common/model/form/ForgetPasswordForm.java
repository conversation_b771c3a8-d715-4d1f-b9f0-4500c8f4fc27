package com.swcares.scgsi.user.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
/**
 * ClassName：com.swcares.scgsi.login.common.model.form.ForgetPasswordForm <br>
 * Description：忘记密码表单对象 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月8日 上午9:20:23 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "忘记密码表单对象")
public class ForgetPasswordForm {
    /** 用户账号 */
    @ApiModelProperty(value = "用户账号", required = false)
    private String userName;
    /** 手机号 */
    @ApiModelProperty(value = "手机号", required = false)
    private String phoneNum;
    /** 手机短信验证码 */
    @ApiModelProperty(value = "手机短信验证码 ", required = false)
    private String smsCode;
    /** 新密码 */
    @ApiModelProperty(value = "新密码", required = false)
    private String password;
    /** 类型 */
    @ApiModelProperty(value = "找回类型(找回的类型1短信2邮箱)", required = true)
    @NotEmpty(message = "找回类型为必填项")
    private String type;
}
