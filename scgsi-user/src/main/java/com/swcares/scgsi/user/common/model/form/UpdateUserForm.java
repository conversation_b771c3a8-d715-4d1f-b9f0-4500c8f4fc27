package com.swcares.scgsi.user.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.user.common.model.form.UpdateUserForm <br>
 * Description：修改用户的表单 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月12日 下午9:20:31 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "修改用户对象")
public class UpdateUserForm {
    /** 用户ID */
    @ApiModelProperty(value = "用户ID", required = true)
    @NotEmpty(message = "用户id为必填项")
    private String userId;
    
    /** 修改人ID */
    private String modifiUserId;
    
    /** 工作航站 */
    @ApiModelProperty(value = "工作航站")
    private String airportCode;
    
    /** 用户状态 */
    @ApiModelProperty(value = "用户状态(0启用,1禁用)", required = true)
    private int userState;
    
    /** 角色id集合 */
    @ApiModelProperty(value = "角色集合")
    private String [] roleIds;
    
    /**修改前角色集合*/
    @ApiModelProperty(value = "修改前角色集合")
    private String [] preRoleIds;
    
    /**
     * 是否系统功能
     */
    @ApiModelProperty(value = "用户状态(0不启用,1启用)")
    private int isSysFunction;
    
}
