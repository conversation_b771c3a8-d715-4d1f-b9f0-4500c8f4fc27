package com.swcares.scgsi.user.service.impl;

import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import jodd.util.StringUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.json.JSONObject;
import org.json.XML;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.view.LoginView;
import com.swcares.scgsi.common.model.vo.ResourceTreeVo;
import com.swcares.scgsi.common.userenum.UserEnum;
import com.swcares.scgsi.common.utils.JwtTokenUtils;
import com.swcares.scgsi.dao.ResourcesDao;
import com.swcares.scgsi.department.common.model.vo.DepartmentTreeVO;
import com.swcares.scgsi.department.dao.DepartmentBaseDao;
import com.swcares.scgsi.dict.service.SysDictDataService;
import com.swcares.scgsi.entity.Resources;
import com.swcares.scgsi.entity.Role;
import com.swcares.scgsi.entity.UserRole;
import com.swcares.scgsi.hum.employee.entity.Employee;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.role.common.enums.RoleTypes;
import com.swcares.scgsi.role.common.model.vo.RoleResourceTreeExtendVo;
import com.swcares.scgsi.role.common.model.vo.RoleResourceTreeVo;
import com.swcares.scgsi.role.dao.RoleDao;
import com.swcares.scgsi.role.dao.RoleRepository;
import com.swcares.scgsi.role.service.RoleService;
import com.swcares.scgsi.service.ResourcesService;
import com.swcares.scgsi.sms.api.SmsService;
import com.swcares.scgsi.sms.entity.SmsSendResult;
import com.swcares.scgsi.user.common.model.form.UpdatePasswordForm;
import com.swcares.scgsi.user.common.model.form.UpdateUserForm;
import com.swcares.scgsi.user.common.model.form.UserListForm;
import com.swcares.scgsi.user.common.model.view.UsersInfoView;
import com.swcares.scgsi.user.common.model.vo.UserDetailVo;
import com.swcares.scgsi.user.common.model.vo.UserListVo;
import com.swcares.scgsi.user.dao.UserBaseDao;
import com.swcares.scgsi.user.dao.UserJpaDao;
import com.swcares.scgsi.user.service.SynchronizationUserService;
import com.swcares.scgsi.user.service.UserService;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.util.Asserts;
import com.swcares.scgsi.util.CharsetUtil;
import com.swcares.scgsi.util.MD5EncryptUtils;
import com.swcares.scgsi.web.ResourceBundleMessageSourceFactory;
import com.swcares.scgsi.wsdl.AxisUtil;
import com.swcares.scgsi.wsdl.UserWsdl;

/**
 * ClassName：com.swcares.scgsi.login.service.impl.LoginServiceImpl <br>
 * Description：用户登录实现类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月12日 上午10:55:23 <br>
 * @version v1.0 <br>
 */
@Service
@Getter
@Setter
@Slf4j
@ConfigurationProperties(prefix = "authtokensources")
@EnableConfigurationProperties(ResourceBundleMessageSourceFactory.class)
public class UserServiceImpl implements UserService {

    private String authTokenUrl;
    private String authTokenInterfaceName;
    private UserWsdl userWsdl;
    private String appId;
    private String appKey;
    private String appUrl;

    private static final String CHAR_UPPER = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";// 密码基础字符大写字母
    private static final String CHAR_LOWER = "abcdefghijklmnopqrstuvwxyz";// 密码基础字符小写字母
    private static final String CHAR_NUM = "0123456789";// 密码基础字符数字

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserJpaDao userJpaDao;

    @Autowired
    private UserBaseDao userDao;

    @Autowired
    private SmsService smsService;

    @Autowired
    private BaseDAO baseDAO;

    @Autowired
    private DepartmentBaseDao departmentBaseDao;

    @Autowired
    private ResourcesService resourcesService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private RoleDao roleDao;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private ResourcesDao resourcesDao;

    @Autowired
    private SysDictDataService dictDataService;
    
    @Autowired
    private SynchronizationUserService synchronizationUserService;

    @Value("${spring.profiles.active}")
    private String profiles;

    @Override
    public LoginView<Object> authTokenForWS(String key) throws Exception {
        String token = (String) redisService.get(key);
        String reqXml = buildAuthTokenXML(token);
        Map<String, String> reqMap = new HashMap<String, String>();
        reqMap.put(AxisUtil.INTERFACE_NAME, authTokenInterfaceName);
        reqMap.put(AxisUtil.REQ_XML_CONTENT, reqXml);
        // 1.调用远程
        Object resXml = AxisUtil.doUrl(reqMap, authTokenUrl, userWsdl);
        // success failure
        if (ObjectUtils.isEmpty(resXml)) {
            log.error("token验证获取数据为空，请求url:{0}", authTokenUrl);
            throw new BusinessException(MessageCode.SYS_TOKEN_IS_NULL_MSG.getCode());
        }
        JSONObject xmlJSONObj = (JSONObject) XML.toJSONObject(resXml.toString()).get("root");
        if (xmlJSONObj.has("failure")) {
            log.error("token验证失败，参数:{0}", reqXml);
            throw new BusinessException(MessageCode.SYS_TOKEN_FAIL_MSG.getCode());
        }
        JSONObject successJson = (JSONObject) xmlJSONObj.get("success");
        if (ObjectUtils.isNotEmpty(successJson)) {
            return loginSuccess(successJson.get("username").toString(),
                    UserEnum.RESOURCE_PROT_WEB.getValue());
        } else {
            log.error("token验证失败，参数:{0}", reqXml);
            throw new BusinessException(MessageCode.SYS_TOKEN_FAIL_MSG.getCode());
        }
    }

    @Override
    public String cacheToken(String token) throws Exception {
        String str = CHAR_UPPER + CHAR_LOWER + CHAR_NUM;// 密码使用大小写字母和数字随机
        String randomChar = getCharAndNumr(30, str);
        redisService.set(randomChar, token, new Long(UserEnum.CACHE_TOKEN_TIME.getValue()));
        return randomChar;
    }

    @Override
    public LoginView<Object> authTokenForH5(String token) throws Exception {
        // 生产pro用单点登录 其他环境直接进入
        if (profiles.equals("pro"))
            return authTokenForH5s(token);
        return loginSuccess(token, UserEnum.RESOURCE_PROT_H5.getValue());
    }

    public LoginView<Object> authTokenForH5s(String token) throws Exception {
        long time = new Date().getTime();
        String paramStr = appId + ":" + appKey + ":" + time;
        String appVerify = MD5EncryptUtils.encryptPassword(paramStr) + ";" + time;
        String url = String.format(appUrl, token);
        HttpHeaders headers = new HttpHeaders();
        headers.add("appverify", appVerify);
        headers.add("x-mas-app-id", appId);
        HttpEntity<String> requestEntity = new HttpEntity<String>(null, headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response =
                restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
        JSONObject jSONObject = new JSONObject(response.getBody());
        if (jSONObject.get("status") != null) {
            String result = (String) jSONObject.get("status");
            if (result.equals("ok")) {
                JSONObject data = (JSONObject) jSONObject.get("data");
                return loginSuccess(data.get("loginName").toString(),
                        UserEnum.RESOURCE_PROT_H5.getValue());
                // return loginSuccess(token, UserEnum.RESOURCE_PROT_H5.getValue());
            } else {
                log.error("token验证失败，参数:{0}", appVerify);
                throw new BusinessException(MessageCode.SYS_TOKEN_FAIL_MSG.getCode());
            }
        } else {
            log.error("token验证获取数据为空，请求url:{0}", url);
            throw new BusinessException(MessageCode.SYS_TOKEN_IS_NULL_MSG.getCode());
        }
    }

    @Override
    public void updatePassword(UpdatePasswordForm updatePasswordForm) throws Exception {
        Employee employee = new Employee();
        try {
            // 通过人员获取部门userId
            Optional<Employee> optional = userJpaDao.findById(updatePasswordForm.getUserId());
            employee = optional.get();
            String privateKey = UserEnum.PASSWORD_ENCRYPT_KEY.getValue();
            String key = null;
            try {
                key = Base64.encodeBase64String(privateKey.getBytes(CharsetUtil.UTF8));
            } catch (UnsupportedEncodingException e) {
                // TODO %CodeTemplates.catchblock.tododesc e.printStackTrace();
                log.error("登录密码加密错误{}", e);
            }
            // 密码解密
            updatePasswordForm.setPassword(AesEncryptUtil.aesDecrypt(key,
                    updatePasswordForm.getPassword()));
            updatePasswordForm.setNewPassword(AesEncryptUtil.aesDecrypt(key,
                    updatePasswordForm.getNewPassword()));
            PasswordEncoder createDelegatingPasswordEncoder =
                    PasswordEncoderFactories.createDelegatingPasswordEncoder();
            String newPassword = createDelegatingPasswordEncoder.encode(updatePasswordForm
                    .getNewPassword());
            if (!createDelegatingPasswordEncoder.matches(updatePasswordForm.getPassword(),
                    employee.getPassWord())) {
                throw new BusinessException(MessageCode.SYS_OLDPASSWORD_ERROR.getCode());
            }
            //旧密码与新密码不能相同
            if (createDelegatingPasswordEncoder.matches(updatePasswordForm.getPassword(),
                    newPassword)) {
                throw new BusinessException(MessageCode.SYS_NEW_OLDPASSWORD_CANNOT_ACCORD.getCode());
            }
            employee.setPassWord(newPassword);
            employee.setPwdUpdDate(new Timestamp(new Date().getTime()));
            employee.setIsFirstLogin(new Integer(UserEnum.NOT_FIRST_LOGIN.getValue()));
            userJpaDao.save(employee);// 保存用户新密码
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("修改密码异常:{}", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public String resetPasswords(String userId, String modifiUserId) {
        String initpwd = "";
        try {
            Employee employee = new Employee();
            employee = userJpaDao.findTById(userId);
            String str = CHAR_UPPER + CHAR_LOWER + CHAR_NUM;// 密码使用大小写字母和数字随机
            initpwd = getCharAndNumr(8, str);// 生成随机密码8位数字和1位字母
            PasswordEncoder createDelegatingPasswordEncoder =
                    PasswordEncoderFactories.createDelegatingPasswordEncoder();
            employee.setPassWord(createDelegatingPasswordEncoder.encode(initpwd));
            employee.setModifiUser(modifiUserId);
            employee.setModifiDate(new Timestamp(new Date().getTime()));
            employee.setPwdUpdDate(new Timestamp(new Date().getTime()));
            userJpaDao.save(employee);// 保存用户新密码
        } catch (Exception e) {
            log.error("重置密码:{}", e);
            throw e;
        }
        return initpwd;
    }

    @Override
    @Transactional
    public void updateUserLockState(String[] userIds, String modifiUserId) {
        List<Employee> list = new ArrayList<Employee>();
        try {
            for (String userId : userIds) {
                Employee employee = new Employee();
                employee = userJpaDao.findTById(userId);
                employee.setLockOutState(new Integer(UserEnum.USER_STATE_USABLE.getValue()));
                employee.setLockOutDate(null);
                employee.setModifiUser(modifiUserId);
                employee.setModifiDate(new Timestamp(new Date().getTime()));
                list.add(employee);
                redisService.deleteKey(employee.getTuNo() + UserEnum.REDIS_PWD_ERROR);
            }
            userJpaDao.saveAll(list);// 批量解锁用户
        } catch (Exception e) {
            log.error("解锁用户异常:{}", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public void updateUserState(String[] userIds, String state, String modifiUserId) {
        List<Employee> list = new ArrayList<Employee>();
        try {
            for (String userId : userIds) {
                Employee employee = new Employee();
                employee = userJpaDao.findTById(userId);
                employee.setUserState(new Integer(state));
                employee.setModifiUser(modifiUserId);
                employee.setModifiDate(new Timestamp(new Date().getTime()));
                list.add(employee);
            }
            userJpaDao.saveAll(list);// 批量保存用户新状态
        } catch (Exception e) {
            log.error("修改用户状态异常:{}", e);
            throw e;
        }
    }

    @Override
    @Transactional
    public void forgetPassword(HttpServletRequest request, String userName, String findNum,
            String smsCode, String password, String type) throws Exception {
        String clientIp = getIpAddr(request);
        Integer ipSendSmsCount =
                (Integer) redisService.get(clientIp + UserEnum.REDIS_SMS_CODE.getValue());
        // 第一步发送信息
        if (StringUtils.isNotEmpty(findNum) && StringUtils.isEmpty(smsCode)) {// 手机号不为空验证码为空时为第一步
            if (ipSendSmsCount != null
                    && ipSendSmsCount > Integer.parseInt(UserEnum.IP_SMS_RETRANSMISSION_THRESHOLD
                            .getValue())) {// 验证IP发送短信是否超过阀值
                throw new BusinessException(MessageCode.IP_SMS_SEND_THRESHOLD.getCode());
            }
            if (UserEnum.SMS_VERIFY.getValue().equals(type)) {
                sendForgetPasswordSms(findNum, clientIp);
            } else if (UserEnum.MAIL_VERIFY.getValue().equals(type)) {// 邮件验证

            }
            return;
        }
        // 第二步验证所有信息并修改密码

        Asserts.isNotEmpty(userName, MessageCode.SYS_USER_NOT_EXIST_PHONE_ERROR.getCode());
        Asserts.isNotEmpty(findNum, MessageCode.SYS_USER_NOT_EXIST_PHONE_ERROR.getCode());
        Asserts.isNotEmpty(smsCode, MessageCode.SYS_LOGIN_PARAM_IS_NULL.getCode());
        Asserts.isNotEmpty(password, MessageCode.SYS_LOGIN_PARAM_IS_NULL.getCode());
        Employee employee = new Employee();
        employee = userJpaDao.findByTuNo(userName);
        checkUserName(employee);// 检查帐号
        checkPhoneNum(findNum, employee);// 检查手机号
        String code = (String) redisService.get(findNum + UserEnum.REDIS_SMS_CODE.getValue());// 获取验证码
        checkSmsCode(code, smsCode);// 验证短信验证码
        String privateKey = UserEnum.PASSWORD_ENCRYPT_KEY.getValue();
        String key = null;
        try {
            key = Base64.encodeBase64String(privateKey.getBytes(CharsetUtil.UTF8));
        } catch (UnsupportedEncodingException e) {
            // TODO %CodeTemplates.catchblock.tododesc e.printStackTrace();
            log.error("解密错误{}", e);
        }
        String newPassword = AesEncryptUtil.aesDecrypt(key, password);
        PasswordEncoder createDelegatingPasswordEncoder =
                PasswordEncoderFactories.createDelegatingPasswordEncoder();
        employee.setPassWord(createDelegatingPasswordEncoder.encode(newPassword));
        userJpaDao.save(employee);// 保存用户新密码
    }

    @Override
    @Transactional
    public void updateOnDutyState(String userId, String onDutyState) throws Exception {
        // TODO %CodeTemplates.methodstub.tododesc
        Employee employee = new Employee();
        employee = userJpaDao.findTById(userId);
        if (UserEnum.ON_DUTY.getValue().equals(onDutyState)) {
            employee.setIsOnDuty(new Integer(UserEnum.ON_DUTY.getValue()));
            employee.setOnDutyDate(new Timestamp(System.currentTimeMillis()));
        } else {
            employee.setIsOnDuty(new Integer(UserEnum.OFF_DUTY.getValue()));
            employee.setOffDutyDate(new Timestamp(System.currentTimeMillis()));
        }
        userJpaDao.save(employee);
    }

    @Override
    public QueryResults getUserList(Pageable pageable, UserListForm form) throws Exception {
        try {
            QueryResults qr = userDao.getUserList(pageable, form);
            List<UserListVo> list = (List<UserListVo>) qr.getList();
            if(ObjectUtils.isNotEmpty(list)){
                list.forEach(e->{
                    e.setTuMobile(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, e.getTuMobile()));
                });
            }
            return qr;
        } catch (Exception e) {
            log.error("获取用户列表错误{}", e);
            throw e;
        }
    }

    @Override
    public void logOut(String userId) {
        redisService.deleteKey(userId);
    }

    /**
     * Title：checkUserName <br>
     * Description：验证帐号是否存在 <br>
     * author：王磊 <br>
     * date：2020年3月25日 下午3:39:16 <br>
     * @param employee <br>
     */
    public static void checkUserName(Employee employee) {
        if (employee == null) {
            throw new BusinessException(MessageCode.SYS_USER_NOT_EXIST_PHONE_ERROR.getCode());
        }
    }

    /**
     * Title：checkSmsCode <br>
     * Description：验证短信验证码 <br>
     * author：王磊 <br>
     * date：2020年3月26日 下午5:27:41 <br>
     * @param sysCode
     * @param code <br>
     */
    private void checkSmsCode(String sysCode, String code) {
        // TODO %CodeTemplates.methodstub.tododesc
        if (StringUtils.isEmpty(sysCode)) {
            throw new BusinessException(MessageCode.SYS_CODE_STALE.getCode());
        }
        if (!sysCode.equals(code)) {
            throw new BusinessException(MessageCode.SYS_CODE_ERROR.getCode());
        }
    }

    /**
     * Title：checkPhoneNum <br>
     * Description：验证手机号码是否系统预留 <br>
     * author：王磊 <br>
     * date：2020年3月26日 下午5:27:37 <br>
     * @param phoneNum
     * @param employee <br>
     */
    private void checkPhoneNum(String phoneNum, Employee employee) {
        if (!phoneNum.equals(employee.getTuMobile())) {
            throw new BusinessException(MessageCode.SYS_USER_NOT_EXIST_PHONE_ERROR.getCode());
        }
    }



    /**
     * Title：loginSuccess <br>
     * Description：H5和web单点登录成功的操作 <br>
     * author：王磊 <br>
     * date：2020年3月25日 下午11:27:40 <br>
     * @param tuAcct 系统账号 <br>
     * @param resourceProt 资源获取类型 <br>
     * @throws Exception 
     */
    @Transactional
    public LoginView<Object> loginSuccess(String tuAcct, String resourceProt) throws Exception {

        Employee employee = userJpaDao.findByTuAcct(tuAcct); // 系统账号
        if (employee == null) {
            throw new BusinessException(MessageCode.SYS_USER_NOT_EXIST.getCode());
        }
        checkLoginState(employee);
        employee.setLoginDate(new Timestamp(System.currentTimeMillis()));
        userJpaDao.save(employee);// 保存用户最新状态
        String token =
                JwtTokenUtils.createToken(employee.getTuNo(), new ArrayList<>(), employee.getId(),
                        false);
        String refToken = JwtTokenUtils.createRefreshToken(employee.getTuNo(), employee.getId());
        List<ResourceTreeVo> resList = new ArrayList<ResourceTreeVo>();
        Map<String, String> moduleMap = new HashMap<String, String>();
        List<Resources> resRedisList = new ArrayList<Resources>();
        try {
            resList = resourcesService.getAllResources(employee.getId(), resourceProt);
            moduleMap = resourcesService.getModuleResources(employee.getId(), resourceProt);
            resRedisList = resourcesDao.getResourcesByUserID(employee.getId());
        } catch (Exception e) {
            // TODO %CodeTemplates.catchblock.tododesc e.printStackTrace();
        }
        redisService.set(employee.getId() + UserEnum.TOKEN_KEY_SUFFIX.getValue() + resourceProt,
                JwtTokenUtils.TOKEN_PREFIX + token, new Long(UserEnum.TOKEN_PAST_DUE.getValue()));// 把token存入redis并设置过期时间
        redisService
                .set(UserEnum.RESOURCES_REDIS_LOGIN.getValue() + employee.getId(), resRedisList);// 资源存入redis
        LoginView<Object> loginView = new LoginView<Object>();
        loginView.setId(employee.getId());
        loginView.setUsername(employee.getTuNo());
        loginView.setIsFirstLogin(new Integer(UserEnum.NOT_FIRST_LOGIN.getValue()));
        loginView.setRefresh(JwtTokenUtils.TOKEN_PREFIX + refToken);
        loginView.setAuthorization(JwtTokenUtils.TOKEN_PREFIX + token);
        loginView.setResource(resList);
        loginView.setModuleMap(moduleMap);
        return loginView;
    }

    /**
     * Title：buildAuthTokenXML <br>
     * Description：构建发送xml  <br>
     * author：王磊 <br>
     * date：2020年3月12日 上午11:03:12 <br>
     * @param token
     * @return <br>
     */
    private String buildAuthTokenXML(String token) {
        StringBuffer param = new StringBuffer("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
        param.append("<root>");
        param.append("<token>");
        param.append(token);
        param.append("</token>");
        param.append("</root>");
        return param.toString();
    }

    /**
     * Title：getCharAndNumr <br>
     * Description：生成随机密码 <br>
     * author：王磊 <br>
     * date：2020年3月26日 下午1:53:02 <br>
     * @param strLength 生成随机字符长度
     * @param charStr 拼接字符串
     * @return <br>
     */
    private static String getCharAndNumr(int strLength, String charStr) {
        Random random = new Random();
        StringBuffer valSb = new StringBuffer();
        int charLength = charStr.length();
        for (int i = 0; i < strLength; i++) {
            int index = random.nextInt(charLength);
            valSb.append(charStr.charAt(index));
        }
        return valSb.toString();
    }

    /**
     * Title：checkLoginState <br>
     * Description：验证登录用户状态 <br>
     * author：王磊 <br>
     * date：2020年3月25日 下午3:39:27 <br>
     * @param employee <br>
     */
    private void checkLoginState(Employee employee) {
        // 判断是否为禁用用户(0 启用、1 禁用)
        if (UserEnum.USER_STATE_UNUSABLE.getValue().equals(employee.getUserState())) {
            throw new BusinessException(MessageCode.SYS_USER_STATE.getCode());
        }
        // 判断是否为锁定用户(0正常,1锁定)
        if (UserEnum.USER_STATE_UNUSABLE.getValue().equals(employee.getLockOutState())) {
            throw new BusinessException(MessageCode.SYS_USER_FREEZED.getCode());
        }
        // 判断是否为首次登录
        // if (employee.getLoginDate() == null) {
        // throw new BusinessException(MessageCode.SYS_PWD_INIT.getCode());
        // }
    }

    @Override
    public UserDetailVo getUserDetail(String userId) throws Exception {
        UserDetailVo userDetailVo = new UserDetailVo();
        List<UserDetailVo> list = userDao.getUserDetail(userId);
        if (!list.isEmpty()) {
            userDetailVo = (UserDetailVo) list.get(0);
            userDetailVo.setTuMobile(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, userDetailVo.getTuMobile()));
        }
        return userDetailVo;
    }

    @Override
    @Transactional
    public void updateUser(UpdateUserForm updateUserForm) {
        try {
            Employee employee = new Employee();
            employee = userJpaDao.findTById(updateUserForm.getUserId());
            employee.setUserState(updateUserForm.getUserState());
            if (StringUtils.isNotEmpty(updateUserForm.getAirportCode())) {
                employee.setAirport3code(updateUserForm.getAirportCode());
            }
            employee.setModifiUser(updateUserForm.getModifiUserId());
            employee.setModifiDate(new Timestamp(new Date().getTime()));
            userJpaDao.save(employee);// 保存用户新密码
            if (updateUserForm.getIsSysFunction() == new Integer(UserEnum.SYS_FUNCTION.getValue())) {
                // 添加方法获取修改人权限并关联

            } else {
                List<UserRole> addUserRoleList = new ArrayList<UserRole>();
                List<UserRole> removeUserRoleList = new ArrayList<UserRole>();
                if ((updateUserForm.getRoleIds() != null && updateUserForm.getRoleIds().length > 0)
                        || (updateUserForm.getPreRoleIds() != null && updateUserForm
                                .getPreRoleIds().length > 0)) {
                    int competenceNum = 0;
                    // 循环比较新的的角色数组来判断原本的数组存在此ID不,不存在则加入新增
                    for (String roleId : updateUserForm.getRoleIds()) {
                        Role r = roleRepository.getOne(roleId);
                        if (r.getCompetence().equals(RoleTypes.ADMIN.getCode())) {// 判断是否是管理员角色
                            competenceNum += 1;// 管理员角色数量判断
                            List<UserRole> urList = new ArrayList<UserRole>();
                            urList = resourcesDao.getUserRoleByRoleId(r.getId());
                            if (urList.size() > 0) {
                                boolean b = false;
                                for (UserRole ur : urList) {
                                    if (!ur.getEmployeeId().equals(employee.getId())) {// 判断当前管理员几角色是否有其他用户关联
                                        b = true;
                                    }
                                }
                                if (b) {// 如果管理角色已有人使用则抛错
                                    throw new BusinessException(
                                            MessageCode.COMPETENCE_ROLE_USED.getCode());
                                }
                            }
                        }
                        boolean isAdd = true;
                        for (String preRoleId : updateUserForm.getPreRoleIds()) {
                            if (roleId.equals(preRoleId)) {
                                isAdd = false;
                            }
                        }
                        if (isAdd) {
                            UserRole userRole = new UserRole();
                            userRole.setEmployeeId(updateUserForm.getUserId());
                            userRole.setRoleId(roleId);
                            addUserRoleList.add(userRole);
                        }
                    }
                    if (competenceNum > new Integer(UserEnum.COMPETENCE_ROLE_NUM.getValue())) {// 判断用户管理员角色
                        throw new BusinessException(MessageCode.COMPETENCE_ROLE_NUM_ERROR.getCode());
                    }
                    // 循环比较原本的角色数组来判断修改后的数组存在此ID不,不存在则加入删除集合
                    for (String preRoleId : updateUserForm.getPreRoleIds()) {
                        boolean isRemove = true;
                        for (String roleId : updateUserForm.getRoleIds()) {
                            if (preRoleId.equals(roleId)) {
                                isRemove = false;
                            }
                        }
                        if (isRemove) {
                            UserRole removeUserRole = new UserRole();
                            removeUserRole.setEmployeeId(updateUserForm.getUserId());
                            removeUserRole.setRoleId(preRoleId);
                            removeUserRoleList.add(removeUserRole);
                        }
                    }
                }
                if (addUserRoleList.size() > 0) {
                    // 保存用户和角色关系
                    resourcesDao.bindUsers(addUserRoleList);
                }
                if (removeUserRoleList.size() > 0) {
                    // 删除角色关系
                    resourcesDao.removeBindUsers(removeUserRoleList);
                }
            }
        } catch (Exception e) {
            log.error("修改用户错误:{}", e);
            throw e;
        }
    }

    @Override
    public List<DepartmentTreeVO> getDepartmentTreeByRole(String userId) throws Exception {
        // 需要通过角色去获取部门再构建树（先使用根部门获取树）
        // String deptId = "b1affa7f-ab67-49bc-a85c-c2e5914cf147";
        List<Role> roleList = roleDao.getRoleList(userId);
        String toId = "";
        for (Role role : roleList) {// 获取修改人的管理角色的部门
            if (role.getCompetence().equals(RoleTypes.ADMIN.getCode())) {
                toId = role.getDepartmentId();
            }
        }
        List<DepartmentTreeVO> list = departmentBaseDao.getDpartmentTreeByToId(toId);
        return buildTree(list);
        // TODO %CodeTemplates.methodstub.tododesc return null;
    }



    /**
     * Title：findUserByDepartmentId <br>
     * Description：通过部门ID查询用户信息 <br>
     * author：于琦海 <br>
     * date：2020/5/21 10:49 <br>
     * @param competence String, toId String
     * @return java.util.List<com.swcares.scgsi.user.common.model.view.UsersInfoView>
     */
    @Override
    public List<UsersInfoView> findUserByDepartmentId(String competence, String toId) {
        if (StringUtils.isBlank(competence) || StringUtils.isBlank(toId)) {
            return new ArrayList<>();
        }
        Map<String, Object> parameters = new HashMap<>(2);
        // 查询出部门底下的所有员工
        StringBuilder allSql =
                new StringBuilder(
                        "SELECT distinct E.ID as key,CONCAT(E.TUNO,E.TU_CNAME) as title  "
                                + "FROM EMPLOYEE E LEFT JOIN USER_ROLE UR ON E.ID = UR.EMPLOYEE_ID "
                                + "LEFT JOIN ROLE R ON R.ID = UR.ROLE_ID where E.TOID =:toId ");
        parameters.put("toId", toId);
        List<UsersInfoView> allResult =
                departmentBaseDao.findUserByDepartmentId(allSql.toString(), parameters);
        allResult.forEach(usersInfoView -> usersInfoView.setDisabled(false));
        // 管理员
        if (competence.equals(RoleTypes.ADMIN.getCode())) {
            // R.COMPETENCE ='0'，查出部门下所有管理员用户
            StringBuilder stopSql =
                    new StringBuilder(
                            "SELECT distinct E.ID as key,CONCAT(E.TUNO,E.TU_CNAME) as title "
                                    + "FROM EMPLOYEE E LEFT JOIN USER_ROLE UR ON E.ID = UR.EMPLOYEE_ID "
                                    + "LEFT JOIN ROLE R ON R.ID = UR.ROLE_ID where R.COMPETENCE ='0' and E.TOID =:toId ");
            List<UsersInfoView> stopResult =
                    departmentBaseDao.findUserByDepartmentId(stopSql.toString(), parameters);
            if (stopResult.size() > 0) {
                allResult.forEach(allResultView -> stopResult.forEach(stopResultView -> {
                    if (stopResultView.getKey().equals(allResultView.getKey())) {
                        allResultView.setDisabled(true);
                    }
                }));
            }
        }
        Collections.sort(allResult, (o1, o2) -> {
            Boolean o1Disabled = o1.getDisabled();
            Boolean o2Disabled = o2.getDisabled();
            if (o1Disabled ^ o2Disabled) {
                return o1Disabled ? 1 : -1;
            }
            return 0;
        });
        return allResult;
    }

    public static List<DepartmentTreeVO> buildTree(List<DepartmentTreeVO> nodes) {
        Map<String, List<DepartmentTreeVO>> sub =
                nodes.stream().filter(node -> !node.getParentKey().equals("@ROOT"))
                        .collect(Collectors.groupingBy(node -> node.getParentKey()));
        nodes.forEach(node -> node.setChildren(sub.get(node.getKey())));
        return nodes.stream().filter(node -> node.getParentKey().equals("@ROOT"))
                .collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<UserListVo> queryUserByNameAndNo(String keySearch) {
        Asserts.notNull(keySearch, MessageCode.PARAM_EXCEPTION.getCode());
        boolean isNum = keySearch.matches("^-?\\d+(\\.\\d+)?$");
        StringBuffer sb =
                new StringBuffer(
                        "select employee.id as id, employee.tuNo as tuNo, employee.TU_CNAME as tuCname ,employee.PHOTO as photo From Employee employee where 1=1 ");
        Map<String, Object> parameters = new HashMap<String, Object>();
        if (isNum) {
            // 工号
            sb.append(" and employee.tuNo like :tuNo ");
            parameters.put("tuNo", keySearch + "%");
        } else {
            // 姓名
            sb.append(" and employee.TU_CNAME like :tuCname ");
            parameters.put("tuCname", keySearch + "%");
        }
        return (List<UserListVo>) baseDAO.findBySQL_comm(sb.toString(), parameters,
                UserListVo.class);
    }

    @Override
    public List<RoleResourceTreeExtendVo> findRoleByUserIdAndModifiUserId(String modifiUserId,
            String userId) {
        List<Role> roleList = roleDao.getRoleList(modifiUserId);
        String toId = "";
        for (Role role : roleList) {// 获取修改人的管理角色的部门
            if (role.getCompetence().equals(RoleTypes.ADMIN.getCode())) {
                toId = role.getDepartmentId();
            }
        }
        if (StringUtils.isEmpty(toId)) {
            return null;
        }
        List<RoleResourceTreeExtendVo> newList = new ArrayList<RoleResourceTreeExtendVo>();
        List<RoleResourceTreeVo> list = roleService.findRoleResourceTreeVoByToId(toId);// 获取角色部门管理的所有非当前管理角色的角色
        List<RoleResourceTreeVo> listUserRole = roleService.findRoleResourceTreeVoByUserId(userId);// 获取被修改用户的角色集合
        for (RoleResourceTreeVo roleResourceTreeVo : list) {
            RoleResourceTreeExtendVo roleResourceTreeExtendVo = new RoleResourceTreeExtendVo();
            for (RoleResourceTreeVo roleResourceTreeForUser : listUserRole) {
                if (roleResourceTreeVo.getKey().equals(roleResourceTreeForUser.getKey())) {
                    roleResourceTreeVo.setIsChecked(UserEnum.CHECKED_ROLE.getValue());
                }
            }
            BeanUtils.copyProperties(roleResourceTreeVo, roleResourceTreeExtendVo);
            if (roleResourceTreeVo.getCompetence().equals(RoleTypes.ADMIN.getCode())) {// 判断是否管理角色
                List<UserRole> userRoles =
                        resourcesDao.getUserRoleByRoleId(roleResourceTreeVo.getKey());// 获取此角色的用户
                for (UserRole userRole : userRoles) {
                    if (!userRole.getEmployeeId().equals(userId)) {// 判断管理角色是否已关联非本用户的用户
                        roleResourceTreeExtendVo.setDisabled(true);
                    }
                }
            }
            newList.add(roleResourceTreeExtendVo);
        }
        return newList;
    }

    @Override
    public List<RoleResourceTreeVo> findRoleByUserId(String userId) {
        return roleService.findRoleResourceTreeVoByUserId(userId);// 获取用户的角色集合
    }

    /**
     * Title：sendForgetPasswordSms <br>
     * Description：发送找回密码的短信 <br>
     * author：王磊 <br>
     * date：2020年6月18日 上午9:29:35 <br>
     * @param telephone 电话号码<br>
     */
    private void sendForgetPasswordSms(String telephone, String ip) {
        String hasSendRandom =
                (String) redisService.get(telephone
                        + UserEnum.REDIS_SMS_RETRANSMISSION_TIME.getValue());
        Integer ipSendSmsCount =
                (Integer) redisService.get(ip + UserEnum.REDIS_SMS_CODE.getValue());
        if (ipSendSmsCount != null) {
            ipSendSmsCount++;
        } else {
            ipSendSmsCount = 1;
        }
        if (StringUtils.isNotBlank(hasSendRandom)) {
            throw new BusinessException(MessageCode.SMS_SEND_TIME_LIMIT.getCode(),
                    new String[] {UserEnum.SMS_RETRANSMISSION_TIME.getValue()});
        } else {
            String sysCode = getCharAndNumr(6, CHAR_NUM);// 生成4位数字验证码
            String msg = String.format(UserEnum.SMS_VERIFY_CONTENT.getValue(), sysCode);
            // 调用短信发送接口，发送成功存redis
            SmsSendResult smsSendResult = smsService.smsSendResultObj(telephone, msg);
            if (null == smsSendResult) {
                throw new BusinessException(MessageCode.SMS_SEND_FAIL.getCode());
            } else {
                if ("0".equals(smsSendResult.getCode())) {
                    // 存入redis,有效期5分钟默认
                    redisService.set(telephone + UserEnum.REDIS_SMS_CODE.getValue(), sysCode,
                            new Long(UserEnum.SMS_CODE_TIME.getValue()));
                    // 存入手机号码,来验证重发时间
                    redisService.set(telephone + UserEnum.REDIS_SMS_RETRANSMISSION_TIME.getValue(),
                            sysCode, new Long(UserEnum.SMS_RETRANSMISSION_TIME.getValue()));
                    // 存入发送短信的ip
                    redisService.set(ip + UserEnum.REDIS_SMS_CODE.getValue(), ipSendSmsCount,
                            new Long(UserEnum.IP_SMS_RETRANSMISSION_TIME.getValue()));
                } else {
                    throw new BusinessException(MessageCode.SMS_SEND_FAIL.getCode());
                }
            }
        }
    }

    /**
     * Title：getIpAddr <br>
     * Description：通过request获取客户端ip <br>
     * author：王磊 <br>
     * date：2020年9月29日 下午3:59:44 <br>
     * @param request
     * @return <br>
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || " unknown ".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || " unknown ".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || " unknown ".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * Title：calSecondifferenceValue <br>
     * Description：计算现在时间距离明天0点有多少秒 <br>
     * author：王磊 <br>
     * date：2020年3月25日 下午4:36:50 <br>
     * @return <br>
     */
    private Long calSecondifferenceValue() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (cal.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }

    @Override
    public void manualSync() {
        String thresholdRedisStr =
                (String) redisService.get(UserEnum.USER_MANUAL_SYNC_THRESHOLD_REDIS.getValue());
        String executionRedisStr =
                (String) redisService.get(UserEnum.USER_MANUAL_SYNC_EXECUTION_REDIS.getValue());
        if(StringUtil.isNotBlank(executionRedisStr)){
            throw new BusinessException(MessageCode.USER_MANUAL_SYNC_EXECUTION.getCode());
        }
        Integer thresholdRedis = new Integer(0);
        if (StringUtils.isNotEmpty(thresholdRedisStr)) {
            thresholdRedis = new Integer(thresholdRedisStr);
        }
        if (ObjectUtils.isNotEmpty(thresholdRedis)) {
            // 获取接口访问阀值
            List<Map<String, String>> dict =
                    dictDataService.getSelectDict(UserEnum.USER_MANUAL_SYNC.getValue());
            Integer threshold = new Integer(0);
            if (StringUtils.equals(dict.get(0).get("KEY"),
                    UserEnum.USER_MANUAL_SYNC_THRESHOLD.getValue())) {
                threshold = new Integer(dict.get(0).get("VALUE"));
            }
            if (thresholdRedis.intValue() >= threshold.intValue()) {
                throw new BusinessException(MessageCode.USER_MANUAL_SYNC_THRESHOLD.getCode(),
                        new String[] {threshold.toString()});
            }
        }
        thresholdRedis++;
        redisService.set(UserEnum.USER_MANUAL_SYNC_THRESHOLD_REDIS.getValue(),
                thresholdRedis.toString(), calSecondifferenceValue());
        synchronizationUserService.synchronizationUser();
    }
    

    /**@Override
    @Transactional
    public void initPwdExportExcel(HttpServletResponse response) throws Exception {
        try {
            List<Map<String, Object>> employeeIds = userJpaDao.getEmployeeAllId();
            // 创建工作薄
            HSSFWorkbook workbook = new HSSFWorkbook();
            // 创建工作表
            HSSFSheet sheet = workbook.createSheet("sheet1");
            HSSFRow rowTitle = sheet.createRow(0);
            rowTitle.createCell(0).setCellValue("姓名");
            rowTitle.createCell(1).setCellValue("工号");
            rowTitle.createCell(2).setCellValue("密码");
            for (int i = 0; i < employeeIds.size(); i++) {
                String str = CHAR_UPPER + CHAR_LOWER + CHAR_NUM;// 密码使用大小写字母和数字随机
                String initpwd = getCharAndNumr(8, str);// 生成随机密码
                Map<String, Object> map = employeeIds.get(i);
                // 创建HSSFRow对象
                HSSFRow row = sheet.createRow(i + 1);
                // 创建HSSFCell对象 设置单元格的值
                row.createCell(0).setCellValue(map.get("tuCname").toString());
                row.createCell(1).setCellValue(map.get("tuNo").toString());
                row.createCell(2).setCellValue(initpwd);
                PasswordEncoder createDelegatingPasswordEncoder = PasswordEncoderFactories.createDelegatingPasswordEncoder();
                initpwd =  createDelegatingPasswordEncoder.encode(initpwd);
                userJpaDao.updateInitPassword(map.get("id").toString(), initpwd);
            }
            ServletOutputStream out = null;
            out = response.getOutputStream();
            response.reset();
            // 设置文件头
            // 下载文件能正常显示中文
            response.setHeader("Content-Disposition", "attchement;filename="
                    + new String("初始化密码信息表.xls".getBytes("UTF-8"), "ISO8859-1"));
            response.setContentType("application/msexcel");
            workbook.write(out);
            workbook.close();
        } catch (Exception e) {
            log.error("初始化密码异常:{}", e);
            throw e;
        }
    }**/

    @Transactional
    @Override
    public void handleUserSensitiveInfo() {
        try {
            List<Employee> employeeList = userJpaDao.findAll();
            log.info("-【处理员工敏感信息（证件号、电话）加密】--共计：[{}]条",employeeList.size());
            for(Employee employee:employeeList){
                employee.setTuIdc(AesEncryptUtil.aesEncryptScgsi(employee.getTuIdc()));
                employee.setTuMobile(AesEncryptUtil.aesEncryptScgsi(employee.getTuMobile()));
            }
            userJpaDao.saveAll(employeeList);
        } catch (Exception e) {
            log.error("处理员工敏感信息,异常:{}", e);
            throw e;
        }
    }
}
