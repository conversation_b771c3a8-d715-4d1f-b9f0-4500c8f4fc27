package com.swcares.scgsi.user.service.impl;

import java.awt.image.BufferedImage;
import java.io.IOException;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.swcares.scgsi.common.userenum.UserEnum;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.user.service.KaptchaService;

/**
 * ClassName：com.swcares.scgsi.login.service.impl.KaptchaServiceImpl <br>
 * Description：验证码服务实现 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月24日 下午4:30:39 <br>
 * @version v1.0 <br>
 */
@Service
public class KaptchaServiceImpl implements KaptchaService {

    private static final String EXPIRES = "Expires";

    private static final String CACHE_CONTROL = "Cache-Control";

    private static final String CACHE_CONTROL_SETTING1 = "no-store, no-cache, must-revalidate";

    private static final String CACHE_CONTROL_SETTING2 = "post-check=0, pre-check=0";

    private static final String PRAGMA = "Pragma";

    private static final String NO_CACHE = "no-cache";

    private static final String IMAGE_TYPE = "image/jpeg";

    private static final String JPG = "jpg";

    @Autowired
    private DefaultKaptcha defaultKaptcha;

    @Autowired
    private RedisService redisService;

    @Override
    public void generateVerifyCodeImage(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        String codeKey = request.getParameter("codeKey");
        response.setDateHeader(EXPIRES, 0);
        response.setHeader(CACHE_CONTROL, CACHE_CONTROL_SETTING1);
        response.addHeader(CACHE_CONTROL, CACHE_CONTROL_SETTING2);
        response.setHeader(PRAGMA, NO_CACHE);
        response.setContentType(IMAGE_TYPE);
        String capText = defaultKaptcha.createText();
        redisService.set(codeKey + UserEnum.REDIS_VERIFY_CODE.getValue(), capText,
                new Long(UserEnum.EXPIRATION_TIME.getValue()));
        BufferedImage bi = defaultKaptcha.createImage(capText);
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            ImageIO.write(bi, JPG, out);
            out.flush();
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }
}
