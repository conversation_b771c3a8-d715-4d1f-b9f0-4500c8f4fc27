package com.swcares.scgsi.user.dao;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.hum.employee.entity.Employee;

/**
 * ClassName：com.swcares.scgsi.user.dao.UserDao <br>
 * Description：用户dao <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月11日 下午11:00:49 <br>
 * @version v1.0 <br>
 */
public interface UserJpaDao  extends BaseJpaDao<Employee, Serializable>{

    /**
     * Title：findByTuNo <br>
     * Description：通过工号获取用户 <br>
     * author：王磊 <br>
     * date：2020年4月3日 上午10:58:35 <br>
     * @param tuNo
     * @return
     * @throws Exception <br>
     */
    Employee findByTuNo(@Param("tuNo") String tuNo) throws Exception;

    /**
     * Title：findByTuAcct<br>
     * Description：通过系统账号获取用户 <br>
     * author：王磊 <br>
     * date：2020年4月3日 上午10:58:56 <br>
     * @param tuAcc
     * @return
     * @throws Exception <br>
     */
    Employee findByTuAcct(@Param("tuAcc") String tuAcc) throws Exception;

    /**
     * Title：getEmployeeAllId <br>
     * Description：获取所有id <br>
     * author：王磊 <br>
     * date：2020年3月26日 下午11:11:47 <br>
     * @return
     * @throws Exception <br>
     */
    @Query(value = "select id,tu_cname as tuCname,tuno as tuNo from employee", nativeQuery = true)
    List<Map<String, Object>> getEmployeeAllId() throws Exception;

    /**
     * Title：updateInitPassword <br>
     * Description：修改密码 <br>
     * author：王磊 <br>
     * date：2020年3月26日 下午11:11:32 <br>
     * @param userID
     * @param password
     * @return
     * @throws Exception <br>
     */
    @Query(value = "update employee set pass_word = ?2 where id= ?1", nativeQuery = true)
    @Modifying(clearAutomatically = true)
    int updateInitPassword(String userID, String password) throws Exception;

    /**
     * Title：findByToId <br>
     * Description：通过部门ID获取员工集合 <br>
     * author：王磊 <br>
     * date：2020年4月9日 下午12:32:40 <br>
     * @param toId
     * @return
     * @throws Exception <br>
     */
    List<Employee> findByToId(@Param("toId") String toId) throws Exception;

  /**
   * Title：findByToIdAndIsOnDuty <br>
   * Description：通过部门和值班标识来获取用户 <br>
   * author：王磊 <br>
   * date：2020年4月11日 下午5:41:23 <br>
   * @param toId
   * @param onDuty
   * @return
   * @throws Exception <br>
   */
    List<Employee> findByToIdAndIsOnDuty(@Param("toId") String toId,@Param("isOnDuty") Integer onDuty) throws Exception;
}
