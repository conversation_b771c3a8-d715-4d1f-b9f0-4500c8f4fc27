package com.swcares.scgsi.user.common.model.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Clob;
import java.sql.SQLException;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.user.common.model.vo.UserListVo <br>
 * Description：用户列表 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月11日 下午11:32:45 <br>
 * @version v1.0 <br>
 */
@ApiModel("用户列表返回vo")
@Data
@EncryptionClassz
public class UserListVo {
    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private String id;
    /**
     * 人员照片
     */
    @ApiModelProperty("用户照片")
    private Clob photo;
    /**
     * 员工编号
     */
    @ApiModelProperty("员工编号")
    private String tuNo;
    /**
     * 中文名
     */
    @ApiModelProperty("员工姓名")
    private String tuCname;
    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String deptName;
    /**
     * 职位
     */
    @ApiModelProperty("职位")
    private String position;
    /**
     * 手机
     */
    @ApiModelProperty("联系电话")
    @Encryption
    private String tuMobile;

    /**
     * 邮件地址
     */
    @ApiModelProperty("邮箱地址")
    private String tuEmail;
    /**
     * 本系统用户状态((0 启用、1 禁用))
     */
    @ApiModelProperty("用户状态")
    private Integer userState;
    /**
     * 本系统用户状态((0 正常、1 锁定))
     */
    @ApiModelProperty("用户锁定状态")
    private Integer userLockState;
    /**
     * 最后登录时间
     */
    @ApiModelProperty("最后登录时间")
    private String loginDate;

    /**
     * 最后修改时间
     */
    @ApiModelProperty("最后修改时间")
    private String modifiDate;

    /**
     * Title：getPhoto <br>
     * Description：重写get方法转换clob <br>
     * author：王磊 <br>
     * date：2020年4月13日 下午4:43:36 <br>
     * @return <br>
     */
    public String getPhoto() {
        String value = null;
        try {
            if (photo != null) {
                value = photo.getSubString((long) 1, (int) photo.length());// 把clob数据转换为string
            }
        } catch (SQLException e) {
            // TODO %CodeTemplates.catchblock.tododesc e.printStackTrace();
        }
        return value;
    }
}
