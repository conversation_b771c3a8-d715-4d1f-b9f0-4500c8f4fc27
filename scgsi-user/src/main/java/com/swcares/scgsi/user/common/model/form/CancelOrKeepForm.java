package com.swcares.scgsi.user.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：CancelOrKeepForm <br>
 * Package：com.swcares.scgsi.user.common.model.form <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 05月29日 15:19 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "取消或者收藏")
public class CancelOrKeepForm {

    @ApiModelProperty(value = "用户ID", required = true)
    private String id;

    @ApiModelProperty(value = "名称", required = true)
    private String name;

    @ApiModelProperty(value = "路径", required = true)
    private String path;
}
