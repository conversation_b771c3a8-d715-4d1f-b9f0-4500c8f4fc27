package com.swcares.scgsi.user.common.model.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.login.common.model.from.UserLoginForm <br>
 * Description：修改密码表单 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月24日 下午4:59:10 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "修改密码表单对象")
public class UpdatePasswordForm {
    /** 用户ID */
    private String userId;

    /** 密码 */
    @ApiModelProperty(value = "密码", required = true)
    @NotEmpty(message = "密码为必填项")
    private String password;

    /** 新密码 */
    @ApiModelProperty(value = "新密码", required = true)
    @NotEmpty(message = "新密码为必填项")
    private String newPassword;
}
