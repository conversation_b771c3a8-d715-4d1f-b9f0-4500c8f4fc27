package com.swcares.scgsi.department.dao;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.department.entity.Users;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：Depart <br>
 * Package：com.swcares.scgsi.department <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月03日 18:49 <br>
 * @version v1.0 <br>
 */
@Repository
public class DepartmentTreeDao {

    @Resource
    private BaseDAO baseDAO;

    /**
      Title：getUserInfoByToId（） <br>
     * Description：通过TOID查询用户信息，转换为UserInfo对象 <br>
     * author：于琦海 <br>
     * date：2020/4/3 18:51 <br>
     * @param toId String
     * @return DepartmentService.UserInfo
     */
    public List<Users> getUserInfoByToId(String toId) {
        StringBuffer sql = new StringBuffer("select * from EMPLOYEE where TOID =:toId");
        Map<String, Object> map = new HashMap<>();
        map.put("toId", toId);
        return (List<Users>) baseDAO.findBySQL_comm(sql.toString(), map,Users.class);
    }
}
