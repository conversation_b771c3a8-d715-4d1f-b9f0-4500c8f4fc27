package com.swcares.scgsi.department.entity;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.sql.Timestamp;

@Data
@Entity
@Table(name = "EMPLOYEE")
@EncryptionClassz
public class Users {


	@Id
	@Column(name = "ID")
	@GeneratedValue(generator = "uuid2")
	@GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
	private String id;

	/**
	 * 员工编号
	 */
	@Column(name = "TUNO")
	private String tuNo;

	/**
	 * 系统账号
	 */
	@Column(name = "TU_ACCT")
	private String tuAcct;

	/**
	 * 人员照片
	 */
	@Column(name = "PHOTO")
	private String photo;

	/**
	 * 中文名
	 */
	@Column(name = "TU_CNAME")
	private String tuCname;

	/**
	 * 性别
	 */
	@Column(name = "TU_SEX")
	private String tuSex;

	/**
	 * 员工岗位2
	 */
	@Column(name = "TU_EXT2")
	private String tuExt2;

	/**
	 * 部门ID
	 */
	@Column(name = "TOID")
	private String toId;

	/**
	 * 专业
	 */
	@Column(name = "TU_SPECIALTY")
	private String tuSpecialty;

	/**
	 * 员工归属属性
	 */
	@Column(name = "TU_OWNPERTITY")
	private String tuOwnpertity;

	/**
	 * 更改状态原因
	 */
	@Column(name = "TU_STATEREASON")
	private String tuStateReason;

	/**
	 * 国家/地区
	 */
	@Column(name = "TU_REGION")
	private String tuRegion;

	/**
	 * 联系电话
	 */
	@Column(name = "TU_TEL")
	@Encryption
	private String tuTel;

	/**
	 * 手机
	 */
	@Column(name = "TU_MOBILE")
	@Encryption
	private String tuMobile;

	/**
	 * 邮件地址
	 */
	@Column(name = "TU_EMAIL")
	private String tuEmail;

	/**
	 * 别名
	 */
	@Column(name = "TU_OTHERNAME")
	private String tuOtherName;

	/**
	 * 身份证号
	 */
	@Column(name = "TU_IDC")
	@Encryption
	private String tuIdc;

	/**
	 * 状态((0 启用、1 禁用))
	 */
	@Column(name = "TU_STATE")
	private String tuState = "1";

	/**
	 * 生日
	 */
	@Column(name = "TU_BDATE")
	private String tuBdate;

	/**
	 * 住址
	 */
	@Column(name = "TU_ADDRE")
	private String tuAddre;

	/**
	 * 民族
	 */
	@Column(name = "TU_NATION")
	private String tuNation;

	/**
	 * 国籍
	 */
	@Column(name = "TU_NATIONAL")
	private String tuNational;

	/**
	 * 政治面貌
	 */
	@Column(name = "TU_POLITICALSTATUS")
	private String tuPoliticalstatus;

	/**
	 * 参加工作时间
	 */
	@Column(name = "TU_WORK_DATE")
	private String tuWorkDate;

	/**
	 * 入职日期
	 */
	@Column(name = "TU_FSTDATE")
	private String tuFstdate;

	/**
	 * 籍贯
	 */
	@Column(name = "TU_ORIGIN")
	private String tuOrigin;

	/**
	 * 学历
	 */
	@Column(name = "TU_EXT1")
	private String tuExt1;

	/**
	 * 锁定状态(0正常,1锁定)
	 */
	@Column(name = "LOCK_OUT_STATE")
	private Integer lockOutState = 0;

	/**
	 * 锁定时间
	 */
	@Column(name = "LOCK_OUT_DATE")
	private Timestamp lockOutDate;

	/**
	 * 密码
	 */
	@Column(name = "PASS_WORD")
	private String passWord;

	/**
	 * 最后登录时间
	 */
	@Column(name = "LOGIN_DATE")
	private Timestamp loginDate;


}