package com.swcares.scgsi.department.common.model.vo;

import lombok.Getter;
import lombok.Setter;

import java.sql.Clob;
import java.sql.SQLException;

/**
 * ClassName：com.swcares.scgsi.department.common.model.vo.EmpForTreeVO <br>
 * Description：用于返回部门下的员工信息（不用employee的原因是最小化的返回数据） <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月14日 下午8:48:03 <br>
 * @version v1.0 <br>
 */
@Getter
@Setter
public class EmpForTreeVO {
    
    /**
     * emp主键
     */
    private String id;
    
    /**
     * 员工姓名
     */
    private String tuCname;
    
    /**
     * 员工工号
     */
    private String TUNO;

    /**
     * 用户头像
     */
    private Clob photo;

    /**
     * Title： getPhoto <br>
     * Description： Clob类型转string <br>
     * author：傅欣荣 <br>
     * date：2020/7/1 13:19 <br>
     * @param
     * @return
     */
    public String getPhoto() {
        String value = null;
        try {
            if (photo != null) {
                value = photo.getSubString((long) 1, (int) photo.length());// 把clob数据转换为string
            }
        } catch (SQLException e) {
        }
        return value;
    }

}
