package com.swcares.scgsi.department.common.model.vo;

import java.util.List;
import com.swcares.scgsi.hum.department.entity.Department;
import lombok.Getter;
import lombok.Setter;

/**
 * ClassName：com.swcares.scgsi.department.common.model.vo.DepartAndUserVO <br>
 * Description：用于部门树的加载 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月14日 下午8:15:40 <br>
 * @version v1.0 <br>
 */
@Getter
@Setter
public class DepartAndUserVO {
    
    private List<Department> depars;
    
    private List<EmpForTreeVO> emps;
}
