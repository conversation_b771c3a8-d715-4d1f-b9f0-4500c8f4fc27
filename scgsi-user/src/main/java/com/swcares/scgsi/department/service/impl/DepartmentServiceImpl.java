package com.swcares.scgsi.department.service.impl;


import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.department.common.model.view.DropdownBoxDepartmentView;
import com.swcares.scgsi.department.common.model.vo.DepartAndUserVO;
import com.swcares.scgsi.department.common.model.vo.DepartmentUserVO;
import com.swcares.scgsi.department.common.model.vo.EmpForTreeVO;
import com.swcares.scgsi.department.common.model.vo.KeySearchDepartVO;
import com.swcares.scgsi.department.dao.DepartmentBaseDao;
import com.swcares.scgsi.department.dao.DepartmentTreeDao;
import com.swcares.scgsi.department.entity.Users;
import com.swcares.scgsi.department.service.DepartmentService;
import com.swcares.scgsi.entity.Role;
import com.swcares.scgsi.hum.department.dao.DepartmentDao;
import com.swcares.scgsi.hum.department.entity.Department;
import com.swcares.scgsi.hum.employee.dao.EmployeeDao;
import com.swcares.scgsi.hum.employee.entity.Employee;
import com.swcares.scgsi.role.dao.RoleRepository;
import com.swcares.scgsi.user.dao.UserJpaDao;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.scgsi.department.Demo <br>
 * Description：部分层次结构服务层 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年1月7日 上午9:55:56 <br>
 * @version v1.0 <br>
 */
@Service
public class DepartmentServiceImpl implements DepartmentService {

    @Resource
    private DepartmentDao departmentDao;

    @Resource
    private DepartmentTreeDao departmentTreeDao;

    @Resource
    private DepartmentBaseDao departmentBaseDao;

    @Resource
    private BaseDAO baseDAO;

    /**
     * Title：getAllDepartMent() <br>
     * Description：获取所有部门信息 <br>
     * author：于琦海 <br>
     * date：2020/3/24 11:05 <br>
     * @return Object
     */
    public Object getAllDepartment(boolean flag) {
        // 查询出所有的部门信息
        List<Department> all = departmentDao.findAll();
        List<MiniDepartment> allDepartment = new ArrayList<>();
        for (Department department : all) {
            MiniDepartment miniDepartment = new MiniDepartment();
            BeanUtils.copyProperties(department, miniDepartment);
            allDepartment.add(miniDepartment);
        }
        List<List<MiniDepartment>> linkList = new LinkedList<>();
        // 从根路径开始
        List<MiniDepartment> rootNode = this.getRootNode(allDepartment);
        this.recursive(rootNode, allDepartment, linkList);
        // 尽早回收引用
        all = null;
        allDepartment = null;
        // 整理返回部门信息
        return this.assemblyDepartment(linkList, rootNode.get(0), flag);
    }

    /**
     * Title：assemblyDepartment() <br>
     * Description：组装部门树行结构数据 <br>
     * author：于琦海 <br>
     * date：2020/3/26 11:09 <br>
     * @param result List<List<MiniDepartment>>
     * @param rootNode MiniDepartment
     * @return DepartmentView
     */
    private Object assemblyDepartment(List<List<MiniDepartment>> result, MiniDepartment rootNode,
            boolean flag) {
        DepartmentView departmentView = new DepartmentView();
        departmentView.setRootNode(rootNode.getToFname());
        List<Map<String, List<MiniDepartment>>> maps = new LinkedList<>();
        // 根据父亲节点进行分组，按照层级结构返回
        for (int i = 0; i < result.size(); i++) {
            List<MiniDepartment> miniDepartments = result.get(i);
            Map<String, List<MiniDepartment>> collect = miniDepartments.stream()
                    .collect(Collectors.groupingBy(MiniDepartment::getToPid));
            maps.add(collect);
        }
        // 倒叙转意一遍
        Map<String, List<SubDepartmentView>> resultMap = new LinkedHashMap<>();
        for (int i = maps.size() - 1; i >= 0; i--) {
            List<SubDepartmentView> subDepartmentViewList = new ArrayList<>();
            Map<String, List<MiniDepartment>> stringListMap = maps.get(i);
            stringListMap.forEach((key, value) -> value.forEach(miniDepartment -> {
                SubDepartmentView subDepartmentView = new SubDepartmentView();
                subDepartmentView.setToId(miniDepartment.getToId());
                subDepartmentView.setToFname(miniDepartment.getToFname());
                subDepartmentView.setToPid(miniDepartment.getToPid());
                subDepartmentViewList.add(subDepartmentView);
            }));
            resultMap.put(String.valueOf(i), subDepartmentViewList);
        }
        List<SubDepartmentView> resultSub = new ArrayList<>();
        // 遍历组装返回所需内容
        resultMap.forEach((key, value) -> {
            // 第一次就把所的往里面加，加的是叶子节点和其父亲节点的数据
            if (resultSub.size() <= 0) {
                if (flag) {
                    getResultList(value);
                }
                resultSub.addAll(value);
                return;
            }
            List<SubDepartmentView> subList = new ArrayList<>();
            value.forEach(subDepartmentView -> {
                SubDepartmentView resultView = new SubDepartmentView();
                BeanUtils.copyProperties(subDepartmentView, resultView);
                List<SubDepartmentView> collect = resultSub.stream()
                        .filter(view -> view.getToPid().equals(resultView.getToId()))
                        .collect(Collectors.toList());
                if (collect.size() > 0) {
                    resultView.setChildren(collect);
                }
                subList.add(resultView);
            });
            resultSub.clear();
            if (flag) {
                getResultList(subList);
            }
            resultSub.addAll(subList);
        });
        departmentView.setChildren(resultSub);
        return departmentView;
    }

    /**
     * Title：getResultList（） <br>
     * Description：获取部门员工，并进行组装 <br>
     * author：于琦海 <br>
     * date：2020/4/4 10:43 <br>
     * @param list List<SubDepartmentView>
     */
    private void getResultList(List<SubDepartmentView> list) {
        list.forEach(subDepartmentView -> {
            if (Objects.isNull(subDepartmentView.getChildren())) {
                List<Users> userInfoByToId =
                        departmentTreeDao.getUserInfoByToId(subDepartmentView.getToId());
                List<SubDepartmentView> result = new ArrayList<>();
                if (Objects.nonNull(userInfoByToId) && userInfoByToId.size() > 0) {
                    userInfoByToId.forEach(user -> {
                        SubDepartmentView departmentView = new SubDepartmentView();
                        departmentView.setId(user.getId());
                        departmentView.setTuCname(user.getTuCname());
                        departmentView.setChildren(null);
                        result.add(departmentView);
                    });
                }
                subDepartmentView.setChildren(result);
            }
        });
    }

    /**
     * Title：recursive（） <br>
     * Description：递归调用查询部门层次结构 <br>
     * author：于琦海 <br>
     * date：2020/3/26 10:29 <br>
     * @param miniDepartments List<MiniDepartment>
     * @param allDepartment List<MiniDepartment>
     * @return Object
     */
    private void recursive(List<MiniDepartment> miniDepartments, List<MiniDepartment> allDepartment,
            List<List<MiniDepartment>> result) {
        List<MiniDepartment> subDepartment = new ArrayList<>();
        for (MiniDepartment miniDepartment : miniDepartments) {
            List<MiniDepartment> subMiniDepartment =
                    this.getSubDepartment(miniDepartment.getToId(), allDepartment);
            if (subMiniDepartment.size() > 0) {
                subDepartment.addAll(subMiniDepartment);
            }
        }
        if (subDepartment.size() <= 0) {
            return;
        }
        result.add(subDepartment);
        this.recursive(subDepartment, allDepartment, result);
    }

    /**
     * Title：getRootNode() <br>
     * Description：获取整个项目的根节点 <br>
     * author：于琦海 <br>
     * date：2020/3/24 15:17 <br>
     * @param departments List<Department>
     * @return List<Department>
     */
    private List<MiniDepartment> getRootNode(List<MiniDepartment> departments) {
        // 获取根节点的部门信息
        return departments.stream()
                .filter(miniDepartment -> "@ROOT".equals(miniDepartment.getToPid()))
                .collect(Collectors.toList());
    }

    /**
     * Title：getSubDepartment（） <br>
     * Description：通过当前节点去获取当前部门的子部门 <br>
     * author：于琦海 <br>
     * date：2020/3/24 15:34 <br>
     * @param toid  String
     * @param departments List<Department>
     * @return List<Department>
     */
    private List<MiniDepartment> getSubDepartment(String toid, List<MiniDepartment> departments) {
        return departments.stream().filter(department -> toid.equals(department.getToPid()))
                .collect(Collectors.toList());
    }

    /**
     * Title：getUserInfo（） <br>
     * Description：根据部门ID查询用户信息 <br>
     * author：于琦海 <br>
     * date：2020/4/9 15:09 <br>
     * @param toId String
     * @return List<DepartmentUserView>
     */
    public List<DepartmentUserVO> getUserInfo(String toId) {
        List<Users> userInfoByToId = departmentTreeDao.getUserInfoByToId(toId);
        List<DepartmentUserVO> result = new ArrayList<>();
        userInfoByToId.forEach(users -> {
            DepartmentUserVO departmentUserVo = new DepartmentUserVO();
            departmentUserVo.setId(users.getId());
            departmentUserVo.setTitle(users.getTuNo() + users.getTuCname());
            result.add(departmentUserVo);
        });
        return result;
    }

    /**
     * Title：getDpartmentChildById <br>
     * Description：根据当前节点查询出下级节点<br>
     * author：夏阳 <br>
     * date：2020年4月14日 下午6:46:34 <br>
     * @param deptId
     * @return departAndUserVO <br>
     */
    @SuppressWarnings("unchecked")
    public DepartAndUserVO getDpartmentChildById(String deptId) {
        // 获取当前子节点的VO
        List<Department> departs = departmentBaseDao.getDpartmentChildById(deptId);

        // 根据当前部门id获取对应的员工信息
        String sql = "select id, TUNO, tu_cname as tuCname ,PHOTO as photo from employee where toid =:toid ";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("toid", deptId);
        List<EmpForTreeVO> emps = (List<EmpForTreeVO>) baseDAO.findBySQL_comm(sql, map, EmpForTreeVO.class);
        
        DepartAndUserVO departAndUserVO = new DepartAndUserVO();
        departAndUserVO.setDepars(departs);
        departAndUserVO.setEmps(emps);
        return departAndUserVO;
    }

    /**
     * Title：findDepartmentName <br>
     * Description：查询部门的 <br>
     * author：于琦海 <br>
     * date：2020/4/20 10:31 <br>
     * @param: DepartmentView
     * @return Set<DepartmentView>
     */
    @Override
    public Set<DropdownBoxDepartmentView> findDepartmentName(List<String> departmentById, String roleId) {
        // 查询出来的是部门ID和名称，需要过滤掉已经有管理员的部门，同时部门是启用状态的
        List<DropdownBoxDepartmentView> departmentInfo = departmentBaseDao.findDepartmentName(departmentById);
        List<DropdownBoxDepartmentView> removeInfo = new ArrayList<>();
        if (departmentInfo.size() > 0) {
            departmentInfo.forEach(dropdownBoxDepartmentView -> {
                // 通过部门ID查询role角色
                if (departmentBaseDao.findRoleByToId(dropdownBoxDepartmentView.getId()) > 0) {
                    removeInfo.add(dropdownBoxDepartmentView);
                }
            });
        }
        departmentInfo.removeAll(removeInfo);
        if (StringUtils.isNotBlank(roleId)) {
            departmentInfo.addAll(departmentBaseDao.findDepartmentInfo(roleId));
        }
        return new HashSet<>(departmentInfo);
    }

    @Data
    public class DepartmentView {
        /** 根节点 */
        private String rootNode;

        /** 部门信息 */
        private List<SubDepartmentView> children;

    }

    @Data
    public class SubDepartmentView {

        /** 姓名 */
        private String toFname;

        /** 部门ID */
        private String toId;

        /** 父亲节点 */
        private String toPid;

        /** 中文名 */
        private String tuCname;

        private String id;

        /** 子部门信息 */
        private List<SubDepartmentView> children;
    }

    @Data
    public class MiniDepartment {
        /** 部门ID */
        private String toId;

        /** 父亲节点 */
        private String toPid;

        /** 姓名 */
        private String toFname;
    }
    /**
     * Title：getDepartmentChildInfo <br>
     * Description： 根据部门id获取下面子部门<br>
     * author：王建文 <br>
     * date：2020-4-15 10:44 <br>
     * @param
     * @return
     */
    @Override
    public QueryResults getDepartmentChildInfo(String deptId,int current,int pageSize,String toId,String toFname){
        return departmentBaseDao.getDepartmentChildInfo(deptId,current,pageSize,toId,toFname);
    }
    /**
     * Title：updateDepartStatus <br>
     * Description： 部门激活禁用<br>
     * author：王建文 <br>
     * date：2020-4-15 11:02 <br>
     * @param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDepartStatus(String id,int status){
        departmentBaseDao.updateDepartStatus(id,status);
    }

    @Autowired
    EmployeeDao employeeDao;

    @Override
    public List<KeySearchDepartVO> keySearchDepartment(String keySearch) {
        Specification<Employee> specification = (root, query, cb) -> {
            List<Predicate> predicatesList = new ArrayList<>();
            if (!StringUtils.isBlank(keySearch)) {
                Predicate namePredicate = cb.like(root.get("tuCname"), keySearch+"%");
                Predicate tunoPredicate = cb.equal(root.get("tuNo"), keySearch);
                predicatesList.add(cb.or(tunoPredicate, namePredicate));
            }
            return cb.and(predicatesList.toArray(new Predicate[predicatesList.size()]));
        };

        List<Employee> employeeOptional = employeeDao.findAll(specification);
        if (employeeOptional.isEmpty()){
           return null;
        }
        List<KeySearchDepartVO> result = new ArrayList<>();
        for (Employee employee : employeeOptional) {
            KeySearchDepartVO departAndUserVO = new KeySearchDepartVO();
            EmpForTreeVO empForTreeVO = new EmpForTreeVO();
            BeanUtils.copyProperties(employee, empForTreeVO);
            empForTreeVO.setTUNO(employee.getTuNo());
            departAndUserVO.setEmps(empForTreeVO);
            StringBuffer sql = new StringBuffer();
            Map<String, Object> paramsMap = new HashMap<String, Object>();
            sql.append("SELECT * FROM DEPARTMENT d START WITH  d.TOID =:toid CONNECT BY PRIOR d.TOPID = d.TOID AND LEVEL <= 3");
            paramsMap.put("toid", employee.getToId());
            List<Department> departs = (List<Department>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, Department.class);
            departAndUserVO.setDepars(departs);
            result.add(departAndUserVO);
        }
        return result;
    }

}
