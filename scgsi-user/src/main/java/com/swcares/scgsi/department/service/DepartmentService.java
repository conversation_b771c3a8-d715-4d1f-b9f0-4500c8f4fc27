package com.swcares.scgsi.department.service;


import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.department.common.model.view.DropdownBoxDepartmentView;
import com.swcares.scgsi.department.common.model.vo.DepartAndUserVO;
import com.swcares.scgsi.department.common.model.vo.KeySearchDepartVO;
import com.swcares.scgsi.department.entity.Users;
import com.swcares.scgsi.hum.department.entity.Department;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：DepartmentService <br>
 * Package：com.swcares.scgsi.department.service <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月20日 10:30 <br>
 * @version v1.0 <br>
 */
public interface DepartmentService {

    /**
     * Title：findDepartmentName <br>
     * Description：查询部门的 <br>
     * author：于琦海 <br>
     * date：2020/4/20 10:31 <br>
     * @param: DepartmentView
     * @return Set<DropdownBoxDepartmentView>
     */
    Set<DropdownBoxDepartmentView> findDepartmentName(List<String> departmentById,String roleId);

    /**
     * Title：getDepartmentChildInfo <br>
     * Description： 根据部门id获取下面子部门<br>
     * author：王建文 <br>
     * date：2020-4-15 10:44 <br>
     * @param
     * @return
     */
    public QueryResults getDepartmentChildInfo(String deptId,int current,int pageSize,String toId,String toFname);
    /**
     * Title：updateDepartStatus <br>
     * Description： 部门激活禁用<br>
     * author：王建文 <br>
     * date：2020-4-15 11:02 <br>
     * @param
     * @return
     */
    public void updateDepartStatus(String id,int status);

    List<KeySearchDepartVO> keySearchDepartment(String keySearch);
}
