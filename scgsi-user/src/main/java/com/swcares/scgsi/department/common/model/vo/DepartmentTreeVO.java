package com.swcares.scgsi.department.common.model.vo;

import java.util.List;
import lombok.Data;

/**
 * ClassName：com.swcares.scgsi.department.common.model.vo.DepartmentTreeVo <br>
 * Description：部门树结构vo <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月13日 下午8:00:40 <br>
 * @version v1.0 <br>
 */
@Data
public class DepartmentTreeVO {

    /** 主键ID */
    private String id;

    /** 部门名称 */
    private String title;

    /** 树的唯一值（目前储存的组织机构代码） */
    private String key;
    
    /** 父级树的唯一值（目前储存的父组织机构代码） */
    private String parentKey;

    /** 子部门集合*/
    private List<DepartmentTreeVO> children;
}
