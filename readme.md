### 模块名
+ `scgsi-web` 后台服务模块，工作人员后台使用，做接口聚合层使用
+ `scgsi-web-pssn` 旅客模块，主要用于微信公众号端领取，同web一样是单独部署的应用，也是接口聚合层
+ `scgis-thrid` 三方接口模块，主要是负责短信接口、hr接口、单点登录接口等
+ `scgis-pay` 支付模块，微信银联支付宝
+ `scgis-data` 数据模块，用于同步trace、foc数据
+ `scgsi-common`   公共模块
+ `scgsi-exception` 异常模块
+ `scgsi-activiti`   审核模块
+ `scgsi-irregular-flight`  不正常航班模块
+ `scgsi-overbooking` 超售模块
+ `scgsi-luggage`    行李模块
+ `scgsi-authority`  权限验证模块
+ `scgsi-admin` 监控模块
+ `scgsi-user` 用户模块
+ `scgsi-quartz` 定时任务模块
+ `scgsi-message` 消息模块

### 打包修改版本命令
+ versions:set -DnewVersion=1.0.3 修改所有pom中版本号
+ versions:revert 回滚
+ versions:commit

### 注意事项
+  山航各子模块包路径均是com.swcares.scgsi打头，成都航按照成都航的要求
+  代码格式化和注释模板必须使用项目工程下doc中的，用户名改为自己，注释一定要清晰，详细；别人一看就能知道做啥的
+  提交代码注释要写清楚，不能写订单开发，正向例子：订单模块-通过订单号查询订单功能实现，订单模块-导出订单报表开发
+  项目里面的警告必须要处理
+  lombok需要安装插件 https://blog.csdn.net/zflovecf/article/details/80178679
+  redis的使用参见RedisTestController【redis部署在山航机房，使用redis需要通过山航VPN!没有vpn项目的redis也能启动，只是不能使用redis服务】
+  multipart处于centos系统长时间会清除tmp文件夹，导致上传会出现错误，需要配置spring.servlet.multipart.location = /data/upload_tmp,需要在服务器先创建这个目录。
+  object\String操作工具用apache-lang包下的ObjectUtils，不能使用spring的
+  对象判空抛异常、字符串判空抛异常、boolean表达式抛异常都是用Asserts下面的方法
+  所有实体包的命名用entity
+  所有的分页form都需要集成pageform
+  sql的编写要注意mysql和oracle通用
+  log打日志，对参数需要加[],例如：log.info("张三密码错误，密码是[{}],异常为：{}", pwd, e);

### 开发环境

- JDK：Java SDK 1.8

- Servlet：Servlet 3.0/3.1

- jar依赖管理：Apache Maven 3.x

- IDE：Eclipse 4.8

- Web容器：Tomcat 9

- 字符编码：UTF-8
