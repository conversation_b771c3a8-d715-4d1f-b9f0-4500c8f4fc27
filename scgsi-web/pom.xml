<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>scgsi-web</artifactId>
    <packaging>jar</packaging>

    <parent>
        <groupId>com.swcares</groupId>
        <artifactId>scgsi</artifactId>
        <version>1.1.20</version>
    </parent>

	<!-- 依赖列表 -->
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<version>5.0.0</version> <!-- 确保版本较新 -->
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-exception</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-common</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-authority</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-data</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-quartz</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-third</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-message</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-user</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-overbooking</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-irregular-flight</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-pay</artifactId>
		</dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-luggage</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codehaus.janino</groupId>
			<artifactId>janino</artifactId>
		</dependency>
		<dependency>
			<groupId>com.oracle</groupId>
			<artifactId>ojdbc6</artifactId>
			<version>11.2.0.4.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/src/main/resources/lib/ojdbc6-11.2.0.4.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot-starter</artifactId>
			<version>2.0.0</version>
		</dependency>
        <dependency>
            <groupId>com.swcares</groupId>
            <artifactId>scgsi-hotel</artifactId>
        </dependency>
		<dependency>
			<groupId>com.swcares</groupId>
			<artifactId>scgsi-hotel-sda</artifactId>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<includeSystemScope>true</includeSystemScope>
					<!--fork:如果没有该项配置,整个devtools不会起作用 -->
					<fork>true</fork>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
