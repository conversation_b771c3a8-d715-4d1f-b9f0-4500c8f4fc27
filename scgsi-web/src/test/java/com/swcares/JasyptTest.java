package com.swcares;

import org.jasypt.encryption.StringEncryptor;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * @ClassName：JasyptTest
 * @Description：@TODO
 * @Copyright：© 2024 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2025/1/10 10:30
 * @version： v1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@WebAppConfiguration
public class JasyptTest {


    @Autowired
    private StringEncryptor encryptor;

    @Test
    public void testEncryptor(){
        System.out.println(encryptor.encrypt("Scgsi#2023"));
        System.out.println(encryptor.encrypt("ftpuser"));
        System.out.println(encryptor.encrypt("userA9527$"));
        System.out.println(encryptor.encrypt("rams"));
        System.out.println(encryptor.encrypt("SDAPortal@mh2016"));
        System.out.println(encryptor.encrypt("12344321"));
        System.out.println(encryptor.encrypt("dmfwzhbzpt"));
    }
}
