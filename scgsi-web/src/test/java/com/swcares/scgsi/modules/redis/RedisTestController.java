package com.swcares.scgsi.modules.redis;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import org.apache.commons.beanutils.BeanUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.swcares.scgsi.redis.RedisService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/redis")
public class RedisTestController {

    @Autowired
    private RedisService redisService;

    @Autowired
    private Redisson redisson;

    @RequestMapping("/redis1")
    public void redis1() {
        redisService.set("summer", "test");
    }

    @RequestMapping("/redis2")
    public void redis2() {
        redisService.hset("summertest", "wocao", builderUser("summer"));
    }

    @RequestMapping("/redis3")
    public void redis3() {
        System.out.println(redisService.hget("summertest", "wocao"));
    }

    @RequestMapping("/redis4")
    public void redis4() {
        List<User> list = new ArrayList<User>();
        list.add(builderUser("张三"));
        list.add(builderUser("李四"));
        list.add(builderUser("王五"));
        redisService.lSet("list", list);
    }

    @RequestMapping("/redis5")
    public void redis5() {
        System.out.println(redisService.lGet("list", 0, -1));
    }

    @RequestMapping("/redis6")
    public void redis6() {
        System.out.println(redisService.get("123123"));
    }

    @RequestMapping("/redis7")
    public void redis7() {
        log.info("=====================redis分布式锁并发-卖票测试开始======================");
        timoutFlag = false;
        // redisService.set(goods, "20");
        ExecutorService es = Executors.newFixedThreadPool(7);
        for (int i = 1; i <= 30; i++) {
            es.submit(new Runnable() {
                @Override
                public void run() {
                    testGoods();
                }
            });
        }
    }

    private static String goods = "goods";// 商品1的数量key

    private static String lockKey = "testLockKey";// 分布式锁的key

    boolean timoutFlag = false;

    public String testGoods() {
        String flag = "FAIL";
        long currentTime = System.currentTimeMillis();

        String currentName = Thread.currentThread().getName() + currentTime;

        RLock lock = redisson.getLock(lockKey);
        try {
            // lock.lockAsync(5 , TimeUnit.SECONDS);
            // lock.lock(5, TimeUnit.SECONDS); //设置60秒自动释放锁 （默认是30秒自动过期）
            Future<Boolean> res = lock.tryLockAsync(30, 5, TimeUnit.SECONDS);
            boolean result = res.get();
            log.info("当前线程[{}]获取锁是否成功[{}]", currentName, result);
            if (result) {
                int currentGoods = Integer.parseInt(redisService.get(goods).toString());
                if (currentGoods > 0) {
                    log.info("当前线程[{}]从redis中获取商品数量为[{}]，将进行-1操作", currentName, currentGoods);
                    if (5 < currentGoods && currentGoods < 7 && !timoutFlag) {
                        Thread.sleep(10000);
                        log.info("当前线程[{}]已经睡眠10秒，然后不作处理返回", currentName);
                        timoutFlag = true;
                    } else {
                        redisService.set(goods, currentGoods - 1);
                        flag = "SUCCESS";
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            log.info("【 释放锁果】当前线程[{}]释放锁开始", currentName);
            try {
                lock.unlock(); // 释放锁
            } catch (Exception e2) {
                log.info("【 释放锁失败】猜测是锁被释放了", currentName, e2);
            }

            log.info("【 释放锁果】当前线程[{}]释放锁结束", currentName);
        }
        log.info("【结果】当前线程[{}]扣减[{}]", currentName, flag);
        return flag;
    }

    @RequestMapping("/redis8")
    public void redis8() {
        log.info("=====================redis分布式锁并发测试-入库测试开始======================");
        ExecutorService es = Executors.newFixedThreadPool(7);
        for (int i = 1; i <= 30; i++) {
            es.submit(new Runnable() {
                @Override
                public void run() {
                    saveUserTest();
                }
            });
        }
    }

    private void saveUserTest() {
        User user = builderUser("王建文创建申领单测试");
        RLock lock = redisson.getLock(user.getName());
        Future<Boolean> res = lock.tryLockAsync(30, 20, TimeUnit.SECONDS);

        try {
            boolean result = res.get();
            if (result) {
                if (null == redisService.hget("summertest", user.getName())) {
                    redisService.hset("summertest", user.getName(), user);
                    log.info("数据成功");
                }
                log.info("[数据已存在]，直接返回请勿重新提交");
            } else {
                log.info("[获取锁失败]，直接返回请勿重新提交");
            }
        } catch (InterruptedException | ExecutionException e) {

        } finally {
            lock.unlock(); // 释放锁
        }
    }

    public User builderUser(String name) {
        return new User(name, "9527", "this is pwd");
    }

    public static void main(String[] args) throws IllegalAccessException, InvocationTargetException {
        User oldUser = new User("张三", "9527", "this is pwd");
        User newUser = new User("李四", "this is pwd", "");
        User usr = new User();
        BeanUtils.copyProperties(oldUser, newUser);
        System.out.println(oldUser);
        System.out.println(newUser);
        System.out.println(usr);
    }

}
