package com.swcares.modules.test.service;

import com.swcares.scgsi.api.ActivitiService;
import com.swcares.scgsi.service.ProcessServiceImpl;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.TaskService;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.modules.test.service <br>
 * Description：activiti测试 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月06日 11:08 <br>
 * @version v1.0 <br>
 */
@SpringBootTest
//@RunWith(SpringJUnit4ClassRunner.class)
public class ActTest {



    @Autowired
    ProcessServiceImpl processService;
    @Autowired
    ActivitiService activitiService;
    @Autowired
    TaskService taskService;
    /*ProcessEngine processEngine = ProcessEngineConfiguration.createStandaloneInMemProcessEngineConfiguration()
            .setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_FALSE)
            .setJdbcUrl("jdbc:oracle:thin:@************:1521/gsspsc")
            .setJdbcUsername("gsspsc")
            .setJdbcPassword("SDAzhbzpt~")
            .setJdbcDriver("oracle.jdbc.driver.OracleDriver")
            .setAsyncExecutorActivate(false)
            .buildProcessEngine();*/



    //=========部署流程文件========================
    //步骤 1.部署流程文件。
    //    2.查询流程定义、选择流程
    //    3.创建流程实例
    //    4.跟踪流程走向
    //    5.处理流程任务{审批、驳回}

//创建组角色 审批流程，， 需同步业务表 角色及用户关联表。

    //1.AOC审批，是一个角色。该角色下的用户，一人审批即通过。  【AOC审批这里使用监听器，在任务开始时触发，根据业务规则查询角色为AOC的所有用户。】
    //2.AOC用户审批，产生审批结果、下一环节处理人{值班经理}    【个人用户查询个人待办任务需覆盖：个人任务查询、个人组任务查询】
    //3.值班经理审批。可为多人即某个部门下的任意一个。也可以是AOC指定的一个人。 【值班经理审批使用list变量接受。添加到用户组任务中】


    //---------------梳理需要提供的方法及接口-----------------------
    //1.流程图—流程图部署
    //2.提交赔偿单—保存业务数据—启动赔偿单审核流程实例—执行发起人提交任务并保存处理业务数据（审核数据表）
    //3.提供用户个人{个人、组}任务查询
    //4.处理任务，封装流程变量。 【流程任务节点查询、节点所需流程变量查询】
    //5.审核流程跟踪{处理节点、审核状态、批注}

    /**
     * 部署流程定义
     */

    @Test
    public void delployFlow(){
        String name = "航延住宿结算审核单";
        //山航航延异常行李审核流程 dp_abnormal_process.bpmn
        //航延AOC一级审核流程 dp_aoc_one_process.png
        //航延AOC二级审核流程 dp_aoc_second_process.png


        /*String bpmnUrl = "process/act_delay_001.bpmn";
        String pngUrl = "process/act_delay_001.png";*/
        //dp_aoc_one_process.bpmn
        //dp_aoc_second_process.png
        String bpmnUrl = "process/hotel/fd_hotel_settle_review.bpmn";
        String pngUrl = "process/hotel/fd_hotel_settle_review.png";
        boolean isSuccess = activitiService.delployFlow(name,bpmnUrl,pngUrl);
        System.out.println("部署流程结果："+isSuccess);
    }

    @Autowired
    RepositoryService repositoryService ;
    /*
     * 查询流程定义
     */

//    @Test
    public void findProcessDefinition() {
        List<ProcessDefinition> list =repositoryService// processEngine.getRepositoryService()//与流程定义和部署对象相关的Service
                .createProcessDefinitionQuery()//创建一个流程定义查询
                /*排序*/
                .orderByProcessDefinitionVersion().asc()//按照版本的升序排列
                //.orderByProcessDefinitionName().desc()//按照流程定义的名称降序排列
                .list();//返回一个集合列表，封装流程定义

        if (list != null && list.size() > 0) {
            for (ProcessDefinition processDefinition : list) {
                System.out.println("流程定义ID:"+processDefinition.getId());//流程定义的key+版本+随机生成数
                System.out.println("流程定义名称:"+processDefinition.getName());//对应HelloWorld.bpmn文件中的name属性值
                System.out.println("流程定义的key:"+processDefinition.getKey());//对应HelloWorld.bpmn文件中的id属性值
                System.out.println("流程定义的版本:"+processDefinition.getVersion());//当流程定义的key值相同的情况下，版本升级，默认从1开始
                System.out.println("资源名称bpmn文件:"+processDefinition.getResourceName());
                System.out.println("资源名称png文件:"+processDefinition.getDiagramResourceName());
                System.out.println("部署对象ID:"+processDefinition.getDeploymentId());
            }
        }
    }




    //启动流程
//    @Test
    public void startProcessInstance() {
        //通过流程定义的key启动流程，会启动版本更高的流程
        //流程定义的key
        Map<String, Object> data = new HashMap<>();
      /*  String key = "dp_aoc";
        String userId = "6775675";
        String orderId = "202004141523170991";
        orderAuditService.launchAuditProcess(userId,"",orderId,null);*/


        String userId1 = "6775675";
        String amount = "600";
        String key2 = "amount_process";
        data.put("submitter",userId1);
        data.put("amount",amount);
        ProcessInstance processInstance = activitiService.startProcessResult(userId1,key2,true,data);
        System.out.println("创建流程实例："+processInstance.getProcessInstanceId());
    }


    /**完成我的任务*/
/*
    @Test
    public void completeMyPersonalTask(){
        //任务ID
        String taskIds = "840005";
        //审核-执行【异常行李值班主任】userId=4235958,91394148,128989752)
        //审核-执行【异常行李经理】userId=4238258,36527728,128989748)
        String userId= "4235958";
        String desc="一级审核通过";//批注
        Map<String, Object> data = new HashMap<String, Object>();
        data.put("result", "2");//任务变量：审批状态 0通过、1不同意 2驳回
        //data.put("handleUser","20200403,20200402");//值班人员多个
        boolean isSuccess =activitiService.complementTask(taskIds,userId,desc,data);
        System.out.println("处理任务：{} 结果{}"+taskIds+isSuccess);
        System.out.println(processService.isProcessEnd("820001"));

    }

  *//*List<String> assigneeList = new ArrayList<String>();
        assigneeList.add("值班1");
        assigneeList.add("值班2");
        assigneeList.add("值班3");*//*

    *//**
     * 查询待办任务
     *//*
    @Test
    public void queryHistoricInstance() {

        //缺 用户组办理人
        *//*List<QueryTask> listQ =  processService.findPersonTaskAll();

        if (listQ != null && listQ.size() > 0) {
            for (QueryTask task : listQ) {
                System.out.println("任务ID：" + task.getId());
                System.out.println("任务名称:" + task.getName());
                System.out.println("任务的创建时间:" + task.getCreateTime());
                System.out.println("任务的办理人:" + task.getAssignee());
                System.out.println("流程实例ID:" + task.getProcessInstanceId());
                System.out.println("执行对象ID:" + task.getExecutionId());
                System.out.println("流程定义ID:" + task.getProcessDefinitionId());
                System.out.println("##################################################");
            }
        }*//*
        String assignee = "值班1";//"","chuange"
        //个人待办任务
        List<QueryTask> listQ = processService.findPersonTaskByUser(assignee);
        //个人组任务
        List<QueryTask> list = processService.findCandidateTaskByUser(assignee);
        listQ.addAll(list);
        if (listQ != null && listQ.size() > 0) {
            for (QueryTask task : listQ) {
                System.out.println("任务ID：" + task.getId());
                System.out.println("任务名称:" + task.getName());
                System.out.println("任务的创建时间:" + task.getCreateTime());
                System.out.println("任务的办理人:" + task.getAssignee());
                System.out.println("流程实例ID:" + task.getProcessInstanceId());
                System.out.println("执行对象ID:" + task.getExecutionId());
                System.out.println("流程定义ID:" + task.getProcessDefinitionId());
                System.out.println("##################################################");
            }
        }

    }


    *//**
     * 查询正在执行的任务办理人表
     *//*
    @Test
    public void findRunPersonTask() {
        String taskId = "45004";
        List<IdentityLink> list = taskService.getIdentityLinksForTask(taskId);
        if (list != null && list.size() > 0) {
            for (IdentityLink identityLink : list) {
                System.out.println(identityLink.getTaskId() + "   " + identityLink.getType() + "   "
                        + identityLink.getProcessInstanceId() + "   " + identityLink.getUserId());
            }
        }
    }

    //============航延=======================
//
    //
    @Autowired
    OrderAuditService orderAuditService;
    @Autowired
    OrderAuditDao orderAuditDao;
    @Test
    public void starts(){
        //，流程key，赔偿单id202004271500654010，
        String userId = "91394140";
        String orderId = "20200427152014445";

        orderAuditService.launchAuditProcess(userId,"",orderId,"");
       *//* OrderAudit orderAudit = new OrderAudit();
        orderAudit.setOrderId(orderId);
        orderAudit.setProcessName("2");//1发起
        orderAudit.setStatus("1");//1已处理
        orderAudit.setAuditor(userId);//处理人
        orderAudit.setRemark("AOC通过");
        orderAudit.setOpinion("0");
        orderAudit.setAuditTime(new Date());
        orderAudit.setCreateTime(new Date());
        orderAuditDao.save(orderAudit);*//*
    }

    *//**
     * 提交审核
     * @throws Exception
     *//*
    @Test
    public void orderAudit() throws Exception {
        OrderAuditProcessDto order = new OrderAuditProcessDto();
      *//*  order.setOrderId("202004031352129850");
        order.setAuditor("20203873");
        order.setOpinion("0");
        order.setRemark("AOC通过");
        order.setHandleUser("010731,010732");
        order.setTaskId("37508");
        order.setProcessNode("2");
*//*   //，流程key，赔偿单id202004271500654010，

        order.setOrderId("202004291506394210");
        order.setAuditor("4235271");
        order.setOpinion("0");
        order.setRemark("AOC经理通过了");
        order.setHandleUser("91394144");
        order.setTaskId("185076");
        order.setProcessNode("2");//3-值班经理
        orderAuditService.handleAuditProcess(order);




    }


    @Resource
    OverBookService overBookService;
    @Test
    public void find() throws ScriptException, ParseException {
       *//* List<OrderAuditRecordVo> order = orderAuditService.findOrderAuditRecord("202003041524345490");
        for (OrderAuditRecordVo orders:order) {
            System.out.println(orders.toString()+"=\n");
        }*//*
        *//*OrderAuditDataDto dod = new OrderAuditDataDto();
        dod.setUserId("010731");
        dod.setType("2");
        dod.setFlightNo("SC5689");
        dod.setFlightDate("2020-03-04");
        dod.setPayType("0");
        dod.setOrderId("2020030415243454");
        List<OrderAuditDataVo> list = orderAuditService.findAuditProcessByUserId(dod);
        System.out.println(list.toString());*//*
*//*
        OverBookConfigInfo overBookConfigInfo= new OverBookConfigInfo();
        OverBookConfigDto configDto = new OverBookConfigDto();
        configDto.setId("1");
        configDto.setPayMoney("11");
        BeanUtils.copyProperties(configDto,overBookConfigInfo);
        System.out.println(overBookConfigInfo.toString());*//*
      *//*  Map<String, Object> data = overBookService.getConfigInfo();
        System.out.println("H5查 ====>>>"+data.toString());

        System.out.println(new RenderResult("1", "success", data));*//*

        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            *//*Date d1 = df.parse("2020-03-16 13:50:00");
            Date d2 = df.parse("2020-03-16 07:00:00");
            long diff = d1.getTime() - d2.getTime();//这样得到的差值是微秒级别
            long hours = (diff%(1000*24*60*60) / (60 * 60 * 1000));
            System.out.println(hours);*//*
       *//* String ruleStr = "100*80/100";
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("js");
        Object result =  engine.eval(ruleStr);

        System.out.println(result.getClass().getName()+"==="+result);
*//*

        *//*
         *//*//200 50 0.1 - 1000
        Double t = 300.5;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm");
        Date time = sdf.parse("2020-03-10 13:40");
        Date time2 = sdf.parse("2020-03-09 12:40");
        long timeout = time2.getTime() - time.getTime();
        double quot = 0.00;
        quot = ((double)timeout) / (1000 * 60 * 60);
        System.out.println(quot);
        DecimalFormat formater = new DecimalFormat();
        formater.setMaximumFractionDigits(2);
        formater.setGroupingSize(0);
        formater.setRoundingMode(RoundingMode.FLOOR);
        System.out.println(formater.format(quot));
    }

    @Test
    public void ind() throws Exception {
        System.out.println(processService.isProcessEnd("577501"));
        Task task = taskService.createTaskQuery()//创建查询对象
                .processInstanceId("577501")//通过流程实例id来查询当前任务
                .singleResult();//获取单个查询结果
        System.out.println(task.toString());

    }*/
}
