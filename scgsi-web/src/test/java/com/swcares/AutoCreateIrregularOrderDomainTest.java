package com.swcares;

import com.swcares.scgsi.entity.FocFlightInfo;
import com.swcares.scgsi.flight.service.impl.AutoCreateIrregularOrderDomain;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
@SpringBootTest
@WebAppConfiguration
public class AutoCreateIrregularOrderDomainTest {

    @Autowired
    AutoCreateIrregularOrderDomain domain;

    @Test
    public void getCurrentFlightSegmentTest() {
        LocalDateTime fixedTime = LocalDateTime.of(2025, 2, 7, 13, 5);
        try (MockedStatic<LocalDateTime> mockedStatic = mockStatic(LocalDateTime.class)) {
            mockedStatic.when(LocalDateTime::now).thenReturn(fixedTime);
        }
        List<FocFlightInfo> segment = new ArrayList<>();
        FocFlightInfo AB = new FocFlightInfo();
        AB.setFlightId("109288640");
        AB.setFlgDelay("");
        AB.setFlightNo("SC2270");
        AB.setFlightDate("2025/02/07");
        AB.setDepartPort("郑州");
        AB.setPod("ZHCC");
        AB.setArrivalPort("桂林");
        AB.setPoa("ZGKL");
        AB.setStd("09:00");
        AB.setAtd(null);

        FocFlightInfo BC = new FocFlightInfo();
        BC.setFlightId("109288794");
        BC.setFlgDelay("延误");
        BC.setFlightNo("SC2270");
        BC.setFlightDate("2015/02/07");
        BC.setDepartPort("桂林");
        BC.setArrivalPort("厦门");
        BC.setPod("ZGKL");
        BC.setPoa("ZSAM");
        BC.setStd("09:00");
        AB.setAtd(null);


        FocFlightInfo AC = new FocFlightInfo();
        AC.setFlightId("1886807047824498688");
        AC.setFlgDelay("");
        AC.setFlightNo("SC2270");
        AC.setFlightDate("2015/02/07");
        AC.setDepartPort("郑州");
        AC.setArrivalPort("厦门");
        AC.setPod("ZHCC");
        AC.setPoa("ZSAM");
        AC.setStd("09:00");
        segment.add(AB);
        segment.add(BC);
        segment.add(AC);

        List<FocFlightInfo> currentFlightSegment = domain.getCurrentFlightSegment(segment, BC, 4);
        assertEquals(1, currentFlightSegment.size());
    }
}
