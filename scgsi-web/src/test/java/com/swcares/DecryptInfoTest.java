package com.swcares;

import com.swcares.scgsi.util.AesEncryptUtil;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
public class DecryptInfoTest {
    public static void main(String[] args) throws Exception{

        String key = "ZXVwc2kjMjAyMHN3Y2FyZXMkZXVwc2lAMjAyMHRyYXZlbHNreQ==";
        String path = "/Users/<USER>/temp/";
        //System.out.println(AesEncryptUtil.aesDecrypt(AesEncryptUtil.AES_KEY, a));
        List<String> orgList = IOUtils.readLines(new FileReader(new File(path + "2.csv")));
        System.out.println("原始数据条数: "+orgList.size());
        List<String> dstList =new ArrayList<>();
        for(int i=0;i<orgList.size();i++){
            String text = orgList.get(i);
            if(i==0 ){dstList.add(text);continue;}
            String[] split = text.split(",");

            split[9] = "@"+AesEncryptUtil.aesDecrypt(key, split[9]);
            split[10] = "@"+AesEncryptUtil.aesDecrypt(key, split[10]);
            split[11] = "@"+AesEncryptUtil.aesDecrypt(key, split[11]);

            String collect = Arrays.stream(split).collect(Collectors.joining(","));
            dstList.add(collect);
            // System.out.println(i+":"+collect);
        }
        System.out.println("处理后条数: "+orgList.size());
        IOUtils.writeLines(dstList,null,new FileOutputStream(path + "2_finall.csv"),"UTF-8");
        Thread.sleep(10000);
    }

}
