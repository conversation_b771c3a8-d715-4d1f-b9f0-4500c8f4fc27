
/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：WebAppMvcConfigurer.java <br>
 * Package：com.swcares.scgsi.config <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 * 
 * <AUTHOR> <br>
 *         date 2020年4月3日 上午10:28:27 <br>
 * @version v1.0 <br>
 */
package com.swcares.scgsi.config;

import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.swcares.scgsi.i18n.GenericResultConverter;

/**   
 * ClassName：com.swcares.scgsi.config.WebAppMvcConfigurer <br>
 * Description：国际化返回配置类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月3日 上午10:28:27 <br>
 * @version v1.0 <br>  
 */
//@Configuration
public class WebAppMvcConfigurer implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 国际化变化拦截器
        LocaleChangeInterceptor localeInterceptor = new LocaleChangeInterceptor();

        registry.addInterceptor(localeInterceptor);
    }

    @Bean
    @SuppressWarnings("rawtypes")
    public GenericResultConverter<?> mappingJackson2HttpMessageConverter(
            ObjectMapper objectMapper) {
        /*******************************************************************
         * GenericResultConverter继承自MappingJackson2HttpMessageConverter. 执行默认的json转换操作，只是在返回响应对象时做国际化处理
         *******************************************************************/
        GenericResultConverter<?> converter = new GenericResultConverter();
        converter.setObjectMapper(objectMapper);
        return converter;
    }

}
