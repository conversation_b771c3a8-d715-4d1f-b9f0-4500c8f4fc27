
/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：WebConfig.java <br>
 * Package：com.swcares.scgsi.config <br>
 * Description：web配置信息 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 * 
 * <AUTHOR> <br>
 *         date 2020年2月10日 上午11:17:40 <br>
 * @version v1.0 <br>
 */
package com.swcares.scgsi.config;

import com.swcares.scgsi.hum.service.HumResourceDataService;
import com.swcares.scgsi.service.wx.WxPayService;
import com.swcares.scgsi.service.yee.CustomAppSdkConfigProvider;
import com.swcares.scgsi.service.yee.YeePayService;
import com.yeepay.g3.sdk.yop.config.AppSdkConfigProvider;
import com.yeepay.g3.sdk.yop.config.AppSdkConfigProviderRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.swcares.scgsi.hum.service.impl.HumResourceDataServiceImpl;
import com.swcares.scgsi.web.ResourceBundleMessageSourceFactory;
import org.springframework.context.annotation.DependsOn;

import javax.annotation.PostConstruct;

/**   
 * ClassName：com.swcares.scgsi.config.WebConfig <br>
 * Description：web配置信息 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月10日 上午11:17:40 <br>
 * @version v1.0 <br>  
 */
@Configuration
public class WebConfig {

    /**
     * Title：builderMessage <br>
     * Description：初始化国际化资源信息 <br>
     * author：夏阳 <br>
     * date：2020年3月20日 下午5:03:47 <br>
     * @return <br>
     */
    @Bean(initMethod = "builderMessage")
    public ResourceBundleMessageSourceFactory builderMessage() {
        return new ResourceBundleMessageSourceFactory();
    }

    /**
     * Title：initBuilderDepartmentData <br>
     * Description：初始化部门信息【适用于系统初次部署】 <br>
     * author：夏阳 <br>
     * date：2020年3月20日 下午5:03:38 <br>
     * @return <br>
     */
    //@Bean(initMethod = "initBuilderDepartmentData")
    public HumResourceDataServiceImpl initBuilderDepartmentData() {
        return new HumResourceDataServiceImpl();
    }

    /**
     * Title：initBuilderEmployeeData <br>
     * Description：初始化员工信息【适用于系统初次部署】 <br>
     * author：夏阳 <br>
     * date：2020年3月20日 下午5:03:43 <br>
     * @return <br>
     */
   // @Bean(initMethod = "initBuilderEmployeeData")
    public HumResourceDataServiceImpl initBuilderEmployeeData() {
        return new HumResourceDataServiceImpl();
    }

    /**
     * Title：departmentIncrementalData <br>
     * Description：部门信息增量同步，需要挪到定时任务 <br>
     * author：夏阳 <br>
     * date：2020年3月20日 下午5:05:51 <br>
     * @return <br>
     */
   // @Bean(initMethod = "departmentIncrementalData")
    public HumResourceDataService departmentIncrementalData(HumResourceDataService h) {
        h.departmentIncrementalData();
        return h;
    }

    /**
     * Title：employeeIncrementalData <br>
     * Description：员工信息增量同步，需要挪到定时任务 <br>
     * author：夏阳 <br>
     * date：2020年3月20日 下午5:06:24 <br>
     * @return <br>
     */
    //@Bean(initMethod = "employeeIncrementalData")
    public HumResourceDataServiceImpl employeeIncrementalData() {
        return new HumResourceDataServiceImpl();
    }

    @Bean(name = "yeePayService")
    public YeePayService initYeePayService(){
        return new YeePayService("merchantInfo.properties");
    }


    @Bean(name = "wxPayService")
    public WxPayService initWxPayService(){
        return new WxPayService("wxinfo.properties");
    }


    /**
     * Title： initYooPayConfig<br>
     * Description： YEE - 初始化配置读取类<br>
     * author：傅欣荣 <br>
     * date：2020/5/21 15:01 <br>
     * @param
     * @return
     */
    @PostConstruct
    public void initYooPayConfig() {
        //全局设置
        AppSdkConfigProvider provider = new CustomAppSdkConfigProvider("yop_sdk_config_default.json");
        AppSdkConfigProviderRegistry.registerCustomProvider(provider);
    }

}
