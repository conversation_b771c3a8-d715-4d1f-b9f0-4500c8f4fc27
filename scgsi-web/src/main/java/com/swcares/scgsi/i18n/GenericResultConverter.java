package com.swcares.scgsi.i18n;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Locale;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.scgsi.web.ResourceBundleMessageSourceFactory;

/**
 * 
 * ClassName：com.swcares.common.web.GenericResultConverter <br>
 * Description：返回结果处理：如国际化等 <br>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2019年2月21日 上午11:02:34 <br>
 * @version v1.0 <br>
 * @param <T> T
 */
public class GenericResultConverter<T> extends MappingJackson2HttpMessageConverter {

    /** RequestAcceptHeaderLocaleResolver **/
    @Autowired
    private transient RequestAcceptHeaderLocaleResolver localeResolver;

    /** http请求 **/
    @Autowired
    private transient HttpServletRequest request;

    /**
     * 
     * Title：writeInternal <br>
     * Description：升级Spring-Boot后，需要对其中调用的方法做相应的变更。<br>
     * <AUTHOR> <br>
     * date 2019年2月21日 下午2:53:49 <br>
     * @param object object
     * @param type type
     * @param outputMessage outputMessage
     * @throws IOException IOException
     * @throws HttpMessageNotWritableException 异常<br>
     * @see org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter#writeInternal(java.lang.Object, java.lang.reflect.Type, org.springframework.http.HttpOutputMessage) <br>
     */
    @Override
    @SuppressWarnings("rawtypes")
    protected void writeInternal(Object object, Type type, HttpOutputMessage outputMessage)
            throws IOException, HttpMessageNotWritableException {
        // 判断对象是否为result对象类型
        if (object instanceof RenderResult<?>) {
            // 设置generic
            RenderResult renderResult = (RenderResult) object;
            try {
                // 国际化处理
                Locale local = localeResolver.resolveLocale(request);
                // 设置meesage
                ResourceBundleMessageSource rbms =
                        ResourceBundleMessageSourceFactory.get(local.toString());
                // 设置i18n
                String i18nMsg = rbms.getMessage(renderResult.getCode() + "", null, local);
                renderResult.setMsg(i18nMsg);
            } catch (Exception e) {
                // 设置错误msg
                renderResult.setMsg(renderResult.getMsg());
            }
        }
        // 执行真正的转换操作
        super.writeInternal(object, type, outputMessage);
    }
}
