/**
 * @author：Gine @date：2015年5月7日
 */
package com.swcares.scgsi.i18n;

import java.util.Locale;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;

/**
 * 
 * ClassName：com.swcares.common.web.RequestAcceptHeaderLocaleResolver <br>
 * Description：基于URL请求的国际化组件 <br>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2019年2月21日 上午11:04:52 <br>
 * @version v1.0 <br>
 */
@Component("localeResolver")
public class RequestAcceptHeaderLocaleResolver extends AcceptHeaderLocaleResolver {

  /**
   * 
   * Title：resolveLocale <br>
   * Description：重写父类resolveLocale方法，返回本地化语言<br>
   * <AUTHOR> <br>
   * date 2019年2月21日 上午11:05:04 <br>
   * @param request request
   * @return rt<br>
   * @see org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver#resolveLocale(javax.servlet.http.HttpServletRequest) <br>
   */
  @Override
  public Locale resolveLocale(HttpServletRequest request) {
    return Locale.SIMPLIFIED_CHINESE;
    /*HttpSession session = request.getSession();
    //session.setAttribute(Global.SESSION_ATTRIBUTE_LOCALE, java.util.Locale.US);
    Locale locale = (Locale) session.getAttribute(Global.SESSION_ATTRIBUTE_LOCALE);
    if (locale == null) {
      session.setAttribute(Global.SESSION_ATTRIBUTE_LOCALE, request.getLocale());
      return request.getLocale();
    } else {
      return locale;
    }*/
  }

  /**
  * 
  * Title：setLocale <br>
  * Description：重写父类设置语言方法<br>
  * <AUTHOR> <br>
  * date 2019年2月21日 上午11:05:16 <br>
  * @param request request
  * @param response response
  * @param locale locale<br>
  * @see org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver#setLocale(javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse, java.util.Locale) <br>
  */
  @Override
  public void setLocale(HttpServletRequest request, HttpServletResponse response, Locale locale) {
  //  request.getSession().setAttribute(Global.SESSION_ATTRIBUTE_LOCALE, locale);
  }

}
