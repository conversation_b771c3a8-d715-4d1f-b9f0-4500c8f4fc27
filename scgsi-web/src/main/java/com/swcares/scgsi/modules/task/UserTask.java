package com.swcares.scgsi.modules.task;

import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import com.swcares.scgsi.hum.service.HumResourceDataService;

/**
 * ClassName：com.swcares.scgsi.modules.task.UserTask <br>
 * Description：User定时任务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年6月2日 下午3:26:34 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class UserTask {

    @Resource
    private HumResourceDataService humResourceDataService;

    @Transactional(rollbackFor = Exception.class)
    public void manualSyncUserHandler(){
        try {
            log.info("-------------->>>同步用户定时任务 -正在执行");
            humResourceDataService.employeeIncrementalData();
        } catch (Exception e) {
            log.error("-------------->>>同步用户定时任务 -执行异常{}",e);
        }
    }

}
