package com.swcares.scgsi.modules.homepage;

import com.swcares.scgsi.flight.service.DataAnalysisService;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.modules.homepage <br>
 * Description：首页图标 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 09月07日 13:22 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/dp/homePage")
public class DataAnalysisController extends BaseController {

    @Autowired
    DataAnalysisService dataAnalysisService;


    @GetMapping("/getPayTypeReceiveData")
    public RenderResult getPayTypeReceiveData(){
        return RenderResult.success(dataAnalysisService.getPayTypeReceiveData());
    }

    @GetMapping("/getAnalysisStatisticsData")
    public RenderResult getAnalysisStatisticsData(){
        return RenderResult.success(dataAnalysisService.getAnalysisStatisticsData());
    }
}
