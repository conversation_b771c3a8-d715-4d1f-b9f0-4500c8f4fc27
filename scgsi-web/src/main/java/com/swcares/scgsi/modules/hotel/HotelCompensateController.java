package com.swcares.scgsi.modules.hotel;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hotel.model.dto.AuditCompleteParamsDTO;
import com.swcares.scgsi.hotel.model.dto.HotelCompensateQueryDTO;
import com.swcares.scgsi.hotel.model.dto.HotelOrderSaveParamDTO;
import com.swcares.scgsi.hotel.model.dto.HotelOrderStatusParamDTO;
import com.swcares.scgsi.hotel.service.HotelCompensateService;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName：HotelCompensateController
 * @Description：航延酒店保障单
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/23 15:46
 * @version： v1.0
 */
@RestController
@RequestMapping("/api/fd/base")
@Api(tags = "航延酒店保障单-接口V1")
public class HotelCompensateController extends BaseController {

    @Autowired
    private HotelCompensateService hotelCompensateService;

    @GetMapping("findFlightPage")
    @ApiOperation(value = "航延酒店赔偿单管理-航班列表")
    public RenderResult findFlightPage(HotelCompensateQueryDTO dto) {
        QueryResults queryResults = hotelCompensateService.findCompensateFlightPage(dto);
        return RenderResult.success(returnPageInfo(queryResults));
    }

    @GetMapping("getOrderInfoByFlightInfo")
    @ApiOperation(value = "航延酒店保障单管理-航班赔偿单列表")
    public RenderResult getOrderInfoByFlightInfo(String flightNo, String flightDate,String status) {
        return RenderResult.success(hotelCompensateService.getOrderInfoByFlightInfo(
                flightNo, flightDate,status));
    }

    @GetMapping("orderDetail")
    @ApiOperation(value = "根据赔付单id获取赔付详情")
    public RenderResult getDetailOrderInfoByOrderId(String orderId) {
        return RenderResult.success(hotelCompensateService.getDetailOrderInfoByOrderId(orderId));
    }
    @GetMapping("orderAuditRecord")
    @ApiOperation(value = "根据赔付单id获取审批记录")
    public RenderResult orderAuditRecord(String orderId) {
        try {
            return RenderResult.success(hotelCompensateService.getOrderAuditRecord(orderId));
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }


    @PostMapping("saveOrderInfo")
    @ApiOperation(value = "保存更新赔付单信息")
    public RenderResult saveOrderInfo(@RequestBody HotelOrderSaveParamDTO dto) {
        try {
            String orderId = hotelCompensateService.saveOrderInfo(dto);
            return RenderResult.success(orderId);
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }

    @PostMapping("updateOrderStatus")
    @ApiOperation(value = "赔偿单管理确认发放，关闭操作")
    public RenderResult updateOrderStatus(@RequestBody @Validated HotelOrderStatusParamDTO dto) {
        try {
            hotelCompensateService.updateOrderStatus(dto.getOrderId(), dto.getStatus());
            return RenderResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return new RenderResult("1", e.getMessage());
        }
    }

    @GetMapping("getHotelList")
    @ApiOperation(value = "服务航站查酒店列表")
    public RenderResult findHotelListByServiceCity(String serviceCity) {
        return RenderResult.success(hotelCompensateService.findHotelListByServiceCity(serviceCity));
    }

    @PostMapping("auditOperation")
    @ApiOperation(value = "赔偿单-审核")
    public RenderResult auditOperation(@RequestBody @Validated AuditCompleteParamsDTO dto) {
        hotelCompensateService.auditOperation(dto);
        return RenderResult.success();
    }

    @PostMapping("changeOrderStatus")
    @ApiOperation(value = "更新保障单状态-保障完成")
    public RenderResult changeOrderStatus(@RequestBody @Validated @NotEmpty(message = "结算单号必填") String accommodationNo) {
        if (StringUtils.isEmpty(accommodationNo)) {
            RenderResult.fail();
        }
        int i = hotelCompensateService.completionOrder(accommodationNo);
        return RenderResult.success(i);
    }
}
