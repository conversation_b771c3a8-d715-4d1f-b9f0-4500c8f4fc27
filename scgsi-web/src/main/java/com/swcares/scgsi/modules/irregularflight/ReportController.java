package com.swcares.scgsi.modules.irregularflight;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.flight.dto.ReportQueryParamDto;
import com.swcares.scgsi.flight.service.ReportService;
import com.swcares.scgsi.flight.vo.ReportInfoVo;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.util.ExportExcelUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;

/**
 * ClassName：com.swcares.scgsi.modules.irregularflight <br>
 * Description：航延报表控制层 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月30日 16:08 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/dp/report")
public class ReportController extends BaseController {
    @Resource
    private ReportService reportService;

    /**
     * Title： getReportDataPage<br>
     * Description：航延报表分页查询<br>
     * author：王建文 <br>
     * date：2020-3-30 16:13 <br>
     *
     * @param reportQueryParamDto 参数接收
     */
    @GetMapping("getReportDataPage")
    public RenderResult getReportDataPage(ReportQueryParamDto reportQueryParamDto) {
        QueryResults queryResults = reportService.getReportDataPage(reportQueryParamDto);
        return RenderResult.success(returnPageInfo(queryResults));
    }

    /**
     * Title：updateFlag <br>
     * Description： 标记<br>
     * author：王建文 <br>
     * date：2020-3-31 10:35 <br>
     *
     * @param ids 主键id
     */
    @PostMapping("updateFlag")
    public RenderResult updateFlag(@RequestParam(name="ids") String ids,@RequestParam(name="status")  String status) {
        try {
            reportService.updateFlag(ids.split(","), status);
            return RenderResult.success();
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }

    @GetMapping("exportData")
    public void exportData(ReportQueryParamDto reportQueryParamDto, HttpServletResponse response) {
        List<ReportInfoVo> reportDtoList = reportService.getExportData(reportQueryParamDto);
        String[] columnNames = {" ID", " 航班号","航班日期",  "起飞", " 到达", "服务航站", "机型", " 赔付类型", "延误原因", "延误时长", " 赔付单号","证件号", "申领人手机号", "赔付单状态", "旅客姓名", "票号", "总赔偿金额",
                "赔偿金额", "客票差价", "领取状态", "支付状态", "代领审核状态", "领取渠道", "领取方式", "开户名", "开户行", "领取账号", "支付时间", "过期时间", "申请人", "是否标记", "终审人", "航段" , "备注", "支付失败原因","旅客ID"};
        ExportExcelUtil<ReportInfoVo> exportExcelUtil = new ExportExcelUtil<ReportInfoVo>();
        String fileName = "航延报表" + DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMMSS);
        exportExcelUtil.exportExcel(fileName, fileName, columnNames, reportDtoList, response, "");
    }
}