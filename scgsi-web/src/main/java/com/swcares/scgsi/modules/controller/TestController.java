
/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：TestController.java <br>
 * Package：com.swcares.scgsi.modules.controller <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 * 
 * <AUTHOR> <br>
 *         date 2020年2月10日 上午11:17:07 <br>
 * @version v1.0 <br>
 */
package com.swcares.scgsi.modules.controller;

import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.common.utils.FocHttpClient;
import com.swcares.scgsi.entity.User;
import com.swcares.scgsi.service.impl.FocAnalyze;
import com.swcares.scgsi.util.UserUtil;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.io.IOException;

/**   
 * ClassName：com.swcares.scgsi.modules.controller.TestController <br>
 * Description：TODO(这里用一句话描述这个类的作用) <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月10日 上午11:17:07 <br>
 * @version v1.0 <br>  
 */
@RequestMapping("/test")
@RestController
@Api(value = "/test", tags = "测试")
public class TestController {

    @Autowired
    private BaseDAO basedao;

    @Autowired
    private UserUtil userUtil;

    @RequestMapping("/test1")
    @ApiOperation(value = "测试方法", notes = "测试方法")
    public RenderResult<?> test1() {
        throw new BusinessException(MessageCode.NO_AUTH.getCode());
    }

    /*
     * @RequestMapping("/test2")
     * 
     * @ApiOperation(value = "测试方法2", notes = "测试方法2") public RenderResult test2() { int a = 1 / 0;
     * return RenderResult.success(a); return new
     * RenderResult(MessageCode.COMPANY_SAVE_FAIL.getCode()); }
     */

    @SuppressWarnings("unused")
    @RequestMapping("/test5")
    public RenderResult<Integer> test5() {

        User user = basedao.findById("1231231", User.class);
        return null;
    }

    @Autowired
    private FocAnalyze focAnalyze;

    @RequestMapping("/test9")
    public void testFoc() {
        String s = null;
        try {
            s = FocHttpClient.doPost("http://172.16.1.206/foc/",
                    "<soapenv:Envelope xmlns:soapenv=\"http://schemas"
                            + ".xmlsoap.org/soap/envelope/\" xmlns:wsy=\"http://WsYdpt.shandongair.com.cn/\">\n"
                            + "   <soapenv:Header/>\n" + "   <soapenv:Body>\n"
                            + "      <wsy:getFlight_dynamic>\n" + "         <!--Optional:-->\n"
                            + "         <wsy:getFlight_dynamic_c_1>ydpt</wsy:getFlight_dynamic_c_1>\n"
                            + "         <!--Optional:-->\n"
                            + "         <wsy:getFlight_dynamic_c_2>sdasdasda</wsy:getFlight_dynamic_c_2>\n"
                            + "         <!--Optional:-->\n"
                            + "         <wsy:getFlight_dynamic_begin_date>20200320</wsy:getFlight_dynamic_begin_date>\n"
                            + "         <!--Optional:-->\n"
                            + "         <wsy:getFlight_dynamic_end_date>20200323</wsy:getFlight_dynamic_end_date>\n"
                            + "         <!--Optional:-->\n"
                            + "         <wsy:getFlight_dynamic_flight_no></wsy:getFlight_dynamic_flight_no>\n"
                            + "         <!--Optional:-->\n"
                            + "         <wsy:getFlight_dynamic_ac_reg></wsy:getFlight_dynamic_ac_reg>\n"
                            + "         <!--Optional:-->\n"
                            + "         <wsy:getFlight_dynamic_pod_cn></wsy:getFlight_dynamic_pod_cn>\n"
                            + "         <!--Optional:-->\n"
                            + "         <wsy:getFlight_dynamic_poa_cn></wsy:getFlight_dynamic_poa_cn>\n"
                            + "      </wsy:getFlight_dynamic>\n" + "   </soapenv:Body>\n"
                            + "</soapenv:Envelope>");
        } catch (IOException e) {
            e.printStackTrace();
        }
        focAnalyze.parseContent(s);
    }

    @RequestMapping("/test10")
    @ApiOperation(value = "测试方法", notes = "测试方法")
    public void test10() {
        System.out.println(userUtil.findBussiTypeByEmpId("8fafef0f-9298-4f72-b36b-f111571de495"));
    }
}
