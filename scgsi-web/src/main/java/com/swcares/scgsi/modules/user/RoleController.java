package com.swcares.scgsi.modules.user;

import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.common.model.vo.ModuleTreeVO;
import com.swcares.scgsi.department.service.DepartmentService;
import com.swcares.scgsi.role.common.model.form.BindResourcesForm;
import com.swcares.scgsi.role.common.model.form.RoleAddForm;
import com.swcares.scgsi.role.common.model.form.RoleListQueryForm;
import com.swcares.scgsi.role.common.model.form.RoleUpdateForm;
import com.swcares.scgsi.role.common.model.view.RoleManagerUserView;
import com.swcares.scgsi.role.service.RoleService;
import com.swcares.scgsi.user.common.model.view.UsersInfoView;
import com.swcares.scgsi.user.service.UserService;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：RoleController <br>
 * Package：com.swcares.scgsi.role.controller <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月07日 14:04 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping(path = "/api/role")
@Api(tags = "角色模块管理")
public class RoleController extends BaseController{

    @Autowired
    private RoleService roleService;

    @Autowired
    private UserService userService;

    @Autowired
    private DepartmentService departmentService;

    /**
     * Title：getRoleList（） <br>
     * Description：角色列表查询 <br>
     * author：于琦海 <br>
     * date：2020/4/7 15:46 <br>
     * @param form RoleListQueryForm
     * @return Object
     */
    @ApiOperation(value = "角色列表查询")
    @PostMapping("/getRoleList")
    public Object getRoleList(@Valid RoleListQueryForm form ,Authentication authentication) {
        return RenderResult.success(returnPageInfo(roleService.getRoleList(form,authentication)));
    }

    /**
     * Title：addRoleInfo() <br>
     * Description：新增角色信息 <br>
     * author：于琦海 <br>
     * date：2020/4/9 9:16 <br>
     * @param addForm RoleAddForm
     * @return RenderResult
     */
    @ApiOperation(value = "新增角色信息")
    @PostMapping("/addRoleInfo")
    public RenderResult addRoleInfo(@RequestBody RoleAddForm addForm, Authentication authentication) {
        roleService.addRoleInfo(addForm, authentication);
        return RenderResult.success();
    }

    /**
     * Title：findDepartmentDropdownBox <br>
     * Description：基本信息选择部门下拉框接口 <br>
     * author：于琦海 <br>
     * date：2020/4/20 9:25 <br>
     * @param authentication Authentication
     * @return Object
     */
    @ApiOperation(value = "基本信息选择部门下拉框接口")
    @GetMapping("/findDepartmentDropdownBox")
    public Object findDepartmentDropdownBox(Authentication authentication,String roleId){
        // 从鉴权中获取到用户ID，通过用户ID查询出当前所有得角色部门去重。
        String id = (String) authentication.getCredentials();
        List<String> departmentById = roleService.findDepartmentById(id);
        if (Objects.isNull(departmentById) || departmentById.size()<=0){
            throw new BusinessException(MessageCode.DEPARTMENT_NULL.getCode());
        }
        return RenderResult.success(departmentService.findDepartmentName(departmentById,roleId));
    }

    /**
     * Title：getUserByRoleID（） <br>
     * Description：根据roleID获取用户信息 <br>
     * author：于琦海 <br>
     * date：2020/4/9 9:17 <br>
     * @param roleId String
     * @return RenderResult<RoleManagerUserView>
     */
    @ApiOperation(value = "根据roleID获取用户信息树结构")
    @GetMapping("/getUserByRoleID")
    public RenderResult<List<RoleManagerUserView.View>> getUserByRoleID(String roleId){
        return RenderResult.success(roleService.getUserByRoleID(roleId));
    }

    /**
     * Title：getResourceByRoleID（） <br>
     * Description：根据roleID查询功能权限树结构 <br>
     * author：于琦海 <br>
     * date：2020/4/9 9:17 <br>
     * @param roleId String
     * @return RenderResult<RoleManagerUserView>
     */
    @ApiOperation(value = "根据roleID查询功能权限树结构")
    @GetMapping("/getResourceByRoleID")
    public RenderResult<List<ModuleTreeVO>> getResourceByRoleID(String roleId){
        return RenderResult.success(roleService.getResourceByRoleID(roleId));
    }

    /**
     * Title：isDisabled（） <br>
     * Description：角色停用启用 <br>
     * author：于琦海 <br>
     * date：2020/4/9 11:00 <br>
     * @param roleId String
     * @return RenderResult
     */
    @ApiOperation(value = "角色停用启用")
    @GetMapping("/isDisabled/{roleId}")
    public RenderResult isDisabled(@PathVariable String roleId,Authentication authentication){
        roleService.isDisabled(roleId,authentication);
        return RenderResult.success();
    }

    /**
     * Title：getFunctional() <br>
     * Description：功能权限展示 <br>
     * author：于琦海 <br>
     * date：2020/4/9 13:33 <br>
     * @param authentication Authentication
     * @return RenderResult
     */
    @ApiOperation(value = "功能权限展示")
    @GetMapping("/getFunctional")
    public RenderResult<List<ModuleTreeVO>> getFunctional(String competence,Authentication authentication) {
        if (StringUtils.isBlank(competence)){
            throw new BusinessException(MessageCode.PARAM_EXCEPTION.getCode());
        }
        return RenderResult.success(roleService.getFunctional(competence,authentication));
    }

    /**
     * Title：findUserByDepartmentId() <br>
     * Description：根据部门ID查询所有员工（competence：0管理员 1普通） <br>
     * author：于琦海 <br>
     * date：2020/4/9 13:33 <br>
     * @param competence String
     * @param toId String
     * @return RenderResult
     */
    @ApiOperation(value = "根据部门ID查询部门的所有员工；competence：0管理员 1普通）")
    @GetMapping("/findUserByDepartmentId")
    public RenderResult<List<UsersInfoView>> findUserByDepartmentId(String competence, String toId) {
        if (StringUtils.isBlank(competence)||StringUtils.isBlank(toId)){
            return RenderResult.success(new ArrayList<>());
        }
        return RenderResult.success(userService.findUserByDepartmentId(competence, toId));
    }

    /**
     * Title：deleteRole <br>
     * Description：根据角色ID删除角色 <br>
     * author：于琦海 <br>
     * date：2020/4/21 15:47 <br>
     * @param roleIds String
     * @return Object
     */
    @ApiOperation(value = "根据角色ID删除角色")
    @DeleteMapping("/deleteRole")
    public RenderResult deleteRole(@RequestBody List<String> roleIds){
        roleService.deleteRole(roleIds);
        return RenderResult.success();
    }

    /**
     * Title：updateRole <br>
     * Description：更新角色信息 <br>
     * author：于琦海 <br>
     * date：2020/4/22 15:04 <br>
     * @param form RoleUpdateForm
     * @param authentication Authentication
     * @return RenderResult
     */
    @ApiOperation(value = "更新角色信息")
    @PostMapping("/updateRole")
    public RenderResult updateRole(@Valid @RequestBody RoleUpdateForm form, Authentication authentication){
        roleService.updateRole(form,authentication);
        return RenderResult.success();
    }

    /**
     * Title：findUserByRoleId <br>
     * Description：通过角色ID查询当前角色的用户信息 <br>
     * author：于琦海 <br>
     * date：2020/4/22 15:30 <br>
     * @param roleId String
     * @return RenderResult
     */
    @ApiOperation(value = "通过角色ID查询当前角色的用户信息")
    @GetMapping("/findUserByRoleId")
    public RenderResult<List<UsersInfoView>> findUserByRoleId(@ApiParam(name = "roleId", value = "角色ID",
            required = true) String roleId){
        return RenderResult.success(roleService.findUserByRoleId(roleId));
    }

    /**
     * Title：bindRoleUsers <br>
     * Description：分配用户 <br>
     * author：于琦海 <br>
     * date：2020/4/22 15:53 <br>
     * @param form BindResourcesForm
     * @return RenderResult
     */
    @ApiOperation(value = "分配用户")
    @PostMapping("/bindRoleUsers")
    public RenderResult bindRoleUsers(@Valid @RequestBody BindResourcesForm form){
        roleService.removeUsersBind(form);
        return RenderResult.success();
    }
}
