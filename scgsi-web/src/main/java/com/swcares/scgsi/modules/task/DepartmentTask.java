package com.swcares.scgsi.modules.task;

import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import com.swcares.scgsi.hum.service.HumResourceDataService;

/**
 * ClassName：com.swcares.scgsi.modules.task.UserTask <br>
 * Description：部门定时任务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年6月9日 下午3:26:34 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class DepartmentTask {

    @Resource
    private HumResourceDataService humResourceDataService;

    public void manualSyncDeptHandler(){
        try {
            log.info("-------------->>>同步部门定时任务 -正在执行");
            humResourceDataService.departmentIncrementalData();;
        } catch (Exception e) {
            log.error("-------------->>>同步部门定时任务 -执行异常{}",e);
        }
        log.info("-------------->>>同步部门定时任务 -执行结束");
    }

}
