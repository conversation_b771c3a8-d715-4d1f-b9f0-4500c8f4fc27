package com.swcares.scgsi.modules.irregularflight;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.custom.LogOperation;
import com.swcares.scgsi.flight.dto.ActAuditParamDto;
import com.swcares.scgsi.flight.dto.ActConfigInfoParam;
import com.swcares.scgsi.flight.dto.ActQueryParamDto;
import com.swcares.scgsi.flight.entity.ActConfigInfo;
import com.swcares.scgsi.flight.service.ActAuditInfoService;
import com.swcares.scgsi.flight.service.ActConfigInfoService;
import com.swcares.scgsi.flight.vo.ActAuditInfoVo;
import com.swcares.scgsi.flight.vo.ActAuditPaxInfoVo;
import com.swcares.scgsi.flight.vo.ActConfigInfoVo;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * ClassName：com.swcares.scgsi.modules.irregularflight <br>
 * Description：代领审核 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月15日 11:01 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/dp/actAudit")
@Api(tags = "web代领审核")
public class ActAuditController extends BaseController {
    @Resource
    private ActConfigInfoService actConfigInfoService;

    @Resource
    private ActAuditInfoService actAuditInfoService;

    /**
     * Title：updateConfigInfo <br>
     * Description：代领审核配置信息新增编辑<br>
     * author：王建文 <br>
     * date：2020-3-15 11:04 <br>
     *
     * @param actConfigInfoParam 类型 0时间1拒绝原因
     */
    @PostMapping("updateConfigInfo")
    @ApiOperation(value = "代领审核配置信息新增编辑(1代领审核配置2异常行李配置)")
    public RenderResult updateConfigInfo(@RequestBody ActConfigInfoParam actConfigInfoParam) {
        try {
            ActConfigInfo actConfigInfo = actConfigInfoParam.getActConfigInfo();
                List<ActConfigInfoVo> actConfigInfoList=actConfigInfoService.getRefuseInfo(actConfigInfo.getType());
                if(actConfigInfoList.size()>0){
                    for(ActConfigInfoVo actConfigInfoVo:actConfigInfoList){
                        if(actConfigInfoVo.getContent().equals(actConfigInfo.getContent())){
                            Map<String,Object> map=new HashMap<>();
                            map.put("code","1");
                           if("2".equals(actConfigInfo.getType())){
                                map.put("msg","已经有对应的行李箱尺寸");
                                return new RenderResult("0", "已经有对应的行李箱尺寸",map);
                            }
                            if("1".equals(actConfigInfo.getType())){
                                map.put("msg","已有相同拒绝原因");
                                return new RenderResult("0", "已有相同拒绝原因",map);
                            }
                        }
                    }
                }

            actConfigInfoService.updateActConfigInfo(actConfigInfo.getType(), actConfigInfo.getContent(), actConfigInfo.getId());
            return RenderResult.success();
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }

    /**
     * Title：deleteRefuseInfo <br>
     * Description：删除拒绝原因<br>
     * author：王建文 <br>
     * date：2020-3-15 11:40 <br>
     *
     * @param ids 主键ids数组集合
     */
    @PostMapping("deleteRefuseInfo")
    @ApiOperation(value = "配置信息删除")
    public RenderResult deleteRefuseInfo(String ids) {
        try {
            actConfigInfoService.deleteRefuseInfo(ids.split(","));
            return RenderResult.success();
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }

    /**
     * Title：getDefaultTime <br>
     * Description：代领审核获取默认缓冲时间<br>
     * author：王建文 <br>
     * date：2020-3-16 9:23 <br>
     */
    @GetMapping("getDefaultTime")
    @ApiOperation(value = "代领审核获取默认缓冲时间")
    public RenderResult getDefaultTime() {
        return RenderResult.success(actConfigInfoService.getDefaultBufferTime());
    }

    /**
     * Title：getRefuseInfo <br>
     * Description： 拒绝原因展示列表<br>
     * author：王建文 <br>
     * date：2020-3-16 9:36 <br>
     *
     * @param type 类型
     */
    @GetMapping("getRefuseInfo")
    @ApiOperation(value = "拒绝原因展示列表")
    public RenderResult getRefuseInfo(String type) {
        return RenderResult.success(actConfigInfoService.getRefuseInfo(type));
    }

    /**
     * Title：getActApplyInfo <br>
     * Description： 代领审核列表信息分页查询<br>
     * author：王建文 <br>
     * date：2020-3-16 10:47 <br>
     *
     * @param actQueryParamDto 参数接收
     */
    @GetMapping("getActApplyInfo")
    @ApiOperation(value = "代领审核列表信息分页查询")
    public RenderResult getActApplyInfo(ActQueryParamDto actQueryParamDto) {
        QueryResults queryResults = actAuditInfoService.getAuditInfoPage(actQueryParamDto);
        return RenderResult.success(returnPageInfo(queryResults));
    }

    /**
     * Title：getActUserInfo <br>
     * Description：根据申领单号获取申领旅客信息<br>
     * author：王建文 <br>
     * date：2020-3-16 14:50 <br>
     *
     * @param applyCode 申领单号
     */
    @GetMapping("getActUserInfo")
    @ApiOperation(value = "根据申领单号获取申领旅客信息")
    public RenderResult getActUserInfo(String applyCode) {
        return RenderResult.success(actAuditInfoService.getActApplyPaxInfo(applyCode));
    }

    /**
     * Title：getAuditInfo <br>
     * Description： 代领旅客审核点击获取审核信息展示<br>
     * author：王建文 <br>
     * date：2020-3-16 15:57 <br>
     *
     * @param applyCode 申领单号
     */
    @GetMapping("getAuditInfo")
    @ApiOperation(value = "代领旅客审核点击获取审核信息展示")
    public RenderResult getAuditInfo(String applyCode) {
        Map<String, Object> showMap = new HashMap<>();
        List<Map<String, Object>> actUserInfo = new ArrayList<>();
        List<ActAuditPaxInfoVo> actAuditPaxInfoVoList = actAuditInfoService.getActApplyPaxInfo(applyCode);
        for (ActAuditPaxInfoVo actAuditPaxInfoVo : actAuditPaxInfoVoList) {
            Map<String, Object> map = new HashMap<>();
            map.put("paxName", actAuditPaxInfoVo.getPaxName());
            map.put("paxId", actAuditPaxInfoVo.getPaxId());
            map.put("idNo", actAuditPaxInfoVo.getIdNo());
            map.put("idType", actAuditPaxInfoVo.getIdType());
            map.put("actRole", actAuditPaxInfoVo.getActRole());
            map.put("imgUrl", actAuditPaxInfoVo.getImgUrl());
            map.put("applyCode", applyCode);
            actUserInfo.add(map);
        }
        showMap.put("actUserInfo", actUserInfo);
        //拒绝原因获取
        List<String> refuseInfo = new ArrayList<>();
        List<ActConfigInfoVo> actConfigInfoList = actConfigInfoService.getRefuseInfo("1");
        for (ActConfigInfoVo actConfigInfoVo : actConfigInfoList) {
            refuseInfo.add(actConfigInfoVo.getContent());
        }
        showMap.put("refuseList", refuseInfo);
        return RenderResult.success(showMap);
    }

    /**
     * Title：saveAuditInfo <br>
     * Description：代领审核信息提交<br>
     * author：王建文 <br>
     * date：2020-3-17 9:40 <br>
     *
     * @param actAuditParamDto 参数接收
     */
    @PostMapping("saveAuditInfo")
    @ApiOperation(value = "代领审核信息提交")
    @LogOperation(operationUrl="/api/dp/actAudit/saveAuditInfo",remark="代领审核信息提交")
    public RenderResult saveAuditInfo(@RequestBody ActAuditParamDto actAuditParamDto) {
        try {
            String applyCode=actAuditParamDto.getAuditInfo().get(0).getApplyCode();
            String applyStatus=actAuditInfoService.getApplyInfoByApplyCode(applyCode).getApplyStatus();
            if("3".equals(applyStatus)||"4".equals(applyStatus)){
                return new RenderResult("1", "代领旅客被冻结或赔付单已关闭!","代领旅客被冻结或赔付单已关闭!");
            }
            actAuditInfoService.saveActAuditInfo(actAuditParamDto);
            return RenderResult.success();
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }

    /**
     * Title：resetActAuditInfo <br>
     * Description： 代领审核撤回<br>
     * author：王建文 <br>
     * date：2020-3-17 11:02 <br>
     *
     * @param applyCode 申领单号
     */
    @PostMapping("resetActAuditInfo")
    @ApiOperation(value = "代领审核撤回")
    public RenderResult resetActAuditInfo(String applyCode) {
        String auditDate = actAuditInfoService.getLastAuditTime(applyCode);
        String defaultTime = actConfigInfoService.getDefaultBufferTime();
        long time = Integer.valueOf(defaultTime) * 60 * 1000;
        Date expirationDate =
                new Date(DateUtils.parseStrToDate(auditDate, DateUtils.YYYY_MM_DD_HH_MM_SS).getTime() + time);
        if (expirationDate.compareTo(new Date()) >= 0) {
            actAuditInfoService.resetActAuditInfo(applyCode);
            return RenderResult.success();
        } else {
            return new RenderResult("1", "超过缓冲配置时间不能撤回!","超过缓冲配置时间不能撤回!");
        }
    }

    /**
     * Title：quickPay <br>
     * Description： 代领审核快速支付<br>
     * author：王建文 <br>
     * date：2020-3-17 11:17 <br>
     *
     * @param applyCode 申领单号
     */
    @PostMapping("quickPay")
    @ApiOperation(value = "代领审核快速支付")
    public RenderResult quickPay(String applyCode) {
        try {
            actAuditInfoService.quickPay(applyCode);
            return RenderResult.success();
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }

    /**
     * Title：getActAuditInfo <br>
     * Description： 根据申领单号查询申领记录<br>
     * author：王建文 <br>
     * date：2020-3-17 14:27 <br>
     *
     * @param applyCode 申领单号
     */
    @GetMapping("getActAuditInfo")
    @ApiOperation(value = "根据申领单号查询申领记录")
    public RenderResult getActAuditInfo(String applyCode) {
        return RenderResult.success(actAuditInfoService.getActAuditRecordByApplyCode(applyCode));
    }
}