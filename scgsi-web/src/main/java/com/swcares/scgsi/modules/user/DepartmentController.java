package com.swcares.scgsi.modules.user;

import com.swcares.scgsi.department.common.model.vo.DepartAndUserVO;
import com.swcares.scgsi.department.common.model.vo.DepartmentUserVO;
import com.swcares.scgsi.department.service.impl.DepartmentServiceImpl;
import com.swcares.scgsi.hum.service.HumResourceDataService;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：DepartmentController <br>
 * Package：com.swcares.scgsi.modules.controller <br>
 * Copyright ? 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月03日 19:53 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping(path = "/api/department")
@Api(tags = "部门树结构")
public class DepartmentController {

    @Autowired
    private DepartmentServiceImpl departmentService;

    /**
     * Title：getDepartmentTree() <br>
     * Description：查询部门树 <br>
     * author：于琦海 <br>
     * date：2020/3/20 10:37 <br>
     * @return RenderResult<Object>
     */
    @ApiOperation(value = "获取山航的部门树行结构:包含人员信息")
    @GetMapping("/getDepartmentTree")
    public RenderResult<Object> getDepartmentTree(){
        return RenderResult.success(departmentService.getAllDepartment(true));
    }

    /**
     * Title：getDepartmentInfo（） <br>
     * Description：获取山航的部门树行结构:不包含人员信息 <br>
     * author：于琦海 <br>
     * date：2020/4/9 14:58 <br>
     * @return RenderResult<Object>
     */
    @ApiOperation(value = "获取山航的部门树行结构:不包含人员信息")
    @GetMapping("/getDepartmentInfo")
    public RenderResult<Object> getDepartmentInfo(){
        return RenderResult.success(departmentService.getAllDepartment(false));
    }

    /**
     * Title：getUserInfo（） <br>
     * Description：通过部门ID获取员工信息 <br>
     * author：于琦海 <br>
     * date：2020/4/9 14:58 <br>
     * @return RenderResult<List<DepartmentUserView>>
     */
    @ApiOperation(value = "通过部门ID获取员工信息")
    @GetMapping("/getUserInfo/{toId}")
    public RenderResult<List<DepartmentUserVO>> getUserInfo(@PathVariable String toId){
        return RenderResult.success(departmentService.getUserInfo(toId));
    }

    /**
     * Title：getDpartmentChildById <br>
     * Description：通过当前节点获取下级部门信息<br>
     * author：夏阳 <br>
     * date：2020年4月14日 下午6:49:08 <br>
     * @param deptId 当前节点部门信息
     * @return <br>
     */
    @ApiOperation(value = "通过当前节点获取下级部门信息")
    @GetMapping("/getDepartment/{deptId}")
    public RenderResult<DepartAndUserVO> getDpartmentChildById(@PathVariable String deptId){
        return RenderResult.success(departmentService.getDpartmentChildById(deptId));
    }

    @Resource
    private HumResourceDataService humResourceDataService;

    /**
     * @title DepartmentController.java
     * @description 手动同步部门增量数据，由接口发起请求调用
     * <AUTHOR>
     * @date 2024/1/16 13:39
     * @return void
     */
    @GetMapping("/manualSync")
    public void manualSync(){
        humResourceDataService.departmentIncrementalData();
    }
}