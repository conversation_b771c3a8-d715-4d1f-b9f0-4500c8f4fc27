package com.swcares.scgsi.modules.compensate;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.scgsi.common.model.form.ContentTraceForm;
import com.swcares.scgsi.common.model.form.FlightInfoListForm;
import com.swcares.scgsi.entity.FocFlightInfo;
import com.swcares.scgsi.flight.dao.impl.FlightCompensateDaoImpl;
import com.swcares.scgsi.flight.dto.PaxParseDto;
import com.swcares.scgsi.flight.enums.PaxStatusEnum;
import com.swcares.scgsi.flight.vo.PaxInfoParseVo;
import com.swcares.scgsi.service.FlightInfoService;
import com.swcares.scgsi.service.TraceService;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName：CompensateBaseController
 * @Description：航延补偿-航班信息查询公共接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/23 15:51
 * @version： v1.0
 */
@RestController
@RequestMapping("/api/dp/base")
@Api(tags = "航延补偿-航班信息查询公共接口V1")
public class CompensateBaseController  extends BaseController {


    @Resource
    private FlightInfoService flightInfoService;

    @Resource
    private FlightCompensateDaoImpl flightCompensateDao;

    @Resource
    private TraceService traceService;

    @GetMapping("getSegment")
    @ApiOperation(value = "通过航班号航班日期获取航段")
    public RenderResult getSegment(String flightNo, String flightDate) {
        flightDate = flightDate.replaceAll("-", "/");
        return RenderResult.success(flightInfoService.getFlightSegment(flightNo, flightDate));
    }


    /**
     * Title：getFlightInfo <br>
     * Description： 获取航班信息<br>
     * author：王建文 <br>
     * date：2020-3-23 14:14 <br>
     *
     * @param flightNo      航班号
     * @param flightDate    航班日期YYYY/MM/DD
     * @param choiceSegment 航段
     */
    @GetMapping("getFlightInfo")
    @ApiOperation(value = "通过航班号航班日期获取航段")
    public RenderResult getFlightInfo(String flightNo, String flightDate, String choiceSegment) {
        flightDate = flightDate.replaceAll("-", "/");
        String[] segment = choiceSegment.split(",");
        String segment1 = segment[0];
        String org = segment1.substring(0, segment1.indexOf("-"));
        String dest = segment1.substring(segment1.indexOf("-") + 1, segment1.length());
        FlightInfoListForm form = new FlightInfoListForm();
        form.setFlightNum(flightNo);
        form.setFlightDate(flightDate);
        form.setOrig(flightCompensateDao.getCityCodeInfoByCityName(org).getAirport4code());
        form.setDest(flightCompensateDao.getCityCodeInfoByCityName(dest).getAirport4code());
        FocFlightInfo flightInfo = flightInfoService.getFlightInfoList(form).get(0);
        //处理延误原因
        List<Map<String,String>> flightSegments=flightInfoService.getFlightSegment(flightNo,flightDate);
        if(flightSegments.size()>0){
            String lateReason="";
            for(Map<String,String> flightSegment:flightSegments){
                FlightInfoListForm form1 = new FlightInfoListForm();
                form1.setFlightNum(flightNo);
                form1.setFlightDate(flightDate);
                form1.setOrig(flightCompensateDao.getCityCodeInfoByCityName(flightSegment.get("DEPART_PORT")).getAirport4code());
                form1.setDest(flightCompensateDao.getCityCodeInfoByCityName(flightSegment.get("ARRIVAL_PORT"))
                        .getAirport4code());
                FocFlightInfo flightInfo1 = flightInfoService.getFlightInfoList(form1).get(0);
                if(StringUtils.isNotBlank(flightInfo1.getDelay_reason())){
                    lateReason+=flightSegment.get("DEPART_PORT")+flightSegment.get("ARRIVAL_PORT")+":"+flightInfo1.getDelay_reason()+",";
                }
            }
            flightInfo.setDelay_reason(lateReason);
        }
        List<FocFlightInfo> showList = new ArrayList<>();
        if (segment.length > 1) {
            // 预计起飞时间
            List<String> etd=new ArrayList<>();
            String flightId = "";
            // 计划起飞时间
            List<String> std=new ArrayList<>();
            for (String segment2 : segment) {
                String org1 = segment2.substring(0, segment2.indexOf("-"));
                String dest1 = segment2.substring(segment2.indexOf("-") + 1, segment2.length());
                FlightInfoListForm form1 = new FlightInfoListForm();
                form1.setFlightNum(flightNo);
                form1.setFlightDate(flightDate);
                form1.setOrig(flightCompensateDao.getCityCodeInfoByCityName(org1).getAirport4code());
                form1.setDest(flightCompensateDao.getCityCodeInfoByCityName(dest1)
                        .getAirport4code());
                FocFlightInfo flightInfo1 = flightInfoService.getFlightInfoList(form1).get(0);
                if(StringUtils.isNotBlank(flightInfo1.getEtd())){
                    etd.add(org1 + ":" + flightInfo1.getEtd());
                }
                if(StringUtils.isNotBlank(flightInfo1.getStd())){
                    std.add(org1 + ":" + flightInfo1.getStd());
                }
                flightId += flightInfo1.getFlightId() + ",";

            }
            if(etd.size()>0){
                LinkedHashSet<String> hashSet =new LinkedHashSet<>(etd);
                ArrayList<String> hashEtd =new ArrayList<>(hashSet);
                String showEtd="";
                for(String etd1:hashEtd){
                    showEtd+=etd1+",";
                }
                flightInfo.setEtd(showEtd.substring(0,showEtd.length()-1));
            }
            if(std.size()>0){
                LinkedHashSet<String> hashSet =new LinkedHashSet<>(std);
                ArrayList<String> hashStd =new ArrayList<>(hashSet);
                String showStd="";
                for(String std1:hashStd){
                    showStd+=std1+",";
                }
                flightInfo.setStd(showStd.substring(0,showStd.length()-1));
            }
            flightInfo.setFlightId(flightId);
        }
        showList.add(flightInfo);
        return RenderResult.success(showList);
    }

    @GetMapping("getPaxInfo")
    @ApiOperation(value = "根据航班航班号所选航段获取旅客信息,【票号不为空的数据】")
    public RenderResult getPaxInfoHotelBusiness(String flightNo, String flightDate, String choiceSegment,
                                                ContentTraceForm form, String selectSegment) {
        RenderResult paxInfo = this.getPaxInfo(flightNo, flightDate, choiceSegment, form, selectSegment);
        if (null == paxInfo.getData()) {
            return paxInfo;
        }
        Map<String,Object> returnFlightPaxInfo=new HashMap<>();
        List<PaxInfoParseVo> paxInfoParseVoList = new ArrayList<>();
        List<PaxInfoParseVo> repeatPaxList=new ArrayList<>();
        returnFlightPaxInfo = (Map<String, Object>) paxInfo.getData();
        paxInfoParseVoList = (List<PaxInfoParseVo>) returnFlightPaxInfo.get("paxList");
        repeatPaxList= (List<PaxInfoParseVo>) returnFlightPaxInfo.get("repeatPaxList");

        //去掉票号为空的数据
        List<PaxInfoParseVo> paxInfoParseVoList2 = paxInfoParseVoList.stream().filter(d -> StringUtils.isNotEmpty(d.getEtNum())).collect(Collectors.toList());
        List<PaxInfoParseVo> repeatPaxList2 = repeatPaxList.stream().filter(d -> StringUtils.isNotEmpty(d.getEtNum())).collect(Collectors.toList());
        returnFlightPaxInfo.put("repeatPaxList",repeatPaxList2);
        returnFlightPaxInfo.put("paxList",paxInfoParseVoList2);

        return RenderResult.success(returnFlightPaxInfo);
    }


    /**
     * Title：getPaxInfo <br>
     * Description： 航班四要素获取旅客信息<br>
     * author：王建文 <br>
     * date：2020-3-23 13:33 <br>
     *
     * @param flightNo      航班号
     * @param flightDate    航班日期
     * @param choiceSegment 航段
     */
    @GetMapping("getPaxInfoCompensate")
    @ApiOperation(value = "根据航班航班号所选航段获取旅客信息")
    public RenderResult getPaxInfo(String flightNo, String flightDate, String choiceSegment,
                                   ContentTraceForm form, String selectSegment) {
        if(StringUtils.isNotBlank(selectSegment)){
            choiceSegment=selectSegment;
        }
        String[] segment = choiceSegment.split(",");
        List<Object> paxList = new ArrayList<>();
        for (String segment1 : segment) {
            String org = segment1.substring(0, segment1.indexOf("-"));
            String dest = segment1.substring(segment1.indexOf("-") + 1, segment1.length());
            ContentTraceForm queryForm = new ContentTraceForm();
            queryForm.setFlightNum(flightNo);
            queryForm.setFlightDate(flightDate);
            queryForm.setOrig(flightCompensateDao.getCityCodeInfoByCityName(org).getAirport3code());
            queryForm
                    .setDest(flightCompensateDao.getCityCodeInfoByCityName(dest).getAirport3code());
            if (StringUtils.isNotBlank(form.getKeySearch())) {
                queryForm.setKeySearch(form.getKeySearch());
            }
            if (StringUtils.isNotBlank(form.getCheckStatus())) {
                queryForm.setCheckStatus(form.getCheckStatus());
            }
            if (StringUtils.isNotBlank(form.getPaxName())) {
                queryForm.setPaxName(form.getPaxName());
            }
            if (StringUtils.isNotBlank(form.getNotContainsN())) {
                queryForm.setNotContainsN(form.getNotContainsN());
            }
            if (StringUtils.isNotBlank(form.getIsCancel())) {
                queryForm.setIsCancel(form.getIsCancel());
            }
            if (StringUtils.isNotBlank(form.getCancelDate())) {
                queryForm.setCancelDate(form.getCancelDate());
            }
            if (StringUtils.isNotBlank(form.getIsPrintTktNo())) {
                queryForm.setIsPrintTktNo(form.getIsPrintTktNo());
            }
            if (StringUtils.isNotBlank(form.getTktStartDate())
                    && StringUtils.isNotBlank(form.getTktEndDate())) {
                queryForm.setTktEndDate(form.getTktEndDate());
                queryForm.setTktStartDate(form.getTktStartDate());
            }
            Object object=traceService.getPsgListByFilght(queryForm);
            if(null!=object){
                paxList.add(object);
            }
        }
        Map<String,Object> returnFlightPaxInfo=new HashMap<>();
        List<PaxInfoParseVo> paxInfoParseVoList = new ArrayList<>();
        List<PaxInfoParseVo> repeatPaxList=new ArrayList<>();
        if(paxList.size()>0){
            for (Object object : paxList) {
                PaxParseDto parseObject = JSON.parseObject(object.toString(), PaxParseDto.class);
                List<PaxInfoParseVo> dataList = parseObject.getDataList();
                if (dataList.size() > 0) {
                    //处理重复旅客数据
                    Map<String,Object> flightPaxInfo =removeDuplicatePax(dataList);
                    List<PaxInfoParseVo> removeDuplicatePaxList=(List<PaxInfoParseVo>)flightPaxInfo.get("removeDuplicatePaxList");
                    List<PaxInfoParseVo> addRepeatPaxList=(List<PaxInfoParseVo>)flightPaxInfo.get("repeatPaxList");
                    if(addRepeatPaxList.size()>0){
                        repeatPaxList.addAll(addRepeatPaxList);
                    }

                    for (PaxInfoParseVo paxInfoParseVo : removeDuplicatePaxList) {
                        Map<String, Object> map =
                                flightCompensateDao.getPaxPayCountAndPayMoney(paxInfoParseVo.getIdx(),
                                        paxInfoParseVo.getFlightNum(), paxInfoParseVo.getFlightDate());
                        if (map.isEmpty()) {
                            paxInfoParseVo.setPayCount("0");
                            paxInfoParseVo.setPayMoney("0");
                        } else {
                            paxInfoParseVo.setPayCount(map.get("PAYCOUNT").toString());
                            paxInfoParseVo.setPayMoney(map.get("PAYMONEY").toString());
                        }
                        //处理旅客取消状态
                        if(StringUtils.isNotBlank(paxInfoParseVo.getStatus())){
                            if(PaxStatusEnum.CL.getKey().equals(paxInfoParseVo.getStatus())||PaxStatusEnum.XR.getKey().equals(paxInfoParseVo.getStatus())){
                                paxInfoParseVo.setCancel(PaxStatusEnum.build(paxInfoParseVo.getStatus()).getKey()+"("+PaxStatusEnum.build(paxInfoParseVo.getStatus()).getValue()+")");
                            }
                            paxInfoParseVo.setStatus(PaxStatusEnum.build(paxInfoParseVo.getStatus()).getKey()+"("+PaxStatusEnum.build(paxInfoParseVo.getStatus()).getValue()+")");
                        }else{
                            paxInfoParseVo.setStatus("未出票");
                        }
                        paxInfoParseVoList.add(paxInfoParseVo);
                    }
                }
            }
        }
        returnFlightPaxInfo.put("repeatPaxList",repeatPaxList);
        returnFlightPaxInfo.put("paxList",paxInfoParseVoList);
        return RenderResult.success(returnFlightPaxInfo);
    }
    /**
     * Title：removeDuplicatePax <br>
     * Description： 新建赔付单旅客，根据身份证号去重按照购票时间先后顺序<br>
     * author：王建文 <br>
     * date：2021-7-5 9:23 <br>
     * @param  dataList 未去重旅客数据
     * @return
     */
    private Map<String,Object> removeDuplicatePax(List<PaxInfoParseVo> dataList){
        Map<String,Object> flightPaxInfo=new HashMap<>();
        //根据身份证号分组保存各个数据
        List<Map<String, List<PaxInfoParseVo>>> newPaxList = new ArrayList<Map<String, List<PaxInfoParseVo>>>();
        //1.获取所有身份证号去重
        List<String> idNos=new ArrayList<>();
        for(PaxInfoParseVo paxInfoParseVo:dataList){
            if (StringUtils.isNotEmpty(paxInfoParseVo.getIdNum())) {
                idNos.add(paxInfoParseVo.getIdNum());
            }
        }
        //2.身份号去重
        HashSet hIdNosList = new HashSet(idNos);
        idNos.clear();
        idNos.addAll(hIdNosList);
        //3.循环身份证号，以身份证号分组保存数据
        for(int i=0;i<idNos.size();i++){
            Map<String, List<PaxInfoParseVo>> mapList=new HashMap<String, List<PaxInfoParseVo>>();
            List<PaxInfoParseVo> voList=new ArrayList<PaxInfoParseVo>();
            for(PaxInfoParseVo paxInfoParseVo :dataList){
                paxInfoParseVo.setPhone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, paxInfoParseVo.getPhone()));
                if(idNos.get(i).equals(paxInfoParseVo.getIdNum())){
                    if(null==mapList.get(idNos.get(i))){
                        voList.add(paxInfoParseVo);
                        mapList.put(idNos.get(i), voList);
                    }else{
                        voList=mapList.get(idNos.get(i));
                        voList.add(paxInfoParseVo);
                        mapList.put(idNos.get(i),voList );
                    }
                }
            }
            newPaxList.add(mapList);
        }
        //4.找出每个身份证号的数据，大于1进行去重操作
        List<PaxInfoParseVo> removeDuplicatePaxList=new ArrayList<>();
        //5.重复旅客数据添加
        List<PaxInfoParseVo> repeatPaxList=new ArrayList<>();
        for(Map<String, List<PaxInfoParseVo>> map:newPaxList){
            for(String key:map.keySet()){
                List<PaxInfoParseVo> paxInfoParseVoList=map.get(key);
                if(paxInfoParseVoList.size()>1){
                    paxListSortByPrintTicketTime(paxInfoParseVoList);
                    removeDuplicatePaxList.add(paxInfoParseVoList.get(paxInfoParseVoList.size()-1));
                    repeatPaxList.add(paxInfoParseVoList.get(paxInfoParseVoList.size()-1));
                }else{
                    removeDuplicatePaxList.addAll(paxInfoParseVoList);
                }
            }
        }
        flightPaxInfo.put("repeatPaxList",repeatPaxList);
        flightPaxInfo.put("removeDuplicatePaxList",removeDuplicatePaxList);
        return flightPaxInfo;
    }
    /**
     * Title： paxListSortByPrintTicketTime<br>
     * Description： 根据旅客购票时间升序排列<br>
     * author：王建文 <br>
     * date：2021-7-5 15:00 <br>
     * @param  list
     * @return
     */
    private  void paxListSortByPrintTicketTime(List<PaxInfoParseVo> list) {
        Collections.sort(list, new Comparator<PaxInfoParseVo>() {
            @Override
            public int compare(PaxInfoParseVo o1, PaxInfoParseVo o2) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    Date dt1 = format.parse(o1.getPrintTicketTime());
                    Date dt2 = format.parse(o2.getPrintTicketTime());
                    if (dt1.getTime() > dt2.getTime()) {
                        return 1;
                    } else if (dt1.getTime() < dt2.getTime()) {
                        return -1;
                    } else {
                        return 0;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return 0;
            }
        });
    }

}
