package com.swcares.scgsi.modules.uploadanddownload;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import cn.hutool.core.lang.UUID;
import com.swcares.scgsi.fileuploadanddownload.UploadAndDownload;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.web.RenderResult;

/**
 * ClassName：com.swcares.scgsi.modules.uploadanddownload.UploadAndDownloadController <br>
 * Description：文件上传下载接口 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年5月13日 下午4:41:05 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/uploadAndDownload")
@Api(value = "/api/uploadAndDownload", tags = "文件上传下载接口")
@Slf4j
public class UploadAndDownloadController {
    @Autowired
    UploadAndDownload uploadAndDownload;

    /**
     * Title：upload <br>
     * Description：文件上传接口 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:36:06 <br>
     * @param request
     * @return 统一返回类
     * @throws Exception <br>
     */
    @PostMapping(value = "/upload")
    @ApiOperation(value = "文件上传", notes = "用于文件上传")
    public RenderResult<Object> upload(HttpServletRequest request,@ApiParam(
            name = "currentUser", value = "用户标识", required = true) String currentUser) {
        log.info("WEB文件上传开始:用户{}!!!!", currentUser);
        List<String> list = new ArrayList<String>();
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
        MultipartFile file = null;
        log.info("WEB文件上传开始:用户{},文件个数{}!!!!", currentUser, files.size());
        if (files.size() == 0) {
            return RenderResult.fail();
        }
        for (int i = 0; i < files.size(); ++i) {
            Date date = new Date();
            file = files.get(i);
            if (StringUtils.isNotEmpty(file.getOriginalFilename())) {
                String fileName = UUID.randomUUID().toString();
                String fileSuffix =
                        file.getOriginalFilename().substring(
                                file.getOriginalFilename().lastIndexOf("."));
                String remote = fileName + "-" + date.getTime() + fileSuffix;// 为防止重名文件需加上时间戳后缀
                try {
                    list.add(uploadAndDownload.ftpUpload(file, remote,currentUser));
                } catch (Exception e) {
                    // TODO %CodeTemplates.catchblock.tododesc e.printStackTrace();
                    e.printStackTrace();
                    log.error("WEB文件上传异常:用户{},文件名{},第{}个文件,异常信息{}!!!!", currentUser, file.getOriginalFilename(), i+1,e);
                }
            }
        }
        log.info("WEB文件上传结束:用户{},文件个数{},上传成功数量{}!!!!", currentUser, files.size(), list.size());
        return RenderResult.success(list);
    }

    /**
     * Title：download <br>
     * Description：文件下载 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:38:40 <br>
     * @param request
     * @param response
     * @param remote 文件名
     * @param remotePath 文件路径
     * @throws Exception <br>
     */
    @PostMapping("/download")
    @ApiOperation(value = "文件下载", notes = "用于文件下载")
    public void download(HttpServletRequest request, HttpServletResponse response,
            @ApiParam(name = "remote", value = "文件名", required = true) @RequestParam(
                    name = "remote") String remote, @ApiParam(name = "remotePath", value = "文件路径",
                    required = true) @RequestParam(name = "remotePath") String remotePath)
            throws Exception {
        // 配置文件下载
        response.setContentType("multipart/form-data");
        // 下载文件能正常显示中文
        response.addHeader("Content-Disposition",
                "attachment;fileName=" + new String(remote.getBytes("UTF-8"), "iso-8859-1"));
        uploadAndDownload.ftpDownload(response.getOutputStream(), remote, remotePath);
    }

    /**
     * Title： IoReadImage<br>
     * Description：图片回显<br>
     * author：王建文 <br>
     * date：2020-4-8 13:21 <br>
     * @param  imgUrl 名称
     * @return
     * @throws Exception 
     */
    @GetMapping(value = "/showImg")
    @ApiOperation(value = "图片回显", notes = "图片回显示")
    public void IoReadImage(
            @ApiParam(name = "imgUrl", value = "文件全路径", required = true) String imgUrl,
            HttpServletResponse response) throws Exception {
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            String remote = imgUrl.substring(imgUrl.lastIndexOf("/") + 1, imgUrl.length());
            String remotePath = imgUrl.substring(0, imgUrl.lastIndexOf("/"));
            uploadAndDownload.ftpDownload(out, remote, remotePath);
            String type = imgUrl.substring(imgUrl.indexOf(".") + 1);
            if (type.equals("png")) {
                response.setContentType("image/png");
            }
            if (type.equals("jpeg")) {
                response.setContentType("image/jpeg");
            }
            if (type.equals("jpg")) {
                response.setContentType("image/jpg");
            }
            out.flush();
        } catch (Exception e) {
            throw e;
        } finally {
            out.close();
        }
    }

    /**
     * Title： deleteImg<br>
     * Description： 删除上传文件<br>
     * author：王建文 <br>
     * date：2020-4-8 13:28 <br>
     * @param  docName 文件名
     * @return
     */
    @PostMapping("/deleteImg")
    public RenderResult<Object> deleteImg(String docName) {
        String path = uploadAndDownload.createDateDirs(uploadAndDownload.getRemotePathTemp());
        path = path + "/" + DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDD);
        File file = new File(path + File.separator + docName);
        if (file.exists()) {
            file.delete();
            return RenderResult.success();
        }
        return new RenderResult<Object>("1", "删除的文件不存在");
    }
}
