package com.swcares.scgsi.modules.user;

import com.swcares.scgsi.role.common.model.form.BindResourcesForm;
import com.swcares.scgsi.role.common.model.form.ResourceAddForm;
import com.swcares.scgsi.role.common.model.form.ResourceUpdateForm;
import com.swcares.scgsi.role.common.model.form.ResourcesQueryForm;
import com.swcares.scgsi.role.service.ResourceService;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ResourceController <br>
 * Package：com.swcares.scgsi.role.controller <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 04月08日 10:33 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping(path = "/api/resource")
@Api(tags = "资源模块管理")
public class ResourceController extends BaseController {

    @Autowired
    private ResourceService resourceService;

    /**
     * Title：getResources() <br>
     * Description：根据模块名称和模块类型查询资源列表 <br>
     * author：于琦海 <br>
     * date：2020/4/8 10:36 <br>
     * @param form ResourcesQueryForm
     * @return Object
     */
    @ApiOperation(value = "资源列表查询")
    @PostMapping("/getResources")
    public Object getResources(@Valid ResourcesQueryForm form) {
        return RenderResult.success(super.returnPageInfo(resourceService.getResources(form)));
    }

    /**
     * Title：addResource() <br>
     * Description：资源新增 <br>
     * author：于琦海 <br>
     * date：2020/4/8 13:13 <br>
     * @param authentication Authentication
     * @param form ResourceAddForm
     * @return RenderResult
     */
    @ApiOperation(value = "资源新增")
    @PostMapping("/addResource")
    public RenderResult addResource(@Valid ResourceAddForm form, Authentication authentication){
        resourceService.addResource(form,authentication);
        return RenderResult.success();
    }

    /**
     * Title：updateResource() <br>
     * Description：资源修改 <br>
     * author：于琦海 <br>
     * date：2020/4/8 13:55 <br>
     * @param form ResourceUpdateForm
     * @return RenderResult
     */
    @ApiOperation(value = "资源修改")
    @PostMapping("/updateResource")
    public RenderResult updateResource(@Valid ResourceUpdateForm form){
        resourceService.updateResource(form);
        return RenderResult.success();
    }

    /**
     * Title：batchDisabled（） <br>
     * Description：批量停用启用 <br>
     * author：于琦海 <br>
     * date：2020/4/8 14:20 <br>
     * @param: null
     * @return:
     */
    @ApiOperation(value = "批量停用启用")
    @PostMapping("/batchDisabled")
    public RenderResult batchDisabled(@RequestBody List<String> id, @RequestParam Boolean status){
        resourceService.batchDisabled(id, status);
        return RenderResult.success();
    }

    /**
     * Title：deleteResource() <br>
     * Description：资源删除<br>
     * author：于琦海 <br>
     * date：2020/4/8 16:31 <br>
     * @param id List<String>
     * @return RenderResult
     */
    @ApiOperation(value = "资源删除")
    @PostMapping("/deleteResource")
    public RenderResult deleteResource(@RequestBody List<String> id){
        resourceService.deleteResource(id);
        return RenderResult.success();
    }

    /**
     * Title：获取模块树 <br>
     * Description：获取模块树 <br>
     * author：于琦海 <br>
     * date：2020/4/9 11:00 <br>
     * @return RenderResult
     */
    @ApiOperation(value = "获取模块树")
    @GetMapping("/getModuleTree")
    public RenderResult getModuleTree(){
        return RenderResult.success(resourceService.getModuleTree());
    }

    /**
     * Title：findResourcesByRoleId <br>
     * Description：通过角色ID获取资源集合 <br>
     * author：于琦海 <br>
     * date：2020/4/22 14:01 <br>
     * @param roleId String
     * @return RenderResult
     */
    @ApiOperation(value = "通过角色ID获取资源集合")
    @GetMapping("/findResourcesByRoleId")
    public RenderResult< List<String>> findResourcesByRoleId(@ApiParam(name = "roleId", value = "角色ID",
            required = true) String roleId) {
        return RenderResult.success(resourceService.findResourcesByRoleId(roleId));
    }

    /**
     * Title：updateRoleResources <br>
     * Description：修改功能权限 <br>
     * author：于琦海 <br>
     * date：2020/4/22 15:53 <br>
     * @param form BindResourcesForm
     * @return RenderResult
     */
    @ApiOperation(value = "修改功能权限")
    @PostMapping("/updateRoleResources")
    public RenderResult updateRoleResources(@Valid @RequestBody BindResourcesForm form){
        resourceService.removeRolesBind(form.getRoleId());
        resourceService.bindResources(form);
        return RenderResult.success();
    }
}
