package com.swcares.scgsi.modules.task;

import com.swcares.scgsi.flight.dao.impl.ActAuditInfoDaoImpl;
import com.swcares.scgsi.flight.service.ActAuditInfoService;
import com.swcares.scgsi.flight.service.ActMessageService;
import com.swcares.scgsi.flight.vo.ActMessageVo;
import com.swcares.scgsi.message.common.model.form.MessageSendForm;
import com.swcares.scgsi.message.common.msgenum.MessageTypeEnum;
import com.swcares.scgsi.message.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.modules.task <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 04月13日 20:15 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class ActOrderInfoMessageTask {
    @Resource
    private ActMessageService actMessageService;

    @Resource
    private MessageService messageService;

    @Resource
    private ActAuditInfoService actAuditInfoService;

    @Resource
    private ActAuditInfoDaoImpl actAuditInfoDao;

    private static final String ACT_ROLE_ID = "7fb16acb-ab7b-45d3-921b-958d8186d494";

    @Transactional(rollbackFor = Exception.class)
    public void sendActOrderMessage() throws Exception {
        //2.组装消息体
        //2.1标题
        String title = "代领旅客审核";
        List<ActMessageVo> actMessageVoList = actMessageService.getActMessageInfo();
        if (actMessageVoList.size() > 0) {
            for (ActMessageVo actMessageVo : actMessageVoList) {
                //1.更新发送状态
                actMessageService.updateActSendStatus(actMessageVo.getApplyCode());
                MessageSendForm messageSendForm = getMessageSendForm();
                messageSendForm.setMsgTitle(title);
                messageSendForm.setMsgContent(getSendContent(actMessageVo));
                messageSendForm.setMsgUser("0");
                messageSendForm.setFlightNo(actMessageVo.getFlightNo());
                messageSendForm.setFlightDate(actMessageVo.getFlightDate());
                String pcUrl = messageSendForm.getPcUrl();
                pcUrl += "?applyCode=" + actMessageVo.getApplyCode();
                messageSendForm.setPcUrl(pcUrl);
                //根据代领审核角色查看所有具有该权限的人员
                List<Map<String,Object>> actRoleUserList=actAuditInfoDao.getHasActRoleUser(ACT_ROLE_ID);
                if(actRoleUserList.size()>0){
                    List<String> actRoleUsers=new ArrayList<>();
                    for(Map<String,Object> map:actRoleUserList){
                        actRoleUsers.add(map.get("ACCOUNT").toString());
                    }
                    String[] userIds =new String[actRoleUsers.size()];
                    actRoleUsers.toArray();
                    messageSendForm.setMsgReplyUser(userIds);
                    messageService.sendMsgToUser(messageSendForm);
                }else{
                    log.error("代领审核消息推送未找到具有该权限的人员");
                }
            }
        }
    }

    private String getSendContent(ActMessageVo actMessageVo) {
        //2.2发送内容
        StringBuffer content = new StringBuffer();
        String flightNo = actMessageVo.getFlightNo();
        String flightDate = actMessageVo.getFlightDate();
        String segment = actMessageVo.getSegment();
        String applyUser = actMessageVo.getApplyUser();
        String applyCount = actMessageVo.getApplyCustNum();
        String payMoney = actMessageVo.getTransAmount();
        content.append("<h3>").append(flightDate).append(" ").append(flightNo);
        content.append(" ").append(segment).append("</h3>");
        content.append("<p>").append("申领人: ").append(applyUser).append("  代领旅客: ");
        content.append(applyCount).append("人").append("</p>");
        content.append("<p>").append("合计申请金额:").append(payMoney).append("</p>");
        System.out.println(content.toString());
        return content.toString();
    }

    private MessageSendForm getMessageSendForm() {
        MessageSendForm sendForm = new MessageSendForm();
        sendForm.setMsgType(MessageTypeEnum.ACT_FOR_PAX.getType());
        sendForm.setMsgTypeName(MessageTypeEnum.ACT_FOR_PAX.getTypeName());
        sendForm.setMsgChildType(MessageTypeEnum.ACT_FOR_PAX.getChildType() + "");
        sendForm.setMsgChildTypeName(MessageTypeEnum.ACT_FOR_PAX.getChildTypeName());
        sendForm.setPcUrl(MessageTypeEnum.ACT_FOR_PAX.getPcUrl());
        sendForm.setMobileUrl(MessageTypeEnum.ACT_FOR_PAX.getMobileUrl());
        sendForm.setIsAudit(MessageTypeEnum.ACT_FOR_PAX.getIsAudit());
        sendForm.setMsgDate(new Date());
        return sendForm;
    }

    @Transactional(rollbackFor = Exception.class)
    public void quickPayActOrder() {
        actAuditInfoService.updateActOrder();
    }

    /**
     * Title： sendMessageToPax<br>
     * Description： 代领旅客审核不通过超过缓冲时间短信推送，并把旅客领取状态置为未领取<br>
     * author：王建文 <br>
     * date：2020-4-27 14:56 <br>
     *
     * @param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendMessageToPax() {
        actAuditInfoService.sendMessageToPax();
    }
}