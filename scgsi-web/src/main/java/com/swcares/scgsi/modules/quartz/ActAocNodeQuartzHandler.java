package com.swcares.scgsi.modules.quartz;

import com.swcares.scgsi.audit.dao.impl.OrderAuditDaoImpl;
import com.swcares.scgsi.audit.enums.AuditNodeAssigneeEnum;
import com.swcares.scgsi.audit.service.OrderAuditService;
import com.swcares.scgsi.audit.vo.AocOndutyUserVo;
import com.swcares.scgsi.audit.vo.AuditNodeTaskInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.TaskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.modules.quartz <br>
 * Description：审核流程中-AOC节点处理人处理 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 04月09日 13:18 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class ActAocNodeQuartzHandler {

    @Resource
    private OrderAuditDaoImpl orderAuditDaoImpl;
    @Resource
    private OrderAuditService orderAuditService;

    @Resource
    private TaskService taskService;

    @Transactional(rollbackFor = Exception.class)
    public void aocNodeHandler(){
        try {
            log.info("-------------->>>审核-执行【设置节点审批人】-正在执行");

            //aoc值班经理
            this.handleTaskAssignee(AuditNodeAssigneeEnum.AOC_NODE_ASSIGNEE.getRole()
                    ,AuditNodeAssigneeEnum.AOC_NODE_ASSIGNEE.getNode(),AuditNodeAssigneeEnum.AOC_NODE_ASSIGNEE.getOnDuty());

            //异常行李主任
            this.handleTaskAssignee(AuditNodeAssigneeEnum.DIRECTOR_NODE_ASSIGNEE.getRole()
                    ,AuditNodeAssigneeEnum.DIRECTOR_NODE_ASSIGNEE.getNode(),AuditNodeAssigneeEnum.DIRECTOR_NODE_ASSIGNEE.getOnDuty());

            //异常行李经理
            this.handleTaskAssignee(AuditNodeAssigneeEnum.MANAGER_NODE_ASSIGNEE.getRole()
                    ,AuditNodeAssigneeEnum.MANAGER_NODE_ASSIGNEE.getNode(),AuditNodeAssigneeEnum.MANAGER_NODE_ASSIGNEE.getOnDuty());

            //异常行李总经理
            this.handleTaskAssignee(AuditNodeAssigneeEnum.GENERAL_MANAGER_NODE_ASSIGNEE.getRole()
                    ,AuditNodeAssigneeEnum.GENERAL_MANAGER_NODE_ASSIGNEE.getNode(),AuditNodeAssigneeEnum.GENERAL_MANAGER_NODE_ASSIGNEE.getOnDuty());


        } catch (Exception e) {
            log.error("-------------->>>审核-执行【设置节点审批人】-执行异常{}",e);
        }
    }



    /**
     * Title： handleTaskAssignee<br>
     * Description： 查询角色下用户正在值班的人，查询节点上没有审批人的数据，进行设置<br>
     * author：傅欣荣 <br>
     * date：2020/7/2 10:39 <br>
     * @param
     * @return
     */
    private void handleTaskAssignee(String roleName,String nodeName,Boolean isOnDuty){

        if(StringUtils.isEmpty(roleName) || StringUtils.isEmpty(nodeName)) return;

        log.info("-------------->>>审核-执行角色[{}]，设置流程节点[{}]-审批人，值班状态[{}]",roleName,nodeName,isOnDuty);

        //查询节点没有执行的数据
        List<AuditNodeTaskInfoVo> aocTaskList = orderAuditDaoImpl.getAuditNodeTaskInfo(nodeName);
        log.info("-------------->>>审核-执行角色[{}]-【查询[{}]节点未设置审批人的任务数据】[{}]",roleName,nodeName,aocTaskList.toString());
        if(aocTaskList.size() < 1) return;

        //查询角色用户值班人员
        AocOndutyUserVo onDutyUser = orderAuditDaoImpl.getRoleOndutyUserInfo(roleName,isOnDuty);
        log.info("-------------->>>审核-执行角色[{}]-【查询用户值班-[{}]数据】[{}]",roleName,isOnDuty,onDutyUser.toString());
        if(StringUtils.isBlank(onDutyUser.getUserId())) return;

        //当前任务节点是nodeName 并且还未指定值班人，查询到有role值班人。 做更新操作，并通知
        for (AuditNodeTaskInfoVo aocInfo :aocTaskList){
            if(StringUtils.isNotBlank(aocInfo.getTaskId()) && StringUtils.isNotBlank(aocInfo.getOrderId())) {
                taskService.addCandidateUser(aocInfo.getTaskId(), onDutyUser.getUserId());
                log.info("-------------->>>审核-执行角色[{}]-【推送节点[{}]审核操作消息】orderId[{}],推送给[{}]"
                        , roleName, nodeName, aocInfo.getOrderId(), aocTaskList.toString());
                //推送消息
                orderAuditService.sendAuditMessage(aocInfo.getOrderId(), onDutyUser.getUserId());
            }
        }

    }
}
