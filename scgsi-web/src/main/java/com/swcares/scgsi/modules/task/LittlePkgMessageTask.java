package com.swcares.scgsi.modules.task;

import com.swcares.scgsi.message.common.model.form.MessageSendForm;
import com.swcares.scgsi.message.common.msgenum.MessageTypeEnum;
import com.swcares.scgsi.message.service.MessageService;
import com.swcares.scgsi.pkg.service.LittlePkgMessageService;
import com.swcares.scgsi.pkg.vo.LittlePkgMessageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.modules.task <br>
 * Description：异常少收行李消息提醒task <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 04月09日 10:49 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class LittlePkgMessageTask {
    @Resource
    private MessageService messageService;

    @Resource
    private LittlePkgMessageService littlePkgMessageService;

    @Transactional(rollbackFor = Exception.class)
    public void sendLittlePkgMessage() throws Exception {
        //2.组装消息体
        //2.1标题
        String title = "少收行李到期结案提醒";
        List<LittlePkgMessageVo> littlePkgMessageVoList = littlePkgMessageService.getLittlePkgMessage();
        if (littlePkgMessageVoList.size() > 0) {
            for (LittlePkgMessageVo littlePkgMessageVo : littlePkgMessageVoList) {
                //1.更新发送状态
                littlePkgMessageService.updateLittlePkgSendStatus(littlePkgMessageVo.getAccidentId());
                MessageSendForm messageSendForm = getMessageSendForm();
                messageSendForm.setMsgTitle(title);
                messageSendForm.setMsgContent(getSendContent(littlePkgMessageVo));
                messageSendForm.setMsgUser("0");
                messageSendForm.setFlightNo(littlePkgMessageVo.getFlightNo());
                messageSendForm.setFlightDate(littlePkgMessageVo.getFlightDate());
                messageSendForm.setPcUrl(messageSendForm.getPcUrl()+littlePkgMessageVo.getAccidentId());
                messageSendForm.setMobileUrl(messageSendForm.getMobileUrl()+littlePkgMessageVo.getAccidentId());
                String[] userIds = { littlePkgMessageVo.getCreateUser() };
                messageSendForm.setMsgReplyUser(userIds);
                messageService.sendMsgToUser(messageSendForm);
            }
        }
    }

    private String getSendContent(LittlePkgMessageVo littlePkgMessageVo) {
        //2.2发送内容
        StringBuffer content = new StringBuffer();
        String flightNo = littlePkgMessageVo.getFlightNo();
        String flightDate = littlePkgMessageVo.getFlightDate();
        String remindDay = littlePkgMessageVo.getRemindDay();
        String paxName = littlePkgMessageVo.getPaxName();
        String accidentId = littlePkgMessageVo.getAccidentId();
        content.append("<h3>").append(flightDate).append(" ").append(flightNo);
        content.append(" ").append("常规少收(").append(remindDay);
        content.append("个自然日)").append("</h3>");
        content.append("<p>").append(paxName).append(" ").append("异常行李事故 ");
        content.append("(事故单号:").append(accidentId).append("),");
        content.append("已达到结案").append("</p>");
        content.append("<p>").append("周期,请尽快处理!").append("</p>");
        System.out.println(content.toString());
        return content.toString();
    }

    private MessageSendForm getMessageSendForm() {
        MessageSendForm sendForm = new MessageSendForm();
        sendForm.setMsgType(MessageTypeEnum.ABNORMAL_LUGGAGE.getType());
        sendForm.setMsgTypeName(MessageTypeEnum.ABNORMAL_LUGGAGE.getTypeName());
        sendForm.setMsgChildType(MessageTypeEnum.ABNORMAL_LUGGAGE.getChildType() + "");
        sendForm.setMsgChildTypeName(MessageTypeEnum.ABNORMAL_LUGGAGE.getChildTypeName());
        sendForm.setPcUrl(MessageTypeEnum.ABNORMAL_LUGGAGE.getPcUrl());
        sendForm.setMobileUrl(MessageTypeEnum.ABNORMAL_LUGGAGE.getMobileUrl());
        sendForm.setIsAudit(MessageTypeEnum.ABNORMAL_LUGGAGE.getIsAudit());
        sendForm.setMsgDate(new Date());
        return sendForm;
    }
}