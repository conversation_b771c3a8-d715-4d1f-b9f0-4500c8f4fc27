package com.swcares.scgsi.modules.controller;

import cn.hutool.poi.excel.ExcelUtil;
import com.google.common.collect.Lists;
import com.swcares.scgsi.aop.EncryptFieldAop;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.flight.dao.AoTransRecordDao;
import com.swcares.scgsi.flight.entity.AoTransRecord;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.web.RenderResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.persistence.FlushModeType;
import javax.persistence.Query;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName：EncryBankNoCtrl
 * @Description：@TODO
 * @Copyright：© 2024 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/9/3 13:21
 * @version： v1.0
 */
@RequestMapping("/bank")
@RestController
@Slf4j
@Deprecated
public class EncryptBankNoCtrl {

    @Autowired
    private AoTransRecordDao aoTransRecordDao;

    @Autowired
    private BaseDAO baseDAO;

    @Autowired
    private EncryptBankNoCtrl encryptBankNoCtrl;

    @RequestMapping("/encrypt")
    @Deprecated
    @Transactional
    //安全加固，加密银行卡号，一次性的
    public RenderResult<?> test1() {
        log.info("【数据加密】开始处理aoTransRecord的银行卡加密--->");
        long total = aoTransRecordDao.count();
        log.info("【数据加密】开始处理aoTransRecord的银行卡加密,数据库总计有【{}】条数据--->", total);
        String sql = "select * from DP_AO_TRANS_RECORD order by id asc ";
        int page = 1;
        while(true){
            QueryResults qr = baseDAO.findBySQLPage_comm(sql, page, 5000, null, AoTransRecord.class);
            log.info("【数据加密】开始处理第【{}】页，共计【{}】条数据--->", page, qr.getList().size());
            page++;
            List<AoTransRecord> list = (List<AoTransRecord>) qr.getList();
            if(ObjectUtils.isNotEmpty(list)){
                list.forEach(e->{
                    if(!e.getApplyAccount().contains(EncryptFieldAop.KEY_PREFIX)){
                        e.setApplyAccount(AesEncryptUtil.aesEncryptScgsi(e.getApplyAccount()));
                        baseDAO.getEntityManager().unwrap(Session.class).setJdbcBatchSize(5000);
                        baseDAO.update(e);
                    }
                });
                log.info("【数据加密】开始处理第【{}】页，共计【{}】条数据--->处理完毕", page, qr.getList().size());
            }else {
                log.info("【数据加密】-->当前page【{}】, 数据库返回为null，数据处理完毕", page);

            }
        }
    }

    @RequestMapping("/encrypt3")
    @Deprecated
    //安全加固，加密银行卡号，一次性的
    public RenderResult<?> test3() {
        log.info("【数据加密】开始处理aoTransRecord的银行卡加密--->");
        long total = aoTransRecordDao.count();
        log.info("【数据加密】开始处理aoTransRecord的银行卡加密,数据库总计有【{}】条数据--->", total);
        int page = 1;

        while(true){
            boolean mark = encryptBankNoCtrl.preHandlerDate(page);
            page++;
            if(!mark || page > 50) break;;
        }

        return null;
    }

    public boolean preHandlerDate(int page){
        ExecutorService es =  Executors.newFixedThreadPool(8);
        CountDownLatch countDownLatch = new CountDownLatch(8);
        QueryResults qr = baseDAO.findBySQLPage_comm("select * from DP_AO_TRANS_RECORD where apply_account not like '%SCGSI_AES%' ", page, 5000, null, AoTransRecord.class);
        log.info("【数据加密】开始处理第【{}】页，共计【{}】条数据--->", page, qr.getList().size());
        List<AoTransRecord> list = (List<AoTransRecord>) qr.getList();
        if(ObjectUtils.isNotEmpty(list)){
            int part = list.size()/8;
            List<List<AoTransRecord>> tmp = Lists.partition(list, part > 0? part: 1);
            tmp.forEach(e->{
                es.execute(()->{
                    encryptBankNoCtrl.handlerDate(e, countDownLatch);
                });
            });
            //阻塞等多线程处理结果,阻塞
            try {
                countDownLatch.await(3, TimeUnit.MINUTES);
            } catch (InterruptedException e) {

            } finally {
                es.shutdown();
            }
            log.info("【数据加密】处理第【{}】页，共计【{}】条数据--->处理完毕", page, qr.getList().size());
            return true;
        }else {
            log.info("【数据加密】-->当前page【{}】, 数据库返回为null，数据处理完毕", page);
            return false;
        }
    }

    @Transactional
    public void handlerDate(List<AoTransRecord> list, CountDownLatch countDownLatch){
        try{
            int i = 0 ;
            for(AoTransRecord e: list){
                if(!e.getApplyAccount().contains(EncryptFieldAop.KEY_PREFIX)){
                    e.setApplyAccount(AesEncryptUtil.aesEncryptScgsi(e.getApplyAccount()));
                    baseDAO.update(e);
                }
                log.info("【数据加密】-->一共【{}】条，当前【{}】条，处理后的账号【{}】", list.size(), i, e.getApplyAccount());
                i++;
            }
        }catch (Exception e) {
            log.info("【数据加密】-->保存数据出错", e);
        }finally {
            countDownLatch.countDown();
        }
    }
}
