package com.swcares.scgsi.modules.uploadanddownload;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.swcares.scgsi.hum.employee.dao.EmployeeDao;
import com.swcares.scgsi.hum.employee.entity.Employee;
import com.swcares.scgsi.util.ExcelImportExportUtil;
import com.swcares.scgsi.web.RenderResult;

@RestController
@RequestMapping("/api/excel")
@Api(value = "/api/excel", tags = "文件上传下载接口")
public class ExcelTestController {
    @Autowired
    EmployeeDao employeeDao;

    @PostMapping("/importExcel")
    @ApiOperation(value = "导入", notes = "导入Excel")
    public RenderResult<Object> importExcel(HttpServletRequest request) throws Exception {
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
        for (int i = 0; i < files.size(); ++i) {
            List<Map<String, Object>>  result = ExcelImportExportUtil.importExcel(files.get(i),0);
            System.out.println(result);
        }
        return null;
    }

    @GetMapping("/exportExcel")
    @ApiOperation(value = "导出", notes = "导出Excel")
    public RenderResult<Object> exportExcel(HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<Serializable> ids = new ArrayList<Serializable>();
        ids.add("1c4fe4bc-1f16-4a17-922e-cb4092974939");
        ids.add("bcc5435f-8b3e-4311-bca0-71b3d088e80f");
        ids.add("2ae534de-23db-4455-a4b1-16e32028629c");
        ids.add("ac6e1ab3-3271-462f-97a9-3b6cdc2d8d29");
        ids.add("5da2476a-2411-46ab-bab5-92c30b780785");
        ids.add("cafa9e23-1721-4f6e-869a-9102aeb50e1e");
        ids.add("2381942a-b90a-4a71-b88f-dad9e17fa294");
        ids.add("81736741-c6a4-43d4-996f-d1b5f9dda55c");
        ids.add("b9b7da09-db9a-4c2a-9e83-fe9b61dfd9fb");
        ids.add("5590a63d-fabd-4058-9449-41d3ebc3c8af");
        ids.add("4d790c1b-6e83-48f3-ba46-0c17cb1df983");
        ids.add("4d05f0a8-f1f0-4451-b5ee-d2393ede1f4e");
        ids.add("97a3da84-a368-490e-80ff-b362d76724c7");
        ids.add("d7bd4b38-bf63-408f-bf93-eff298f6a151");
        ids.add("2827266b-f6de-45b4-b409-2809b1663978");
        ids.add("36c5c60f-bec6-434a-88ff-6326f97d1e9f");
        ids.add("4971d4ec-f3b0-4171-a812-918c9ee6228b");
        ids.add("4c347738-f313-481c-9082-b9b585ccda8d");
        ids.add("9609f9b9-8fa9-4ae0-bdac-f022627c8d29");
        ids.add("328ed1b5-8bfb-4bbd-bd7d-43e2fdde122d");
        ids.add("e580f6bc-523c-45f5-8177-c1f7d52a7e17");
        ids.add("a5f31155-bf90-4220-9b17-92f8e9b58655");
        ids.add("370b2e22-5c26-467f-b546-be762450f818");
        ids.add("81ac5d9f-f0ba-465c-904e-f2edcb35b071");
        ids.add("ed9cf4e2-7450-40aa-b931-505d3437f99e");
        ids.add("08e89164-905f-425c-b66e-d52b99d3a994");
        ids.add("f0e6f71b-edb3-4260-80e9-7d41071635a7");
        List<Employee> list = employeeDao.findAllById(ids);
        for(Employee employee : list){
            employee.setPhoto(null);
        }
        String[] columnNames =
                new String[] {"id", "tuNo", "tuAcct", "tuCname", "tuSex", "tuExt2",
                        "toId", "tuSpecialty", "tuOwnpertity", "tuStateReason", "tuRegion",
                        "tuTel", "tuMobile", "tuEmail", "tuOtherName", "tuIdc", "tuState",
                        "tuBdate", "tuAddre", "tuNation", "tuNational", "tuPoliticalstatus",
                        "tuWorkDate", "tuFstdate", "tuOrigin", "tuExt1", "userState",
                        "lockOutState", "lockOutDate", "passWord", "loginDate"};
        String[] keys =
                new String[] {"id", "tuNo", "tuAcct", "tuCname", "tuSex", "tuExt2",
                        "toId", "tuSpecialty", "tuOwnpertity", "tuStateReason", "tuRegion",
                        "tuTel", "tuMobile", "tuEmail", "tuOtherName", "tuIdc", "tuState",
                        "tuBdate", "tuAddre", "tuNation", "tuNational", "tuPoliticalstatus",
                        "tuWorkDate", "tuFstdate", "tuOrigin", "tuExt1", "userState",
                        "lockOutState", "lockOutDate", "passWord", "loginDate"};
        ExcelImportExportUtil.exportExcel(response, "123456", list, null, null);
        return null;
    }

}
