package com.swcares.scgsi.modules.sms;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.swcares.scgsi.sms.api.SmsService;
import com.swcares.scgsi.sms.form.SmsSendRecordForm;
import com.swcares.scgsi.sms.form.SmsTemplateForm;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * ClassName：com.swcares.scgsi.modules.sms.SmsController <br>
 * Description：短信管理的控制层 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月13日 下午8:59:05 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/sms")
@Api(value = "/api/sms", tags = "短信管理接口")
public class SmsController extends BaseController {

    @Autowired
    private SmsService smsService;

    /**
     * Title：getSmsRecodes <br>
     * Description：条件及分页查询短信信息<br>
     * author：夏阳 <br>
     * date：2020年4月13日 下午9:03:24 <br>
     * @param smsSendRecordForm
     * @return <br>
     */
    @PostMapping("/recodes")
    @ApiOperation(value = "条件及分页查询短信信息", notes = "条件及分页查询短信信息")
    public RenderResult<Map<String, Object>> getSmsRecodes(@ApiParam(name = "smsSendRecordForm",
            value = "条件查询对象", required = true) SmsSendRecordForm smsSendRecordForm) {
        return RenderResult
                .success(returnPageInfo(smsService.getSmsSendRecords(smsSendRecordForm)));
    }

    /**
     * Title：getSmsRecodes <br>
     * Description：条件及分页查询短信信息<br>
     * author：夏阳 <br>
     * date：2020年4月13日 下午9:03:24 <br>
     * @param smsSendRecordForm
     * @return <br>
     */
    @PostMapping("/export")
    @ApiOperation(value = "根据条件导出短信信息", notes = "根据条件导出短信信息")
    public void exportSmsRecodes(
            @ApiParam(name = "exportSmsRecodes", value = "根据条件导出短信信息",
                    required = true) SmsSendRecordForm smsSendRecordForm,
            HttpServletResponse response) {
        smsService.exportSmsRecoreds(smsSendRecordForm, response);
    }

    /**
     * Title：resendSms <br>
     * Description：对发送失败的短信重复发送 <br>
     * author：夏阳 <br>
     * date：2020年4月14日 下午4:18:37 <br>
     * @param ids 主键集合
     * @return <br>
     */
    @PostMapping("/resendSms")
    @ApiOperation(value = "对发送失败的短信重复发送", notes = "对发送失败的短信重复发送")
    public RenderResult<Boolean> resendSms(@ApiParam(name = "ids", value = "短信主键集合",
            required = true) @RequestParam("ids") List<Serializable> ids) {
        return RenderResult.success(smsService.resendSms(ids));
    }

    /**
     * Title：saveTemplate <br>
     * Description：保存短信模板 <br>
     * author：夏阳 <br>
     * date：2020年4月15日 下午1:56:01 <br>
     * @param smsTemplate <br>
     */
    @PostMapping("/template/save")
    @ApiOperation(value = "保存短信模板", notes = "保存短信模板")
    public RenderResult<?> saveTemplate(@ApiParam(name = "smsTemplate", value = "保存短信模板",
            required = true) SmsTemplateForm smsTemplateForm) {
        smsService.saveTemplate(smsTemplateForm);
        return RenderResult.success();
    }

    /**
     * Title：updateTemplate <br>
     * Description：修改短信模板 <br>
     * author：夏阳 <br>
     * date：2020年4月15日 下午1:57:37 <br>
     * @param smsTemplate <br>
     */
    @PostMapping("/template/update")
    @ApiOperation(value = "修改短信模板", notes = "修改短信模板")
    public RenderResult<?> updateTemplate(@ApiParam(name = "smsTemplate", value = "修改短信模板",
            required = true) SmsTemplateForm smsTemplateForm) {
        smsService.updateTemplate(smsTemplateForm);
        return RenderResult.success();
    }

    /**
     * Title：deleteTemplate <br>
     * Description：根据主键删除模板<br>
     * author：夏阳 <br>
     * date：2020年4月15日 下午3:31:18 <br>
     * @param id <br>
     */
    @PostMapping("/template/delete")
    @ApiOperation(value = "根据主键删除模板", notes = "根据主键删除模板")
    public RenderResult<?> deleteTemplate(@ApiParam(name = "ids", value = "根据主键删除模板",
            required = true) @RequestParam("ids") String[] ids) {
        smsService.deleteTemplate(ids);
        return RenderResult.success();
    }

    /**
     * Title：getSmsTemplates <br>
     * Description：分页查询模板信息 <br>
     * author：夏阳 <br>
     * date：2020年4月15日 下午3:33:13 <br>
     * @param smsTemplateForm
     * @return <br>
     */
    @PostMapping("/templates")
    @ApiOperation(value = "分页查询模板信息", notes = "分页查询模板信息")
    public RenderResult<Map<String, Object>> getSmsTemplates(@ApiParam(name = "smsTemplateForm",
            value = "分页查询模板信息", required = true) SmsTemplateForm smsTemplateForm) {
        return RenderResult.success(returnPageInfo(smsService.queryTemplates(smsTemplateForm)));
    }

    /**
     * Title：deleteTemplate <br>
     * Description：批量启用停用 0：启用  1禁用<br>
     * author：夏阳 <br>
     * date：2020年4月20日 下午1:20:40 <br>
     * @param ids <br>
     */
    @PostMapping("/template/updatestate")
    @ApiOperation(value = "批量启用停用", notes = "批量启用停用")
    public RenderResult<?> updatestate(
            @ApiParam(name = "ids", value = "主键集合",
                    required = true) @RequestParam("ids") String[] ids,
            @ApiParam(name = "state", value = "停启用状态【1：启用  0禁用】",
                    required = true) @RequestParam("state") String state) {
        smsService.updateTemplatesState(ids, state);
        return RenderResult.success();
    }

}
