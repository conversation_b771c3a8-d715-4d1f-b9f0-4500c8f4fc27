package com.swcares.scgsi.modules.overbook;

import com.swcares.scgsi.overbook.dto.OverBookConfigSaveDto;
import com.swcares.scgsi.overbook.dto.OverBookListQueryDto;
import com.swcares.scgsi.overbook.service.OverBookService;
import com.swcares.scgsi.overbook.vo.OverBookListVo;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.util.ExcelImportExportUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.modules.audit <br>
 * Description：旅客超售 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月13日 15:52 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/dp/mobile/overBook")
@Slf4j
@Api(tags = "WEB-旅客超售")
public class OverBookController extends BaseController {

    @Resource
    OverBookService overBookService;

    /**
     * Title：getOverBookWebList <br>
     * Description： WEB-旅客超售列表 -  查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:08 <br>
     * @param  overBookListQueryDto
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("getOverBookWebList")
    @ApiOperation(value = "旅客超售列表")
    public RenderResult getOverBookWebList(OverBookListQueryDto overBookListQueryDto) {
        return RenderResult.success(returnPageInfo(overBookService.overBookWebList(overBookListQueryDto)));
    }
    /**
     * Title：getOverBookWebDetails <br>
     * Description： WEB-旅客超售列表 - 详情查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:08 <br>
     * @param  overId
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("getOverBookWebDetails")
    @ApiOperation(value = "详情查询")
    public RenderResult getOverBookWebDetails(String overId) {
        return RenderResult.success(overBookService.overBookWebDetails(overId));
    }

    /**
     * Title：saveOverBookConfig <br>
     * Description：WEB旅客超售-规则保存<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:15 <br>
     * @param overBookConfigSave
     * @return com.swcares.scgsi.web.RenderResult
     */
    @PostMapping("saveOverBookConfig")
    @ApiOperation(value = "保存")
    public RenderResult saveOverBookConfig(@RequestBody OverBookConfigSaveDto overBookConfigSave) {
        log.info("web超售-规则保存【前端请求参数】"+overBookConfigSave.toString());
        try {
            overBookService.saveOverBookConfig(overBookConfigSave.getTimeDifferenceInfo(),
                    overBookConfigSave.getLowestStandInfo(),overBookConfigSave.getRefundInfo());
            return RenderResult.success();
        } catch (Exception e) {
            return RenderResult.fail();
        }
    }

    /**
     * Title：getConfigInfo <br>
     * Description：WEB旅客超售-规则查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:15 <br>
     */
    @GetMapping("getWebConfigInfo")
    @ApiOperation(value = "规则查询详情")
    public RenderResult findOverBookConfig() {
        return RenderResult.success(overBookService.findOverBookConfig());
    }

    /**
     * Title：downloadExcel <br>
     * Description：web-超售列表导出<br>
     * author：傅欣荣 <br>
     * date：2020/3/25 9:21 <br>
     * @param  overBookListQueryDto, response
     */
    @GetMapping("downloadExcel")
    @ApiOperation(value = "超售列表导出")
    public void downloadExcel(OverBookListQueryDto overBookListQueryDto,HttpServletResponse response) {
        try {
            List<OverBookListVo> resultList = overBookService.excelOverBookList(overBookListQueryDto);
            String[] columnNames = { "状态","类型","超售单号"," 航班号","航班日期","航班计划起飞"," 旅客姓名", "票号",
                    "航段"," 赔偿总金额","赔偿金额","客票价","领取状态","服务单编号","服务航站"," 改签航班号","改签航班日期",
                    "改签航班计划起飞","创建人","审核人姓名","发放人"};
            String[] keys = { "status", "type", "overId", "flightNo", "flightDate", "oldPlanDate", "paxName", "tktNo", "segment", "sumMoney",
                    "payMoney", "priceSpread", "receiveStatus", "orderId", "serviceCity" , "overBookFlightNo" , "overBookFlightDate",
                    "planDate","createUser","auditor","grantUser"};
            String fileName = "超售管理列表" + DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMMSS);
            ExcelImportExportUtil.exportExcel(response, fileName, resultList, columnNames, keys);
        } catch (Exception e) {
           log.error("web-超售列表导出异常",e);
        }
    }


    /**
     * Title：getConfigInfo <br>
     * Description：web - 旅客超售-规则查询<br>
     * author：傅欣荣 <br>
     * date：2020/3/20 14:15 <br>
     * @param
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("getConfigInfo")
    @ApiOperation(value = "规则查询")
    public RenderResult getConfigInfo() {
        return RenderResult.success(overBookService.getConfigInfo());
    }



}
