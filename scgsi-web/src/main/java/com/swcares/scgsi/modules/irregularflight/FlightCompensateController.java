package com.swcares.scgsi.modules.irregularflight;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.form.ContentTraceForm;
import com.swcares.scgsi.common.model.form.FlightInfoListForm;
import com.swcares.scgsi.dict.service.impl.SysDictDataServiceImpl;
import com.swcares.scgsi.custom.LogOperation;
import com.swcares.scgsi.entity.FocFlightInfo;
import com.swcares.scgsi.flight.dao.impl.FlightCompensateDaoImpl;
import com.swcares.scgsi.flight.dto.FlightQueryParamDto;
import com.swcares.scgsi.flight.dto.OrderInfoParamDto;
import com.swcares.scgsi.flight.dto.PaxInfoQueryParamDto;
import com.swcares.scgsi.flight.dto.PaxParseDto;
import com.swcares.scgsi.flight.enums.PaxStatusEnum;
import com.swcares.scgsi.flight.service.FlightCompensateService;
import com.swcares.scgsi.flight.vo.PaxInfoParseVo;
import com.swcares.scgsi.flight.vo.PaxInfoVo;
import com.swcares.scgsi.service.FlightInfoService;
import com.swcares.scgsi.service.TraceService;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.kit.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.scgsi.flight.controller <br>
 * Description：不正常航班赔偿 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月03日 19:05 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/dp/sys")
@Api(tags = "web不正常航班")
@Slf4j
public class FlightCompensateController extends BaseController {

    private final static String CROSS_DAY_FLIGHT = "CROSS_DAY_FLIGHT";

    @Resource
    private FlightCompensateService flightCompensateService;

    @Resource
    private FlightInfoService flightInfoService;

    @Resource
    private FlightCompensateDaoImpl flightCompensateDao;

    @Resource
    private TraceService traceService;

    @Autowired
    private SysDictDataServiceImpl sysDictDataServiceImpl;

    @GetMapping("getSegment")
    @ApiOperation(value = "通过航班号航班日期获取航段")
    public RenderResult getSegment(String flightNo, String flightDate) {
        flightDate = flightDate.replaceAll("-", "/");
        return RenderResult.success(flightInfoService.getFlightSegment(flightNo, flightDate));
    }

    /**
     * Title：getPaxInfo <br>
     * Description： 航班四要素获取旅客信息<br>
     * author：王建文 <br>
     * date：2020-3-23 13:33 <br>
     *
     * @param flightNo      航班号
     * @param flightDate    航班日期
     * @param choiceSegment 航段
     */
    @GetMapping("getPaxInfo")
    @ApiOperation(value = "根据航班航班号所选航段获取旅客信息")
    public RenderResult getPaxInfo(String flightNo, String flightDate, String choiceSegment,
            ContentTraceForm form,String selectSegment) {
        Map<String, Object> returnFlightPaxInfo = flightCompensateService.getReturnFlightPaxInfo(flightNo, flightDate, choiceSegment, form, selectSegment);
        return RenderResult.success(returnFlightPaxInfo);
    }

    /**
     * Title：getFlightInfo <br>
     * Description： 获取航班信息<br>
     * author：王建文 <br>
     * date：2020-3-23 14:14 <br>
     *
     * @param flightNo      航班号
     * @param flightDate    航班日期YYYY/MM/DD
     * @param choiceSegment 航段
     */
    @GetMapping("getFlightInfo")
    @ApiOperation(value = "通过航班号航班日期获取航段")
    public RenderResult getFlightInfo(String flightNo, String flightDate, String choiceSegment) {
        List<FocFlightInfo> showList = flightCompensateService.getFocFlightInfos(flightNo, flightDate, choiceSegment);
        return RenderResult.success(showList);
    }


    /**
     * Title：saveOrderInfo <br>
     * Description： 保存赔付单<br>
     * author：王建文 <br>
     * date：2020-3-3 19:08 <br>
     *
     * @param orderInfoParamDto
     */
    @PostMapping("saveOrderInfo")
    @ApiOperation(value = "保存更新赔付单信息")
    public RenderResult saveOrderInfo(@RequestBody OrderInfoParamDto orderInfoParamDto) {
        log.info("创建赔偿单的时候前端参数为【{}】", JSONUtil.toJsonStr(orderInfoParamDto));
        try {
            String orderId = flightCompensateService.saveOrderInfo(orderInfoParamDto);
            return RenderResult.success(orderId);
        } catch (Exception e) {
            log.error("创建赔偿单的时候异常", e);
            return new RenderResult("1", e.getMessage());
        }
    }

    /**
     * Title：getServiceOrderInfo <br>
     * Description： 补偿单管理列表<br>
     * author：王建文 <br>
     * date：2020-3-4 16:36 <br>
     *
     * @param flightQueryParamDto
     */
    @GetMapping("getServiceOrderInfo")
    @ApiOperation(value = "赔偿单管理列表")
    public RenderResult getServiceOrderInfo(FlightQueryParamDto flightQueryParamDto) {
        QueryResults queryResults = flightCompensateService.getFlightInfoPage(flightQueryParamDto);
        return RenderResult.success(returnPageInfo(queryResults));
    }

    /**
     * Title：getOrderInfoByFlightINoAndFlightDate <br>
     * Description： 根据航班号航班日期查询所有的赔付单<br>
     * author：王建文 <br>
     * date：2020-3-5 14:43 <br>
     *
     * @param flightNo   航班号
     * @param flightDate 航班日期
     */
    @GetMapping("getOrderInfoByFlightINoAndFlightDate")
    @ApiOperation(value = "根据航班号航班日期查询所有的赔付单")
    public RenderResult getOrderInfoByFlightINoAndFlightDate(String flightNo, String flightDate,String payType,String status) {
        return RenderResult.success(flightCompensateService.getOrderInfoByFlightINoAndFlightDate(
                flightNo, flightDate,payType,status));
    }

    /**
     * Title：getDetailOrderInfoByOrderId <br>
     * Description：根据赔付单id获取赔付详情 <br>
     * author：王建文 <br>
     * date：2020-3-6 11:39 <br>
     *
     * @param orderId 赔付单id
     */
    @GetMapping("getDetailOrderInfoByOrderId")
    @ApiOperation(value = "根据赔付单id获取赔付详情")
    public RenderResult getDetailOrderInfoByOrderId(String orderId) {
        return RenderResult.success(flightCompensateService.getOrderDetailInfoByOrderId(orderId));
    }

    /**
     * Title：getPaxOrderInfo <br>
     * Description： 获取旅客赔偿次数详情<br>
     * author：王建文 <br>
     * date：2020-3-23 17:28 <br>
     *
     * @param paxId      旅客id
     * @param flightNo   航班号
     * @param flightDate 航班日期
     */
    @GetMapping("getPaxOrderInfo")
    @ApiOperation(value = "获取旅客赔偿次数详情")
    public RenderResult getPaxOrderInfo(String paxId, String flightNo, String flightDate) {
        return RenderResult.success(flightCompensateService.getPaxOrderInfo(paxId, flightNo,
                flightDate));
    }

    /**
     * Title：getPaxInfoByOrderId <br>
     * Description：赔偿单管理旅客信息查询<br>
     * author：王建文 <br>
     * date：2020-3-6 16:20 <br>
     *
     * @param paxInfoQueryParamDto
     */
    @GetMapping("getPaxInfoByOrderId")
    @ApiOperation(value = "赔偿单管理旅客信息查询")
    public RenderResult getPaxInfoByOrderId(PaxInfoQueryParamDto paxInfoQueryParamDto) {
        List<PaxInfoVo> list = flightCompensateDao.getPaxInfoList(paxInfoQueryParamDto);
        if(ObjectUtils.isNotEmpty(list)){
            list.forEach(e->{
                e.setTelephone(AesEncryptUtil.aesEncrypt(AesEncryptUtil.AES_PHONE_KEY, e.getTelephone()));
            });
        }
        return RenderResult.success(list);
    }

    /**
     * Title：updateOrderStatus <br>
     * Description：赔偿单管理确认发放，关闭操作<br>
     * author：王建文 <br>
     * date：2020-3-10 11:43 <br>
     *
     * @param orderId 赔付单id
     * @param status  状态
     */
    @PostMapping("updateOrderStatus")
    @ApiOperation(value = "赔偿单管理确认发放，关闭操作")
    public RenderResult updateOrderStatus(String orderId, String status) {
        try {
            flightCompensateService.updateOrderStatus(orderId, status);
            return RenderResult.success();
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }

    /**
     * Title：updatePaxStatus <br>
     * Description： 激活禁领旅客领取资格<br>
     * author：王建文 <br>
     * date：2020-3-10 13:56 <br>
     *
     * @param paxIds 旅客ids
     * @param status 状态
     */
    @PostMapping("updatePaxStatus")
    @ApiOperation(value = "旅客冻结，激活操作")
    @LogOperation(operationUrl="/api/dp/sys/updatePaxStatus",remark="旅客冻结，激活操作")
    public RenderResult updatePaxStatus(String paxIds, String status, String orderId) {
        try {
            String[] paxids1 = paxIds.split(",");
            flightCompensateService.updatePaxStatus(paxids1, status, orderId);
            return RenderResult.success();
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }

    // 获取编辑权限
    @GetMapping("getEditPermission")
    @ApiOperation(value = "获取编辑权限")
    public RenderResult getEditPermission(String orderId) {
        return RenderResult.success(flightCompensateService.getEditPermission(orderId));
    }
    /**
     * Title：getSelectCityInfo <br>
     * Description： 获取航站下拉选项<br>
     * author：王建文 <br>
     * date：2020-3-25 14:23 <br>
     */
    @GetMapping("getSelectCityInfo")
    @ApiOperation(value = "获取航站下拉选项")
    public RenderResult getSelectCityInfo() {
        return RenderResult.success(flightCompensateDao.getSelectCityInfo());
    }

}
