package com.swcares.scgsi.modules.pkg;

import com.alibaba.fastjson.JSON;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.common.model.form.ContentTraceForm;
import com.swcares.scgsi.fileuploadanddownload.UploadAndDownload;
import com.swcares.scgsi.flight.dto.PaxParseDto;
import com.swcares.scgsi.flight.vo.PaxInfoParseVo;
import com.swcares.scgsi.pkg.dao.impl.PkgInfoServiceDaoImpl;
import com.swcares.scgsi.pkg.dto.PkgInfoDto;
import com.swcares.scgsi.pkg.dto.PkgInfoQueryParamDto;
import com.swcares.scgsi.pkg.service.PkgInfoService;
import com.swcares.scgsi.pkg.vo.PkgExportInfoVo;
import com.swcares.scgsi.service.TraceService;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.util.ExcelImportExportUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.scgsi.modules.pkg <br>
 * Description：异常行李 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月22日 9:32 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/dp/pkg")
@Api(tags = "web异常行李")
public class PkgInfoController extends BaseController {
    private static final String FILE_NAME = "pkgInfoTemplate.xlsx";

    @Resource
    private PkgInfoService pkgInfoService;

    @Resource
    private TraceService traceService;

    @Resource
    private PkgInfoServiceDaoImpl pkgInfoServiceDao;

    @Autowired
    UploadAndDownload uploadAndDownload;

    /**
     * Title：pkgMatch <br>
     * Description： 异常行李H5行李匹配<br>
     * author：王建文 <br>
     * date：2020-3-22 11:19 <br>
     *
     * @param queryParamDto 参数接收
     */
    @GetMapping("pkgMatch")
    @ApiOperation(value = "异常行李H5行李匹配")
    public RenderResult pkgMatch(PkgInfoQueryParamDto queryParamDto) {
        return RenderResult.success(pkgInfoService.getPkgInfo(queryParamDto));
    }

    /**
     * Title：detailInfo <br>
     * Description： 行李异常详情<br>
     * author：王建文 <br>
     * date：2020-3-22 15:40 <br>
     *
     * @param accidentId 事故单号
     */
    @GetMapping("detailInfo")
    @ApiOperation(value = "行李异常详情")
    public RenderResult detailInfo(String accidentId) {
        return RenderResult.success(pkgInfoService.getPkgDetailInfo(accidentId));
    }

    /**
     * Title：pkgCase <br>
     * Description： 事故单结案<br>
     * author：王建文 <br>
     * date：2020-3-22 15:48 <br>
     *
     * @param accidentId 事故单号
     */
    @PostMapping("pkgCase")
    @ApiOperation(value = "事故单结案")
    public RenderResult pkgCase(String accidentId) {
        pkgInfoService.pkgCase(accidentId.split(","));
        return RenderResult.success();
    }

    /**
     * Title：getPkgInfoPage <br>
     * Description： 分页查询异常行李列表<br>
     * author：王建文 <br>
     * date：2020-3-22 17:31 <br>
     *
     * @param queryParamDto 查询参数
     */
    @GetMapping("getPkgInfoPage")
    @ApiOperation(value = "分页查询异常行李列表")
    public RenderResult getPkgInfoPage(PkgInfoQueryParamDto queryParamDto) {
        QueryResults queryResults = pkgInfoService.getPkgInfoPage(queryParamDto);
        return RenderResult.success(returnPageInfo(queryResults));
    }

    /**
     * Title：importDataByExcel <br>
     * Description： 异常行李导入<br>
     * author：王建文 <br>
     * date：2020-4-20 13:34 <br>
     *
     * @param
     */
    @PostMapping("/importDataByExcel")
    @ApiOperation(value = "导入", notes = "异常行李数据导入")
    public RenderResult<Object> importDataByExcel(HttpServletRequest request) throws Exception {
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
        Map<String, Object> resultData = new HashMap<>();
        int totalExitCount = 0;
        String totalExitContent = "";
        int totalFailureCount = 0;
        String totalFailureContent = "";
        int totalFailParamCount = 0;
        String totalFailParamContent = "";
        if (files.size() > 0) {
            //解析破损行李
            List<Map<String, Object>> result = ExcelImportExportUtil.importExcel(files.get(0), 0);
            for (Map<String, Object> map : result) {
                if (null == map.get("航班号") || null == map.get("航班日期")) {
                    return new RenderResult("1", "模板导入有误!", "模板导入有误");
                }
            }
            if (result.size() > 0) {
                Map<String, Object> resultMap = parseData(result, "1");
                int exitCount = Integer.valueOf(resultMap.get("exitCount").toString());
                if (exitCount > 0) {
                    totalExitCount += exitCount;
                    totalExitContent += resultMap.get("exitContent").toString();
                }
                int failureCount = Integer.valueOf(resultMap.get("failureCount").toString());
                if (failureCount > 0) {
                    totalFailureCount += failureCount;
                    totalFailureContent += resultMap.get("failureContent").toString();
                }
                int failParamCount = Integer.valueOf(resultMap.get("failParamCount").toString());
                if (failParamCount > 0) {
                    totalFailParamCount += failParamCount;
                    totalFailParamContent += resultMap.get("failParamContent").toString();
                }
            }
            //解析少收行李
            List<Map<String, Object>> littlePkgInfo = ExcelImportExportUtil.importExcel(files.get(0), 1);
            if (littlePkgInfo.size() > 0) {
                Map<String, Object> resultMap = parseData(littlePkgInfo, "2");
                int exitCount = Integer.valueOf(resultMap.get("exitCount").toString());
                if (exitCount > 0) {
                    totalExitCount += exitCount;
                    totalExitContent += resultMap.get("exitContent").toString();
                }
                int failureCount = Integer.valueOf(resultMap.get("failureCount").toString());
                if (failureCount > 0) {
                    totalFailureCount += failureCount;
                    totalFailureContent += resultMap.get("failureContent").toString();
                }
                int failParamCount = Integer.valueOf(resultMap.get("failParamCount").toString());
                if (failParamCount > 0) {
                    totalFailParamCount += failParamCount;
                    totalFailParamContent += resultMap.get("failParamContent").toString();
                }
            }
            List<Map<String, Object>> morePkgInfo = ExcelImportExportUtil.importExcel(files.get(0), 2);
            if (morePkgInfo.size() > 0) {
                Map<String, Object> resultMap = parseData(morePkgInfo, "3");
                int exitCount = Integer.valueOf(resultMap.get("exitCount").toString());
                if (exitCount > 0) {
                    totalExitCount += exitCount;
                    totalExitContent += resultMap.get("exitContent").toString();
                }
                int failureCount = Integer.valueOf(resultMap.get("failureCount").toString());
                if (failureCount > 0) {
                    totalFailureCount += failureCount;
                    totalFailureContent += resultMap.get("failureContent").toString();
                }
                int failParamCount = Integer.valueOf(resultMap.get("failParamCount").toString());
                if (failParamCount > 0) {
                    totalFailParamCount += failParamCount;
                    totalFailParamContent += resultMap.get("failParamContent").toString();
                }
            }
            //处理返回值
            resultData.put("totalExitCount", totalExitCount);
            resultData.put("totalExitContent", totalExitContent);
            resultData.put("totalFailureCount", totalFailureCount);
            resultData.put("totalFailureContent", totalFailureContent);
            resultData.put("totalFailParamCount", totalFailParamCount);
            resultData.put("totalFailParamContent", totalFailParamContent);
        }
        return RenderResult.success(resultData);
    }

    /**
     * Title：parseData <br>
     * Description： 解析异常行李数据<br>
     * author：王建文 <br>
     * date：2020-4-20 14:49 <br>
     *
     * @param data         excel表格数据
     * @param accidentType 1破损行李,2少收行李，3多收行李，4内件缺失行李
     * @return
     */
    private Map<String, Object> parseData(List<Map<String, Object>> data, String accidentType) throws Exception {
        Map<String, Object> resultMap = new HashMap<>();
        //标记重复数据
        int exitCount = 0;
        //记录重复数据
        String exitContent = "";
        //失效数据
        int failureCount = 0;
        //失效数据内容
        String failureContent = "";
        //参数填写不正确数据
        int failParamCount = 0;
        //参数填写不正确数据内容
        String failParamContent = "";
        for (Map<String, Object> map : data) {
            String flightNo = map.get("航班号") == null ? "" : map.get("航班号").toString().toUpperCase();
            String flightDate = map.get("航班日期") == null ? "" : map.get("航班日期").toString();
            flightDate = DateUtils.parseStrToStr(flightDate, DateUtils.YYYY_MM_DD);
            String keySearch = "";
            if ("3".equals(accidentType)) {
                //多收行李
                keySearch = map.get("行李编号") == null ? "" : map.get("行李编号").toString();
            } else {
                keySearch = map.get("证件号/票号") == null ? "" : map.get("证件号/票号").toString();
            }
            PaxInfoParseVo paxInfoParseVo = null;
            if (StringUtils.isNotBlank(flightNo) && StringUtils.isNotBlank(flightDate) && StringUtils.isNotBlank(keySearch)) {
                paxInfoParseVo = authPaxInfo(flightNo, flightDate, keySearch);
            }
            //验证导入数据是否有效
            if (null == paxInfoParseVo) {
                failureCount++;
                failureContent += flightNo + "," + flightDate + "," + keySearch + ";";
            } else {
                if ("1".equals(accidentType)) {
                    boolean authPoSunPkgParamFlag = authPoSunPkgParam(map);
                    if (authPoSunPkgParamFlag) {
                        //参数必填项为空
                        failParamCount++;
                        failParamContent += flightNo + "," + flightDate + "," + keySearch + ";";
                    } else {
                        //判断是否已经创建了该异常行李数据
                        String pkgNo = map.get("行李编号").toString();
                        boolean flag = pkgInfoServiceDao.judgeIsExit(pkgNo, accidentType, false);
                        if (flag) {
                            exitCount++;
                            exitContent += flightNo + "," + flightDate + "," + keySearch + ";";
                        } else {
                            PkgInfoDto pkgInfoDto = setPoSunPkgInfo(map);
                            PkgInfoDto pkgInfo = setPkgCommonInfo(pkgInfoDto, map, paxInfoParseVo, pkgNo, accidentType);
                            pkgInfoService.savePkgInfoByExcel(pkgInfo, paxInfoParseVo);
                        }
                    }
                }
                if ("2".equals(accidentType)) {
                    boolean authLittlePkgParamFlag = authLittlePkgParam(map);
                    if (authLittlePkgParamFlag) {
                        //参数必填项为空
                        failParamCount++;
                        failParamContent += flightNo + "," + flightDate + "," + keySearch + ";";
                    } else {
                        accidentType = map.get("异常类型(2少收4内件缺失)").toString();
                        //判断是否已经创建了该异常行李数据
                        String pkgNo = map.get("行李编号").toString();
                        boolean flag = pkgInfoServiceDao.judgeIsExit(pkgNo, accidentType, false);
                        if (flag) {
                            exitCount++;
                            exitContent += flightNo + "," + flightDate + "," + keySearch + ";";
                        } else {
                            PkgInfoDto pkgInfoDto = setLittlePkgInfo(map);
                            PkgInfoDto pkgInfo = setPkgCommonInfo(pkgInfoDto, map, paxInfoParseVo, pkgNo, accidentType);
                            pkgInfoService.savePkgInfoByExcel(pkgInfo, paxInfoParseVo);
                        }
                    }
                }
                if ("3".equals(accidentType)) {
                    boolean authMorePkgParamFlag = authMorePkgParam(map);
                    if (authMorePkgParamFlag) {
                        //参数必填项为空
                        failParamCount++;
                        failParamContent += flightNo + "," + flightDate + "," + keySearch + ";";
                    } else {
                        //判断是否已经创建了该异常行李数据
                        String pkgNo = map.get("行李编号").toString();
                        boolean flag = pkgInfoServiceDao.judgeIsExit(pkgNo, accidentType, false);
                        if (flag) {
                            exitCount++;
                            exitContent += flightNo + "," + flightDate + "," + keySearch + ";";
                        } else {
                            PkgInfoDto pkgInfoDto = setMorePkgInfo(map);
                            PkgInfoDto pkgInfo = setPkgCommonInfo(pkgInfoDto, map, paxInfoParseVo, pkgNo, accidentType);
                            pkgInfoService.savePkgInfoByExcel(pkgInfo, paxInfoParseVo);
                        }
                    }
                }
            }
            //存放数据
            resultMap.put("exitCount", exitCount);
            resultMap.put("exitContent", exitContent);
            resultMap.put("failureCount", failureCount);
            resultMap.put("failureContent", failureContent);
            resultMap.put("failParamCount", failParamCount);
            resultMap.put("failParamContent", failParamContent);
        }
        return resultMap;
    }

    /**
     * Title：authPaxInfo <br>
     * Description： 判断导入旅客数据是否有效<br>
     * author：王建文 <br>
     * date：2020-4-20 15:13 <br>
     *
     * @param
     * @return
     */
    private PaxInfoParseVo authPaxInfo(String flightNo, String flightDate, String keySearch) {
        ContentTraceForm form = new ContentTraceForm();
        form.setFlightNum(flightNo);
        form.setFlightDate(flightDate.replaceAll("-", "/"));
        form.setKeySearch(keySearch);
        Object object = traceService.getPsgListByFilght(form);
        PaxParseDto parseObject = JSON.parseObject(object.toString(), PaxParseDto.class);
        List<PaxInfoParseVo> dataList = parseObject.getDataList();
        if (dataList.size() > 0) {
            return dataList.get(0);
        }
        return null;
    }

    /**
     * Title：setPoSunPkgInfo <br>
     * Description：设置破损行李信息<br>
     * author：王建文 <br>
     * date：2020-4-20 15:56 <br>
     *
     * @param
     * @return
     */
    private PkgInfoDto setPoSunPkgInfo(Map<String, Object> map) {
        PkgInfoDto pkgInfoDto = new PkgInfoDto();
        String telephone = map.get("联系电话").toString();
        pkgInfoDto.setTelephone(telephone);
        String pkgSegment = map.get("行李航段").toString();
        pkgInfoDto.setPkgSegment(pkgSegment);
        String pkgType = map.get("行李类型").toString();
        pkgInfoDto.setPkgType(pkgType);
        String overWeightTktNo = map.get("逾重行李票号") == null ? "" : map.get("逾重行李票号").toString();
        pkgInfoDto.setOverWeightTktNo(overWeightTktNo);
        String damagePart = map.get("破损部位").toString();
        pkgInfoDto.setDamagePart(damagePart);
        String damagePartRemark = map.get("破损部位备注") == null ? "" : map.get("破损部位备注").toString();
        pkgInfoDto.setDamagePartRemark(damagePartRemark);
        String damageType = map.get("破损类型").toString();
        pkgInfoDto.setDamageType(damageType);
        String damageTypeRemark = map.get("破损类型备注") == null ? "" : map.get("破损类型备注").toString();
        pkgInfoDto.setDamageTypeRemark(damageTypeRemark);
        String damageLevel = map.get("破损程度").toString();
        pkgInfoDto.setDamageLevel(damageLevel);
        String pkgBrand = map.get("行李品牌") == null ? "" : map.get("行李品牌").toString();
        pkgInfoDto.setPkgBrand(pkgBrand);
        String remark = map.get("备注说明") == null ? "" : map.get("备注说明").toString();
        pkgInfoDto.setRemark(remark);
        String pkgSize = map.get("行李箱尺寸").toString();
        pkgInfoDto.setPkgSize(pkgSize);
        String payMoney = map.get("赔偿金额") == null ? "" : map.get("赔偿金额").toString();
        pkgInfoDto.setPayMoney(payMoney);
        return pkgInfoDto;
    }

    /**
     * Title：authPoSunPkgParam <br>
     * Description：判断破损行李必填参数值是否为空<br>
     * author：王建文 <br>
     * date：2020-4-21 9:54 <br>
     *
     * @param map 参数值
     * @return
     */
    private boolean authPoSunPkgParam(Map<String, Object> map) {
        if (null == map.get("行李编号") || null == map.get("联系电话") || null == map.get("行李航段") || null == map.get("行李类型") || null == map.get("破损部位") ||
                null == map.get("破损类型") || null == map.get("破损程度") || null == map.get("行李箱尺寸") || null == map.get("服务航站")) {
            return true;
        }
        return false;
    }

    /**
     * Title：authLittlePkgParam <br>
     * Description：少收行李参数必填项验证<br>
     * author：王建文 <br>
     * date：2020-4-21 10:38 <br>
     *
     * @param map 少收参数
     * @return
     */
    private boolean authLittlePkgParam(Map<String, Object> map) {
        if (null == map.get("行李编号") || null == map.get("航程及经停站") || null == map.get("行李航段") || null == map.get("行李类型") || null == map.get("丢失重量") ||
                null == map.get("少收类型") || null == map.get("事故提醒") || null == map.get("服务航站") || null == map.get("异常类型(2少收4内件缺失)")) {
            return true;
        }
        return false;
    }

    private PkgInfoDto setLittlePkgInfo(Map<String, Object> map) {
        PkgInfoDto pkgInfoDto = new PkgInfoDto();
        String pkgSegmentStop = map.get("航程及经停站").toString();
        pkgInfoDto.setPkgSegmentStop(pkgSegmentStop);
        String pkgSegment = map.get("行李航段").toString();
        pkgInfoDto.setPkgSegment(pkgSegment);
        String pkgType = map.get("行李类型").toString();
        pkgInfoDto.setPkgType(pkgType);
        String overWeightTktNo = map.get("逾重行李票号") == null ? "" : map.get("逾重行李票号").toString();
        pkgInfoDto.setOverWeightTktNo(overWeightTktNo);
        String littlePkgWeight = map.get("丢失重量").toString();
        pkgInfoDto.setLittlePkgWeight(littlePkgWeight);
        String littlePkgType = map.get("少收类型").toString();
        pkgInfoDto.setLittlePkgType(littlePkgType);
        String remindDay = map.get("事故提醒").toString();
        pkgInfoDto.setRemindDay(remindDay);
        String remark = map.get("备注说明") == null ? "" : map.get("备注说明").toString();
        pkgInfoDto.setRemark(remark);
        String payMoney = map.get("赔偿金额") == null ? "" : map.get("赔偿金额").toString();
        pkgInfoDto.setPayMoney(payMoney);
        return pkgInfoDto;
    }

    /**
     * Title：authMorePkgParam <br>
     * Description： 多收行李必填参数验证<br>
     * author：王建文 <br>
     * date：2020-4-21 13:58 <br>
     *
     * @param map 参数值
     * @return
     */
    private boolean authMorePkgParam(Map<String, Object> map) {
        if (null == map.get("始发航站") || null == map.get("行李航班日期") || null == map.get("行李航班号") || null == map.get("行李类型") || null == map.get("服务航站")) {
            return true;
        }
        return false;
    }

    /**
     * Title：setMorePkgInfo <br>
     * Description： 多收行李<br>
     * author：王建文 <br>
     * date：2020-4-21 14:02 <br>
     *
     * @param map 参数值
     * @return
     */
    private PkgInfoDto setMorePkgInfo(Map<String, Object> map) {
        PkgInfoDto pkgInfoDto = new PkgInfoDto();
        String pkgFlightNo = map.get("行李航班号").toString();
        pkgInfoDto.setPkgFlightNo(pkgFlightNo);
        String pkgSegment = map.get("始发航站").toString();
        pkgInfoDto.setPkgSegment(pkgSegment);
        String pkgType = map.get("行李类型").toString();
        pkgInfoDto.setPkgType(pkgType);
        String pkgFlightDate = map.get("行李航班日期").toString();
        pkgInfoDto.setPkgFlightDate(DateUtils.parseStrToStr(pkgFlightDate, DateUtils.YYYY_MM_DD));
        return pkgInfoDto;
    }

    /**
     * Title：setPkgCommonInfo <br>
     * Description： 设置异常行李公共信息<br>
     * author：王建文 <br>
     * date：2020-4-21 10:52 <br>
     *
     * @param
     * @return
     */
    public PkgInfoDto setPkgCommonInfo(PkgInfoDto pkgInfoDto, Map<String, Object> map, PaxInfoParseVo paxInfoParseVo, String pkgNo,
            String accidentType) {
        pkgInfoDto.setPaxId(paxInfoParseVo.getIdx());
        if (!"1".equals(accidentType)) {
            pkgInfoDto.setTelephone(paxInfoParseVo.getPhone());
        }
        pkgInfoDto.setPkgNo(pkgNo);
        pkgInfoDto.setAccidentType(accidentType);
        String serviceCity = map.get("服务航站").toString();
        pkgInfoDto.setServiceCity(serviceCity);
        return pkgInfoDto;
    }

    /**
     * Title：exportData <br>
     * Description： 异常行李导出<br>
     * author：王建文 <br>
     * date：2020-4-21 16:58 <br>
     *
     * @param queryParamDto 查询参数
     * @return
     */
    @GetMapping("exportData")
    public void exportData(PkgInfoQueryParamDto queryParamDto, HttpServletResponse response) throws Exception {
        List<PkgExportInfoVo> pkgPageInfoVoList = pkgInfoService.getExportPkgInfo(queryParamDto);
        String[] columnNames = { "状态", "类型", "行李事故单号", "航班号", "航班日期", "航段", "服务航站", "旅客姓名", "行李号","证件号", "赔偿总额", "赔付单数量", "尺寸", "审核人", "数据导入", "发放人", "申请时间" };
        String[] keys = { "status", "accidentType", "accidentId", "flightNo", "flightDate", "segment", "serviceCity", "paxName", "pkgNo","idNo", "totalPay",
                "payCount", "pkgSize","auditor", "dataType", "grantUser", "createTime" };
        String fileName = "异常行李报表" + DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMMSS);
        ExcelImportExportUtil.exportExcel(response, fileName, pkgPageInfoVoList, columnNames, keys);
    }

    /**
     * Title：downLoadExcel <br>
     * Description：异常行李导入模板下载<br>
     * author：王建文 <br>
     * date：2020-4-22 13:59 <br>
     *
     * @param
     * @return
     */
    @GetMapping("downLoadExcel")
    public void downLoadExcel(HttpServletResponse response) throws Exception {
        // 配置文件下载
        response.setContentType("multipart/form-data");
        // 下载文件能正常显示中文
        response.addHeader(
                "Content-Disposition",
                "attachment;fileName=" + new String(FILE_NAME.getBytes("UTF-8"), "iso-8859-1"));
        uploadAndDownload.ftpDownload(response.getOutputStream(), FILE_NAME, uploadAndDownload.getRemotePath());
    }

}