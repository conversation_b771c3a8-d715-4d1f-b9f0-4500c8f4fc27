package com.swcares.scgsi.modules.user;


import cn.hutool.core.util.ObjectUtil;
import com.swcares.scgsi.common.model.view.LoginView;
import com.swcares.scgsi.user.common.model.form.ForgetPasswordForm;
import com.swcares.scgsi.user.common.model.form.UpdatePasswordForm;
import com.swcares.scgsi.user.service.KaptchaService;
import com.swcares.scgsi.user.service.UserService;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * ClassName：com.swcares.scgsi.login.controller.LoginController <br>
 * Description：消息系统接口 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月12日 上午10:00:04 <br>
 * @version v1.0 <br>
 */
@Api(tags = "用户登录相关接口")
@RestController
@RequestMapping("/api/login")
public class LoginController {
    @Autowired
    private UserService userService;

    @Autowired
    private KaptchaService kaptchaService;

    @Value("${SSO.redirectUrl}")
    private String SSORedirectUrl;

    /**
     * Title：tokenLoginExternal <br>
     * Description：给山东航统一平台使用的函数<br>
     * author：王磊 <br>
     * date：2020年3月4日 下午04:59:30 <br>
     * @param token 传入token
     * @return <br>
     * @throws Exception 
     */
    @ApiOperation(value = "web单点登录")
    @PostMapping("/tokenLoginExternal")
    public ModelAndView tokenLoginExternal(
            @ApiParam(name = "ICMS_SSO_USER_TOKEN", value = "token值", required = true) @RequestParam(
                    name = "ICMS_SSO_USER_TOKEN") String token) throws Exception {
        Assert.notNull(token, "token值不能为空!");
        String randomChar = userService.cacheToken(token);
        ModelAndView mv = new ModelAndView();
        RenderResult.success(null);
        mv.addObject("key", randomChar);
        mv.setViewName("redirect:" + SSORedirectUrl);
        return mv;
    }

    /**
     * Title：tokenLogin <br>
     * Description：根据传入参数获取token并执行登录<br>
     * author：王磊 <br>
     * date：2020年3月4日 下午04:59:30 <br>
     * @param userId 用户ID
     * @return <br>
     * @throws Exception 
     */
    @ApiOperation(value = "web单点登录")
    @PostMapping("/tokenLogin")
    public RenderResult<Object> tokenLogin(@ApiParam(name = "key", value = "key值") @RequestParam(
            name = "key") String key) throws Exception {
        Assert.notNull(key, "token值不能为空!");
        LoginView<Object> loginView = userService.authTokenForWS(key);
        return RenderResult.success(loginView);
    }

    /**
     * Title：getKaptchaImage <br>
     * Description：获取图形验证码方法 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午5:04:38 <br>
     * @param response
     * @param request
     * @throws Exception <br>
     */
    @GetMapping("/getCodeImage")
    @ApiOperation(value = "获取验证码图片", notes = "获取验证码图片")
    public void getKaptchaImage(HttpServletResponse response, HttpServletRequest request)
            throws Exception {
        kaptchaService.generateVerifyCodeImage(request, response);
    }

    /**
     * Title：updatePassword <br>
     * Description：用户修改密码方法 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:47:43 <br>
     * @param updatePasswordForm 修改密码的form
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "修改密码")
    @PostMapping("/updatePassword")
    public RenderResult<Object> updatePassword(
            @Valid @RequestBody UpdatePasswordForm updatePasswordForm) throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String userId = (String) auth.getCredentials();
        updatePasswordForm.setUserId(userId);
        userService.updatePassword(updatePasswordForm);
        return RenderResult.success();
    }

    /**
     * Title：forgetPassword <br>
     * Description：忘记密码方法 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:48:12 <br>
     * @param forgetPasswordForm 忘记密码修改的form
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "忘记密码")
    @PostMapping("/forgetPassword")
    public RenderResult<Object> forgetPassword(HttpServletRequest request,
            @Valid @RequestBody ForgetPasswordForm forgetPasswordForm) throws Exception {
        userService.forgetPassword(request,forgetPasswordForm.getUserName(),
                forgetPasswordForm.getPhoneNum(), forgetPasswordForm.getSmsCode(),
                forgetPasswordForm.getPassword(), forgetPasswordForm.getType());
        return RenderResult.success();
    }

    /**
     * Title：logOut <br>
     * Description：用户登出方法 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:49:32 <br>
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "登出")
    @GetMapping("/logOut")
    public RenderResult<Object> logOut() throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        if(ObjectUtil.isNotEmpty(auth)){
            String userId = (String) auth.getCredentials();
            userService.logOut(userId);
        }
        return RenderResult.success();
    }
    /** @GetMapping("/initPwdExportExcel")
     @ApiOperation(value = "初始化所有用户密码并excel", notes = "初始化所有用户密码并excel")
     public void initPwdExportExcel(HttpServletResponse response) throws Exception {
         try {
             loginService.initPwdExportExcel(response);
         } catch (Exception e) {
              throw e;
         }
     }**/

    @ApiOperation(value = "处理员工证件号、电话号码加密")
    @GetMapping("/handleUserSensitiveInfo")
    public RenderResult<Object> handleUserSensitiveInfo() throws Exception {
        userService.handleUserSensitiveInfo();
        return RenderResult.success();
    }
}
