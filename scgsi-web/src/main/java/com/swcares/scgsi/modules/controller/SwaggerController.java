package com.swcares.scgsi.modules.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.common.model.form.UserQueryForm;
import com.swcares.scgsi.web.RenderResult;

/**
 * ClassName：com.swcares.scgsi.modules.controller.TestController <br>
 * Description：TODO(这里用一句话描述这个类的作用) <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月27日 下午8:08:06 <br>
 * @version v1.0 <br>
 */
@RequestMapping("/swaggerTest")
@RestController
@Api(value = "/swaggerTest", tags = "swagger测试")
public class SwaggerController {

    @PostMapping("/test1")
    @ApiOperation(value = "测试方法", notes = "测试方法")
    @ApiResponses({
        @ApiResponse(code=302,message="302测试"),
        @ApiResponse(code=400,message="请求参数没填好"),
        @ApiResponse(code=404,message="请求路径没有或页面跳转路径不对")
    })
    public RenderResult<?> test1(@ApiParam(name = "page", value = "页码", required = true) Integer page,
            @ApiParam(name = "pageSize", value = "每页行数", required = true) Integer pageSize) {
        return RenderResult.success();
    }

    @GetMapping("/test2")
    @ApiOperation(value = "测试方法2", notes = "测试方法2")
    @ApiImplicitParams({
        @ApiImplicitParam(name="mobile",value="手机号",required=true,paramType="path"),
        @ApiImplicitParam(name="password",value="密码",required=true,paramType="path"),
        @ApiImplicitParam(name="age",value="年龄",required=true,paramType="path",dataType="Integer")
    })
    public RenderResult<Integer> test2(String mobile,String password,Integer age) {
        int a = 10000;
        return RenderResult.success(a);
    }
    
    @PostMapping("/test3")
    @ApiOperation(value = "测试方法3", notes = "测试方法3")
    public RenderResult<Integer> test3(@RequestBody UserQueryForm userQueryForm) {
        return RenderResult.success();
    }

}
