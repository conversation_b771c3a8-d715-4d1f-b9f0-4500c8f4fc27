package com.swcares.scgsi.modules.monitoring;

import com.swcares.scgsi.common.model.form.ContentIdxForm;
import com.swcares.scgsi.common.model.form.ContentTraceForm;
import com.swcares.scgsi.service.TraceService;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：TraceController <br>
 * Package：com.swcares.scgsi <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 03月12日 13:59 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping(path = "/trace")
@Api(tags = "trace接口获取数据")
public class TraceController {

    @Autowired
    private TraceService traceService;

    /**
     * Title：getPsgListByFilght() <br>
     * Description：通过航班查询旅客列表 <br>
     * author：于琦海 <br>
     * date：2020/3/12 14:03 <br>
     * @param form ContentTraceForm
     * @return Object
     */
    @ApiOperation(value = "通过航班查询旅客列表")
    @PostMapping("/getPsgListByFilght")
    @Deprecated
    public Object getPsgListByFilght(@Valid ContentTraceForm form){
        return RenderResult.success(traceService.getPsgListByFilght(form));
    }

    /**
     * Title：getPsgByIdx() <br>
     * Description：通过Idx查询旅客信息 <br>
     * author：于琦海 <br>
     * date：2020/3/12 14:03 <br>
     * @param idx String
     * @return Object
     */
    @ApiOperation(value = "通过Idx查询旅客信息")
    @PostMapping("/getPsgByIdx/{idx}")
    @Deprecated
    public Object getPsgByIdx(@PathVariable String idx){
        ContentIdxForm contentIdxForm = new ContentIdxForm(idx);
        return RenderResult.success(traceService.getPsgByIdx(contentIdxForm));
    }
}
