package com.swcares.scgsi.modules.task;

import com.swcares.scgsi.hotel.service.HotelCompensateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @ClassName：HotelExpireOrderTask
 * @Description：航延酒店期满赔偿单
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/11/7 9:25
 * @version： v1.0
 */
@Component
@Slf4j
public class HotelExpireOrderTask {
    @Autowired
    HotelCompensateService hotelCompensateService;

    @Transactional(rollbackFor = Exception.class)
    public void expireOrderInfo() throws Exception {
        int i = hotelCompensateService.expireOrderInfo();
        log.info("--【scgsi-web】--【航延酒店-处理逾期赔偿单定时任务】--处理逾期订单共计：【{}】条",i);
    }
}
