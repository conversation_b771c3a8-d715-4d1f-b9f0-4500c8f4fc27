package com.swcares.scgsi.modules.task;

import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * ClassName：com.swcares.scgsi.modules.task <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月02日 13:25 <br>
 * @version v1.0 <br>
 */
@Component
public class TestTask {
    /**
     * Title：test <br>
     * Description： 定时任务测试<br>
     * author：王建文 <br>
     * date：  <br>
     * @param
     * @return
     */
    public void test(){
        System.out.println("定时任务test方法:"+new Date().toString());
    }
    public void test1(){
        System.out.println("定时任务test1方法:"+new Date().toString());
    }
}