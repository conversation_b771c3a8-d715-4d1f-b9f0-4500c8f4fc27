package com.swcares.scgsi.modules.dict;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.dict.entity.SysDictDataInfo;
import com.swcares.scgsi.dict.service.SysDictDataService;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * ClassName：com.swcares.scgsi.modules.dict <br>
 * Description：数据字典接口 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月20日 22:22 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/sys/dict")
public class SysDictDataController extends BaseController {
    @Resource
    private SysDictDataService dictDataService;

    /**
     * Title：save <br>
     * Description： 保存数据字典<br>
     * author：王建文 <br>
     * date：2020-3-20 22:25 <br>
     *
     * @param sysDictDataInfo 参数接收
     */
    @PostMapping("save")
    public RenderResult save(@RequestBody SysDictDataInfo sysDictDataInfo) {
        try {
            dictDataService.save(sysDictDataInfo);
            return RenderResult.success();
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }

    /**
     * Title：updateStatus <br>
     * Description： 激活禁用数据字典<br>
     * author：王建文 <br>
     * date：2020-3-20 22:10 <br>
     *
     * @param ids    主键id
     * @param status 状态0激活1禁用
     */
    @PostMapping("updateStatus")
    public RenderResult updateStatus(String ids, String status) {
        try {
            dictDataService.updateStatus(ids, status);
            return RenderResult.success();
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }

    /**
     * Title：getSelectDict <br>
     * Description：根据数据类型获取可用下拉选项<br>
     * author：王建文 <br>
     * date：2020-3-20 22:16 <br>
     *
     * @param type 数据类型
     */
    @GetMapping("getSelectDict")
    public RenderResult getSelectDict(String type) {
        try {
            return RenderResult.success(dictDataService.getSelectDict(type));
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }

    @GetMapping("getDictPage")
    public RenderResult getDictPage(String keySearch, int current, int pageSize) {
        QueryResults queryResults = dictDataService.getDictPage(keySearch, current, pageSize);
        return RenderResult.success(returnPageInfo(queryResults));
    }


    public String ALLOW_LOGIN = "allowlogin";
    @GetMapping("/allowlogin")
    public RenderResult isPasswordLogin() {
        try {
            return RenderResult.success(dictDataService.getSelectDict("ALLOW_LOGIN"));
        } catch (Exception e) {
            return new RenderResult("1", e.getMessage());
        }
    }
}