package com.swcares.scgsi.modules.audit;

import com.swcares.scgsi.audit.dto.LaunchAuditProcessVo;
import com.swcares.scgsi.audit.dto.OrderAuditDataDto;
import com.swcares.scgsi.audit.dto.OrderAuditProcessParamDto;
import com.swcares.scgsi.audit.service.OrderAuditService;
import com.swcares.scgsi.overbook.service.PaxCompensateService;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * ClassName：com.swcares.scgsi.modules.audit <br>
 * Description：赔偿单审核 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月13日 15:25 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/dp/sys")
@Api(tags = "WEB-赔偿单审核")
public class OrderAuditController extends BaseController {
    @Resource
    private OrderAuditService orderAuditService;
    @Resource
    PaxCompensateService paxCompensateService;


    /**
     * Title：deployAuditProcess <br>
     * Description：部署山航流程图（一级，二级审核）<br>
     * author：傅欣荣 <br>
     * date：2020/3/24 10:44 <br>
     * @param
     * @return
     */
    @PostMapping("deployProcess")
    public RenderResult deployAuditProcess(String deployName ,String bpmnName ,String bpmnPng){
        try {
            orderAuditService.deployAuditProcess(deployName,
                    bpmnName,bpmnPng);

          /*  orderAuditService.deployAuditProcess("航延AOC二级审核流程",
                    "process/dp_order_aoc.bpmn","process/dp_order_aoc.png");
            orderAuditService.deployAuditProcess("航延AOC一级审核流程",
                    "process/dp_order_aoc1.bpmn","process/dp_order_aoc1.png");
                    orderAuditService.deployAuditProcess("山航航延异常行李审核流程",
                    "process/dp_abnormal_process.bpmn","process/dp_abnormal_process.png");
*/
            return RenderResult.success();
        } catch (Exception e) {
            return RenderResult.fail();
        }

    }

    /**
     * Title：launchAuditProcess <br>
     * Description： 手动触发发起审核流程<br>
     * author：傅欣荣 <br>
     * date：2020/4/9 11:12 <br>
     * @param
     * @return
     */
    @PostMapping("launchAuditProcess")
    public RenderResult launchAuditProcess(@RequestBody LaunchAuditProcessVo launchAuditProcessVo){
        orderAuditService.launchAuditProcess(launchAuditProcessVo.getUserId(),
                launchAuditProcessVo.getUserRole(),launchAuditProcessVo.getOrderId(),launchAuditProcessVo.getHandleUser());
        return RenderResult.success();

    }


    /**
     * Title：saveOrderInfo <br>
     * Description：获取赔偿单审核列表数据<br>
     * author：傅欣荣 <br>
     * date：2020/3/13 15:37 <br>
     * @param  orderAuditDataDto
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("getOrderAuditInfo")
    @ApiOperation(value = "赔偿单审核列表")
    public RenderResult getOrderAuditInfo(OrderAuditDataDto orderAuditDataDto) {
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        orderAuditDataDto.setUserId(userId);
        return RenderResult.success(returnPageInfo(orderAuditService.findAuditProcessByUserId(orderAuditDataDto)));
    }

    /**
     * Title： getAuditNode<br>
     * Description：  根据taskid 获取当前节点 2-aoc 3值班经理 <br>
     * author：傅欣荣 <br>
     * date：2020/4/15 10:28 <br>
     * @param
     * @return
     */
    @GetMapping("getAuditNode")
    public RenderResult getAuditNode(String taskId){
        return RenderResult.success(orderAuditService.getAuditNode(taskId));
    }

    /**
     * Title： getIsAocByUserId<br>
     * Description：  查询userId 是否为aoc人员  0 存在，1不存在<br>
     * author：傅欣荣 <br>
     * date：2020/4/15 10:28 <br>
     * @param
     * @return
     */
    @GetMapping("getIsAocByUserId")
    public RenderResult getIsAocByUserId(String userId){
        userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        return RenderResult.success(orderAuditService.getIsAocByUserId(userId));
    }




    /**
     * Title：getOrderAuditRecord <br>
     * Description： 获取赔偿单审核记录list<br>
     * author：傅欣荣 <br>
     * date：2020/3/13 15:40 <br>
     * @param  orderId
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("getOrderAuditRecord")
    @ApiOperation(value = "赔偿单审核记录")
    public RenderResult getOrderAuditRecord(String orderId) {
        return RenderResult.success(orderAuditService.findOrderAuditRecord(orderId));
    }


    /**
     * Title：saveOrderAuditRecord <br>
     * Description：处理赔偿单审批<br>
     * author：傅欣荣 <br>
     * date：2020/3/13 15:45 <br>
     * @param  orderAuditPd
     * @return com.swcares.scgsi.web.RenderResult
     */
    @PostMapping("handleAudit")
    @ApiOperation(value = "审批")
    public RenderResult saveOrderAuditRecord(@RequestBody OrderAuditProcessParamDto orderAuditPd) {
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        orderAuditPd.getOrderAuditInfo().setAuditor(userId);
        orderAuditService.handleAuditProcess(orderAuditPd.getOrderAuditInfo());
        return RenderResult.success();
    }

    /**
     * Title：getUserTaskIdByOrderId <br>
     * Description： WEB- 审核详情界面- 根据用户id 、赔偿单id查询流程审核任务id<br>
     * author：傅欣荣 <br>
     * date：2020/5/20 14:08 <br>
     * @param
     * @return
     */
    @GetMapping("getUserOrderTaskId")
    public RenderResult getUserTaskIdByOrderId(String orderId){
        String userId = (String) AuthenticationUtil.getAuthentication().getPrincipal();
        return RenderResult.success(orderAuditService.getUserTaskIdByOrderId(orderId,userId));
    }

}
