package com.swcares.scgsi.modules.hotel;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hotel.model.dto.HotelCompensateQueryDTO;
import com.swcares.scgsi.hotel.model.dto.HotelOrderAuditParamDTO;
import com.swcares.scgsi.hotel.service.HotelCompensateService;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：HotelOrderAuditController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/12/14 9:38
 * @version： v1.0
 */

@RestController
@RequestMapping("/api/fd/hotelAudit")
@Api(tags = "航延酒店保障单-接口V1")
public class HotelOrderAuditController extends BaseController {

    @Autowired
    private HotelCompensateService hotelCompensateService;

    @GetMapping("findOrderAuditPage")
    @ApiOperation(value = "航延酒店赔偿单-审核列表")
    public RenderResult findOrderAuditPage(HotelOrderAuditParamDTO dto) {
        QueryResults queryResults = hotelCompensateService.findOrderAuditPage(dto);
        return RenderResult.success(returnPageInfo(queryResults));
    }
}
