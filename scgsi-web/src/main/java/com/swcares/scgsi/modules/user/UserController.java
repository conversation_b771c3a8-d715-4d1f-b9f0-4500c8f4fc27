package com.swcares.scgsi.modules.user;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.alibaba.fastjson.JSON;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.dao.ResourcesDao;
import com.swcares.scgsi.department.common.model.vo.DepartmentTreeVO;
import com.swcares.scgsi.entity.Resources;
import com.swcares.scgsi.entity.Role;
import com.swcares.scgsi.entity.RoleResources;
import com.swcares.scgsi.entity.UserRole;
import com.swcares.scgsi.hum.employee.dao.EmployeeDao;
import com.swcares.scgsi.hum.employee.entity.Employee;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.role.common.model.vo.RoleResourceTreeExtendVo;
import com.swcares.scgsi.role.common.model.vo.RoleResourceTreeVo;
import com.swcares.scgsi.role.dao.ResourcesRepository;
import com.swcares.scgsi.role.dao.RoleRepository;
import com.swcares.scgsi.user.common.model.form.CancelOrKeepForm;
import com.swcares.scgsi.user.common.model.form.UpdateUserForm;
import com.swcares.scgsi.user.common.model.form.UserListForm;
import com.swcares.scgsi.user.common.model.vo.UserDetailVo;
import com.swcares.scgsi.user.service.UserService;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.util.IdWorker;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;

/**
 * ClassName：com.swcares.scgsi.login.controller.UserController <br>
 * Description：用户管理接口 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年3月12日 上午10:00:04 <br>
 * @version v1.0 <br>
 */
@Api(tags = "用户相关接口")
@RestController
@RequestMapping("/api/user")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;
    
    @Autowired
    private EmployeeDao employeeDao;

    @Autowired
    private IdWorker idWorker;

    @Autowired
    private RoleRepository roleRepository;

    @Resource
    private ResourcesDao resourcesDao;

    @Resource
    private ResourcesRepository resourcesRepository;

    @Autowired
    private RedisService redisService;
    
    /**
     * Title：resetPasswords <br>
     * Description：充值密码方法 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午5:07:09 <br>
     * @param userId
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "重置密码")
    @PostMapping("/resetPasswords")
    public RenderResult<Object> resetPasswords(@ApiParam(name = "userId", value = "需要重置密码的用户ID",
            required = true) @RequestParam(name = "userId") String userId) throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String modifiUserId = (String) auth.getCredentials();
        String pwd = userService.resetPasswords(userId, modifiUserId);
        return RenderResult.success(pwd);
    }

    /**
     * Title：updateUserState <br>
     * Description：修改用户的禁用启用状态<br>
     * author：王磊 <br>
     * date：2020年5月13日 下午5:08:06 <br>
     * @param userIds 修改的用户id
     * @param state 状态
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "禁用和启用用户")
    @PostMapping("/updateUserState")
    public RenderResult<Object> updateUserState(
            @ApiParam(name = "userIds", value = "需要重置密码的用户ID集合", required = true) @RequestParam(
                    name = "userIds") String[] userIds,
            @ApiParam(name = "state", value = "状态(0启用,1禁用)", required = true) @RequestParam(
                    name = "state") String state) throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String modifiUserId = (String) auth.getCredentials();
        userService.updateUserState(userIds, state, modifiUserId);
        return RenderResult.success();
    }

    @ApiOperation(value = "解锁用户")
    @PostMapping("/updateUserLockState")
    public RenderResult<Object> updateUserLockState(
            @ApiParam(name = "userIds", value = "需要重置密码的用户ID集合", required = true) @RequestParam(
                    name = "userIds") String[] userIds) throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String modifiUserId = (String) auth.getCredentials();
        userService.updateUserLockState(userIds, modifiUserId);
        return RenderResult.success();
    }

    /**
     * Title：updateOnDutyState <br>
     * Description：用户值班和解除值班方法 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:52:42 <br>
     * @param onDutyState 值班状态(0未值班1值班)
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "用户值班和解除值班(0未值班1值班)")
    @PostMapping("/updateOnDutyState")
    public RenderResult<Object> updateOnDutyState(
            @ApiParam(name = "onDutyState", value = "值班状态(0未值班1值班)", required = true) @RequestParam(
                    name = "onDutyState") String onDutyState) throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String userId = (String) auth.getCredentials();
        userService.updateOnDutyState(userId, onDutyState);
        return RenderResult.success();
    }

    /**
     * Title：getUserList <br>
     * Description：获取用户列表 <br>
     * author：王磊 <br>
     * date：2020年4月12日 上午12:04:12 <br>
     * @param form
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "用户列表")
    @GetMapping("/getUserList")
    public RenderResult<Map<String, Object>> getUserList(@Valid UserListForm form) throws Exception {
        Pageable pageable = PageRequest.of(form.getCurrent(), form.getPageSize());
        QueryResults queryResults = userService.getUserList(pageable, form);
        return RenderResult.success(this.returnPageInfo(queryResults));
    }

    /**
     * Title：manualSync <br>
     * Description：手动同步用户数据 <br>
     * author：王磊 <br>
     * date：2020年4月12日 上午12:12:42 <br>
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "手动增量同步用户数据")
    @GetMapping("/manualSyncUser")
    public RenderResult<Object> manualSync() throws Exception {
        userService.manualSync();
        return RenderResult.success();
    }

    /**
     * Title：getUserDetail <br>
     * Description：获取当前登录用户详情 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午4:53:13 <br>
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "获取用户详细信息")
    @GetMapping("/getUserDetail")
    public RenderResult<Object> getUserDetail(
            @ApiParam(name = "userId", value = "用户ID") @RequestParam(name = "userId",
                    required = false) String userId) throws Exception {
        if (StringUtils.isEmpty(userId)) {
            Authentication auth = AuthenticationUtil.getAuthentication();
            userId = (String) auth.getCredentials();
        }
        UserDetailVo userDetailVo = userService.getUserDetail(userId);
        return RenderResult.success(userDetailVo);
    }


    /**
     * Title：updateUser <br>
     * Description：修改用户基本信息 <br>
     * author：王磊 <br>
     * date：2020年4月12日 下午9:31:20 <br>
     * @param updateUserForm
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "修改用户")
    @PostMapping("/updateUser")
    public RenderResult<QueryResults> updateUser(@Valid @RequestBody UpdateUserForm updateUserForm)
            throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String modifiUserId = (String) auth.getCredentials();
        updateUserForm.setModifiUserId(modifiUserId);
        userService.updateUser(updateUserForm);
        return RenderResult.success();
    }

    /**
     * Title：getDepartmentTreeByRole <br>
     * Description：通过用户角色部门ID获取部门树 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午5:09:27 <br>
     * @return
     * @throws Exception <br>
     */
    @ApiOperation(value = "通过用户角色部门ID获取部门树")
    @GetMapping("/getDepartmentTreeByRole")
    public RenderResult<Object> getDepartmentTreeByRole() throws Exception {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String userId = (String) auth.getCredentials();
        List<DepartmentTreeVO> list = userService.getDepartmentTreeByRole(userId);
        return RenderResult.success(list);
    }

    /**
     * Title：logOut <br>
     * Description：根据姓名和工号模糊查询用户信息 <br>
     * author：夏阳 <br>
     * date：2020年4月21日 上午10:31:24 <br>
     * @return
     * @throws Exception <br>
     */
    @GetMapping("/retrieveUsers")
    @ApiOperation(value = "根据姓名和工号模糊查询用户信息", notes = "根据姓名和工号模糊查询用户信息")
    public RenderResult<Object> queryUserByNameAndNo(String keySearch) {
        return RenderResult.success(userService.queryUserByNameAndNo(keySearch));
    }

    /**
     * Title：findRoleByUpdate <br>
     * Description：TODO(这里用一句话描述这个方法的作用) <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午5:09:54 <br>
     * @param userId
     * @return <br>
     */
    @GetMapping("/findRoleByUpdate")
    @ApiOperation(value = "根据登录人的管理角色toid和编辑用户的id来构建角色树", notes = "根据登录人的管理角色toid和编辑用户的id来构建角色树")
    public RenderResult<List<RoleResourceTreeExtendVo>> findRoleByUpdate(@ApiParam(name = "userId",
            value = "用户ID", required = true) @RequestParam(name = "userId") String userId) {
        Authentication auth = AuthenticationUtil.getAuthentication();
        String modifiUserId = (String) auth.getCredentials();
        return RenderResult.success(userService.findRoleByUserIdAndModifiUserId(modifiUserId,
                userId));
    }

    /**
     * Title：findRoleByUserId <br>
     * Description：根据用户的id来构建角色树 <br>
     * author：王磊 <br>
     * date：2020年5月13日 下午5:11:47 <br>
     * @param userId
     * @return <br>
     */
    @GetMapping("/findRoleByUserId")
    @ApiOperation(value = "根据用户的id来构建角色树", notes = "根据用户的id来构建角色树")
    public RenderResult<List<RoleResourceTreeVo>> findRoleByUserId(@ApiParam(name = "userId",
            value = "用户ID", required = true) @RequestParam(name = "userId") String userId) {
        return RenderResult.success(userService.findRoleByUserId(userId));
    }

    /**
     * Title：createSuperAdmin <br>
     * Description：创建超级管理员（上线专属） <br>
     * author：于琦海 <br>
     * date：2020/5/7 9:05 <br>
     * @return void
     */
    @GetMapping(path = "/createSuperAdmin")
    @Transactional(rollbackFor = Exception.class)
    public void createSuperAdmin() {
        Employee employee = new Employee();
        employee.setToId("1");
        employee.setTuAcct("superadmin");
        employee.setTuNo("superadmin");
        employee.setTuCname("山东地服超级管理员用户");
        employee.setTUEname("superadmin");
        employee.setUserState(0);
        PasswordEncoder createDelegatingPasswordEncoder =
                PasswordEncoderFactories.createDelegatingPasswordEncoder();
        employee.setPassWord(createDelegatingPasswordEncoder.encode("admin2020#Scgsi"));
        Employee employeeSave = employeeDao.save(employee);

        // role
        Role role = new Role();
        role.setName("超级管理员角色");
        role.setCompetence("0");
        role.setDepartmentId("1");
        role.setStatus("1");
        role.setCode("R" + idWorker.nextId());
        role.setFounder(employeeSave.getTuAcct() + employeeSave.getTuCname());
        role.setCreateTime(DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS));
        Role roleSave = roleRepository.save(role);
        String roleId = roleSave.getId();

        UserRole userRole = new UserRole();
        userRole.setRoleId(roleId);
        String employeeId = employeeSave.getId();
        userRole.setEmployeeId(employeeId);
        List<UserRole> userRoleList = new ArrayList<>();
        userRoleList.add(userRole);
        resourcesDao.bindUsers(userRoleList);

        List<Resources> all = resourcesRepository.findAll();
        List<RoleResources> roleResources = new ArrayList<>();
        for (Resources resources : all) {
            RoleResources roleResourcesInfo = new RoleResources();
            roleResourcesInfo.setResourcesID(resources.getId());
            roleResourcesInfo.setRoleID(roleId);
            roleResources.add(roleResourcesInfo);
        }
        resourcesDao.bindResources(roleResources);
    }

    /**
     * Title：收藏的菜单 <br>
     * Description： <br>
     * author：于琦海 <br>
     * date：2020/5/28 10:39 <br>
     * @param authentication Authentication
     * @return RenderResult
     */
    @GetMapping("/favoriteMenu")
    @ApiOperation(value = "收藏的菜单")
    public RenderResult<Set<CancelOrKeepForm>> favoriteMenu(Authentication authentication) {
        String id = (String) authentication.getCredentials();
        Set<Object> objectSet = redisService.sGet(id);
        if (objectSet != null && objectSet.size() > 0) {
            Set<CancelOrKeepForm> result = new HashSet<>();
            objectSet.forEach(jsonStr -> result.add(JSON.parseObject(String.valueOf(jsonStr), CancelOrKeepForm.class)));
            return RenderResult.success(result);
        }
        return RenderResult.success();
    }

    /**
     * Title：cancelOrKeep <br>
     * Description：取消或者收藏 <br>
     * author：于琦海 <br>
     * date：2020/5/28 10:47 <br>
     * @param:[key , authentication]
     * @return com.swcares.scgsi.web.RenderResult
     */
    @GetMapping("/cancelOrKeep")
    @ApiOperation(value = "取消或者收藏")
    public RenderResult cancelOrKeep(CancelOrKeepForm form, Authentication authentication) {
        String id = (String) authentication.getCredentials();
        String jsonStr = JSON.toJSONString(form);
        Set<Object> objects = redisService.sGet(id);
        if (objects != null && objects.size() > 0) {
            for (Object str : objects) {
                CancelOrKeepForm cancelOrKeepForm = JSON.parseObject(String.valueOf(str), CancelOrKeepForm.class);
                if (cancelOrKeepForm.getId().equals(form.getId())) {
                    redisService.setRemove(id, jsonStr);
                    return RenderResult.success();
                }
            }
        }
        redisService.sSet(id, jsonStr);
        return RenderResult.success();
    }

}
