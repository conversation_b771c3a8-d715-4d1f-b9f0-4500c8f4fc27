package com.swcares.scgsi.modules.setting;

import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.swcares.scgsi.setting.form.TableColumnsForm;
import com.swcares.scgsi.setting.service.TableSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * ClassName：com.swcares.scgsi.modules.setting.TableSettingController <br>
 * Description：用于前端table设置的ctrl <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年5月19日 下午2:10:11 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/table")
@Api(value = "/api/table", tags = "页面表格管理接口")
public class TableSettingController {
    
    @Autowired
    private TableSettingService tableSettingService;

   /**
    * Title：saveTableColumns <br>
    * Description：存储当前用户的表格设置信息<br>
    * author：夏阳 <br>
    * date：2020年5月19日 下午1:13:40 <br>
    * @param tableColumnsForm <br>
    */
    @PostMapping("/savetablecolumns")
    @ApiOperation(value = "存储当前用户的表格设置信息", notes = "存储当前用户的表格设置信息")
    public void saveTableColumns(@RequestBody TableColumnsForm tableColumnsForm) {
        tableSettingService.saveTableColumns(tableColumnsForm);
    }

    /**
    * Title：getTableColumns <br>
    * Description：根据当前用户获取所有的表格字段信息 <br>
    * author：夏阳 <br>
    * date：2020年5月19日 下午2:08:05 <br>
    * @return <br>
    */
    @PostMapping("/gettablecolumns")
    @ApiOperation(value = "根据当前用户获取所有的表格字段信息 ", notes = "根据当前用户获取所有的表格字段信息 ")
    public Map<String, List<String>> getTableColumns(){
        return tableSettingService.getTableColumns();
    }
}
