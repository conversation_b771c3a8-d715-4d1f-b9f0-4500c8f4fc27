package com.swcares.scgsi.modules.task;

import com.swcares.scgsi.flight.dao.impl.FlightCompensateDaoImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * ClassName：com.swcares.scgsi.modules.task <br>
 * Description：逾期赔付单处理当前航班日期+1年<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 11月18日 10:34 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class ExpireOrderTask {
    @Resource
    private FlightCompensateDaoImpl flightCompensateDao;

    @Transactional(rollbackFor = Exception.class)
    public void expireOrderInfo() throws Exception {
        flightCompensateDao.expireOrderInfo();
    }
}