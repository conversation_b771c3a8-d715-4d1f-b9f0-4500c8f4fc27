package com.swcares.scgsi.modules.monitoring;

import com.swcares.scgsi.common.model.VO.FlightDetailVO;
import com.swcares.scgsi.common.model.form.FlightInfoListForm;
import com.swcares.scgsi.common.model.form.FlightListForm;
import com.swcares.scgsi.entity.FocFlightInfo;
import com.swcares.scgsi.service.FlightInfoService;
import com.swcares.scgsi.service.impl.FlightInfoServiceImpl;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FlightInfoController <br>
 * Package：com.swcares.scgsi.modules.controller <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年 03月20日 10:03 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping(path = "/api/flightInfo")
@Api(tags = "FOC获取航班信息")
public class FlightInfoController extends BaseController {

    @Autowired
    private FlightInfoService flightInfoService;

    @Autowired
    private FlightInfoServiceImpl infoService;
    /**
     * Title：getFlightInfo() <br>
     * Description：通过航班ID查询航班信息 <br>
     * author：于琦海 <br>
     * date：2020/3/20 10:37 <br>
     * @param flightId String
     * @return RenderResult<FlightDetailVO>
     */
    @ApiOperation(value = "通过航班ID查询航班信息")
    @GetMapping("/getFlightInfo")
    public RenderResult<FlightDetailVO> getFlightInfo(String flightId){
        return  RenderResult.success(infoService.getDelayFlightInfo(flightInfoService.getFlightInfoById(flightId)));
    }

    /**
     * Title：getFlightSegment() <br>
     * Description：通过航班号航班日期查询航班航段 <br>
     * author：于琦海 <br>
     * date：2020/3/20 10:37 <br>
     * @param flightNum String
     * @param flightDate String
     * @return Object
     */
    @ApiOperation(value = "通过航班号航班日期查询航班航段")
    @PostMapping("/getFlightSegment")
    public Object getFlightSegment(String flightNum,String flightDate) {
        return RenderResult.success(flightInfoService.getFlightSegment(flightNum,flightDate));
    }

    /**
     * Title：getFlightInfoList（） <br>
     * Description：通过航班四要素查询航班信息列表 <br>
     * author：于琦海 <br>
     * date：2020/3/20 14:30 <br>
     * @param form FlightInfoListForm
     * @return RenderResult<List<FocFlightInfo>>
     */
    @ApiOperation(value = "通过航班四要素查询航班信息列表")
    @PostMapping("/getFlightInfoList")
    public RenderResult<List<FocFlightInfo>> getFlightInfoList(FlightInfoListForm form){
        // 通过航班号和航班日期查询所有的然后再筛选
        return RenderResult.success(flightInfoService.getFlightInfoList(form));
    }

    /**
     * Title：getFlightList（） <br>
     * Description：web端根据条件查询航班列表 <br>
     * author：于琦海 <br>
     * date：2020/3/30 15:04 <br>
     * @param form FlightListForm
     * @return RenderResult<QueryResults>
     */
    @ApiOperation(value = "web端根据条件查询航班列表")
    @PostMapping("/getFlightList")
    public Object getFlightList(@Valid FlightListForm form){
        return RenderResult.success(super.returnPageInfo(flightInfoService.getFlightList(form)));
    }

}
