package com.swcares.scgsi.modules.audit;

import com.swcares.scgsi.department.service.DepartmentService;
import com.swcares.scgsi.department.service.impl.DepartmentServiceImpl;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * ClassName：com.swcares.scgsi.modules.audit <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 04月03日 16:33 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/api/department")
public class DepartController extends BaseController{
    @Resource
    private DepartmentServiceImpl departmentService;
    /**
     * Title：getAllDepartMent <br>
     * Description： 获取部门树<br>
     * author：王建文 <br>
     * date：2020-4-3 16:35 <br>
     * @param
     * @return
     */
    @GetMapping("getAllDepartMent")
    public RenderResult getAllDepartMent(){
        return RenderResult.success(departmentService.getAllDepartment(true));
    }
    /**
     * Title：getDepartmentChildInfo <br>
     * Description： 根据部门id获取部门信息<br>
     * author：王建文 <br>
     * date：2020-4-15 10:53 <br>
     * @param deptId 部门id
     */
    @GetMapping("getDepartmentChildInfo")
    public RenderResult getDepartmentChildInfo(String deptId,int current,int pageSize,String toId,String toFname){
        return RenderResult.success(returnPageInfo(departmentService.getDepartmentChildInfo(deptId,current,pageSize,toId,toFname)));
    }
    /**
     * Title：updateDepartStatus <br>
     * Description： 部门激活禁用<br>
     * author：王建文 <br>
     * date：2020-4-15 11:02 <br>
     * @param
     */
    @PostMapping("updateDepartStatus")
    public RenderResult updateDepartStatus(String id,int status){
        try {
            departmentService.updateDepartStatus(id,status);
            return RenderResult.success();
        }catch (Exception e){
            return RenderResult.fail();
        }
    }
}