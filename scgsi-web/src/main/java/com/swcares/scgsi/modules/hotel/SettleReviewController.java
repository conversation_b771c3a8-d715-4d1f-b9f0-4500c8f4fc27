package com.swcares.scgsi.modules.hotel;

import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.PageResults;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hotel.dao.HotelSettlementDao;
import com.swcares.scgsi.hotel.model.dto.AuditCompleteParamsDTO;
import com.swcares.scgsi.hotel.model.dto.PaxAccommodationReportDTO;
import com.swcares.scgsi.hotel.model.dto.SettleReviewAuditDTO;
import com.swcares.scgsi.hotel.model.dto.SettleReviewListDTO;
import com.swcares.scgsi.hotel.model.vo.*;
import com.swcares.scgsi.hotel.service.SettleReviewService;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.util.ExcelImportExportUtil;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName：SettleReviewController
 * @Description： 结算审核
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/23 10:36
 * @version： v1.0
 */
@RequestMapping("/api/hotel/settleReview")
@RestController
@Api(tags = "web酒店结算审核")
public class SettleReviewController extends BaseController {

    @Autowired
    SettleReviewService settleReviewService;

    @PostMapping("/page")
    @ApiOperation("结算审核列表")
    public RenderResult<PageResults<SettleReviewListVO>> getSettleReviewPage(@RequestBody @Validated SettleReviewListDTO dto){
        QueryResults queryResults = settleReviewService.getSettleReviewPage(dto);
        List<SettleReviewListVO> list = new ArrayList<>();
        //排除所有实际未服务旅客的酒店结算
        for (SettleReviewListVO settleReviewListVO : (List<SettleReviewListVO>) queryResults.getList()) {
            if(0 == settleReviewListVO.getPayPaxNumber() || ObjectUtils.isEmpty(settleReviewListVO.getPayPaxNumber())){
                continue;
            }
            list.add(settleReviewListVO);
        }
        PageResults<SettleReviewListVO> pageResults=new PageResults<>();
        pageResults.setList(list);
        pageResults.setPager(queryResults.getPagination());
        return RenderResult.success(pageResults);
    }

    @GetMapping("/view")
    @ApiOperation("结算审核详情")
    public RenderResult<SettleReviewDetailVO> getSettleReviewInfo(@ApiParam(value = "结算审核id",required = true) @RequestParam(value = "settleId")String settleId){
       try {
           return RenderResult.success(settleReviewService.getSettleReviewInfo(settleId));
       }catch (Exception e){
           return new RenderResult("1",e.getMessage());
       }
    }

    @GetMapping("/find/hotelInfo")
    @ApiOperation("获取酒店信息")
    public RenderResult<List<HotelInfoVO>> getHotelInfo(@ApiParam(value = "已选的服务航站中文") @RequestParam(value = "serviceCity",required = false)List<String> serviceCity){
        return RenderResult.success(settleReviewService.getHotelInfo(serviceCity));
    }

    @GetMapping("/find/CheckInfo")
    @ApiOperation("核验")
    public RenderResult<CheckInfoVO> getCheckInfo(@ApiParam(value = "审批单号（页面输入）",required = true) @RequestParam(value = "reviewNo")String reviewNo,
                                                  @ApiParam(value = "结算审核单号（后端返回的）",required = true) @RequestParam(value = "auditNo")String auditNo){
            return RenderResult.success(settleReviewService.getCheckInfo(reviewNo, auditNo));
    }

    @GetMapping("/checkBack")
    @ApiOperation("核验退回")
    public RenderResult checkBack(@ApiParam(value = "结算审核单号（后端返回的）",required = true) @RequestParam(value = "auditNo")String auditNo,
                                  @ApiParam(value = "备注信息",required = true) @RequestParam(value = "remark")String remark){
            settleReviewService.checkBack(auditNo,remark);
        return RenderResult.success();
    }

    @GetMapping("/submit")
    @ApiOperation("提交结算审核单")
    public RenderResult submitSettleReviewNo(@ApiParam(value = "结算审核单号（后端返回的）",required = true) @RequestParam(value = "auditNo")String auditNo){
        try {
            settleReviewService.submitSettleReviewNo(auditNo);
        }catch (Exception e){
            return new RenderResult("1",e.getMessage());
        }
        return RenderResult.success();
    }

    @GetMapping("/find/auditors")
    @ApiOperation("查询可进行审核的人员")
    public RenderResult<List<AuditorsInfoVO>> getAuditors(@ApiParam(value = "结算审核单id",required = true) @RequestParam(value = "businessKey")String businessKey,
                                                          @ApiParam(value = "审核人姓名") @RequestParam(value = "userInfo",required = false)String userInfo){
        return RenderResult.success(settleReviewService.getAuditors(businessKey, userInfo));
    }

    @PostMapping("/save/auditors")
    @ApiOperation("保存审核人员")
    public RenderResult saveAuditors(@ApiParam(value = "结算审核单id",required = true) @RequestParam(value = "businessKey")String businessKey,
                                     @ApiParam(value = "审核人工号集合",required = true) @RequestParam(value = "auditors")List<String> auditors){
            settleReviewService.saveAuditors(businessKey, auditors);
            return RenderResult.success();
    }

//    @GetMapping("/doneCheck")
//    @ApiOperation(value = "核验完成")
//    public RenderResult doneCheck(@ApiParam(value = "结算审核单号（后端返回的）",required = true) @RequestParam(value = "auditNo")String auditNo) {
//        try {
//            settleReviewService.doneCheck(auditNo);
//            return RenderResult.success();
//        } catch (Exception e) {
//            return new RenderResult("1", e.getMessage());
//        }
//    }

    @PostMapping("/auditOperation")
    @ApiOperation(value = "审核")
    public RenderResult auditOperation(@RequestBody @Validated SettleReviewAuditDTO dto) throws Exception {
        settleReviewService.auditOperation(dto);
        return RenderResult.success();

    }

    @GetMapping("/noBody")
    @ApiOperation(value = "酒店无人入住时修改结算单以及酒店住宿单的 结算和核验状态")
    public void updateSettleInfoByNoBody(@ApiParam(value = "结算审核单id",required = true) @RequestParam(value = "settleId") String settleId){
        settleReviewService.updateByNoBody(settleId);
    }

    //**********************************tanrui*******************************//
    /**
     * @title getPaxAccommodationReportPage
     * @description 报表分页查询接口
     * <AUTHOR>
     * @date 2022/9/26 10:55
     * @param dto
     * @return RenderResult<PaxAccommodationReportVO>
     */
    @PostMapping("/report")
    @ApiOperation(value = "报表分页查询接口")
    public RenderResult<PageResults<PaxAccommodationReportChildVO>> getPaxAccommodationReportPage(@RequestBody @Validated PaxAccommodationReportDTO dto){
        QueryResults reportPage = settleReviewService.getPaxAccommodationReportPage(dto);
        PageResults<PaxAccommodationReportChildVO> pageResults=new PageResults<>();
        pageResults.setList((List<PaxAccommodationReportChildVO>)reportPage.getList());
        pageResults.setPager(reportPage.getPagination());
        return RenderResult.success(pageResults);
    }


    /**
     * @title paxAccommodationReportExport
     * @description 报表导出
     * <AUTHOR>
     * @date 2022/9/26 11:26
     * @param dto
     * @param response
     * @return void
     */
    @PostMapping("/report/export")
    @ApiOperation(value = "报表导出接口")
    public void paxAccommodationReportExport(@RequestBody @Validated PaxAccommodationReportDTO dto, HttpServletResponse response){
        QueryResults paxAccommodationReportPage = settleReviewService.getPaxAccommodationReportPage(dto);
        List<PaxAccommodationReportChildVO> re = (List<PaxAccommodationReportChildVO>) paxAccommodationReportPage.getList();
        List<PaxAccommodationReportVO> paxAccommodationReportVOS = re.stream().map(vo -> {
            PaxAccommodationReportVO paxAccommodationReportVO = new PaxAccommodationReportVO();
            BeanUtils.copyProperties(vo, paxAccommodationReportVO);
            return paxAccommodationReportVO;
        }).collect(Collectors.toList());
        try {
            //获取当前年月日 时分
            String tableName = "明细报表" + DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMM);
            String[] columnNames = {"标记","航班日期","航班号","航段","服务类型","酒店名称","服务天数","服务保障单单号","服务单发起时间",
            "审核单号","旅客姓名","票号","证件号","手机号","房间类型","房间号","入住时间","入住办理方式","服务航站","出发航站","到达航站",
            "服务单状态","机型","发起人","审核人","结算审核人","结算审核时间","完成结算人","完成结算日期"};
            String[] keys = { "isFlag","flightDate","flightNo","segment","payType","hotelName","guaranteeDayCount","orderNo",
                    "createTime","auditNo","paxName","tktNo","idNo","telephone","roomType","roomNo","checkInTime","checkInMode",
                    "serviceCity","orgCityAirport","dstCityAirport","status","acType","createUser","examineUser","settleReviewUser",
                    "settleReviewTime","settleUser","settleTime"};
            ExcelImportExportUtil.exportExcel(response,tableName,paxAccommodationReportVOS,columnNames,keys);
        } catch (IOException e) {
            throw new BusinessException(MessageCode.REPORT_EXPORT_FAIL.getCode());
        }
    }

    @GetMapping("/updateFlag")
    @ApiOperation(value = "修改标记接口")
    public RenderResult<Object> updateFlag(@RequestParam(value = "paxIds") @ApiParam(value = "旅客id",required = true) List<String> paxIds,@RequestParam(value = "flag") @ApiParam(value = "需要修改成为的值",required = true) String flag){
        //@RequestParam  默认 required = true
        settleReviewService.updateFlag(paxIds,flag);
        return RenderResult.success();
    }

}
