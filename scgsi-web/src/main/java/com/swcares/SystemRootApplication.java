package com.swcares;

import org.springframework.boot.SpringApplication;
import org.springframework. boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import com.swcares.scgsi.base.ScgsiRepositoryImpl;

@SpringBootApplication
@EnableAutoConfiguration(exclude = {
        org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class
        ,org.activiti.spring.boot.SecurityAutoConfiguration.class})
@EnableJpaRepositories(repositoryBaseClass= ScgsiRepositoryImpl.class)
@EnableAsync
public class SystemRootApplication {

    public static void main(String[] args) {
        SpringApplication.run(SystemRootApplication.class, args);
    }
    
}
