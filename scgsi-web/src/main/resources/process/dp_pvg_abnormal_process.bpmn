<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:tns="http://www.activiti.org/test" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" expressionLanguage="http://www.w3.org/1999/XPath" id="m1591940723320" name="" targetNamespace="http://www.activiti.org/test" typeLanguage="http://www.w3.org/2001/XMLSchema">
  <process id="pvg_amount_process" isClosed="false" isExecutable="true" name="山航航延异常行李_浦东审核流程" processType="None">
    <startEvent id="_2" name="开始"/>
    <userTask activiti:assignee="${submitter}" activiti:exclusive="true" id="_4" name="提交赔偿单申请"/>
    <exclusiveGateway gatewayDirection="Unspecified" id="_5" name="判断流转流程"/>
    <userTask activiti:exclusive="true" id="_6" name="异常行李值班主任">
      <extensionElements>
        <activiti:taskListener class="com.swcares.scgsi.audit.listener.ActivitiAssignUsersListener" event="create">
          <activiti:field name="assigneeUser">
            <activiti:string>浦东行查主任审核</activiti:string>
          </activiti:field>
          <activiti:field name="onDuty">
            <activiti:string>true</activiti:string>
          </activiti:field>
        </activiti:taskListener>
      </extensionElements>
    </userTask>
    <userTask activiti:exclusive="true" id="_7" name="异常行李值班主任">
      <extensionElements>
        <activiti:taskListener class="com.swcares.scgsi.audit.listener.ActivitiAssignUsersListener" event="create">
          <activiti:field name="assigneeUser">
            <activiti:string>浦东行查主任审核</activiti:string>
          </activiti:field>
          <activiti:field name="onDuty">
            <activiti:string>true</activiti:string>
          </activiti:field>
        </activiti:taskListener>
      </extensionElements>
    </userTask>
    <userTask activiti:exclusive="true" id="_8" name="异常行李值班主任">
      <extensionElements>
        <activiti:taskListener class="com.swcares.scgsi.audit.listener.ActivitiAssignUsersListener" event="create">
          <activiti:field name="assigneeUser">
            <activiti:string>浦东行查主任审核</activiti:string>
          </activiti:field>
          <activiti:field name="onDuty">
            <activiti:string>true</activiti:string>
          </activiti:field>
        </activiti:taskListener>
      </extensionElements>
    </userTask>
    <userTask activiti:exclusive="true" id="_9" name="异常行李经理">
      <extensionElements>
        <activiti:taskListener class="com.swcares.scgsi.audit.listener.ActivitiAssignUsersListener" event="create">
          <activiti:field name="assigneeUser">
            <activiti:string>浦东行查经理审核</activiti:string>
          </activiti:field>
        </activiti:taskListener>
      </extensionElements>
    </userTask>
    <userTask activiti:exclusive="true" id="_10" name="异常行李经理">
      <extensionElements>
        <activiti:taskListener class="com.swcares.scgsi.audit.listener.ActivitiAssignUsersListener" event="create">
          <activiti:field name="assigneeUser">
            <activiti:string>浦东行查经理审核</activiti:string>
          </activiti:field>
        </activiti:taskListener>
      </extensionElements>
    </userTask>
    <userTask activiti:exclusive="true" id="_11" name="异常行李总经理">
      <extensionElements>
        <activiti:taskListener class="com.swcares.scgsi.audit.listener.ActivitiAssignUsersListener" event="create">
          <activiti:field name="assigneeUser">
            <activiti:string>浦东行查部领导审核</activiti:string>
          </activiti:field>
        </activiti:taskListener>
      </extensionElements>
    </userTask>
    <exclusiveGateway gatewayDirection="Unspecified" id="_3" name="审核状态"/>
    <exclusiveGateway gatewayDirection="Unspecified" id="_12" name="审核状态"/>
    <exclusiveGateway gatewayDirection="Unspecified" id="_13" name="审核状态"/>
    <endEvent id="_14" name="EndEvent"/>
    <sequenceFlow id="_15" sourceRef="_2" targetRef="_4"/>
    <sequenceFlow id="_16" sourceRef="_4" targetRef="_5"/>
    <sequenceFlow id="_17" name="二级审核判断" sourceRef="_5" targetRef="_7">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${amount>=500 && amount<=999}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_18" name="三级审核判断" sourceRef="_5" targetRef="_8">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${amount>=1000}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_19" name="一级审核判断" sourceRef="_5" targetRef="_6">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${amount>=0 && amount<=499}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_20" sourceRef="_6" targetRef="_3"/>
    <sequenceFlow id="_21" name="通过 || 不通过" sourceRef="_3" targetRef="_14">
      <extensionElements>
        <activiti:executionListener class="com.swcares.scgsi.audit.listener.ActivitiMsgPushListener" event="start"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 0 || result==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_22" name="驳回" sourceRef="_3" targetRef="_4">
      <extensionElements>
        <activiti:executionListener class="com.swcares.scgsi.audit.listener.ActivitiMsgPushListener" event="end"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_23" sourceRef="_7" targetRef="_12"/>
    <sequenceFlow id="_24" name="通过" sourceRef="_12" targetRef="_9">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_25" name="驳回" sourceRef="_12" targetRef="_4">
      <extensionElements>
        <activiti:executionListener class="com.swcares.scgsi.audit.listener.ActivitiMsgPushListener" event="start"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_26" name="不通过" sourceRef="_12" targetRef="_14">
      <extensionElements>
        <activiti:executionListener class="com.swcares.scgsi.audit.listener.ActivitiMsgPushListener" event="start"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_27" sourceRef="_9" targetRef="_13"/>
    <sequenceFlow id="_28" name="通过 || 不通过" sourceRef="_13" targetRef="_14">
      <extensionElements>
        <activiti:executionListener class="com.swcares.scgsi.audit.listener.ActivitiMsgPushListener" event="start"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 0 || result==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_30" name="驳回" sourceRef="_13" targetRef="_7">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 2}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway gatewayDirection="Unspecified" id="_31" name="审核状态"/>
    <exclusiveGateway gatewayDirection="Unspecified" id="_32" name="审核状态"/>
    <exclusiveGateway gatewayDirection="Unspecified" id="_33" name="审核状态"/>
    <sequenceFlow id="_34" sourceRef="_8" targetRef="_31"/>
    <sequenceFlow id="_35" name="通过" sourceRef="_31" targetRef="_10">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_29" name="驳回" sourceRef="_31" targetRef="_4">
      <extensionElements>
        <activiti:executionListener class="com.swcares.scgsi.audit.listener.ActivitiMsgPushListener" event="start"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_36" name="不通过" sourceRef="_31" targetRef="_14">
      <extensionElements>
        <activiti:executionListener class="com.swcares.scgsi.audit.listener.ActivitiMsgPushListener" event="start"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_37" sourceRef="_10" targetRef="_32"/>
    <sequenceFlow id="_38" sourceRef="_32" targetRef="_11">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_39" name="驳回" sourceRef="_32" targetRef="_8">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_40" name="不通过" sourceRef="_32" targetRef="_14">
      <extensionElements>
        <activiti:executionListener class="com.swcares.scgsi.audit.listener.ActivitiMsgPushListener" event="start"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_41" sourceRef="_11" targetRef="_33"/>
    <sequenceFlow id="_42" name="通过 || 不通过" sourceRef="_33" targetRef="_14">
      <extensionElements>
        <activiti:executionListener class="com.swcares.scgsi.audit.listener.ActivitiMsgPushListener" event="start"/>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 0 || result==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_43" name="驳回" sourceRef="_33" targetRef="_10">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${result == 2}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram documentation="background=#3C3F41;count=1;horizontalcount=1;orientation=0;width=842.4;height=1195.2;imageableWidth=832.4;imageableHeight=1185.2;imageableX=5.0;imageableY=5.0" id="Diagram-_1" name="New Diagram">
    <bpmndi:BPMNPlane bpmnElement="ynt_amount_process">
      <bpmndi:BPMNShape bpmnElement="_2" id="Shape-_2">
        <omgdc:Bounds height="32.0" width="32.0" x="235.0" y="5.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_4" id="Shape-_4">
        <omgdc:Bounds height="55.0" width="85.0" x="210.0" y="90.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_5" id="Shape-_5" isMarkerVisible="false">
        <omgdc:Bounds height="32.0" width="32.0" x="240.0" y="185.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_6" id="Shape-_6">
        <omgdc:Bounds height="55.0" width="85.0" x="20.0" y="305.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_7" id="Shape-_7">
        <omgdc:Bounds height="55.0" width="85.0" x="210.0" y="305.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_8" id="Shape-_8">
        <omgdc:Bounds height="55.0" width="85.0" x="425.0" y="300.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_9" id="Shape-_9">
        <omgdc:Bounds height="55.0" width="85.0" x="210.0" y="470.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_10" id="Shape-_10">
        <omgdc:Bounds height="55.0" width="85.0" x="430.0" y="470.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_11" id="Shape-_11">
        <omgdc:Bounds height="55.0" width="85.0" x="435.0" y="660.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_3" id="Shape-_3" isMarkerVisible="false">
        <omgdc:Bounds height="32.0" width="32.0" x="40.0" y="395.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_12" id="Shape-_12" isMarkerVisible="false">
        <omgdc:Bounds height="32.0" width="32.0" x="235.0" y="390.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_13" id="Shape-_13" isMarkerVisible="false">
        <omgdc:Bounds height="32.0" width="32.0" x="235.0" y="570.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_14" id="Shape-_14">
        <omgdc:Bounds height="32.0" width="32.0" x="230.0" y="845.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_31" id="Shape-_31" isMarkerVisible="false">
        <omgdc:Bounds height="32.0" width="32.0" x="455.0" y="390.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_32" id="Shape-_32" isMarkerVisible="false">
        <omgdc:Bounds height="32.0" width="32.0" x="460.0" y="570.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_33" id="Shape-_33" isMarkerVisible="false">
        <omgdc:Bounds height="32.0" width="32.0" x="460.0" y="755.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="_35" id="BPMNEdge__35" sourceElement="_31" targetElement="_10">
        <omgdi:waypoint x="471.0" y="422.0"/>
        <omgdi:waypoint x="471.0" y="470.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_34" id="BPMNEdge__34" sourceElement="_8" targetElement="_31">
        <omgdi:waypoint x="471.0" y="355.0"/>
        <omgdi:waypoint x="471.0" y="390.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_15" id="BPMNEdge__15" sourceElement="_2" targetElement="_4">
        <omgdi:waypoint x="251.0" y="37.0"/>
        <omgdi:waypoint x="251.0" y="90.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_37" id="BPMNEdge__37" sourceElement="_10" targetElement="_32">
        <omgdi:waypoint x="476.0" y="525.0"/>
        <omgdi:waypoint x="476.0" y="570.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_36" id="BPMNEdge__36" sourceElement="_31" targetElement="_14">
        <omgdi:waypoint x="487.0" y="406.0"/>
        <omgdi:waypoint x="560.0" y="660.0"/>
        <omgdi:waypoint x="262.0" y="861.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_17" id="BPMNEdge__17" sourceElement="_5" targetElement="_7">
        <omgdi:waypoint x="256.0" y="217.0"/>
        <omgdi:waypoint x="256.0" y="305.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_39" id="BPMNEdge__39" sourceElement="_32" targetElement="_8">
        <omgdi:waypoint x="460.0" y="586.0"/>
        <omgdi:waypoint x="400.0" y="460.0"/>
        <omgdi:waypoint x="425.0" y="327.5"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_16" id="BPMNEdge__16" sourceElement="_4" targetElement="_5">
        <omgdi:waypoint x="256.0" y="145.0"/>
        <omgdi:waypoint x="256.0" y="185.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_38" id="BPMNEdge__38" sourceElement="_32" targetElement="_11">
        <omgdi:waypoint x="476.0" y="602.0"/>
        <omgdi:waypoint x="476.0" y="660.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_19" id="BPMNEdge__19" sourceElement="_5" targetElement="_6">
        <omgdi:waypoint x="240.0" y="201.0"/>
        <omgdi:waypoint x="60.0" y="230.0"/>
        <omgdi:waypoint x="60.0" y="305.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_18" id="BPMNEdge__18" sourceElement="_5" targetElement="_8">
        <omgdi:waypoint x="272.0" y="201.0"/>
        <omgdi:waypoint x="470.0" y="250.0"/>
        <omgdi:waypoint x="470.0" y="300.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_40" id="BPMNEdge__40" sourceElement="_32" targetElement="_14">
        <omgdi:waypoint x="460.0" y="586.0"/>
        <omgdi:waypoint x="400.0" y="700.0"/>
        <omgdi:waypoint x="262.0" y="861.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_20" id="BPMNEdge__20" sourceElement="_6" targetElement="_3">
        <omgdi:waypoint x="56.0" y="360.0"/>
        <omgdi:waypoint x="56.0" y="395.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_42" id="BPMNEdge__42" sourceElement="_33" targetElement="_14">
        <omgdi:waypoint x="475.0" y="786.0"/>
        <omgdi:waypoint x="475.0" y="830.0"/>
        <omgdi:waypoint x="262.0" y="861.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_41" id="BPMNEdge__41" sourceElement="_11" targetElement="_33">
        <omgdi:waypoint x="476.0" y="715.0"/>
        <omgdi:waypoint x="476.0" y="755.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_22" id="BPMNEdge__22" sourceElement="_3" targetElement="_4">
        <omgdi:waypoint x="40.0" y="411.0"/>
        <omgdi:waypoint x="5.0" y="260.0"/>
        <omgdi:waypoint x="210.0" y="117.5"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_21" id="BPMNEdge__21" sourceElement="_3" targetElement="_14">
        <omgdi:waypoint x="55.0" y="426.0"/>
        <omgdi:waypoint x="55.0" y="640.0"/>
        <omgdi:waypoint x="230.0" y="861.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_43" id="BPMNEdge__43" sourceElement="_33" targetElement="_10">
        <omgdi:waypoint x="492.0" y="771.0"/>
        <omgdi:waypoint x="535.0" y="645.0"/>
        <omgdi:waypoint x="515.0" y="497.5"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_24" id="BPMNEdge__24" sourceElement="_12" targetElement="_9">
        <omgdi:waypoint x="251.0" y="422.0"/>
        <omgdi:waypoint x="251.0" y="470.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_23" id="BPMNEdge__23" sourceElement="_7" targetElement="_12">
        <omgdi:waypoint x="251.0" y="360.0"/>
        <omgdi:waypoint x="251.0" y="390.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_26" id="BPMNEdge__26" sourceElement="_12" targetElement="_14">
        <omgdi:waypoint x="235.0" y="406.0"/>
        <omgdi:waypoint x="160.0" y="570.0"/>
        <omgdi:waypoint x="230.0" y="861.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_25" id="BPMNEdge__25" sourceElement="_12" targetElement="_4">
        <omgdi:waypoint x="235.0" y="406.0"/>
        <omgdi:waypoint x="160.0" y="265.0"/>
        <omgdi:waypoint x="210.0" y="117.5"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_28" id="BPMNEdge__28" sourceElement="_13" targetElement="_14">
        <omgdi:waypoint x="248.5" y="599.5"/>
        <omgdi:waypoint x="248.5" y="845.1965193707209"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_27" id="BPMNEdge__27" sourceElement="_9" targetElement="_13">
        <omgdi:waypoint x="251.0" y="525.0"/>
        <omgdi:waypoint x="251.0" y="570.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_29" id="BPMNEdge__29" sourceElement="_31" targetElement="_4">
        <omgdi:waypoint x="487.0" y="406.0"/>
        <omgdi:waypoint x="560.0" y="310.0"/>
        <omgdi:waypoint x="295.0" y="117.5"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_30" id="BPMNEdge__30" sourceElement="_13" targetElement="_7">
        <omgdi:waypoint x="267.0" y="586.0"/>
        <omgdi:waypoint x="320.0" y="470.0"/>
        <omgdi:waypoint x="295.0" y="332.5"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
