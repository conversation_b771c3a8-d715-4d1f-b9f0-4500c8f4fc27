<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:tns="http://www.activiti.org/test" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" expressionLanguage="http://www.w3.org/1999/XPath" id="m1664346686750" name="" targetNamespace="http://www.activiti.org/test" typeLanguage="http://www.w3.org/2001/XMLSchema">
  <process id="hotel_order_v1" isClosed="false" isExecutable="true" name="航延住宿保障单审核" processType="None">
    <startEvent id="_2" name="StartEvent"/>
    <userTask activiti:assignee="${assignee}" activiti:exclusive="true" id="submitter" name="提交"/>
    <sequenceFlow id="_6" sourceRef="_2" targetRef="submitter"/>
    <userTask activiti:candidateUsers="roleId:航延酒店AOC审核" activiti:exclusive="true" id="common" name="AOC审核"/>
    <exclusiveGateway gatewayDirection="Unspecified" id="_10" name="ExclusiveGateway"/>
    <endEvent id="end" name="EndEvent"/>
    <sequenceFlow id="_3" name="提交" sourceRef="submitter" targetRef="common">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[
        ]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_5" sourceRef="common" targetRef="_10"/>
    <sequenceFlow id="_7" name="同意或不同意" sourceRef="_10" targetRef="end">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${optionCode == 'AGREE' || optionCode == 'REJECT'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_8" name="驳回" sourceRef="_10" targetRef="submitter">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${optionCode == 'BACK'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram documentation="background=#3C3F41;count=1;horizontalcount=1;orientation=0;width=842.4;height=1195.2;imageableWidth=832.4;imageableHeight=1185.2;imageableX=5.0;imageableY=5.0" id="Diagram-_1" name="New Diagram">
    <bpmndi:BPMNPlane bpmnElement="hotel_order_v1">
      <bpmndi:BPMNShape bpmnElement="_2" id="Shape-_2">
        <omgdc:Bounds height="32.0" width="32.0" x="175.0" y="45.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="submitter" id="Shape-submitter">
        <omgdc:Bounds height="55.0" width="85.0" x="150.0" y="155.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="common" id="Shape-common">
        <omgdc:Bounds height="55.0" width="85.0" x="150.0" y="250.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_10" id="Shape-_10" isMarkerVisible="false">
        <omgdc:Bounds height="32.0" width="32.0" x="175.0" y="345.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="end" id="Shape-end">
        <omgdc:Bounds height="32.0" width="32.0" x="175.0" y="435.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="_3" id="BPMNEdge__3" sourceElement="submitter" targetElement="common">
        <omgdi:waypoint x="192.5" y="210.0"/>
        <omgdi:waypoint x="192.5" y="250.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_5" id="BPMNEdge__5" sourceElement="common" targetElement="_10">
        <omgdi:waypoint x="191.0" y="305.0"/>
        <omgdi:waypoint x="191.0" y="345.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_6" id="BPMNEdge__6" sourceElement="_2" targetElement="submitter">
        <omgdi:waypoint x="191.0" y="77.0"/>
        <omgdi:waypoint x="191.0" y="155.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_7" id="BPMNEdge__7" sourceElement="_10" targetElement="end">
        <omgdi:waypoint x="191.0" y="377.0"/>
        <omgdi:waypoint x="191.0" y="435.0"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_8" id="BPMNEdge__8" sourceElement="_10" targetElement="submitter">
        <omgdi:waypoint x="207.0" y="361.0"/>
        <omgdi:waypoint x="315.0" y="265.0"/>
        <omgdi:waypoint x="235.0" y="182.5"/>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
