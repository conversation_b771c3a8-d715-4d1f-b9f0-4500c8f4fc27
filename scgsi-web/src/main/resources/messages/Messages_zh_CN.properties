#0000-0099****代表系统级别提示
#1000-9999****代表基础模块的提示
#################################系统操作提示#################################
0=操作成功
-1=操作失败
0000=系统异常，请联系管理员
0001=操作未授权
0002=会话过期
0003=参数异常!
0004=请重新登陆!
0009=短期内多次发起请求,请稍后再试!

##################################部门模块####################################
1121=部门信息为空
1122=资源为空
1123=当前角色类型至多绑定一个用户
1124=请先解绑该角色下的用户
1125=选择的用户必须为当前角色部门及其子部门下
1126=当前选择的用户已拥有一个管理员身份
####################################################################

#################################通用异常提示 #################################
#messagecode没有记录，未使用？
0005=系统基础配置信息为空 
#messagecode没有记录，未使用？
0006=通讯错误
#messagecode没有记录，未使用？
0007=数据不存在
0008=请求参数{0}为空!
#############################################################################


#################################用户模块#################################
100100=验证码已过期，请重新获取！
100101=验证码不匹配，请重新输入！
100102=用户已被锁定，请联系系统管理员进行解锁！
100103=账号或密码错误，请重新输入！
100104=该账号还未启用，请联系系统管理员分配账号权限！
100105=系统密码错误（该密码为本系统密码，非门户网密码），请重新输入。一天连续输错密码五次账号将被锁定，请谨慎输入（当前连续输错{0}次）
100106=当前密码为初始密码，请立即修改密码！
100107=旧密码错误
100108=单点登录验证失败
100109=单点登录验证获取数据为空
100110=账号或手机号或验证码错误，请重新输入。
100111=输入参数为空，请检查参数。
100112=帐号错误，请重新输入！
100113=用户管理员角色超过规定数量！
100114=请先选择节点进行资源添加!
100115=管理员角色已使用，请重新选择！
100116=验证码推送失败，请稍后再试！
100117=验证码已推送过,如未收到请间隔{0}秒后再次点击获取！
100118=客户端发送密码找回短信已超过阀值,请稍后再试！
100119=每个自然日仅允许手动同步{0}次，您已超出今日手动同步次数！
100122=增量同步用户数据还在进行中请稍后再试！
100123=新旧密码不能一样！
#############################################################################

#################################quartzJob模块#################################
100150=创建定时任务失败:任务名称:{0}
100151=更新定时任务失败:任务名称:{0}
100152=删除定时任务失败:任务名称:{0}
100153=恢复定时任务失败:任务名称:{0}
100154=定时任务执行失败:任务名称:{0}
100155=定时任务暂停失败:任务名称:{0}
#############################################################################

#################################部门模块#################################
100120=新增部门失败！失败原因：{0}
100121=新增部门失败！失败原因：{0}

#############################################################################

#################################机构模块#################################
100130=新增公司失败！失败原因：{0}
100131=新增部门失败！失败原因：{0}

#############################################################################

#################################Excel报错#################################
100200=导入文件不存在！
100201=导入失败: 文件大小不能超过{0}M！
#############################################################################

#################################短信模块#################################
100400=没有查询到该短信记录，请联系管理员！
100401=短信模板主键为空，请检查输入参数！
100402=该短信模板已经存在，请核验后再操作！
100403=该类型短信模板，不存在，请配置！
100404=该电话号码当日发送验证码次数已达到上限，请联系管理员！
#############################################################################

#################################H5超售模块#################################
100500=超售赔偿单赔偿金额不能为小于1！
100501=超售旅客id查询无数据！
100502=超售赔偿金额前后端计算不一致，请核对！
100503=超售列表数据详情查询-改签航班时差计算，异常！
100504=超售改签航班时差，查无对应规则！请联系管理员配置！
100505=超售退票查无对应规则！请联系管理员配置！
100506=改签航班计划起飞时间不能为空！
100507=验证失败，非本航站出发航班，请核对后再输入！
100508=验证失败，只能由发起人进行发放操作！
100509=服务航站[{0}]—查询无对应航站数据!
#################################################################

#################################文件操作#################################
100600=文件上传失败！
100601=文件不存在！
100602=文件下载失败！
100603=上传文件失败: 文件大小不能超过{0}M！
100604=上传文件类型不符合规范,请重新选择文件上传{0}！
#############################################################################

#################################审核模块#################################
100700=审核-用户再次发起-赔偿单审核处理异常！
100701=审核-赔偿单审核发起流程，异常! 赔偿单id:{0}！
100702=审核-发起审核-当前orderId已发起过审核流程，用户不是流程发起人不能发起审核,异常! 赔偿单id:{0} 发起人：{1}
100703=审核-taskId不能为空！
100704=审核-赔偿单审核提交数据AOC角色审批||发起，HandleUser不能为空！
100705=审核-发起推送信息，接收人为null！
100706=赔偿单查询无数据，赔偿单号：{0}
100707=查询数据-服务航站不能为空
100708=赔偿单已审核，不可再次审核！
100709=审核人员选择错误，不能选择用户本人:{0}！
100710=审核人员字段解密错误!
100711=审核消息发送失败！
########################################################################

#################################三方支付#################################
100800=授权code不能为空！
100801=授权state不能为空！
100802=授权openid不能为空！
########################################################################


#################################表格设置#################################
101000=未获取到当前登录用户信息，请联系管理员！
########################################################################


#################################WEB服务补偿#################################
101900=勾选结案的数据存在非本航站数据！
101901=补偿单已经提交成功，请勿重复提交，请刷新后查看!
101902=未从鲁雁管家(TRACE接口)获取到旅客数据，请联系管理员

########################################################################

#################################WEB航延住宿#################################
103000=航延住宿保障单保存异常！
103001=航延住宿保障单保存获取redis锁失败！
103002=航延住宿审核任务不存在！
103003=航延住宿审核business业务标记不能为空！
103004=航延住宿保障单审核获取redis锁失败！
103005=航延住宿审核businessKey不能为空！
103006=航延住宿订单不存在！
103007=该审批任务已被处理!
103008=报表导出失败！
103021=结算审批单不存在
103022=结算单不存在
103023=审批单不存在
103024=结算单当前状态不支持该操作
103025=航延住宿单保存异常！
103026=保障单提交失败
103027=保障单状态不支持当前操作
########################################################################