spring:
  boot:
    admin:
      client:
        url: http://localhost:8769
  application:
    name: admin-server
  datasource: #jdbc:oracle:thin:@<host>:<port>:<SID>
    # 这个206是我做的代理地址。代理到测试库
    url: *********************************************
    # 这个是dev开发库
    #url: ******************************************
    username: <PERSON><PERSON>(0wkg3fCkekM5jwZpEOCyIw==)
    password: ENC(EwdYwq2B7OlQuiQNPhDF7CSikfiq0Ese)
    driver-class-name: oracle.jdbc.driver.OracleDriver
    type: com.alibaba.druid.pool.DruidDataSource
    #最大活跃数
    maxActive: 20
    #初始化数量
    initialSize: 1
    #最大连接等待超时时间
    maxWait: 60000
    #打开PSCache，并且指定每个连接PSCache的大小
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    #通过connectionProperties属性来打开mergeSql功能；慢SQL记录
    #connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    minIdle: 1
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: select 1 from dual
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
  #JPA设置
  jpa:
    show-sql: true
    open-in-view: false
    properties:
      hibernate:
        hbm2ddl.auto: none
        dialect: org.hibernate.dialect.Oracle10gDialect
        format_sql: true
        temp:
          use_jdbc_metadata_defaults: false
  #文件大小设置
  servlet:
    multipart:
      #上传文件总的最大值
      max-request-size: 40MB
      #单个文件的最大值
      max-file-size: 20MB
      #文件上传临时文件夹
      location: /data/upload_tmp
  #redis
  redis:
    cluster:
      type: redission  #jedis redission
      nodes:
         - ************:7001
         - ************:7002
         - ************:7003
         - ************:7004
         - ************:7005
         - ************:7006
      password: ENC(LwYkqIag0uaNRDpBYqNg4gx4wo0lCtoi)
      maxRedirects: 3
      poolConfig:
        max-total: 8
        max-idle: 8
        max-wait-millis: -1
        min-idle: 2
      pingConnectionInterval: 1000
#==================activiti========
  activiti:
    database-schema-update: false
    check-process-definitions: false
#===============国际化============== 
international:
  message:
    #internationalization profiles
    base_names: [messages/Messages_en_US, messages/Messages_zh_CN, messages/Messages_zh_TW]
#===============FTP=================
ftp:
  ip: ************
  port: 21
  username: ftpuser
  password: ENC(TkmVyYbKDsBie427jYgR6Ju3ycTt89m/)
#==============文件保存路径(文件大小单位为字节)==========
upload-file:
  remote-path: /upload/official
  remote-path-temp: /upload/temp
  file-max-size: 4194304
  file-suffix: bmp,jpg,jpeg,png,tif,gif,pcx,tga,exif,fpx,svg,psd,cdr,pcd,dxf,ufo,eps,ai,raw,WMF,webp
#============对称加密密钥============  
aesencrypt:
   # key scgsi#2020swcares$scgsi#2020swcares 密钥必须32位以上，进行base64加密后放入secretkey
   secretKey: c2Nnc2kjMjAyMHN3Y2FyZXMkc2Nnc2kjMjAyMHN3Y2FyZXM=
#============人力接口的配置============  
hum:
  inits: false
  departmentresources:
    url: http://************/hum/?wsdl
    authCode: 05e16459-4991-44e4-8d60-e4aa77c157d4
    userWsdl:
      userName: rams
      password: ENC(faEH3v8AkcYlf/biV3kXvMiY+UunpVofeQFMdSvHsO4=)
    map: {pageQueryOrgan: "pageQueryOrgan"}
  employeeresources:
    url: http://************/hum/?wsdl
    authCode: 05e16459-4991-44e4-8d60-e4aa77c157d4
    userWsdl:
      userName: rams
      password: ENC(faEH3v8AkcYlf/biV3kXvMiY+UunpVofeQFMdSvHsO4=)
    map: {pageQueryUser: "pageQueryUser"}
#============单点登录验证token接口的配置============  
authtokensources:
   # 人力接口地址
   authTokenUrl: http://************/login/?wsdl
   authTokenInterfaceName: ssoTicketValidate
   userWsdl:
      userName: ramstest
      password: ENC(Fhpoahz7TzKuQte19xzSeP1WrstF50T9)
   #H5应用ID
   appId: EPortal
   #H5应用KEY
   appKey: 0647513c-88f1-46c9-b764-b38e19f0e4e6
   #H5地址
   appUrl: https://mp.sda.cn/v4/lifeCycle/verificationToken?token=%s
#==========swagger启用禁用配置===================
swagger:
    enable: true
#==========数据库加解密密钥==============
jasypt:
  encryptor:
    password: EbfYkitulv73I2p0mXI50JMXoaxZTKJ7
pay:
   allowNode: ************,192.168.30.69
   wx:
     appid: zBj+sFPp2xeS9lOouozhgG1FrDWT+x5AdQtyEUjC+JU=
     appSecret: D3gkxH9FPxcbY9m9TYvC9nsY1PPSfarTm+lNXbKYJ3MwOJlaWM4Zh0FccB03fMcv

#==============admin监控配置==================    
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
   #================web单点重定向地址===================
SSO:
  redirectUrl: http://192.168.12.39:8000/user/login
#==============sms短信配置==================
sms:
  url: http://************/sms/?wsdl
  username: gssp
  password: ENC(6adgMPhQMxEZp4utRDOHx6d+EhDYhsP+)
  clientId: cn.sda.marketing.gssp
  operationCode: cn.sda.management.scsms.SMSService.submit
#==========登录验证码启用禁用配置===================
verifyCode:
    enable: false
#=============trace接口信息===================
trace:
  mothod: getPsgListByFilght
  appkey: ENC(Osarx9Y8B5IVWO2/2ed37N90A/vKMEa2)
  cont:
    url: http://************:80/getPsgListByFilght
    sysid: 2011
    subchannelcode: 2011001
  idx:
    url: http://************:80/getPsgByIdx
    sysid: 2012
    subchannelcode: 2012001
#========== 获取foc的接口路径，通过nginx进行转发===========
foc:
  url: http://************/foc/
#===================访问频率限制===================
firewall:
  minuteUser:
    frequentlyAccessDuration: 60
    astrictTime: 300
    frequentlyAccessThreshhold: 20
  minuteOpenId:
    frequentlyAccessDuration: 60
    astrictTime: 300
    frequentlyAccessThreshhold: 5
  minuteIp:
    frequentlyAccessDuration: 60
    astrictTime: 300
    frequentlyAccessThreshhold: 10
  dayIp:
    frequentlyAccessDuration: 86400
    astrictTime: 300
    frequentlyAccessThreshhold: 30
  dayOpenId:
    frequentlyAccessDuration: 86400
    astrictTime: 300
    frequentlyAccessThreshhold: 10
#========== foc接口异常短信预警===========
alarm:
  mobile: 18628218225,15208235547