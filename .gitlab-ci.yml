image: maven:3-eclipse-temurin-17

variables:
  SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
  GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task

stages:
  - build
  - sonarqube-check
  - sonarqube-vulnerability-report

build:
  stage: build
  script:
    - mvn clean install -DskipTests
  artifacts:
    paths:
      - target/
    expire_in: 1 hour
sonarqube-check:
  stage: sonarqube-check
  dependencies:
    - build
  cache:
    policy: pull
    key: "${CI_COMMIT_SHORT_SHA}"
    paths:
      - sonar-scanner/
  script:
    - mvn verify sonar:sonar -Dsonar.host.url=http://**************:9000 -Dsonar.token=sqp_b55e1b68d3867efb6c934a80cb539972254d60da
  allow_failure: true
  rules:
    - when: always
sonarqube-vulnerability-report:
  stage: sonarqube-vulnerability-report
  script:
    - 'curl -u "${scgsi_sonar}:" "http://**************:9000/api/issues/gitlab_sast_export?projectKey=2025-IPRD-O-0002&branch=${CI_COMMIT_BRANCH}&pullRequest=${CI_MERGE_REQUEST_IID}" -o gl-sast-sonar-report.json'
  allow_failure: true
  rules:
    - when: always
  artifacts:
    expire_in: 1 day
    reports:
      sast: gl-sast-sonar-report.json