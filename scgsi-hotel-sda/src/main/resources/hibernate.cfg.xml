<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-configuration PUBLIC
    "-//Hibernate/Hibernate Configuration DTD//EN"
    "http://www.hibernate.org/dtd/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
  <session-factory>
    <property name="connection.url">********************************************</property>
    <property name="connection.driver_class">oracle.jdbc.OracleDriver</property>
    <property name="gsspsc"/>
    <property name="SDAzhbzpt~"/>

      <!-- DB schema will be updated if needed -->
<!--     <property name="hibernate.hbm2ddl.auto">update</property>-->
    <mapping class="com.swcares.sdahotel.entity.CityCode"/>
    <mapping class="com.swcares.sdahotel.entity.FdHotelFlightInfo"/>
    <mapping class="com.swcares.sdahotel.entity.FdHotelMiddleInfo"/>
    <mapping class="com.swcares.sdahotel.entity.FdHotelOrderInfo"/>
    <mapping class="com.swcares.sdahotel.entity.FdHotelPaxInfo"/>
    <mapping class="com.swcares.sdahotel.entity.FdHotelSettlementInfo"/>
    <mapping class="com.swcares.sdahotel.entity.FdHotelAccommodation"/>
    <mapping class="com.swcares.sdahotel.entity.FdHotelPssnInfo"/>
    <mapping class="com.swcares.sdahotel.entity.FdHotelRoomInfo"/>
    <mapping class="com.swcares.sdahotel.entity.FdHotelUserInfo"/>
    <mapping class="com.swcares.sdahotel.entity.FdHotelInfo"/>
      <mapping class="com.swcares.sdahotel.entity.FdHotelPostEvalution"/>
  </session-factory>
</hibernate-configuration>
