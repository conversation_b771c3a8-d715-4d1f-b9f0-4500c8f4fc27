package com.swcares.sdahotel.common.global;/**
 * 作者：徐士昊
 * 时间：2022/07/11 10:55
 **/


import com.alibaba.fastjson.JSON;
import com.swcares.sdahotel.common.context.ParamContext;
import com.swcares.sdahotel.common.exception.EncryptAndDecryptException;
import com.swcares.sdahotel.common.kit.AESUtil;
import com.swcares.sdahotel.common.kit.JSONUtils;
import com.swcares.sdahotel.common.kit.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

/**
 * <AUTHOR>
 * Date on 2022/07/11  10:55
 * 请求解密
 */

@Slf4j
public class MyHttpInputMessage implements HttpInputMessage {
    private HttpHeaders headers;

    private InputStream body;


    public MyHttpInputMessage(HttpInputMessage httpInputMessage) throws IOException {
        this.headers = httpInputMessage.getHeaders();
        InputStream body = httpInputMessage.getBody();
        this.body = IOUtils.toInputStream(easpString(IOUtils.toString(httpInputMessage.getBody(), "UTF-8")));

    }

    @Override
    public InputStream getBody() throws IOException {
        return body;
    }

    @Override
    public HttpHeaders getHeaders() {
        return headers;
    }


    /**
     * @param requestData
     * @return
     */
    public String easpString(String requestData) {
        log.info("前端传递过来的数据：" + requestData);
        if (requestData != null && !requestData.equals("")) {
            String s = "params";
            if (!requestData.contains(s)) {
                throw new RuntimeException("缺失params参数");
            } else {
                try {
                    Map<String, Object> map = JSON.parseObject(requestData);
                    String params = (String) map.get("params");
                    String aeskey = AESUtil.getAesKey().get("aesKey");
                    String aesiv = AESUtil.getAesKey().get("aesIv");
                    String decodeParams = StringUtil.rtrim(StringUtil.ltrim(AESUtil.desEncrypt(params, aeskey, aesiv), '"'), '"');
                    //放到全局上下文中
                    ParamContext.setParams(decodeParams);
                    //预处理请求中的数据，重新设置params字段
                    map.put("params", JSON.parseObject(decodeParams));
                    return JSONUtils.map2json(map);
                } catch (Exception e) {
                    log.error("解密时报错了：" + e);
                    throw new EncryptAndDecryptException("解密时报错，请检查生成加密报文的参数");
                }
            }
        }
        return "";
    }
}
