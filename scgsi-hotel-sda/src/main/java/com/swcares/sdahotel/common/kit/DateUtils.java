package com.swcares.sdahotel.common.kit;


import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.validator.routines.DateValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期工具类, 继承org.apache.commons.lang.time.DateUtils类
 *
 * <AUTHOR>
 * @version 2014-4-15
 */
public class DateUtils {
	public static final Logger logger = LoggerFactory.getLogger(DateUtils.class);

	public static final String YYYY_MM_DD = "yyyy-MM-dd";

	/**
	 * 得到当前日期字符串 格式（yyyy-MM-dd）
	 */
	public static String getDate() {
		return getDate("yyyy-MM-dd");
	}

	/**
	 * 得到当前日期字符串 传入的日期格式例如：（yyyy-MM-dd）||（yyyyMMdd）
	 */
	public static String getDate(String pattern) {
		return DateFormatUtils.format(new Date(), pattern);
	}

	/**
	 * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
	 */
	public static String formatDate(Date date, Object... pattern) {
		String formatDate = null;
		if (pattern != null && pattern.length > 0) {
			formatDate = DateFormatUtils.format(date, pattern[0].toString());
		} else {
			formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
		}
		return formatDate;
	}

	public static final String getDateTime(String aMask, Date aDate) {
		SimpleDateFormat df = null;
		String returnValue = "";

		if (aDate == null) {
			logger.error("aDate is null!");
		} else {
			df = new SimpleDateFormat(aMask);
			returnValue = df.format(aDate);
		}

		return (returnValue);
	}

	/**
	 * 得到日期时间字符串，转换格式（yyyy-MM-dd HH:mm:ss）
	 */
	public static String formatDateTime(Date date) {
		return formatDate(date, "yyyy-MM-dd HH:mm:ss");
	}

	/**
	 * 得到当前时间字符串 格式（HH:mm:ss）
	 */
	public static String getTime() {
		return formatDate(new Date(), "HH:mm:ss");
	}

	/**
	 * 得到当前日期和时间字符串 格式（yyyy-MM-dd HH:mm:ss）
	 */
	public static String getDateTime() {
		return formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
	}

	/**
	 * 得到当前年份字符串 格式（yyyy）
	 */
	public static String getYear() {
		return formatDate(new Date(), "yyyy");
	}

	/**
	 * 得到当前月份字符串 格式（MM）
	 */
	public static String getMonth() {
		return formatDate(new Date(), "MM");
	}

	/**
	 * 得到当天字符串 格式（dd）
	 */
	public static String getDay() {
		return formatDate(new Date(), "dd");
	}

	/**
	 * 得到当前星期字符串 格式（E）星期几
	 */
	public static String getWeek() {
		return formatDate(new Date(), "E");
	}

	/**
	 * 获取过去的天数
	 *
	 * @param date
	 * @return
	 */
	public static long pastDays(Date date) {
		long t = new Date().getTime() - date.getTime();
		return t / (24 * 60 * 60 * 1000);
	}

	/**
	 * 获取过去的小时
	 *
	 * @param date
	 * @return
	 */
	public static long pastHour(Date date) {
		long t = DateUtil.getStandardTime().getTime() - date.getTime();
		return t / (60 * 60 * 1000);
	}

	/**
	 * 获取过去的分钟
	 *
	 * @param date
	 * @return
	 */
	public static long pastMinutes(Date date) {
		long t = DateUtil.getStandardTime().getTime() - date.getTime();
		return t / (60 * 1000);
	}

	/**
	 * 转换为时间（天,时:分:秒.毫秒）
	 *
	 * @param timeMillis
	 * @return
	 */
	public static String formatDateTime(long timeMillis) {
		long day = timeMillis / (24 * 60 * 60 * 1000);
		long hour = (timeMillis / (60 * 60 * 1000) - day * 24);
		long min = ((timeMillis / (60 * 1000)) - day * 24 * 60 - hour * 60);
		long s = (timeMillis / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
		long sss = (timeMillis - day * 24 * 60 * 60 * 1000 - hour * 60 * 60 * 1000 - min * 60 * 1000 - s * 1000);
		return (day > 0 ? day + "," : "") + hour + ":" + min + ":" + s + "." + sss;
	}

	/**
	 * 获取两个日期之间的天数
	 *
	 * @param before
	 * @param after
	 * @return
	 */
	public static double getDistanceOfTwoDate(Date before, Date after) {
		long beforeTime = before.getTime();
		long afterTime = after.getTime();
		return (afterTime - beforeTime) / (1000 * 60 * 60 * 24);
	}

	/**
	 * 判断给定日期是否为月末的一天
	 *
	 * @param date
	 * @return true:是|false:不是
	 */
	public static boolean isLastDayOfMonth(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DATE, (calendar.get(Calendar.DATE) + 1));
		if (calendar.get(Calendar.DAY_OF_MONTH) == 1) {
			return true;
		}
		return false;
	}

	/**
	 * 获取下个月的日期格式
	 *
	 * @param date
	 * @return
	 */
	public static String getNextMonth(Date date, String parr) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH, 1);
		SimpleDateFormat format = new SimpleDateFormat(parr);
		String result = format.format(calendar.getTime());
		return result;
	}

	/**
	 * 根据字符串得到日期
	 *
	 * @param s
	 * @param style
	 * @return
	 */
	public static Date parseToDate(String s, String style) {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat();
		simpleDateFormat.applyPattern(style);
		Date date = null;
		if (s == null || s.length() < 8) {
			return null;
		}
		try {
			date = simpleDateFormat.parse(s);
		} catch (ParseException ex) {
			logger.error("String 转换Date类型异常", ex);
		}
		return date;
	}

	/**
	 * 根据传入的日期和当前日期比较，得到分钟
	 *
	 * @param endDate
	 * @param nowDate
	 * @return
	 */
	public static long getMinutesFormDate(Date endDate, Date nowDate) {

		// long nd = 1000 * 24 * 60 * 60;
		// long nh = 1000 * 60 * 60;
		long nm = 1000 * 60;
		// long ns = 1000;
		// 获得两个时间的毫秒时间差异
		long diff = endDate.getTime() - nowDate.getTime();
		// 计算差多少天
		// long day = diff / nd;
		// 计算差多少小时
		// long hour = diff % nd / nh;
		// 计算差多少分钟 long min = diff % nd % nh / nm;
		long min = diff / nm;
		// 计算差多少秒//输出结果
		// long sec = diff % nd % nh % nm / ns;
		return min;
	}

	/**
	 * 验证日期格式yyyyMMdd
	 *
	 * @param value
	 * @return 通过返回true
	 */
	public static boolean isValid(String value) {
		return DateValidator.getInstance().isValid(value, "yyyyMMdd");
	}

	/**
	 * 验证日期格式yyyyMMdd
	 *
	 * @param value
	 * @return 不通过返回true
	 */
	public static boolean isNotValid(String value) {
		return !isValid(value);
	}

	/****
	 * 传入具体日期 ，返回具体日期减天数。
	 *
	 * @param date 日期(2014-04-20)
	 * @return 2014-03-20
	 * @throws ParseException
	 */
	public static String getSubDay(Date date, int day) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		// Date dt = sdf.parse(date);
		Calendar rightNow = Calendar.getInstance();
		rightNow.setTime(date);
		rightNow.add(Calendar.DAY_OF_MONTH, day);
		Date dt1 = rightNow.getTime();
		String reStr = sdf.format(dt1);
		return reStr;
	}

	/****
	 * 传入具体日期 ，以及传入的日期格式，返回具体日期减天数。
	 *
	 * @param date 日期(2014-04-20)
	 * @return 2014-03-20
	 * @throws ParseException
	 */
	public static String getSubDay(Date date, int day, String pattern) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
		// Date dt = sdf.parse(date);
		Calendar rightNow = Calendar.getInstance();
		rightNow.setTime(date);
		rightNow.add(Calendar.DAY_OF_MONTH, day);
		Date dt1 = rightNow.getTime();
		String reStr = sdf.format(dt1);
		return reStr;
	}

	/****
	 * 传入具体日期 ，返回具体日期减月数。
	 *
	 * @param date 日期(2014-04-20)
	 * @return 2014-03-20
	 * @throws ParseException
	 */
	public static String getSubMonth(Date date, int month, String pattern) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
		Calendar rightNow = Calendar.getInstance();
		rightNow.setTime(date);
		rightNow.add(Calendar.MONTH, month);
		Date dt1 = rightNow.getTime();
		String reStr = sdf.format(dt1);
		return reStr;
	}

	/**
	 * 判断两个日期大小
	 *
	 * @param beginDate
	 * @param endDate
	 * @return 前者大于后者返回true 反之false
	 * @throws Exception
	 */
	public static boolean compareDate(Date beginDate, Date endDate) throws Exception {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		Calendar c1 = Calendar.getInstance();
		Calendar c2 = Calendar.getInstance();
		c1.setTime(beginDate);
		c2.setTime(endDate);
		int result = c1.compareTo(c2);
		if (result > 0)
			return true;
		else
			return false;
	}

	/**
	 * 判断两个日期大小
	 *
	 * @param beginDate
	 * @param endDate
	 * @return 前者大于后者返回true 反之false
	 * @throws Exception
	 */
	public static boolean compareDate(Date beginDate, String endDate) throws Exception {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		Calendar c1 = Calendar.getInstance();
		Calendar c2 = Calendar.getInstance();
		c1.setTime(beginDate);
		c2.setTime(sdf.parse(endDate));
		int result = c1.compareTo(c2);
		if (result > 0)
			return true;
		else
			return false;
	}

	/**
	 * 输入毫秒数，转化为日期
	 *
	 * @param time
	 * @return
	 */
	public static Date transLongToDate(long time) {
		/**
		 * 直接用SimpleDateFormat格式化 Date对象，即可得到相应格式的日期 字符串。
		 */
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");// 12小时制
		Date date = new Date();
		date.setTime(time);
		System.out.println(simpleDateFormat.format(date));
		return date;
	}

	/*
	 * 将时间转换为时间戳
	 */
	public static String dateToStamp(String s) throws ParseException {
		String res;
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Date date = simpleDateFormat.parse(s);
		long ts = date.getTime();
		res = String.valueOf(ts);
		return res;
	}

	/**
	 * 判断当前时间是否在[startTime, endTime]区间，注意时间格式要一致
	 *
	 * @param nowTime   当前时间
	 * @param startTime 开始时间
	 * @param endTime   结束时间
	 * @return
	 * <AUTHOR>
	public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
		if (nowTime.getTime() == startTime.getTime() || nowTime.getTime() == endTime.getTime()) {
			return true;
		}

		Calendar date = Calendar.getInstance();
		date.setTime(nowTime);

		Calendar begin = Calendar.getInstance();
		begin.setTime(startTime);

		Calendar end = Calendar.getInstance();
		end.setTime(endTime);

		if (date.after(begin) && date.before(end))
			return true;

		return false;

	}

	/**
	 * 将日期转换成周几
	 *
	 * @param date 格式
	 * @return 返回： 周一,周二,周三,周四,周五,周六,周日,
	 */
	public static String getWeekDay(String date) {
		String result = StringUtils.EMPTY;
		String[] arr = { "周日", "周一", "周二", "周三", "周四", "周五", "周六" };
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Calendar c = Calendar.getInstance();
			c.setTime(sdf.parse(date));
			int week_of_year = c.get(Calendar.DAY_OF_WEEK);
			result = arr[week_of_year - 1];
		} catch (ParseException e) {
			logger.error("日期转换周错误,参数信息：{}，异常信息：{}", date, e);
		}
		return result;
	}

	/**
	 * 获取输入日期后几天的时间
	 *
	 * @param date
	 * @return
	 */
	public static String getNextDay(Date date, Integer n) {
		String nowDate = null;
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DAY_OF_YEAR, n);
		nowDate = format.format(calendar.getTime());
		return nowDate;
	}

	/**
	 * 获取输入日期后几个月的时间
	 *
	 * @param date
	 * @return
	 */
	public static String getNextMonth(Date date, Integer n) {
		String nowDate = null;
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH, n);
		nowDate = format.format(calendar.getTime());
		return nowDate;
	}

	/**
	 * 获取输入日期后几年的时间
	 *
	 * @param date
	 * @return
	 */
	public static String getNextYear(Date date, Integer n) {
		String nowDate = null;
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.YEAR, n);
		nowDate = format.format(calendar.getTime());
		return nowDate;
	}

	/***
	 * <AUTHOR>
	 * @DESCRIPTION:获取传入时间hour小时之前的时间
	 * @params:
	 * @param hour
	 * @return: java.util.Date
	 * @Date: 2019/10/18 8:38
	 * @Modified By:
	*/
	public static Date getTimeByHour(Date date,int hour) {

		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY) - hour);
		//这个是返回字符串
//		return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(calendar.getTime());
		return calendar.getTime();
	}

	/***
	 * 获取传入时间hour小时之后的时间
	 * @param date
	 * @param hour
	 * @return
	 */
	public static Date getTimeByHourAfter(Date date,int hour) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY) + hour);
		return calendar.getTime();
	}

	/**
	 * 根据出生日期计算年龄
	 *
	 * @param birthDay
	 * @return 未来日期返回0
	 * @throws Exception
	 */
	public static int getAge(Date birthDay) throws Exception {

		Calendar cal = Calendar.getInstance();

		if (cal.before(birthDay)) {
			return 0;
		}

		int yearNow = cal.get(Calendar.YEAR);
		int monthNow = cal.get(Calendar.MONTH);
		int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
		cal.setTime(birthDay);

		int yearBirth = cal.get(Calendar.YEAR);
		int monthBirth = cal.get(Calendar.MONTH);
		int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);

		int age = yearNow - yearBirth;

		if (monthNow <= monthBirth) {
			if (monthNow == monthBirth) {
				if (dayOfMonthNow < dayOfMonthBirth) {
					age--;
				}
			} else {
				age--;
			}
		}

		return age;
	}
	/**
	 *计算两个日期中间相差月份
	 * @param date1 <String>
	 * @param date2 <String>
	 * @return int
	 * @throws ParseException
	 */
	public static int getMonthSpace(String date1, String date2) throws ParseException {
		int result = 0;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Calendar start = Calendar.getInstance();
		Calendar end = Calendar.getInstance();
		start.setTime(sdf.parse(date1));
		end.setTime(sdf.parse(date2));
		result = end.get(Calendar.MONTH) - start.get(Calendar.MONTH);
		int month = (end.get(Calendar.YEAR) - start.get(Calendar.YEAR)) * 12;
		return Math.abs(month + result);
	}

	/**
	　* @MethodName: compare
	　* @Description:  时间比较
	  * @param time
	  * @param time2
	  * @return
	　* @return boolean
	　* @author: YCHu
	　* @Note: Nothing more.
	　* @date: 2019年12月19日 下午3:18:12
	 * @throws ParseException
	 */
	public static boolean compare(Date time,String time2) throws ParseException{
		if (time!=null&& StringUtils.isNoneBlank(time2)) {
			String[] strNow = new SimpleDateFormat("yyyy-MM-dd").format(time).toString().split("-");
			Integer month = Integer.parseInt(strNow[1]);
			Integer day = Integer.parseInt(strNow[2]);
			String m = month.toString();
			if (month<10) {
				m ="0"+m;
			}
			String d = day.toString();
			if (day<10) {
				d ="0"+d;
			}
			String[] strings = time2.split("-");
			//String[] strings = new SimpleDateFormat("yyyy-MM-dd").format(time2).toString().split("-");
			Integer moInteger = Integer.parseInt(strings[1]);
			Integer daInteger = Integer.parseInt(strings[2]);
			String mString = moInteger.toString();
			if (moInteger<10) {
				mString ="0"+mString;
			}
			String dString = daInteger.toString();
			if (daInteger<10) {
				dString ="0"+dString;
			}
			time2=strings[0]+"-"+mString+"-"+dString;
			String year = getYear();
			String time1 = year+"-"+m+"-"+d;
			SimpleDateFormat s = new SimpleDateFormat("yyyy-MM-dd");
			int compareTo = s.parse(time1).compareTo(s.parse(time2));
			if (compareTo>0) {
				return true;
			}else {
				return false;
			}
		}
		return false;
	}

	/**
	 * @param args
	 * @throws ParseException
	 */
	/*
	 * public static void main(String[] args) throws ParseException { long a =
	 * DateUtils.getMinutesFormDate(new
	 * Date(),DateUtils.parseToDate("2019-06-11 21:12:00", "yyyy-MM-dd HH:mm:ss"));
	 * System.out.println(a); }
	 */
	public static void main(String[] args) {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date parse = null;
		try {
			parse = simpleDateFormat.parse("2019-12-06 14:45:50");
		} catch (ParseException e) {
			e.printStackTrace();
		}
		assert parse != null;
		long l = pastMinutes(parse);
		System.out.println(l);
	}
	/**
	 * 将String日期转换为String 返回格式为：yyyy年MM月dd日，如2010年12月14日
	 *
	 * @param time
	 * @return
	 */
	public static String DateToStringCn(String time) throws ParseException {
		if (StringUtils.isEmpty(time)) {
			return "";
		} else {
			time = time.trim().substring(0,10);
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Date date = sdf.parse(time);
		SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy年MM月dd日");
		return sdf1.format(date);
	}

	/**
	　* @MethodName: getYear
	　* @Description: 根据日期获取年字符串
	  * @param time
	  * @return
	　* @return String
	　* @author: YCHu
	　* @Note: Nothing more.
	　* @date: 2020年6月2日 上午10:12:16
	 */
	public static String getYear(Date time) {
		if (time!=null) {
			String[] strNow = new SimpleDateFormat("yyyy-MM-dd").format(time).toString().split("-");
			Integer year = Integer.parseInt(strNow[0]);
			String y = year.toString();
			if (year<10) {
				y ="0"+y;
			}
			return y;
		}
		return null;
	}

	/**
	　* @MethodName: getMonth
	　* @Description: 根据日期获取月字符串
	  * @param time
	  * @return
	　* @return String
	　* @author: YCHu
	　* @Note: Nothing more.
	　* @date: 2020年6月2日 上午9:23:17
	 */
	public static String getMonth(Date time) {
		if (time!=null) {
			String[] strNow = new SimpleDateFormat("yyyy-MM-dd").format(time).toString().split("-");
			Integer month = Integer.parseInt(strNow[1]);
			String m = month.toString();
			if (month<10) {
				m ="0"+m;
			}
			return m;
		}
		return null;
	}

	/**
	　* @MethodName: getDay
	　* @Description: 根据日期获取日字符串
	  * @param time
	  * @return
	　* @return String
	　* @author: YCHu
	　* @Note: Nothing more.
	　* @date: 2020年6月2日 上午9:25:19
	 */
	public static String getDay(Date time) {
		if (time!=null) {
			String[] strNow = new SimpleDateFormat("yyyy-MM-dd").format(time).toString().split("-");
			Integer day = Integer.parseInt(strNow[2]);
			String d = day.toString();
			if (day<10) {
				d ="0"+d;
			}
			return d;
		}
		return null;
	}

	/**
	　* @MethodName: getBeforeDay
	　* @Description: 获取日期的前一天
	  * @param time
	  * @return
	　* @return String
	　* @author: YCHu
	　* @Note: Nothing more.
	　* @date: 2020年6月2日 上午9:39:10
	 */
	public static String getBeforeDay(String time,int n) {
		Calendar c = Calendar.getInstance();
		Date date = null;
        try {
            date = new SimpleDateFormat("yy-MM-dd").parse(time);
        } catch (ParseException e) {
            logger.error("转换出错:{}");
        }
		assert date != null;
		c.setTime(date);
        int day1 = c.get(Calendar.DATE);
        c.set(Calendar.DATE, day1 - 1);
        String dayBefore = new SimpleDateFormat("yyyy-MM-dd").format(c.getTime());
		return dayBefore;
	}

	/**
	　* @MethodName: getAfterDay
	　* @Description: 获取日期的后几天
	  * @param time
	  * @return
	　* @return String
	　* @author: YCHu
	　* @Note: Nothing more.
	　* @date: 2020年6月2日 上午9:46:26
	 */
	public static String getAfterDay(Date time,int n) {
		Calendar c = Calendar.getInstance();
		c.setTime(time);
	    int day1 = c.get(Calendar.DATE);
	    c.set(Calendar.DATE, day1 + 1);
	    String dayAfter = new SimpleDateFormat("dd").format(c.getTime());
		return dayAfter;
	}

	public static String getBeforeYear(Date time) {
		Calendar c = Calendar.getInstance();
		SimpleDateFormat format = new SimpleDateFormat("yyyy");
		c.setTime(time);
		c.add(Calendar.YEAR, -1);
		Date m = c.getTime();
		return format.format(m);
	}

	public static int getYearNext(Date date, Integer n) {
		String nowDate = null;
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.YEAR, n);
		nowDate = new SimpleDateFormat("yyyy").format(calendar.getTime());
		return Integer.parseInt(nowDate);
	}
}
