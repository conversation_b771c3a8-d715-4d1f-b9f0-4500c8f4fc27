package com.swcares.sdahotel.common.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public enum DateFormatEnum {
    yyyyMMddHHmmss("yyyyMMddHHmmss"),
    yyyyMMddHHmmss2("yyyy-MM-dd HH:mm:ss"),
    yyyyMMdd("yyyyMMdd"),
    yyyy_MM_dd("yyyy-MM-dd"),
    yyyy_MM("yyyy-MM");

    private String value;

    public String getValue() {
        return value;
    }

    DateFormatEnum(String value) {
        this.value = value;
    }

    /**
     * type转枚举类型
     *
     * @param value value
     * @return DateFormatEnum
     */
    public static DateFormatEnum get(String value) {
        if (Objects.isNull(value))
            return null;

        for (DateFormatEnum dateFormat : values())
            if (dateFormat.value.equals(value))
                return dateFormat;
        return null;
    }
}
