package com.swcares.sdahotel.common.interceptors;/**
 * 作者：徐士昊
 * 时间：2022/07/06 13:26
 **/

import com.jfinal.kit.JsonKit;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.config.SdaConstantConfig;
import com.swcares.sdahotel.common.kit.ResponseKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 * Date on 2022/07/06  13:26
 */
public class IPLimitInterceptor implements HandlerInterceptor {


    @Autowired
    RedisService redisService;




    public static final String PREFIX = "sda_hotel_visit_ip_";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String ip = getIpAddress(request);
        //兼容IP穿透不生效
        String[] split = ip.split(",");
        if (split.length == 2) {
            ip = split[0];
        }
        String key = PREFIX + ip;
        long count = redisService.incr(key, 1);
        // 测试时发现存在ip有效期为-1的情况，添加判断进行重新设置有效期
        if (count == 1 || redisService.getExpire(key) == -1) {
            redisService.expire(key, 60);
        }
        if (count > SdaConstantConfig.IPVisitedlimitedPerMinute) {
            ml(response,"每分钟访问次数不允许超过200次，请稍后重试");
            return false;
        }

        return true;
    }


    public void ml(HttpServletResponse response,String msg) {
        RenderResult result = RenderResult.build(MessageCode.FAIL);
        result.setMsg(msg);
        String json = JsonKit.toJson(result);
        ResponseKit.renderJson(response, json);
    }
    /**
     * 获取用户真实IP地址，不使用request.getRemoteAddr();的原因是有可能用户使用了代理软件方式避免真实IP地址,
     * <p>
     * 可是，如果通过了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP值，究竟哪个才是真正的用户端的真实IP呢？
     * 答案是取X-Forwarded-For中第一个非unknown的有效IP字符串。
     * <p>
     * 如：X-Forwarded-For：*************, *************, *************,
     * *************
     * <p>
     * 用户真实IP为： *************
     *
     * @param request
     * @return
     */
    private static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            if ("127.0.0.1".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip)) {
                //根据网卡取本机配置的IP
                InetAddress inet = null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (UnknownHostException e) {
                    e.printStackTrace();
                }
                ip = inet.getHostAddress();
            }
        }
        return ip;
    }
}
