package com.swcares.sdahotel.common.enums;

/**
 * 作者：徐士昊
 * 时间：2023/2/17 0017 14:04
 **/
public enum AmdVerifycationEnum {

    AWAIT("0","待核验"), PASS("1","通过"),BACK("2","退回");
    private  String code;

    private String desc;

    AmdVerifycationEnum(String code, String desc){
        this.code=code;
        this.desc=desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }



    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

}
