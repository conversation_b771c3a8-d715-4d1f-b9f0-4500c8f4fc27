package com.swcares.sdahotel.common.kit;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicReference;

/**
 *
 * JSON工具类
 * <AUTHOR>
 * @email  624185547
 * @version 1.0
 *
 */
public class JSONUtils {
    private static Logger log = LoggerFactory.getLogger(JSONUtils.class);

	/**
	 * 格式化日期
	 */
	private static final AtomicReference<SimpleDateFormat> FMT = new AtomicReference<>(new SimpleDateFormat(
			"yyyy-MM-dd"));
	private Map<String, Object> jsonMap = new HashMap<String, Object>();

	public static final ObjectMapper mapper = new ObjectMapper();
	static {
		mapper.getSerializerProvider().setNullValueSerializer(new JsonSerializer<Object>() {

			@Override
			public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
				gen.writeString("");
			}
		});
	}
	/**
	 * 清理map
	 * <AUTHOR>
	 */
	public void clear() {
		jsonMap.clear();
	}

	/**
	 * 添加元素 <br/>
	 * <AUTHOR>
	 * @param key 键
	 * @param value 值
	 * @return Map
	 */
	public Map<String, Object> put(String key, Object value) {
		jsonMap.put(key, value);
		return jsonMap;
	}

	/**
	 * 判断是否要加引号
	 * return (value instanceof Integer || value instanceof Boolean
	 * || value instanceof Double || value instanceof Float
	 * || value instanceof Short || value instanceof Long || value
	 * instanceof Byte);
	 * <AUTHOR>
	 * @param value
	 * @return boolean
	 */
	private static boolean isNoQuote(Object value) {
		if (value instanceof Integer) {
			return true;
		} else if (value instanceof Boolean) {
			return true;
		} else if (value instanceof Double) {
			return true;
		} else if (value instanceof Float) {
			return true;
		} else if (value instanceof Short) {
			return true;
		} else if (value instanceof Long) {
			return true;
		} else if (value instanceof Byte) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 判断是否要加引号
	 * <AUTHOR>
	 * @param value
	 * @return boolean
	 */
	private static boolean isQuote(Object value) {
		if (value instanceof String) {
			return true;
		} else if (value instanceof Character) {
			return true;
		} else {
			return false;
		}

	}

	/**
	 * 返回形如{'apple':'red','lemon':'yellow'}的字符串
	 * <AUTHOR>
	 * @return String
	 * @see Object#toString()
	 */
	@SuppressWarnings("unchecked")
	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder();
		sb.append("{");
		Set<Entry<String, Object>> set = jsonMap.entrySet();
		for (Entry<String, Object> entry : set) {
			Object value = entry.getValue();
			if (value == null) {
				continue;// 对于null值，不进行处理，页面上的js取不到值时也是null
			}
			sb.append("'").append(entry.getKey()).append("':");
			if (value instanceof JSONUtils) {
				sb.append(value.toString());
			} else if (isNoQuote(value)) {
				sb.append(value);
			} else if (value instanceof Date) {
				sb.append("'").append(FMT.get().format(value)).append("'");
			} else if (isQuote(value)) {
				sb.append("'").append(value).append("'");
			} else if (value.getClass().isArray()) {
				sb.append(arrayToStr(value));
			} else if (value instanceof Map) {
				sb.append(fromObject((Map<String, Object>) value).toString());
			} else if (value instanceof List) {
				sb.append(listToStr((List<Object>) value));
			} else {
				sb.append(fromObject(value).toString());
			}
			sb.append(",");
		}
		int len = sb.length();
		if (len > 1) {
			sb.delete(len - 1, len);
		}
		sb.append("}");
		return sb.toString();
	}

	/**
	 * 数组拼接
	 * <AUTHOR>
	 * @param array 数组
	 * @return String
	 */
	public static String arrayToStr(Object array) {
		if (!array.getClass().isArray()) {
			return "[]";
		}
		StringBuilder sb = new StringBuilder();
		sb.append("[");
		int len = Array.getLength(array);
		Object v = null;
		for (int i = 0; i < len; i++) {
			v = Array.get(array, i);
			if (v instanceof Date) {
				sb.append("'").append(FMT.get().format(v)).append("'").append(",");
			} else if (isQuote(v)) {
				sb.append("'").append(v).append("'").append(",");
			} else if (isNoQuote(v)) {
				sb.append(i).append(",");
			} else {
				sb.append(fromObject(v)).append(",");
			}
		}
		len = sb.length();
		if (len > 1) {
			sb.delete(len - 1, len);
		}
		sb.append("]");
		return sb.toString();
	}

	/**
	 * list 集合 生成
	 * <AUTHOR>
	 * @param list 集合
	 * @return String
	 */
	@SuppressWarnings("unchecked")
	public static String listToStr(List list) {
		if (list == null) {
			return null;
		}
		StringBuilder sb = new StringBuilder();
		sb.append("[");
		Object value = null;
		for (java.util.Iterator<Object> it = list.iterator(); it.hasNext();) {
			value = it.next();
			if (value instanceof Map) {
				sb.append(fromObject((Map) value).toString()).append(",");
			} else if (isNoQuote(value)) {
				sb.append(value).append(",");
			} else if (isQuote(value)) {
				sb.append("'").append(value).append("'").append(",");
			} else {
				sb.append(fromObject(value).toString()).append(",");
			}
		}
		int len = sb.length();
		if (len > 1) {
			sb.delete(len - 1, len);
		}
		sb.append("]");
		return sb.toString();
	}

	/**
	 * 从一个bean装载数据，返回一个JsonUtil对象。 <br/>
	 * <AUTHOR>
	 * @param bean 实体
	 * @return JSONUtils
	 */
	@SuppressWarnings("unchecked")
	public static JSONUtils fromObject(Object bean) {
		JSONUtils json = new JSONUtils();
		if (bean == null) {
			return json;
		}
		Class cls = bean.getClass();
		Field[] fs = cls.getDeclaredFields();
		Object value = null;
		String fieldName = null;
		Method method = null;
		int len = fs.length;
		for (int i = 0; i < len; i++) {
			fieldName = fs[i].getName();
			try {
				method = cls.getMethod(getGetter(fieldName), (Class[]) null);
				value = method.invoke(bean, (Object[]) null);
			} catch (Exception e) {
                log.error("", e);
				continue;
			}
			json.put(fieldName, value);
		}
		return json;
	}

	/**
	 * 从Map中装载数据 <br/>
	 * <AUTHOR>
	 * @param map map
	 * @return JSONUtils
	 */
	public static JSONUtils fromObject(Map<String, Object> map) {
		JSONUtils json = new JSONUtils();
		if (map == null) {
			return json;
		}
		json.getMap().putAll(map);
		return json;
	}

	private static String getGetter(String property) {
		return "get" + property.substring(0, 1).toUpperCase()
				+ property.substring(1, property.length());
	}

	public Map<String, Object> getMap() {
		return this.jsonMap;
	}

	public JSONUtils() {

	}

	public static ObjectMapper getInstance() {

		return mapper;
	}

	/**
	 * javaBean,list,array convert to json string
	 * @throws JsonProcessingException
	 */
	public static String obj2json(Object obj) throws JsonProcessingException {
		return mapper.writeValueAsString(obj);
	}

	/**
	 * json string convert to javaBean
	 */
	public static <T> T json2pojo(String jsonStr, Class<T> clazz)
			throws Exception {
		return mapper.readValue(jsonStr, clazz);
	}

	/**
	 * json string convert to map
	 * @throws IOException
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 */
	public static <T> Map<String, Object> json2map(String jsonStr) throws JsonParseException, JsonMappingException, IOException {
		return mapper.readValue(jsonStr, Map.class);
	}

	/**
	 * json string convert to map with javaBean
	 */
	public static <T> Map<String, T> json2map(String jsonStr, Class<T> clazz)
			throws Exception {
		Map<String, Map<String, Object>> map = (Map<String, Map<String, Object>>) mapper.readValue(jsonStr,
				new TypeReference<Map<String, T>>() {
		});
		Map<String, T> result = new HashMap<String, T>();
		for (Entry<String, Map<String, Object>> entry : map.entrySet()) {
			result.put(entry.getKey(), map2pojo(entry.getValue(), clazz));
		}
		return result;
	}

	/**
	 * json array string convert to list with javaBean
	 */
	public static <T> List<T> json2list(String jsonArrayStr, Class<T> clazz)
			throws Exception {

		//忽略JSON字符串中存在而Java对象实际没有的属性
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

		//驼峰转换
		mapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
		mapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);

		List<Map<String, Object>> list = (List<Map<String, Object>>) mapper.readValue(jsonArrayStr,
				new TypeReference<List<T>>() {
		});
		List<T> result = new ArrayList<T>();
		for (Map<String, Object> map : list) {
			result.add(map2pojo(map, clazz));
		}
		return result;
	}

	/**
	 * map convert to javaBean
	 */
	public static <T> T map2pojo(Map map, Class<T> clazz) {
		return mapper.convertValue(map, clazz);
	}

	/**
	 * map convert to json
	 * @throws JsonProcessingException
	 */
	public static String map2json(Map<String, Object> map) throws JsonProcessingException {
		return mapper.writeValueAsString(map);
	}


	/**
	 * 获取泛型的Collection Type
	 * @param collectionClass 泛型的Collection
	 * @param elementClasses 元素类
	 * @return JavaType Java类型
	 * @since 1.0
	 */
	public static JavaType getCollectionType(Class<?> collectionClass, Class<?>... elementClasses) {
		return mapper.getTypeFactory().constructParametricType(collectionClass, elementClasses);
	}

	/**
	 * json 转 List<T>
	 */
	public static <T> List<T> jsonToList(String jsonString, Class<T> clazz) throws Exception{
		JavaType javaType = getCollectionType(List.class,clazz);

		@SuppressWarnings("unchecked")
		List<T> ts = (List<T>) mapper.readValue(jsonString, javaType);
		return ts;
	}

	/**
	 * 使用Feign调用传递的Map参数转换为bean时报错解决方法
	 * @param obj
	 * @param clazz
	 * @return
	 */
	public static Object getObjectFormParam(Object obj,Class clazz) {
		ObjectMapper mapper = new ObjectMapper();
		Object objs = mapper.convertValue(obj, clazz);
		return objs;
	}


	/**
	 * 此处为方法说明
	 * <AUTHOR>
	 * @param args aa
	 */
	public static void main(String[] args){
		//        Map<String,Object> map1 = new HashMap<String,Object>();
		//        map1.put("name", "张三");
		//        map1.put("age", 2);
		//        Map<String,Object> map2 = new HashMap<String,Object>();
		//        map2.put("name", "张三");
		//        map2.put("age", 22);
		//        List<Map<String,Object>> list = new LinkedList<Map<String,Object>>();
		//        list.add(map1);
		//        list.add(map2);
		//        System.out.println(JSONUtils.fromObject(map1));
		//        JSONUtils util = new JSONUtils();
		//        util.put("name", "张三");
		//        util.put("blog", "http://www.sojson.com/blog/");
		//        util.put("ttt", true);
		//        util.put("dd", 1.3d);
		//        util.put("ff", map1);
		//        util.put("date", new java.util.Date());
		//        int[] a = new int[] { 2, 3, 4, 5 };
		//        util.put("arr", a);
		try {
			System.out.println(obj2json(null));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
	}

}
