package com.swcares.sdahotel.common.enums;

/**
 * 作者：徐士昊
 * 时间：2023/2/17 0017 14:30
 **/
public enum SettleStatusEnum {

    AWAIT("0","待审核"), AUDITING("1","审核中"),BACK("2","驳回"),PASS("3","通过");
    private  String code;

    private String desc;

    SettleStatusEnum(String code, String desc){
        this.code=code;
        this.desc=desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }



    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }


}
