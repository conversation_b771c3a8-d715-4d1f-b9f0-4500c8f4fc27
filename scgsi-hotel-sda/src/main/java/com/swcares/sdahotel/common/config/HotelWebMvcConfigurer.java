package com.swcares.sdahotel.common.config;/**
 * 作者：徐士昊
 * 时间：2022/05/11 15:08
 **/

import com.swcares.sdahotel.common.interceptors.HotelTokenInterceptor;
import com.swcares.sdahotel.common.interceptors.IPLimitInterceptor;
import com.swcares.sdahotel.common.interceptors.LogInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * Date on 2022/05/11  15:08
 */
@Configuration
public class HotelWebMvcConfigurer implements WebMvcConfigurer {


    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        registry.addInterceptor( hotelTokenInterceptor())
                .addPathPatterns("/api/sda/**")
                .excludePathPatterns("/api/sda/hotel/user/login", "/api/sda/hotel/user/testIn","/api/sda/hotel/evalution/add");

        registry.addInterceptor(ipLimitInterceptor()).addPathPatterns("/api/sda/**");
        registry.addInterceptor(logInterceptor()).addPathPatterns("/api/sda/**");

    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/sda/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS")
                .maxAge(3600)
                .allowedHeaders("*");

    }

    /**
     * 拦截器中需要注入组件，因此拦截器需要作为一个bean 注册到spring容器中
     *
     * @return
     */
    @Bean
    public IPLimitInterceptor ipLimitInterceptor() {
        return new IPLimitInterceptor();
    }

    /**
     *
     * @return
     */
    @Bean
    public LogInterceptor logInterceptor(){return  new LogInterceptor();}

    @Bean
    public  HotelTokenInterceptor hotelTokenInterceptor(){
        return new HotelTokenInterceptor();
    }


}
