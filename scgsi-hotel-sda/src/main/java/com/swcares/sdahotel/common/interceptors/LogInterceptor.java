package com.swcares.sdahotel.common.interceptors;/**
 * 作者：徐士昊
 * 时间：2022/08/11 15:29
 **/

import com.swcares.sdahotel.common.context.ParamContext;
import com.swcares.sdahotel.common.context.RsContext;
import com.swcares.sdahotel.common.context.UserContext;
import com.swcares.sdahotel.common.kit.SdaIpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * Date on 2022/08/11  15:29
 */
@Slf4j
public class LogInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        return true;
    }

    /**
     * 执行过程出现异常，全局捕获后，改方法不执行
     *
     * @param request
     * @param response
     * @param handler
     * @param modelAndView
     * @throws Exception
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
//        System.out.println("log ---处理后");
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        String ip = SdaIpUtil.getRemoteHost(request);
        String userId = UserContext.getUserId();
        String resultStr = RsContext.get();
        String params = ParamContext.getParams();
        String servletPath = request.getServletPath();
        String str = String.format("用户%s，ip地址%s，请求路径%s，请求参数%s，,响应结果%s", userId, ip, servletPath, params, resultStr);
        log.info(str);
    }




}
