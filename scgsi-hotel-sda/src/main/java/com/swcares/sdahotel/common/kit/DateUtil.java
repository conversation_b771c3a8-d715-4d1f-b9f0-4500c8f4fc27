package com.swcares.sdahotel.common.kit;

import com.swcares.sdahotel.common.enums.DateFormatEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URL;
import java.net.URLConnection;
import java.util.Date;
import java.util.Objects;

public class DateUtil {
    public static final Logger log = LoggerFactory.getLogger(DateUtil.class);

    /**
     * 根据pattern修改月/日
     *
     * @param date   日期
     * @param amount 数
     * @return add
     */
    public static String add(String date, int amount) {
        if (StringUtils.isEmpty(date))
            return "";
        DateFormatEnum pattern = DateFormatEnum.yyyyMMdd;
        switch (date.length()) {
            case 10:
                pattern = DateFormatEnum.yyyy_MM_dd;
                break;
            case 7:
                pattern = DateFormatEnum.yyyy_MM;
                break;
            default:
                return "";
        }
        return DateFormatUtil.format(add(DateFormatUtil.parse(date, pattern), amount, pattern), pattern);
    }

    /**
     * 根据pattern修改月/日
     *
     * @param date    日期
     * @param amount  数
     * @param pattern pattern
     * @return add
     */
    public static Date add(Date date, int amount, DateFormatEnum pattern) {
        if (Objects.isNull(date))
            return null;
        switch (pattern) {
            case yyyy_MM_dd:
                return DateUtils.addDays(date, amount);
            case yyyy_MM:
                return DateUtils.addMonths(date, amount);
            default:
                return date;
        }
    }
    /**
     * 获取网络百度时间
     * @return
     */
    public static Date getStandardTime() {
        URL url = null;
		try {
			url = new URL("https://www.baidu.com");
			//取得资源对象
	        URLConnection uc=url.openConnection();//生成连接对象
	        uc.connect(); //发出连接
	        long ld=uc.getDate(); //取得网站日期时间（时间戳）
	        Date date=new Date(ld); //转换为标准时间对象
	        return date;
		} catch (IOException e) {
			log.error("获取标准时间失败：{}",e);
		}
        return new Date();
    }

}
