/**
 * Copyright 2019-2021 覃海林(q<PERSON><PERSON><PERSON><PERSON>@163.com).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.swcares.sdahotel.common.kit;

import com.jfinal.kit.HashKit;

import java.security.MessageDigest;

public class Md5Kit {


	/** Md5 加密方法
	 * @param passWord
	 * @param loginName
	 * @return
	 */
	public static String md5(String value) {
		char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };

		try {
			byte[] btInput = value.getBytes("UTF-8");

			// 获得MD5摘要算法的 MessageDigest 对象
			MessageDigest mdInst = MessageDigest.getInstance("MD5");
			// 使用指定的字节更新摘要
			mdInst.update(btInput);
			// 获得密文
			byte[] md = mdInst.digest();
			// 把密文转换成十六进制的字符串形式
			int j = md.length;
			char str[] = new char[j * 2];
			int k = 0;
			for (int i = 0; i < j; i++) {
				byte byte0 = md[i];
				str[k++] = hexDigits[byte0 >>> 4 & 0xf];
				str[k++] = hexDigits[byte0 & 0xf];
			}
			String salt=new String(str);
			return HashKit.md5(HashKit.sha256(salt+ HashKit.md5(value)));
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

	}

   public static void main(String[] args) {
	   System.out.println(md5("123456"));
   }

}
