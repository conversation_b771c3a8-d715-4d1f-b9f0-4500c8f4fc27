package com.swcares.sdahotel.common.config;
/**
 * 作者：徐士昊
 * 时间：2022/07/07 14:09
 **/

/**
 * <AUTHOR>
 * Date on 2022/07/07  14:09
 */
public class SdaConstantConfig {

    //同一IP访问次数上限不超过200
    public static int IPVisitedlimitedPerMinute = 200;
    //数据加密状态
    public static boolean securityOpen=true;
    //登入登出的凭证前缀
    public  static  String TOKEN_PREFIEX="sdahoteluserloginout";
    //F,C,D,I,P 头等舱   W，R是高级经济舱，其余的是经济舱
    public  static final String HIGN_CABIN="F,C,D,I,P";
    //登录错误错误次数限制

    public static   int  LOGIN_ERROR_LIMITS=5;


}
