package com.swcares.sdahotel.common.global;/**
 * 作者：徐士昊
 * 时间：2022/07/08 14:12
 **/

import com.swcares.sdahotel.common.annotations.NoSecuriyAnnotation;
import com.swcares.sdahotel.common.config.SdaConstantConfig;
import org.springframework.core.MethodParameter;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;

import java.io.IOException;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * Date on 2022/07/08  14:12
 * 拦截request请求，处理请求信息
 */
@RestControllerAdvice(basePackages = "com.swcares.sdahotel.modules")
@Order(Ordered.HIGHEST_PRECEDENCE)
public class DecryptAdvice implements RequestBodyAdvice {

    @Override
    public boolean supports(MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {

        //如果不开启加密传输，那么不必转换请求报文
        if (!SdaConstantConfig.securityOpen) {
            return false;
        }

        boolean no = methodParameter.getMethod().isAnnotationPresent(NoSecuriyAnnotation.class);
        //方法上有该注解，表示不必解密
        if (no) {
            return false;
        }
        return true;

    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) throws IOException {
        return new MyHttpInputMessage(httpInputMessage);
    }

    @Override
    public Object afterBodyRead(Object o, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return o;
    }

    @Override
    public Object handleEmptyBody(Object o, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return o;
    }


}
