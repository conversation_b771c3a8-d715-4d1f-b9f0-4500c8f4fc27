package com.swcares.sdahotel.common.kit;/**
 * 作者：徐士昊
 * 时间：2022/05/11 14:56
 **/

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * Date on 2022/05/11  14:56
 */
@Slf4j
public class ResponseKit {


    public static void renderJson(
            HttpServletResponse response, String jsonData) {

        response.setContentType("application/json;charset=UTF-8");
        try {
            response.getWriter().write(jsonData);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

}
