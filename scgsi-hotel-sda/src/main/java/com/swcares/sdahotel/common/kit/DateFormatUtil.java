package com.swcares.sdahotel.common.kit;

import com.swcares.sdahotel.common.enums.DateFormatEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.Date;
import java.util.Objects;

public class DateFormatUtil {

    public static final Logger log = LoggerFactory.getLogger(DateFormatUtil.class);

    private DateFormatUtil() {
    }

    /**
     * 字符串转Date
     *
     * @param date    字符串
     * @param pattern 格式
     * @return 日期
     */
    public static Date parse(String date, DateFormatEnum pattern) {
        if (StringUtils.isEmpty(date))
            return null;

        try {
            return DateUtils.parseDate(date, pattern.getValue());
        } catch (ParseException e) {
            log.error("", e);
            return null;
        }
    }

    /**
     * 格式化date,null返回空字符串
     *
     * @param date    日期
     * @param pattern 格式
     * @return {@code pattern}格式日期字符串
     */
    public static String format(Date date, DateFormatEnum pattern) {
        if (Objects.isNull(date))
            return "";

        return DateFormatUtils.format(date, pattern.getValue());
    }

    /**
     * 格式化为日期,null返回空字符串
     *
     * @param date 日期
     * @return yyyyMMdd
     */
    public static String formatDate(Date date) {
        return format(date, DateFormatEnum.yyyyMMdd);
    }

    /**
     * 格式化为日期时间,null返回空字符串
     *
     * @param date 日期
     * @return yyyyMMddHHmmss
     */
    public static String formatShortDateTime(Date date) {
        return format(date, DateFormatEnum.yyyyMMddHHmmss);
    }
}
