package com.swcares.sdahotel.common.kit;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.IOException;
import java.security.MessageDigest;
import java.util.Base64;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 字符操作工具类
 * <AUTHOR>
 *
 */
public class StringUtil {

    private final static Log log = LogFactory.getLog(StringUtil.class);


    /**
     * 密码方法
     * @param password
     * @param algorithm
     * @return
     */
    public static String encodePassword(String password, String algorithm) {
        byte[] unencodedPassword = password.getBytes();

        MessageDigest md = null;

        try {
            // first create an instance, given the provider
            md = MessageDigest.getInstance(algorithm);
        } catch (Exception e) {
            log.error("Exception: " + e);

            return password;
        }

        md.reset();

        // call the update method one or more times
        // (useful when you don't know the size of your data, eg. stream)
        md.update(unencodedPassword);

        // now calculate the hash
        byte[] encodedPassword = md.digest();

        StringBuilder buf = new StringBuilder();

        for (int i = 0; i < encodedPassword.length; i++) {
            if ((encodedPassword[i] & 0xff) < 0x10) {
                buf.append("0");
            }

            buf.append(Long.toString(encodedPassword[i] & 0xff, 16));
        }

        return buf.toString();
    }

    /**
     * Encode a string using Base64 encoding. Used when storing passwords
     * as cookies.
     *
     * This is weak encoding in that anyone can use the decodeString
     * routine to reverse the encoding.
     *
     * @param str
     * @return String
     */
    public static String encodeString(String str)  {
        return Base64.getEncoder().encodeToString(str.getBytes()).trim();    }

    /**
     * Decode a string using Base64 encoding.
     *
     * @param str
     * @return String
     */
    public static String decodeString(String str) {
		return new String(Base64.getDecoder().decode(str));
    }

    /**
     * 利用正则表达式判断字符串是否是数字
     * @param str
     * @return
     */
    public static boolean isNumeric(String str){
           Pattern pattern = Pattern.compile("[0-9]*");
           Matcher isNum = pattern.matcher(str);
           if( !isNum.matches() ){
               return false;
           }
           return true;
    }

    /**
     * ADD BY KELLEN
     * 06-12-20
     */
	public static boolean isEmpty(String str){
		if(null==str){
			return true;
		}else if("".equalsIgnoreCase(str)){
			return true;
		}else return false;
	}

	public static String getPrefix(String str,int prefix){
		String prefixx = str;
		if(!isEmpty(str)){
			prefixx = str.substring(0,prefix);
		}
		return prefixx;
	}

	public static String getBackfix(String str,int prefix){
		String prefixx = str;
		if(!isEmpty(str)){
			prefixx = str.substring(prefix);
		}
		return prefixx;
	}

	/**
	 * author:kellen
	 * modified date:07-03-01
	 * email address validate
	 * @param aEmailAddress
	 * @return
	 */
	public static boolean isValidEmail(String aEmailAddress){
	    if (aEmailAddress == null) return false;

	    boolean result = true;

	    if ( ! hasNameAndDomain(aEmailAddress) ) {
	    	result = false;
	    }

	    return result;
	  }

	private static boolean hasNameAndDomain(String aEmailAddress){
	    String[] tokens = aEmailAddress.split("@");
	    return
	     tokens.length == 2 &&
	     !StringUtil.isEmpty( tokens[0] ) &&
	     !StringUtil.isEmpty( tokens[1] ) &&
	     -1!=tokens[1].indexOf(".");
	  }

	 public   static   String   constructSql   (String   sql,Object[]   array)   {
	    	String from="?";
			StringBuilder bf= new StringBuilder("");
			int   i   =   0;
			StringTokenizer st = new StringTokenizer(sql,from,true);
			while (st.hasMoreTokens())
			{
				String tmp = st.nextToken();

				if(tmp.equals(from))
				{
					bf.append(String.valueOf(array[i]));
					i++;
				}
				else
				{
					bf.append(tmp);
				}
			}
			return bf.toString();
	    }
	 /**
	  * 查询数组中是否包含某个字符
	  * @param strs
	  * @param s
	  * @return
	  */
	 public static boolean isHave(String[] strs,String s){
		 /*此方法有两个参数，第一个是要查找的字符串数组，第二个是要查找的字符或字符串
		 * */
		 if(strs !=null && strs.length>0) {
			 for(int i=0;i<strs.length;i++){
				 if(strs[i].indexOf(s)!=-1){//循环查找字符串数组中的每个字符串中是否包含所有查找的内容
					 return true;//查找到了就返回真，不在继续查询
				 }
			 }
		 }
		 return false;//没找到返回false
	}


	public static boolean isNotEmpty(String str) {
		return !isEmpty(str);
	}

	public static String replaceBlank(String str) {
		String dest = "";
		if (str!=null) {
			Pattern p = Pattern.compile("\t| ");
			Matcher m = p.matcher(str);
			dest = m.replaceAll("");
		}
		return dest;
	}

    /**
     * 判断字符串内容是否为空
     *
     * @param c
     * @return 为空返回true
     */
    public static boolean isEmpty(CharSequence c) {
        return StringUtils.isEmpty(c) || "null".equals(c);
    }

    /**
     * 判断字符串内容是否不为空
     *
     * @param c
     * @return 不为空返回true
     */
    public static boolean isNotEmpty(CharSequence c) {
        return StringUtils.isEmpty(c);
    }
    /**
     * 去掉指定字符串前面指定的字符
     * @param str
     * @param c
     * @return
     */
    public static String ltrim(String str, char c) {
    	if(isEmpty(str)) return "";
    	str = str.trim();
        char[] chars = str.toCharArray();
        int len = chars.length;
        int st = 0;
        while ( (st < len) && (chars[st] == c) ){
            st ++;
        }
        return (st >0)? str.substring(st, len): str;
    }

    /**
     * 去掉指定字符串后面指定的字符
     * @param str
     * @param c
     * @return
     */
    public static String rtrim(String str, char c) {
    	if(isEmpty(str)) return "";
    	str = str.trim();
        char[] chars = str.toCharArray();
        int len = chars.length;
        int st = 0;
        while ( (st < len) && (chars[len-1] == c) ){
            len --;
        }
        return (len<chars.length)? str.substring(st, len): str;
    }

	/**
	 * 获取汉字转拼音，英文字符不变
	 * @param chinese 汉字串
	 * @return 汉语拼音
	 */
	public static String getFullSpell(String chinese) {
		StringBuffer pybf = new StringBuffer();
		char[] arr = chinese.toCharArray();
		HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
		defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
		defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
		for (int i = 0; i < arr.length; i++) {
			if (arr[i] > 128) {
				try {
					pybf.append(PinyinHelper.toHanyuPinyinStringArray(arr[i], defaultFormat)[0]);
				} catch (BadHanyuPinyinOutputFormatCombination e) {
					e.printStackTrace();
				}
			} else {
				pybf.append(arr[i]);
			}
		}
		return pybf.toString();
	}


}
