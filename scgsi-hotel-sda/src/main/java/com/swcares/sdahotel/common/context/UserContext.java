package com.swcares.sdahotel.common.context;/**
 * 作者：徐士昊
 * 时间：2022/11/14 0014 15:58
 **/

/**
 * <AUTHOR>
 * Date on 2022/11/14 0014  15:58
 */
public class UserContext {
    private   static ThreadLocal<String> userHolder=new ThreadLocal<>();
    public static   void setUserId(String userId){
        userHolder.set(userId);
    }

    public static  String getUserId(){
       return  userHolder.get();
    }
}
