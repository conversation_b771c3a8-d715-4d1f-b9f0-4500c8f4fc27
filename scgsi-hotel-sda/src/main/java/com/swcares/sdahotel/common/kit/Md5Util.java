package com.swcares.sdahotel.common.kit;

import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

/**
 * MD5加密类
 * @描述：
 * @作者：cyh
 * @版本：V1.0
 * @创建时间：：2016-11-21 下午10:55:57
 *
 */
public final class Md5Util {

	private static final Logger LOGGER = LoggerFactory.getLogger(Md5Util.class);
	/**
	 * 普通MD5
	 * <AUTHOR>
	 * @time 2016-6-11 下午8:00:28
	 * @param input
	 * @return
	 */
	public static String MD5(String input) {
		MessageDigest md5 = null;
		try {
			md5 = MessageDigest.getInstance("MD5");
		} catch (NoSuchAlgorithmException e) {
			return "check jdk";
		} catch (Exception e) {
            LOGGER.error("", e);
			return "";
		}
		char[] charArray = input.toCharArray();
		byte[] byteArray = new byte[charArray.length];

		for (int i = 0; i < charArray.length; i++)
			byteArray[i] = (byte) charArray[i];
		byte[] md5Bytes = md5.digest(byteArray);
		StringBuilder hexValue = new StringBuilder();
		for (int i = 0; i < md5Bytes.length; i++) {
			int val = ((int) md5Bytes[i]) & 0xff;
			if (val < 16)
				hexValue.append("0");
			hexValue.append(Integer.toHexString(val));
		}
		return hexValue.toString();

	}




	/**
	 * 加盐MD5
	 * <AUTHOR>
	 * @time 2016-6-11 下午8:45:04
	 * @param password
	 * @return
	 */
	/*public static String generate(String password,String salt) {
		password = md5Hex(password + salt);
		char[] cs = new char[48];
		for (int i = 0; i < 48; i += 3) {
			cs[i] = password.charAt(i / 3 * 2);
			char c = salt.charAt(i / 3);
			cs[i + 1] = c;
			cs[i + 2] = password.charAt(i / 3 * 2 + 1);
		}
		return new String(cs);
	}  */

	/**
	 * 校验加盐后是否和原文一致  【可能暂时没用 会报错】
	 * <AUTHOR>
	 * @time 2016-6-11 下午8:45:39
	 * @param password
	 * @param md5
	 * @return
	 */
	public static boolean verify(String password, String md5) {
		char[] cs1 = new char[32];
		char[] cs2 = new char[16];
		for (int i = 0; i < 48; i += 3) {
			cs1[i / 3 * 2] = md5.charAt(i);
			cs1[i / 3 * 2 + 1] = md5.charAt(i + 2);
			cs2[i / 3] = md5.charAt(i + 1);
		}
		String salt = new String(cs2);
		return md5Hex(password + salt).equals(new String(cs1));
	}

	/**
	 * 获取十六进制字符串形式的MD5摘要
	 */
	private static String md5Hex(String src) {
		try {
			MessageDigest md5 = MessageDigest.getInstance("MD5");
			byte[] bs = md5.digest(src.getBytes());
			return new String(new Hex().encode(bs));
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 对密码按照密码，盐进行加密
	 * @param password 密码
	 * @param salt 盐
	 * @return
	 */
	public static String encryptPassword(String password, String salt) {
		return hash(password + salt);
	}


	public static String hash(String s) {
		try {
			return new String(toHex(md5(s)).getBytes("UTF-8"), "UTF-8");
		} catch (Exception e) {
			LOGGER.error("not supported charset...{}", e);
			return s;
		}
	}

	private static byte[] md5(String s) {
		MessageDigest algorithm;
		try {
			algorithm = MessageDigest.getInstance("MD5");
			algorithm.reset();
			algorithm.update(s.getBytes("UTF-8"));
			byte[] messageDigest = algorithm.digest();
			return messageDigest;
		} catch (Exception e) {
			LOGGER.error("MD5 Error...", e);
		}
		return null;
	}
	private static final String toHex(byte hash[]) {
		if (hash == null) {
			return null;
		}
		StringBuilder buf = new StringBuilder(hash.length * 2);
		int i;

		for (i = 0; i < hash.length; i++) {
			if ((hash[i] & 0xff) < 0x10) {
				buf.append("0");
			}
			buf.append(Long.toString(hash[i] & 0xff, 16));
		}
		return buf.toString();
	}

	/**
	 * MD5加密
	 * @param paramMap 加密参数map
	 * @param key 加密秘钥
	 * @return
	 */
	public static String MD5EncodeByMap(Map<String, String> paramMap, String key){
		StringBuilder originBuilder = new StringBuilder();
		for (Map.Entry<String, String> table : paramMap.entrySet()){
			originBuilder.append(table.getKey()).append("=").append(table.getValue()).append("&");
		}
		String origin = originBuilder.toString();
		origin = origin.substring(0, origin.length()-1) + key;
		return MD5(origin).toUpperCase();
	}

}
