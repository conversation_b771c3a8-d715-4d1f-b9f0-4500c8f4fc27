package com.swcares.sdahotel.common.enums;/**
 * 作者：徐士昊
 * 时间：2022/11/9 0009 15:18
 **/

/**
 * <AUTHOR>
 * Date on 2022/11/9 0009  15:18
 */
public enum OrderStatusEnum {
    //0草稿、1审核中、2通过、3待保障、4关闭,5未通过6驳回7保障中8逾期9保障完成
    DRAFT("0", "草稿"), UNDER_CHECK("1", "审核中"), PASS_CHECK("2", "通过"),
    AWAIT_GURANTEE("3", "待保障"), UNDER_GURANTEE("4", "保障中"), UNPASS_CHECK("5", "未通过"),
    REJECT("6", "驳回"), OVER_DEADLINE("8", "逾期"),
    COMPLETE("7", "保障完成"),CHOSE("9", "关闭");

    private OrderStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    private String code;
    private String desc;

    public String getCode(){
        return  this.code;
    }
}
