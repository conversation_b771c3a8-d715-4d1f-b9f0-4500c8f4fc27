package com.swcares.sdahotel.common.exception;/**
 * 作者：徐士昊
 * 时间：2022/05/31 13:58
 **/

import com.alibaba.fastjson.JSON;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.config.SdaConstantConfig;
import com.swcares.sdahotel.common.context.ParamContext;
import com.swcares.sdahotel.common.context.UserContext;
import com.swcares.sdahotel.common.kit.AESUtil;
import com.swcares.sdahotel.common.kit.SdaIpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;

/**
 * <AUTHOR>
 * Date on 2022/05/31  13:58
 * @desc 原定义的BussinessException每种错误都有代码，为了不影响原代码，自定义全局异常处理机制
 * 由于HotelException继承至Exeption，所以要设定优先级，否则将被EXCEPTION处理。
 * 全部异常捕获后，不在进入EncryptAdvice，导致开启数据加密模式失效
 */
@RestControllerAdvice(basePackages = "com.swcares.sdahotel.modules")
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class HotelExceptionHandler {


    @ExceptionHandler(com.swcares.sdahotel.common.exception.HotelException.class)
    public RenderResult handHotelException(HttpServletRequest req, HotelException ex) {
        String msg = ex.getMsg();
        String ip = SdaIpUtil.getRemoteHost(req);
        String userId = UserContext.getUserId();
        String servletPath = req.getServletPath();
        String params = ParamContext.getParams();
        RenderResult rr = RenderResult.build(MessageCode.FAIL.getCode(), msg, resData());
        String str = String.format("山航酒店异常记录：用户%s，ip地址%s，请求路径%s，请求参数%s，,响应结果%s", userId, ip, servletPath, params, JSON.toJSONString(rr));
        log.info(str);
        return rr;
    }

    @ExceptionHandler(com.swcares.sdahotel.common.exception.EncryptAndDecryptException.class)
    public RenderResult encryptAndDecryptException(HttpServletRequest req, EncryptAndDecryptException ex) {
        String msg = ex.getMsg();
        return RenderResult.build(MessageCode.FAIL.getCode(), msg, resData());
    }

    /**
     * 如果开始了加密传输，需要改为加密数据
     *
     * @return
     */
    private String resData() {
        //如果不开启加密传输，那么不必转换请求报文
        String data="{}";
        if (!SdaConstantConfig.securityOpen) {
            return data;
        } else {
            try {
                String aeskey = AESUtil.getAesKey().get("aesKey");
                String aesiv = AESUtil.getAesKey().get("aesIv");
                String encryptData = AESUtil.encrypt(data, aeskey, aesiv);
                return encryptData;
            } catch (RuntimeException | ParseException e) {
                log.info("加密错误");
                return null;

            }
        }
    }

}
