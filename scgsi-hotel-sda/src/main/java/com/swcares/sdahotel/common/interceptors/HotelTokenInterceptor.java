package com.swcares.sdahotel.common.interceptors;/**
 * 作者：徐士昊
 * 时间：2022/05/11 15:34
 **/

import com.jfinal.kit.JsonKit;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.common.utils.JwtTokenUtils;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.config.SdaConstantConfig;
import com.swcares.sdahotel.common.context.UserContext;
import com.swcares.sdahotel.common.kit.ResponseKit;
import io.jsonwebtoken.Claims;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * Date on 2022/05/11  15:34
 */
@Order(1)
public class HotelTokenInterceptor implements HandlerInterceptor {

    @Autowired
    RedisService redisService;
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        String token = request.getHeader("token");
        if (StringUtils.isBlank(token)) {
            ml(response,"token不允许为空");
            return false;
        }

        String key= SdaConstantConfig.TOKEN_PREFIEX+token;
        //存在key 说明用户没有登出
       if( !redisService.existsKey(key)){
           ml(response,"redisToken已过期，请重新登录");
           return false;
       }


        boolean expiration = JwtTokenUtils.isExpiration(token);
        if(expiration){
            ml(response,"jwtToken已过期，请重新登录");
            return false;
        }
        Claims tokenBody = JwtTokenUtils.getTokenBody(token);
        //用户名放到attr
        String username= tokenBody.getSubject();
        String userid = tokenBody.get("id").toString();
        request.setAttribute("username",username);
        request.setAttribute("userid",userid);
        //放在线程里
        UserContext.setUserId(userid);
        //用户名放在params
        return true;

    }


    public void ml(HttpServletResponse response,String msg) {
        RenderResult result = RenderResult.build(MessageCode.FAIL);
        result.setMsg(msg);
        String json = JsonKit.toJson(result);
        ResponseKit.renderJson(response, json);
    }
}
