package com.swcares.sdahotel.common.kit;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;
import java.text.ParseException;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

/**
 * AES加密工具类
 */
public class AESUtil {
	protected static final Log log = LogFactory.getLog(AESUtil.class);
	public static boolean initialized = false;
	public static final String ALGORITHM = "AES/ECB/PKCS7Padding";

	private final static String sKey = "entitycardcode12";//key,用于实体卡二维码参数加密
    private final static String ivParameter = "12entitycardcode";//偏移量,用于实体卡二维码参数加密

	/**
	 * 加密方法
	 * @param data  要加密的数据
	 * @param key 加密key
	 * @param iv 加密iv
	 * @return 加密的结果
	 * @throws Exception
	 */
	public static String encrypt(String data, String key, String iv) {
		try {

			Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");//"算法/模式/补码方式"NoPadding PkcsPadding
			int blockSize = cipher.getBlockSize();

			byte[] dataBytes = data.getBytes("utf-8");
			int plaintextLength = dataBytes.length;
			if (plaintextLength % blockSize != 0) {
				plaintextLength = plaintextLength + (blockSize - (plaintextLength % blockSize));
			}

			byte[] plaintext = new byte[plaintextLength];
			System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);

			SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
			IvParameterSpec ivspec = new IvParameterSpec(iv.getBytes());

			cipher.init(Cipher.ENCRYPT_MODE, keyspec, ivspec);
			byte[] encrypted = cipher.doFinal(plaintext);

			return new Base64().encodeToString(encrypted);

		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 解密方法
	 * @param data 要解密的数据
	 * @param key  解密key
	 * @param iv 解密iv
	 * @return 解密的结果
	 * @throws Exception
	 */
	public static String desEncrypt(String data, String key, String iv) {
		try {
			byte[] encrypted1 = new Base64().decode(data);

			Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
			SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
			IvParameterSpec ivspec = new IvParameterSpec(iv.getBytes());

			cipher.init(Cipher.DECRYPT_MODE, keyspec, ivspec);

			byte[] original = cipher.doFinal(encrypted1);
			String originalString = new String(original);
			return originalString;
		} catch (Exception e) {
			return null;
		}
	}

	public static Map<String,String> getAesKey() throws ParseException {
		Map<String,String> mapkey = new HashMap<>();
		String stimestamp="";
		String iv = "";
		String monthEn= "";
		stimestamp = DateUtils.dateToStamp(DateUtils.getDate("yyyy-MM-dd"));
		String result = stimestamp;
		Calendar cale = Calendar.getInstance();
		int month = cale.get(Calendar.MONTH) + 1;
		switch (month) {
		case 1:
			monthEn = "JAN";
			break;
		case 2:
			monthEn = "FEB";
			break;
		case 3:
			monthEn = "MAR";
			break;
		case 4:
			monthEn = "APR";
			break;
		case 5:
			monthEn = "MAY";
			break;
		case 6:
			monthEn = "JUN";
			break;
		case 7:
			monthEn = "JUL";
			break;
		case 8:
			monthEn = "AUG";
			break;
		case 9:
			monthEn = "SEP";
			break;
		case 10:
			monthEn = "OCT";
			break;
		case 11:
			monthEn = "NOV";
			break;
		case 12:
			monthEn = "DEC";
			break;
		default:
			monthEn = "TRY";
			break;
		}
		result+=monthEn;
		iv =monthEn+stimestamp;
		mapkey.put("aesKey", Md5Util.MD5(result).substring(4, 20));
		mapkey.put("aesIv", Md5Util.MD5(iv).substring(4, 20));
		return mapkey;
	}


    /**
     * @param   bytes  要被解密的字节数组
     * @param   key    加/解密要用的长度为32的字节数组（256位）密钥
     * @return String  解密后的字符串
     */
    public static String Aes256Decode(byte[] bytes, byte[] key){
        initialize();
        String result = null;
        try{
            Cipher cipher = Cipher.getInstance(ALGORITHM, "BC");
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES"); //生成加密解密需要的Key
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            byte[] decoded = cipher.doFinal(bytes);
            result = new String(decoded, "UTF-8");
        }catch(Exception e){
            e.printStackTrace();
        }
        return result;
    }

    public static void initialize(){
        if (initialized) return;
        Security.addProvider(new BouncyCastleProvider());
        initialized = true;
    }

    /**
    　* @MethodName: getKey
    　* @Description:	密钥固定,不根据时间获取
      * @return
      * @throws ParseException
    　* @return Map<String,String>
    　* @author: YCHu
    　* @Note: Nothing more.
    　* @date: 2019年12月5日 上午9:57:15
     */
    public static Map<String,String> getKey() {
    	Map<String, String> mapkey = new HashMap<String, String>();
		mapkey.put("aesKey", Md5Util.MD5(sKey).substring(4, 20));
		mapkey.put("aesIv", Md5Util.MD5(ivParameter).substring(4, 20));
		return mapkey;
	}

}
