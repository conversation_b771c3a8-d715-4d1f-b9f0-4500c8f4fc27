package com.swcares.sdahotel.common.enums;

/**
 * 作者：徐士昊
 * 时间：2022/11/01 11:39
 **/
public enum AmdStatusEnum {

    AWAIT_GUARANTEE("0", "待保障"), UNDER_GUARANTEE("1", "保障中"),
    AWAIT_SETTLE("2", "待结算"), UNDER_SETTLE("3", "结算审核中"),
    COMPLETE_SETTLE("4", "结算完成"),COMPELETED("5","已完成");

    private String code;
    private String desc;
    private AmdStatusEnum(String code,String desc){
        this.code=code;
        this.desc=desc;
    }

    public String  getCode(){
        return  this.code;
    }
}
