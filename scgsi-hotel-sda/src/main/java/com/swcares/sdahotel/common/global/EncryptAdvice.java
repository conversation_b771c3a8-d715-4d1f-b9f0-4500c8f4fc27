package com.swcares.sdahotel.common.global;/**
 * 作者：徐士昊
 * 时间：2022/07/15 9:23
 **/

import com.alibaba.fastjson.JSON;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.annotations.NoSecuriyAnnotation;
import com.swcares.sdahotel.common.config.SdaConstantConfig;
import com.swcares.sdahotel.common.context.RsContext;
import com.swcares.sdahotel.common.kit.AESUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * <AUTHOR>
 * Date on 2022/07/15  9:23
 */
@Slf4j
@Order
@RestControllerAdvice(basePackages = "com.swcares.sdahotel.modules")
public class EncryptAdvice implements ResponseBodyAdvice {
    @Override
    public boolean supports(MethodParameter methodParameter, Class aClass) {
        //如果不开启加密传输，那么不必转换请求报文
        if (!SdaConstantConfig.securityOpen) {
            return false;
        }
        boolean no = methodParameter.getMethod().isAnnotationPresent(NoSecuriyAnnotation.class);
        //方法上有该注解，表示响应数据不必加密
        if (no) {
            return false;
        }
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {

        RenderResult rs = (RenderResult) o;
        Object data = rs.getData();
        if (data != null) {
            try {
                String plaintData = JSON.toJSONString(data);
                //放在线程的Context里面
                RsContext.set(plaintData);
                String aeskey = AESUtil.getAesKey().get("aesKey");
                String aesiv = AESUtil.getAesKey().get("aesIv");
                String encryptData = AESUtil.encrypt(plaintData, aeskey, aesiv);
                RenderResult rrs = RenderResult.build(rs.getCode(), rs.getMsg(), encryptData);
                return rrs;

            } catch (Exception e) {
                e.printStackTrace();
                log.error("响应报文加密出错了",e);
            }

        }
        return o;
    }


}
