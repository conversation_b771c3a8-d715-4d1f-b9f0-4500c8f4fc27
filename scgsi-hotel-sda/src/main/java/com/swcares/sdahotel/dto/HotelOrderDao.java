package com.swcares.sdahotel.dto;/**
 * 作者：徐士昊
 * 时间：2022/09/22 14:44
 **/

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.sdahotel.entity.FdHotelOrderInfo;
import com.swcares.sdahotel.vo.HotelOrderVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Date on 2022/09/22  14:44
 */
@Repository
public class HotelOrderDao {
    @Autowired
    private BaseDAO baseDAO;


    public FdHotelOrderInfo getById(String id) {
        FdHotelOrderInfo order = baseDAO.findById(id, FdHotelOrderInfo.class);
        return order;
    }


    public List<FdHotelOrderInfo> listByIdNo(String idNo) {

        return null;

    }


    /**
     * 查询有效的山航住宿单 根据旅客证件号、当前登录用户（宾馆）的宾馆ID和山航住宿单状态（保障中7和待保障 3-有效）
     *
     * @param idNo
     * @param hotelId
     * @return
     */
    public List<HotelOrderVo> findValidateOrdersByIdNo(String idNo, String hotelId) {
        StringBuilder sb = new StringBuilder();
        Map<String, Object> params = new HashMap<>();
        params.put("idNo", idNo);
        params.put("hotelId", hotelId);
        sb.append("SELECT p.ID as paxId, o.ID as orderId, o.ORDER_NO as orderNo,  o.FLIGHT_NO as flightNo, o.FLIGHT_DATE as flightDate " +
                "  FROM FD_HOTEL_PAX_INFO p " +
                "  LEFT JOIN FD_HOTEL_ORDER_INFO o " +
                "    ON p.ORDER_ID = o.ID " +
                "  LEFT JOIN FD_HOTEL_MIDDLE_INFO m " +
                "    ON m.ORDER_ID = o.ID " +
                " WHERE p.ID_NO = :idNo " +
//                "   AND o.EXPIRY_DATE >sysdate " +  20221205注释掉，不在加入过期条件
                "   and （o.status='3'  or o.status='4'）" +
                "   AND m.HOTEL_ID = :hotelId ");
        List<HotelOrderVo> list = (List<HotelOrderVo>) baseDAO.findBySQL_comm(sb.toString(), params, HotelOrderVo.class);
        return list;
    }


    public void update(FdHotelOrderInfo orderInfo) {
        baseDAO.update(orderInfo);

    }

}
