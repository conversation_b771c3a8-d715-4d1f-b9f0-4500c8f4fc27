package com.swcares.sdahotel.dao;/**
 * 作者：徐士昊
 * 时间：2023/1/29 0029 13:27
 **/

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.sdahotel.entity.FdHotelPostEvalution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * Date on 2023/1/29 0029  13:27
 */
@Repository
public class EvalutionDao {
    @Autowired
    BaseDAO baseDAO;


    public void create(FdHotelPostEvalution evalution){
        baseDAO.save(evalution);
    }
}
