package com.swcares.sdahotel.dao;/**
 * 作者：徐士昊
 * 时间：2022/05/31 10:18
 **/

import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.StrKit;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.sdahotel.entity.FdHotelUserInfo;
import com.swcares.sdahotel.vo.HotelUserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Date on 2022/05/31  10:18
 */
@Repository
public class HotelUserDao {
    @Autowired
    BaseDAO baseDAO;


    public FdHotelUserInfo getByAccount(String account) {
        Map params = new HashMap<String, String>();
        params.put("account", account);
        return baseDAO.findByProperty(FdHotelUserInfo.class, params);
    }


    public void deleteByIds(String[] ids) {
        for (String id : ids) {
            FdHotelUserInfo hotel = baseDAO.findById(id, FdHotelUserInfo.class);
            baseDAO.getEntityManager().remove(hotel);
        }
    }

    public void add(FdHotelUserInfo hotelUserInfo) {
        baseDAO.save(hotelUserInfo);
    }

    public FdHotelUserInfo getById(String id) {
        return baseDAO.findById(id, FdHotelUserInfo.class);
    }

    public void update(FdHotelUserInfo hotelUserInfo) {
        baseDAO.update(hotelUserInfo);
    }

    public void deleteById(String id) {
        FdHotelUserInfo hotelUserInfo = baseDAO.findById(id, FdHotelUserInfo.class);
        baseDAO.getEntityManager().remove(hotelUserInfo);
    }

    public QueryResults list(int pageSize, int pageNumber, JSONObject query) {
        String account = query.get("account").toString();
        HashMap params = new HashMap();
        StringBuilder hql = new StringBuilder();
        hql.append("from FdHotelUserInfo  where 1=1 ");
        if (StrKit.notBlank(account)) {
            params.put("account", "%" + account + "%");
            hql.append("and account like :account");
        }
        hql.append(" order by createDate desc ");
        QueryResults qr = baseDAO.queryEntityPage(hql.toString(), params, FdHotelUserInfo.class, pageNumber, pageSize);
        return qr;
    }

//TODO CITY3CODE字段取消，直接查serviceCity
    public List listWithNoPage(FdHotelUserInfo hotelUserInfo){
        String account = hotelUserInfo.getAccount();
        StringBuilder sql=new StringBuilder();
        sql.append("  select u.account,u.hotel_id as hotelId,  u.id, u.user_name as userName, u.user_status as userStatus, u.user_type as userType,to_char (u.last_login_date,'yyyy-MM-dd HH24:mi:ss') as lastLoginDate,h.hotel_name as hotelName, h.service_city as serviceCity,u.create_date as createDate\n" +
                "  from fd_hotel_user_info u\n" +
                "  left join fd_hotel_info h\n" +
                "  on u.hotel_id = h.id\n" +
                " where 1=1");
        Map<String,Object> params=new HashMap<>();

        if(StrKit.notBlank(account)){
            params.put("account","%"+account+"%");
            sql.append(" and account like :account");
        }
        List users= baseDAO.findBySQL_comm(sql.toString(),params, HotelUserVo.class);
        return users;
    }




}
