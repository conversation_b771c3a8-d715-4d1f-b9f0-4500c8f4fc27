package com.swcares.sdahotel.dao;/**
 * 作者：徐士昊
 * 时间：2022/11/14 0014 15:31
 **/

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.sdahotel.entity.FdHotelSettlementInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * Date on 2022/11/14 0014  15:31
 */
@Repository
public class SdaHotelSettlementDao {

    @Autowired
    BaseDAO baseDAO;

    public void insert(FdHotelSettlementInfo settlementInfo) {
        baseDAO.save(settlementInfo);
    }

    public void insert2(FdHotelSettlementInfo settlementInfo) {
        baseDAO.save(settlementInfo);
        baseDAO.getEntityManager().flush();
    }

    public List<FdHotelSettlementInfo> findByAmdNo(String amdNo) {
        String hql = "from FdHotelSettlementInfo where accommodationNo='" + amdNo + "' order by createTime desc ";
        List<FdHotelSettlementInfo> list = baseDAO.findAllByHql(hql, FdHotelSettlementInfo.class);
        return list;
    }

    public void update(FdHotelSettlementInfo settlementInfo) {
        baseDAO.update(settlementInfo);
    }
}
