package com.swcares.sdahotel.dao;/**
 * 作者：徐士昊
 * 时间：2022/08/26 9:34
 **/

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.sdahotel.entity.CityCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Date on 2022/08/26  9:34
 */
@Repository
public class CityDao {

    @Autowired
    private BaseDAO baseDAO;

    public List<CityCode> listAll() {
        String sql = "select AIRPORT_3CODE as airport3Code,CITY_CH_NAME as cityChName from city_code";
        Map params = new HashMap<>();
        List  cities= baseDAO.findBySQL_comm(sql, params, CityCode.class);
        return cities;
    }


}
