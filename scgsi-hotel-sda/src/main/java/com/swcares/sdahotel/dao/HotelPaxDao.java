package com.swcares.sdahotel.dao;/**
 * 作者：徐士昊
 * 时间：2022/09/23 14:18
 **/

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.sdahotel.entity.FdHotelPaxInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * Date on 2022/09/23  14:18
 */
@Repository
public class HotelPaxDao {
    @Autowired
    BaseDAO baseDAO;

    public FdHotelPaxInfo  getById(String id){
        String hql="from FdHotelPaxInfo where id=:id";
        hql= hql.replace(":id","'"+id+"'");
        FdHotelPaxInfo paxInfo = baseDAO.findOneByHql(hql, FdHotelPaxInfo.class);
        baseDAO.getEntityManager().detach(paxInfo);
        return  paxInfo;
    }


}
