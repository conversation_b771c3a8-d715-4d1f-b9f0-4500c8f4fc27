package com.swcares.sdahotel.entity;/**
 * 作者：徐士昊
 * 时间：2022/09/16 15:50
 **/

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * Date on 2022/09/16  15:50
 */
@Entity
@Table(name = "FD_HOTEL_SETTLEMENT_INFO", schema = "GSSPSC", catalog = "")
public class FdHotelSettlementInfo {
    private String  id;
    private String accommodationNo;
    private String auditNo;
    private String orderId;
    private String hotelId;
    private String isAuthFlag;
    private String checkRemark;
    private String status;
    private String createId;
    private Date createTime;
    private String remark;
    private String  accommodationId;
    private Date  submitTime;
    private Date updateTime;

    @Id
    @Column(name = "ID", nullable = false, precision = 0, length = 64)
    public String  getId() {
        return id;
    }

    public void setId(String  id) {
        this.id = id;
    }

    @Basic
    @Column(name = "ACCOMMODATION_NO", nullable = true, length = 32)
    public String getAccommodationNo() {
        return accommodationNo;
    }

    public void setAccommodationNo(String accommodationNo) {
        this.accommodationNo = accommodationNo;
    }

    @Basic
    @Column(name = "AUDIT_NO", nullable = true, length = 32)
    public String getAuditNo() {
        return auditNo;
    }

    public void setAuditNo(String auditNo) {
        this.auditNo = auditNo;
    }

    @Basic
    @Column(name = "ORDER_ID", nullable = true, length = 64)
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Basic
    @Column(name = "HOTEL_ID", nullable = true, length = 64)
    public String getHotelId() {
        return hotelId;
    }

    public void setHotelId(String hotelId) {
        this.hotelId = hotelId;
    }

    @Basic
    @Column(name = "IS_AUTH_FLAG", nullable = true, length = 2)
    public String getIsAuthFlag() {
        return isAuthFlag;
    }

    public void setIsAuthFlag(String isAuthFlag) {
        this.isAuthFlag = isAuthFlag;
    }

    @Basic
    @Column(name = "CHECK_REMARK", nullable = true, length = 225)
    public String getCheckRemark() {
        return checkRemark;
    }

    public void setCheckRemark(String checkRemark) {
        this.checkRemark = checkRemark;
    }

    @Basic
    @Column(name = "STATUS", nullable = true, length = 2)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Basic
    @Column(name = "CREATE_ID", nullable = true, length = 225)
    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    @Basic
    @Column(name = "CREATE_TIME", nullable = true)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date  createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "REMARK", nullable = true, length = 1000)
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FdHotelSettlementInfo that = (FdHotelSettlementInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(accommodationNo, that.accommodationNo) &&
                Objects.equals(auditNo, that.auditNo) &&
                Objects.equals(orderId, that.orderId) &&
                Objects.equals(hotelId, that.hotelId) &&
                Objects.equals(isAuthFlag, that.isAuthFlag) &&
                Objects.equals(checkRemark, that.checkRemark) &&
                Objects.equals(status, that.status) &&
                Objects.equals(createId, that.createId) &&
                Objects.equals(createTime, that.createTime) &&
                Objects.equals(remark, that.remark);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, accommodationNo, auditNo, orderId, hotelId, isAuthFlag, checkRemark, status, createId, createTime, remark);
    }

    @Basic
    @Column(name = "ACCOMMODATION_ID", nullable = true, precision = 0)
    public String  getAccommodationId() {
        return accommodationId;
    }

    public void setAccommodationId(String  accommodationId) {
        this.accommodationId = accommodationId;
    }

    @Basic
    @Column(name = "SUBMIT_TIME", nullable = true)
    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date  submitTime) {
        this.submitTime = submitTime;
    }

    @Basic
    @Column(name = "UPDATE_TIME", nullable = true)
    public Date  getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
