package com.swcares.sdahotel.entity;/**
 * 作者：徐士昊
 * 时间：2022/09/16 15:50
 **/

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * Date on 2022/09/16  15:50
 */
@Entity
@Table(name = "FD_HOTEL_USER_INFO", schema = "GSSPSC", catalog = "")
public class FdHotelUserInfo {
    private String id;
    private String hotelId;
    private String account;
    private String password;
    private String userType;
    private String userStatus;
    private Date createDate;
    private Date modifyDate;
    private String createId;
    private String modifyId;
    private String userName;
    private String userDeleted;
    private Date lastLoginDate;
    private String statereason;

    @Id
    @Column(name = "ID", nullable = false, length = 32)
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Basic
    @Column(name = "HOTEL_ID", nullable = true, length = 32)
    public String getHotelId() {
        return hotelId;
    }

    public void setHotelId(String hotelId) {
        this.hotelId = hotelId;
    }

    @Basic
    @Column(name = "ACCOUNT", nullable = true, length = 255)
    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    @Basic
    @Column(name = "PASSWORD", nullable = true, length = 255)
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Basic
    @Column(name = "USER_TYPE", nullable = true, length = 2)
    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    @Basic
    @Column(name = "USER_STATUS", nullable = true, length = 2)
    public String getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(String userStatus) {
        this.userStatus = userStatus;
    }

    @Basic
    @Column(name = "CREATE_DATE", nullable = true)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "MODIFY_DATE", nullable = true)
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Basic
    @Column(name = "CREATE_ID", nullable = true, length = 32)
    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    @Basic
    @Column(name = "MODIFY_ID", nullable = true, length = 32)
    public String getModifyId() {
        return modifyId;
    }

    public void setModifyId(String modifyId) {
        this.modifyId = modifyId;
    }

    @Basic
    @Column(name = "USER_NAME", nullable = true, length = 255)
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Basic
    @Column(name = "USER_DELETED", nullable = true, length = 2)
    public String getUserDeleted() {
        return userDeleted;
    }

    public void setUserDeleted(String userDeleted) {
        this.userDeleted = userDeleted;
    }

    @Basic
    @Column(name = "LAST_LOGIN_DATE", nullable = true)
    public Date getLastLoginDate() {
        return lastLoginDate;
    }

    public void setLastLoginDate(Date lastLoginDate) {
        this.lastLoginDate = lastLoginDate;
    }

    @Basic
    @Column(name = "STATEREASON", nullable = true, length = 255)
    public String getStatereason() {
        return statereason;
    }

    public void setStatereason(String statereason) {
        this.statereason = statereason;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FdHotelUserInfo that = (FdHotelUserInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(hotelId, that.hotelId) &&
                Objects.equals(account, that.account) &&
                Objects.equals(password, that.password) &&
                Objects.equals(userType, that.userType) &&
                Objects.equals(userStatus, that.userStatus) &&
                Objects.equals(createDate, that.createDate) &&
                Objects.equals(modifyDate, that.modifyDate) &&
                Objects.equals(createId, that.createId) &&
                Objects.equals(modifyId, that.modifyId) &&
                Objects.equals(userName, that.userName) &&
                Objects.equals(userDeleted, that.userDeleted) &&
                Objects.equals(lastLoginDate, that.lastLoginDate) &&
                Objects.equals(statereason, that.statereason);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, hotelId, account, password, userType, userStatus, createDate, modifyDate, createId, modifyId, userName, userDeleted, lastLoginDate, statereason);
    }
}
