package com.swcares.sdahotel.entity;/**
 * 作者：徐士昊
 * 时间：2022/08/26 9:33
 **/

import javax.persistence.*;
import java.util.Objects;

/**
 * <AUTHOR>
 * Date on 2022/08/26  9:33
 */
@Entity
@Table(name = "CITY_CODE", schema = "GSSPSC", catalog = "")

public class CityCode {

    private String airport4Code;
    private String airport3Code;
    private String city3Code;
    private String cityChName;
    private String cityEnName;
    private String cityAbbr;
    private String cityTelNo;
    private String dOrI;
    private String aBase;
    private String aTdir;
    private String aDist;
    private String aAttr;
    private String aClas;
    private String aGzt;

    @Basic
    @Column(name = "AIRPORT_4CODE", nullable = true, length = 4)
    @Id
    public String getAirport4Code() {
        return airport4Code;
    }

    public void setAirport4Code(String airport4Code) {
        this.airport4Code = airport4Code;
    }

    @Basic
    @Column(name = "AIRPORT_3CODE", nullable = true, length = 3)
    public String getAirport3Code() {
        return airport3Code;
    }

    public void setAirport3Code(String airport3Code) {
        this.airport3Code = airport3Code;
    }

    @Basic
    @Column(name = "CITY_3CODE", nullable = true, length = 3)
    public String getCity3Code() {
        return city3Code;
    }

    public void setCity3Code(String city3Code) {
        this.city3Code = city3Code;
    }

    @Basic
    @Column(name = "CITY_CH_NAME", nullable = true, length = 50)
    public String getCityChName() {
        return cityChName;
    }

    public void setCityChName(String cityChName) {
        this.cityChName = cityChName;
    }

    @Basic
    @Column(name = "CITY_EN_NAME", nullable = true, length = 50)
    public String getCityEnName() {
        return cityEnName;
    }

    public void setCityEnName(String cityEnName) {
        this.cityEnName = cityEnName;
    }

    @Basic
    @Column(name = "CITY_ABBR", nullable = true, length = 10)
    public String getCityAbbr() {
        return cityAbbr;
    }

    public void setCityAbbr(String cityAbbr) {
        this.cityAbbr = cityAbbr;
    }

    @Basic
    @Column(name = "CITY_TEL_NO", nullable = true, length = 10)
    public String getCityTelNo() {
        return cityTelNo;
    }

    public void setCityTelNo(String cityTelNo) {
        this.cityTelNo = cityTelNo;
    }

    @Basic
    @Column(name = "D_OR_I", nullable = true, length = 1)
    public String getdOrI() {
        return dOrI;
    }

    public void setdOrI(String dOrI) {
        this.dOrI = dOrI;
    }

    @Basic
    @Column(name = "A_BASE", nullable = true, length = 30)
    public String getaBase() {
        return aBase;
    }

    public void setaBase(String aBase) {
        this.aBase = aBase;
    }

    @Basic
    @Column(name = "A_TDIR", nullable = true, length = 4)
    public String getaTdir() {
        return aTdir;
    }

    public void setaTdir(String aTdir) {
        this.aTdir = aTdir;
    }

    @Basic
    @Column(name = "A_DIST", nullable = true, length = 6)
    public String getaDist() {
        return aDist;
    }

    public void setaDist(String aDist) {
        this.aDist = aDist;
    }

    @Basic
    @Column(name = "A_ATTR", nullable = true, length = 1)
    public String getaAttr() {
        return aAttr;
    }

    public void setaAttr(String aAttr) {
        this.aAttr = aAttr;
    }

    @Basic
    @Column(name = "A_CLAS", nullable = true, length = 2)
    public String getaClas() {
        return aClas;
    }

    public void setaClas(String aClas) {
        this.aClas = aClas;
    }

    @Basic
    @Column(name = "A_GZT", nullable = true, length = 500)
    public String getaGzt() {
        return aGzt;
    }

    public void setaGzt(String aGzt) {
        this.aGzt = aGzt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CityCode cityCode = (CityCode) o;
        return Objects.equals(airport4Code, cityCode.airport4Code) &&
                Objects.equals(airport3Code, cityCode.airport3Code) &&
                Objects.equals(city3Code, cityCode.city3Code) &&
                Objects.equals(cityChName, cityCode.cityChName) &&
                Objects.equals(cityEnName, cityCode.cityEnName) &&
                Objects.equals(cityAbbr, cityCode.cityAbbr) &&
                Objects.equals(cityTelNo, cityCode.cityTelNo) &&
                Objects.equals(dOrI, cityCode.dOrI) &&
                Objects.equals(aBase, cityCode.aBase) &&
                Objects.equals(aTdir, cityCode.aTdir) &&
                Objects.equals(aDist, cityCode.aDist) &&
                Objects.equals(aAttr, cityCode.aAttr) &&
                Objects.equals(aClas, cityCode.aClas) &&
                Objects.equals(aGzt, cityCode.aGzt);
    }

    @Override
    public int hashCode() {
        return Objects.hash(airport4Code, airport3Code, city3Code, cityChName, cityEnName, cityAbbr, cityTelNo, dOrI, aBase, aTdir, aDist, aAttr, aClas, aGzt);
    }
}
