package com.swcares.sdahotel.entity;/**
 * 作者：徐士昊
 * 时间：2022/09/16 15:49
 **/

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * Date on 2022/09/16  15:49
 */
@Entity
@Table(name = "FD_HOTEL_FLIGHT_INFO", schema = "GSSPSC", catalog = "")
public class FdHotelFlightInfo {
    private String id;
    private String orderId;
    private String flightId;
    private String flightNo;
    private String flightDate;
    private String segment;
    private String planeCode;
    private String std;
    private String etd;
    private String delayTime;
    private String lateReason;
    private String createId;
    private Date createTime;

    @Id
    @Column(name = "ID", nullable = false, precision = 0)
    public String  getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Basic
    @Column(name = "ORDER_ID", nullable = true, length = 32)
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Basic
    @Column(name = "FLIGHT_ID", nullable = true, length = 200)
    public String getFlightId() {
        return flightId;
    }

    public void setFlightId(String flightId) {
        this.flightId = flightId;
    }

    @Basic
    @Column(name = "FLIGHT_NO", nullable = true, length = 7)
    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    @Basic
    @Column(name = "FLIGHT_DATE", nullable = true, length = 10)
    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate;
    }

    @Basic
    @Column(name = "SEGMENT", nullable = true, length = 40)
    public String getSegment() {
        return segment;
    }

    public void setSegment(String segment) {
        this.segment = segment;
    }

    @Basic
    @Column(name = "PLANE_CODE", nullable = true, length = 10)
    public String getPlaneCode() {
        return planeCode;
    }

    public void setPlaneCode(String planeCode) {
        this.planeCode = planeCode;
    }

    @Basic
    @Column(name = "STD", nullable = true, length = 100)
    public String getStd() {
        return std;
    }

    public void setStd(String std) {
        this.std = std;
    }

    @Basic
    @Column(name = "ETD", nullable = true, length = 100)
    public String getEtd() {
        return etd;
    }

    public void setEtd(String etd) {
        this.etd = etd;
    }

    @Basic
    @Column(name = "DELAY_TIME", nullable = true, length = 10)
    public String getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(String delayTime) {
        this.delayTime = delayTime;
    }

    @Basic
    @Column(name = "LATE_REASON", nullable = true, length = 500)
    public String getLateReason() {
        return lateReason;
    }

    public void setLateReason(String lateReason) {
        this.lateReason = lateReason;
    }

    @Basic
    @Column(name = "CREATE_ID", nullable = true, length = 32)
    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    @Basic
    @Column(name = "CREATE_TIME", nullable = true)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FdHotelFlightInfo that = (FdHotelFlightInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(orderId, that.orderId) &&
                Objects.equals(flightId, that.flightId) &&
                Objects.equals(flightNo, that.flightNo) &&
                Objects.equals(flightDate, that.flightDate) &&
                Objects.equals(segment, that.segment) &&
                Objects.equals(planeCode, that.planeCode) &&
                Objects.equals(std, that.std) &&
                Objects.equals(etd, that.etd) &&
                Objects.equals(delayTime, that.delayTime) &&
                Objects.equals(lateReason, that.lateReason) &&
                Objects.equals(createId, that.createId) &&
                Objects.equals(createTime, that.createTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, orderId, flightId, flightNo, flightDate, segment, planeCode, std, etd, delayTime, lateReason, createId, createTime);
    }
}
