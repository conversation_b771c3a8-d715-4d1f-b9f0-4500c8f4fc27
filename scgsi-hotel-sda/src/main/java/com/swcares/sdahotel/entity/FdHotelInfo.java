package com.swcares.sdahotel.entity;/**
 * 作者：徐士昊
 * 时间：2022/09/16 15:49
 **/

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * Date on 2022/09/16  15:49
 */
@Entity
@Table(name = "FD_HOTEL_INFO", schema = "GSSPSC", catalog = "")
public class FdHotelInfo {
    private String id;
    private String hotelName;
    private String conName;
    private String hotelPhone;
    private String hotelEmail;
    private String hotelAddr;
    private String hotelStatus;
    private Date createDate;
    private Date modifyDate;
    private String createId;
    private String modifyId;
    private String hotelDeleted;
    private String serviceCity;
    private String singlePrice;
    private String doublePrice;
    private String settlementType;
    private String addType;

    @Id
    @Column(name = "ID", nullable = false, length = 32)
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Basic
    @Column(name = "HOTEL_NAME", nullable = true, length = 255)
    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    @Basic
    @Column(name = "CON_NAME", nullable = true, length = 255)
    public String getConName() {
        return conName;
    }

    public void setConName(String conName) {
        this.conName = conName;
    }

    @Basic
    @Column(name = "HOTEL_PHONE", nullable = true, length = 255)
    public String getHotelPhone() {
        return hotelPhone;
    }

    public void setHotelPhone(String hotelPhone) {
        this.hotelPhone = hotelPhone;
    }

    @Basic
    @Column(name = "HOTEL_EMAIL", nullable = true, length = 255)
    public String getHotelEmail() {
        return hotelEmail;
    }

    public void setHotelEmail(String hotelEmail) {
        this.hotelEmail = hotelEmail;
    }

    @Basic
    @Column(name = "HOTEL_ADDR", nullable = true, length = 255)
    public String getHotelAddr() {
        return hotelAddr;
    }

    public void setHotelAddr(String hotelAddr) {
        this.hotelAddr = hotelAddr;
    }

    @Basic
    @Column(name = "HOTEL_STATUS", nullable = true, length = 2)
    public String getHotelStatus() {
        return hotelStatus;
    }

    public void setHotelStatus(String hotelStatus) {
        this.hotelStatus = hotelStatus;
    }

    @Basic
    @Column(name = "CREATE_DATE", nullable = true)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "MODIFY_DATE", nullable = true)
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Basic
    @Column(name = "CREATE_ID", nullable = true, length = 32)
    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    @Basic
    @Column(name = "MODIFY_ID", nullable = true, length = 32)
    public String getModifyId() {
        return modifyId;
    }

    public void setModifyId(String modifyId) {
        this.modifyId = modifyId;
    }

    @Basic
    @Column(name = "HOTEL_DELETED", nullable = true, length = 2)
    public String getHotelDeleted() {
        return hotelDeleted;
    }

    public void setHotelDeleted(String hotelDeleted) {
        this.hotelDeleted = hotelDeleted;
    }

    @Basic
    @Column(name = "SERVICE_CITY", nullable = true, length = 255)
    public String getServiceCity() {
        return serviceCity;
    }

    public void setServiceCity(String serviceCity) {
        this.serviceCity = serviceCity;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FdHotelInfo that = (FdHotelInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(hotelName, that.hotelName) &&
                Objects.equals(conName, that.conName) &&
                Objects.equals(hotelPhone, that.hotelPhone) &&
                Objects.equals(hotelEmail, that.hotelEmail) &&
                Objects.equals(hotelAddr, that.hotelAddr) &&
                Objects.equals(hotelStatus, that.hotelStatus) &&
                Objects.equals(createDate, that.createDate) &&
                Objects.equals(modifyDate, that.modifyDate) &&
                Objects.equals(createId, that.createId) &&
                Objects.equals(modifyId, that.modifyId) &&
                Objects.equals(hotelDeleted, that.hotelDeleted) &&
                Objects.equals(serviceCity, that.serviceCity);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, hotelName, conName, hotelPhone, hotelEmail, hotelAddr, hotelStatus, createDate, modifyDate, createId, modifyId, hotelDeleted, serviceCity);
    }

    @Basic
    @Column(name = "SINGLE_PRICE", nullable = true, length = 5)
    public String getSinglePrice() {
        return singlePrice;
    }

    public void setSinglePrice(String singlePrice) {
        this.singlePrice = singlePrice;
    }

    @Basic
    @Column(name = "DOUBLE_PRICE", nullable = true, length = 5)
    public String getDoublePrice() {
        return doublePrice;
    }

    public void setDoublePrice(String doublePrice) {
        this.doublePrice = doublePrice;
    }

    @Basic
    @Column(name = "SETTLEMENT_TYPE", nullable = true, length = 2)
    public String getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType;
    }

    @Basic
    @Column(name = "ADD_TYPE", nullable = true, length = 2)
    public String getAddType() {
        return addType;
    }

    public void setAddType(String addType) {
        this.addType = addType;
    }
}
