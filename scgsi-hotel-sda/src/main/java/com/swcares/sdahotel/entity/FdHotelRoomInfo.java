package com.swcares.sdahotel.entity;/**
 * 作者：徐士昊
 * 时间：2022/09/16 15:50
 **/

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * Date on 2022/09/16  15:50
 */
@Entity
@Table(name = "FD_HOTEL_ROOM_INFO", schema = "GSSPSC", catalog = "")
public class FdHotelRoomInfo {
    private String id;
    private String hotelId;
    private String roomNo;
    private String roomType;
    private String roomStatus;
    private Date createDate;
    private Date modifyDate;
    private String createId;
    private String modifyId;
    private String roomDeleted;

    @Id
    @Column(name = "ID", nullable = false, length = 32)
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Basic
    @Column(name = "HOTEL_ID", nullable = true, length = 32)
    public String getHotelId() {
        return hotelId;
    }

    public void setHotelId(String hotelId) {
        this.hotelId = hotelId;
    }

    @Basic
    @Column(name = "ROOM_NO", nullable = true, length = 255)
    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    @Basic
    @Column(name = "ROOM_TYPE", nullable = true, length = 2)
    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    @Basic
    @Column(name = "ROOM_STATUS", nullable = true, length = 2)
    public String getRoomStatus() {
        return roomStatus;
    }

    public void setRoomStatus(String roomStatus) {
        this.roomStatus = roomStatus;
    }

    @Basic
    @Column(name = "CREATE_DATE", nullable = true)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "MODIFY_DATE", nullable = true)
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Basic
    @Column(name = "CREATE_ID", nullable = true, length = 32)
    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    @Basic
    @Column(name = "MODIFY_ID", nullable = true, length = 32)
    public String getModifyId() {
        return modifyId;
    }

    public void setModifyId(String modifyId) {
        this.modifyId = modifyId;
    }

    @Basic
    @Column(name = "ROOM_DELETED", nullable = true, length = 2)
    public String getRoomDeleted() {
        return roomDeleted;
    }

    public void setRoomDeleted(String roomDeleted) {
        this.roomDeleted = roomDeleted;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FdHotelRoomInfo that = (FdHotelRoomInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(hotelId, that.hotelId) &&
                Objects.equals(roomNo, that.roomNo) &&
                Objects.equals(roomType, that.roomType) &&
                Objects.equals(roomStatus, that.roomStatus) &&
                Objects.equals(createDate, that.createDate) &&
                Objects.equals(modifyDate, that.modifyDate) &&
                Objects.equals(createId, that.createId) &&
                Objects.equals(modifyId, that.modifyId) &&
                Objects.equals(roomDeleted, that.roomDeleted);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, hotelId, roomNo, roomType, roomStatus, createDate, modifyDate, createId, modifyId, roomDeleted);
    }
}
