package com.swcares.sdahotel.entity;/**
 * 作者：徐士昊
 * 时间：2022/09/16 15:50
 **/

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * Date on 2022/09/16  15:50
 */
@Entity
@Table(name = "FD_HOTEL_PSSN_INFO", schema = "GSSPSC", catalog = "")
public class FdHotelPssnInfo {
    private String id;
    private String accommodationNo;
    private String hotelId;
    private String hotelPaxId;
    private String roomId;
    private String checkInMode;
    private String guaranteeDayCount;
    private String roomType;
    private String orderId;
    private Date checkInTime;
    private String roomNo;

    @Id
    @Column(name = "ID", nullable = false, length = 32)
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Basic
    @Column(name = "ACCOMMODATION_NO", nullable = true, length = 32)
    public String getAccommodationNo() {
        return accommodationNo;
    }

    public void setAccommodationNo(String accommodationNo) {
        this.accommodationNo = accommodationNo;
    }

    @Basic
    @Column(name = "HOTEL_ID", nullable = true, length = 32)
    public String getHotelId() {
        return hotelId;
    }

    public void setHotelId(String hotelId) {
        this.hotelId = hotelId;
    }

    @Basic
    @Column(name = "HOTEL_PAX_ID", nullable = true, length = 32)
    public String getHotelPaxId() {
        return hotelPaxId;
    }

    public void setHotelPaxId(String hotelPaxId) {
        this.hotelPaxId = hotelPaxId;
    }

    @Basic
    @Column(name = "ROOM_ID", nullable = true, length = 32)
    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    @Basic
    @Column(name = "CHECK_IN_MODE", nullable = true, length = 2)
    public String getCheckInMode() {
        return checkInMode;
    }

    public void setCheckInMode(String checkInMode) {
        this.checkInMode = checkInMode;
    }

    @Basic
    @Column(name = "GUARANTEE_DAY_COUNT", nullable = true, length = 5)
    public String getGuaranteeDayCount() {
        return guaranteeDayCount;
    }

    public void setGuaranteeDayCount(String guaranteeDayCount) {
        this.guaranteeDayCount = guaranteeDayCount;
    }

    @Basic
    @Column(name = "ROOM_TYPE", nullable = true, length = 2)
    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    @Basic
    @Column(name = "ORDER_ID", nullable = true, length = 255)
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FdHotelPssnInfo that = (FdHotelPssnInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(accommodationNo, that.accommodationNo) &&
                Objects.equals(hotelId, that.hotelId) &&
                Objects.equals(hotelPaxId, that.hotelPaxId) &&
                Objects.equals(roomId, that.roomId) &&
                Objects.equals(checkInMode, that.checkInMode) &&
                Objects.equals(guaranteeDayCount, that.guaranteeDayCount) &&
                Objects.equals(roomType, that.roomType) &&
                Objects.equals(orderId, that.orderId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, accommodationNo, hotelId, hotelPaxId, roomId, checkInMode, guaranteeDayCount, roomType, orderId);
    }

    @Basic
    @Column(name = "CHECK_IN_TIME", nullable = true)
    public Date getCheckInTime() {
        return checkInTime;
    }

    public void setCheckInTime(Date checkInTime) {
        this.checkInTime = checkInTime;
    }

    @Basic
    @Column(name = "ROOM_NO", nullable = true, length = 255)
    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }
}
