package com.swcares.sdahotel.entity;/**
 * 作者：徐士昊
 * 时间：2022/09/16 15:50
 **/

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * Date on 2022/09/16  15:50
 */
@Entity
@Table(name = "FD_HOTEL_ORDER_INFO", schema = "GSSPSC", catalog = "")
public class FdHotelOrderInfo {
    private String id;
    private String orderNo;
    private String payType;
    private String flightNo;
    private String flightDate;
    private String serviceCity;
    private String choiceSegment;
    private String remark;
    private String status;
    private String flightId;
    private Date expiryDate;
    private String createId;
    private Date createTime;
    private String updateUser;
    private Date updateTime;
    private String closeUser;
    private Date closeTime;
    private String etdServiceDays;
    private String serviceNum;

    @Id
    @Column(name = "ID", nullable = false, precision = 0)
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Basic
    @Column(name = "ORDER_NO", nullable = true, length = 64)
    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    @Basic
    @Column(name = "PAY_TYPE", nullable = true, length = 2)
    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    @Basic
    @Column(name = "FLIGHT_NO", nullable = true, length = 32)
    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    @Basic
    @Column(name = "FLIGHT_DATE", nullable = true, length = 64)
    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate;
    }

    @Basic
    @Column(name = "SERVICE_CITY", nullable = true, length = 32)
    public String getServiceCity() {
        return serviceCity;
    }

    public void setServiceCity(String serviceCity) {
        this.serviceCity = serviceCity;
    }

    @Basic
    @Column(name = "CHOICE_SEGMENT", nullable = true, length = 64)
    public String getChoiceSegment() {
        return choiceSegment;
    }

    public void setChoiceSegment(String choiceSegment) {
        this.choiceSegment = choiceSegment;
    }

    @Basic
    @Column(name = "REMARK", nullable = true, length = 225)
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Basic
    @Column(name = "STATUS", nullable = true, length = 2)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Basic
    @Column(name = "FLIGHT_ID", nullable = true, length = 64)
    public String getFlightId() {
        return flightId;
    }

    public void setFlightId(String flightId) {
        this.flightId = flightId;
    }

    @Basic
    @Column(name = "EXPIRY_DATE", nullable = true)
    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    @Basic
    @Column(name = "CREATE_ID", nullable = true, length = 64)
    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    @Basic
    @Column(name = "CREATE_TIME", nullable = true)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "UPDATE_USER", nullable = true, length = 64)
    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    @Basic
    @Column(name = "UPDATE_TIME", nullable = true)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Basic
    @Column(name = "CLOSE_USER", nullable = true, length = 64)
    public String getCloseUser() {
        return closeUser;
    }

    public void setCloseUser(String closeUser) {
        this.closeUser = closeUser;
    }

    @Basic
    @Column(name = "CLOSE_TIME", nullable = true)
    public Date getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(Date closeTime) {
        this.closeTime = closeTime;
    }

    @Basic
    @Column(name = "ETD_SERVICE_DAYS", nullable = true, length = 2)
    public String getEtdServiceDays() {
        return etdServiceDays;
    }

    public void setEtdServiceDays(String etdServiceDays) {
        this.etdServiceDays= etdServiceDays;
    }

    @Basic
    @Column(name = "SERVICE_NUM", nullable = true, length = 2)
    public String getServiceNum() {
        return serviceNum;
    }

    public void setServiceNum(String serviceNum) {
        this.serviceNum = serviceNum;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FdHotelOrderInfo that = (FdHotelOrderInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(orderNo, that.orderNo) &&
                Objects.equals(payType, that.payType) &&
                Objects.equals(flightNo, that.flightNo) &&
                Objects.equals(flightDate, that.flightDate) &&
                Objects.equals(serviceCity, that.serviceCity) &&
                Objects.equals(choiceSegment, that.choiceSegment) &&
                Objects.equals(remark, that.remark) &&
                Objects.equals(status, that.status) &&
                Objects.equals(flightId, that.flightId) &&
                Objects.equals(expiryDate, that.expiryDate) &&
                Objects.equals(createId, that.createId) &&
                Objects.equals(createTime, that.createTime) &&
                Objects.equals(updateUser, that.updateUser) &&
                Objects.equals(updateTime, that.updateTime) &&
                Objects.equals(closeUser, that.closeUser) &&
                Objects.equals(closeTime, that.closeTime) &&
                Objects.equals(etdServiceDays, that.etdServiceDays) &&
                Objects.equals(serviceNum, that.serviceNum);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, orderNo, payType, flightNo, flightDate, serviceCity, choiceSegment, remark, status, flightId, expiryDate, createId, createTime, updateUser, updateTime, closeUser, closeTime, etdServiceDays, serviceNum);
    }
}
