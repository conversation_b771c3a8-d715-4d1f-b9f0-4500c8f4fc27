package com.swcares.sdahotel.entity;/**
 * 作者：徐士昊
 * 时间：2022/09/16 15:50
 **/

import javax.persistence.*;
import java.util.Objects;

/**
 * <AUTHOR>
 * Date on 2022/09/16  15:50
 */
@Entity
@Table(name = "FD_HOTEL_MIDDLE_INFO", schema = "GSSPSC", catalog = "")
public class FdHotelMiddleInfo {
    private String  id;
    private String orderId;
    private String hotelId;

    @Id
    @Column(name = "ID", nullable = false, precision = 0)
    public String  getId() {
        return id;
    }

    public void setId(String  id) {
        this.id = id;
    }

    @Basic
    @Column(name = "ORDER_ID", nullable = true, length = 64)
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Basic
    @Column(name = "HOTEL_ID", nullable = true, length = 64)
    public String getHotelId() {
        return hotelId;
    }

    public void setHotelId(String hotelId) {
        this.hotelId = hotelId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FdHotelMiddleInfo that = (FdHotelMiddleInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(orderId, that.orderId) &&
                Objects.equals(hotelId, that.hotelId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, orderId, hotelId);
    }
}
