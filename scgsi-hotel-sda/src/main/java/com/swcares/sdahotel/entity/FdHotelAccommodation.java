package com.swcares.sdahotel.entity;/**
 * 作者：徐士昊
 * 时间：2022/09/16 15:49
 **/

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * Date on 2022/09/16  15:49
 */
@Entity
@Table(name = "FD_HOTEL_ACCOMMODATION", schema = "GSSPSC", catalog = "")
public class FdHotelAccommodation {
    private String id;
    private String accommodationNo;
    private String hotelId;
    private String accommodationStatus;
    private Date guaranteeComTime;
    private Date settleTime;
    private String settleId;
    private String singleAmount;
    private String doubleAmount;
    private String singlePrice;
    private String doublePrice;
    private String orderId;
    private Long preSumMoney;
    private Date createDate;
    private Date modifyDate;
    private String createId;
    private String modifyId;
    private Long realSumMoney;
    private String noteInfo;
    private String isVerification;
    private String settleStatus;
    private String settlementType;
    private String firstClassAmount;
    private String economyClassAmount;
    private String firstClassDayAmount;
    private String economyClassDayAmount;
    private String singleDayAmount;
    private String doubleDayAmount;

    @Id
    @Column(name = "ID", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Basic
    @Column(name = "ACCOMMODATION_NO", nullable = true, length = 255)
    public String getAccommodationNo() {
        return accommodationNo;
    }

    public void setAccommodationNo(String accommodationNo) {
        this.accommodationNo = accommodationNo;
    }

    @Basic
    @Column(name = "HOTEL_ID", nullable = true, length = 32)
    public String getHotelId() {
        return hotelId;
    }

    public void setHotelId(String hotelId) {
        this.hotelId = hotelId;
    }

    @Basic
    @Column(name = "ACCOMMODATION_STATUS", nullable = true, length = 2)
    public String getAccommodationStatus() {
        return accommodationStatus;
    }

    public void setAccommodationStatus(String accommodationStatus) {
        this.accommodationStatus = accommodationStatus;
    }

    @Basic
    @Column(name = "GUARANTEE_COM_TIME", nullable = true)
    public Date getGuaranteeComTime() {
        return guaranteeComTime;
    }

    public void setGuaranteeComTime(Date guaranteeComTime) {
        this.guaranteeComTime = guaranteeComTime;
    }

    @Basic
    @Column(name = "SETTLE_TIME", nullable = true)
    public Date getSettleTime() {
        return settleTime;
    }

    public void setSettleTime(Date settleTime) {
        this.settleTime = settleTime;
    }

    @Basic
    @Column(name = "SETTLE_ID", nullable = true, length = 32)
    public String getSettleId() {
        return settleId;
    }

    public void setSettleId(String settleId) {
        this.settleId = settleId;
    }

    @Basic
    @Column(name = "SINGLE_AMOUNT", nullable = true, length = 5)
    public String getSingleAmount() {
        return singleAmount;
    }

    public void setSingleAmount(String singleAmount) {
        this.singleAmount = singleAmount;
    }

    @Basic
    @Column(name = "DOUBLE_AMOUNT", nullable = true, length = 5)
    public String getDoubleAmount() {
        return doubleAmount;
    }

    public void setDoubleAmount(String doubleAmount) {
        this.doubleAmount = doubleAmount;
    }

    @Basic
    @Column(name = "SINGLE_PRICE", nullable = true, length = 5)
    public String getSinglePrice() {
        return singlePrice;
    }

    public void setSinglePrice(String singlePrice) {
        this.singlePrice = singlePrice;
    }

    @Basic
    @Column(name = "DOUBLE_PRICE", nullable = true, length = 5)
    public String getDoublePrice() {
        return doublePrice;
    }

    public void setDoublePrice(String doublePrice) {
        this.doublePrice = doublePrice;
    }

    @Basic
    @Column(name = "ORDER_ID", nullable = true, length = 255)
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Basic
    @Column(name = "PRE_SUM_MONEY", nullable = true, precision = 2)
    public Long getPreSumMoney() {
        return preSumMoney;
    }

    public void setPreSumMoney(Long preSumMoney) {
        this.preSumMoney = preSumMoney;
    }

    @Basic
    @Column(name = "CREATE_DATE", nullable = true)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "MODIFY_DATE", nullable = true)
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Basic
    @Column(name = "CREATE_ID", nullable = true, length = 32)
    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    @Basic
    @Column(name = "MODIFY_ID", nullable = true, length = 32)
    public String getModifyId() {
        return modifyId;
    }

    public void setModifyId(String modifyId) {
        this.modifyId = modifyId;
    }

    @Basic
    @Column(name = "REAL_SUM_MONEY", nullable = true, precision = 2)
    public Long getRealSumMoney() {
        return realSumMoney;
    }

    public void setRealSumMoney(Long realSumMonry) {
        this.realSumMoney = realSumMonry;
    }

    @Basic
    @Column(name = "NOTE_INFO", nullable = true, length = 1000)
    public String getNoteInfo() {
        return noteInfo;
    }

    public void setNoteInfo(String noteInfo) {
        this.noteInfo = noteInfo;
    }

    @Basic
    @Column(name = "IS_VERIFICATION", nullable = true, length = 2)
    public String getIsVerification() {
        return isVerification;
    }

    public void setIsVerification(String isVerification) {
        this.isVerification = isVerification;
    }

    @Basic
    @Column(name = "SETTLE_STATUS", nullable = true, length = 2)
    public String getSettleStatus() {
        return settleStatus;
    }

    public void setSettleStatus(String settleStatus) {
        this.settleStatus = settleStatus;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FdHotelAccommodation that = (FdHotelAccommodation) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(accommodationNo, that.accommodationNo) &&
                Objects.equals(hotelId, that.hotelId) &&
                Objects.equals(accommodationStatus, that.accommodationStatus) &&
                Objects.equals(guaranteeComTime, that.guaranteeComTime) &&
                Objects.equals(settleTime, that.settleTime) &&
                Objects.equals(settleId, that.settleId) &&
                Objects.equals(singleAmount, that.singleAmount) &&
                Objects.equals(doubleAmount, that.doubleAmount) &&
                Objects.equals(singlePrice, that.singlePrice) &&
                Objects.equals(doublePrice, that.doublePrice) &&
                Objects.equals(orderId, that.orderId) &&
                Objects.equals(preSumMoney, that.preSumMoney) &&
                Objects.equals(createDate, that.createDate) &&
                Objects.equals(modifyDate, that.modifyDate) &&
                Objects.equals(createId, that.createId) &&
                Objects.equals(modifyId, that.modifyId) &&
                Objects.equals(realSumMoney, that.realSumMoney) &&
                Objects.equals(noteInfo, that.noteInfo) &&
                Objects.equals(isVerification, that.isVerification) &&
                Objects.equals(settleStatus, that.settleStatus);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, accommodationNo, hotelId, accommodationStatus, guaranteeComTime, settleTime, settleId, singleAmount, doubleAmount, singlePrice, doublePrice, orderId, preSumMoney, createDate, modifyDate, createId, modifyId, realSumMoney, noteInfo, isVerification, settleStatus);
    }

    @Basic
    @Column(name = "SETTLEMENT_TYPE", nullable = true, length = 2)
    public String getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType;
    }

    @Basic
    @Column(name = "FIRST_CLASS_AMOUNT", nullable = true, length = 50)
    public String getFirstClassAmount() {
        return firstClassAmount;
    }

    public void setFirstClassAmount(String firstClassAmount) {
        this.firstClassAmount = firstClassAmount;
    }

    @Basic
    @Column(name = "ECONOMY_CLASS_AMOUNT", nullable = true, length = 50)
    public String getEconomyClassAmount() {
        return economyClassAmount;
    }

    public void setEconomyClassAmount(String economyClassAmount) {
        this.economyClassAmount = economyClassAmount;
    }

    @Basic
    @Column(name = "FIRST_CLASS_DAY_AMOUNT", nullable = true, length = 255)
    public String getFirstClassDayAmount() {
        return firstClassDayAmount;
    }

    public void setFirstClassDayAmount(String firstClassDayAmount) {
        this.firstClassDayAmount = firstClassDayAmount;
    }

    @Basic
    @Column(name = "ECONOMY_CLASS_DAY_AMOUNT", nullable = true, length = 255)
    public String getEconomyClassDayAmount() {
        return economyClassDayAmount;
    }

    public void setEconomyClassDayAmount(String economyClassDayAmount) {
        this.economyClassDayAmount = economyClassDayAmount;
    }

    @Basic
    @Column(name = "SINGLE_DAY_AMOUNT", nullable = true, length = 100)
    public String getSingleDayAmount() {
        return singleDayAmount;
    }

    public void setSingleDayAmount(String singleDayAmount) {
        this.singleDayAmount = singleDayAmount;
    }

    @Basic
    @Column(name = "DOUBLE_DAY_AMOUNT", nullable = true, length = 100)
    public String getDoubleDayAmount() {
        return doubleDayAmount;
    }

    public void setDoubleDayAmount(String doubleDayAmount) {
        this.doubleDayAmount = doubleDayAmount;
    }
}
