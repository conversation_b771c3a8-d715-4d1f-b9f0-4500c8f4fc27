package com.swcares.sdahotel.entity;/**
 * 作者：徐士昊
 * 时间：2022/09/16 15:50
 **/

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;

import javax.persistence.*;
import java.util.Objects;

/**
 * <AUTHOR>
 * Date on 2022/09/16  15:50
 */
@Entity
@Table(name = "FD_HOTEL_PAX_INFO", schema = "GSSPSC", catalog = "")
@EncryptionClassz
public class FdHotelPaxInfo {
    private String id;
    private String orderId;
    private String paxId;
    private String paxName;
    private String idType;
    @Encryption
    private String idNo;
    private String sex;
    @Encryption
    private String telephone;
    private String segment;
    private String orgCityAirp;
    private String dstCityAirp;
    private String paxStatus;
    private String mainClass;
    private String subClass;
    private String tktNo;
    private String withBaby;
    private String isChild;
    private String babyPaxName;
    private String tktIssueDate;
    private String switchOff;
    private String etdServiceDay;
    private String pnr;
    private String isFlag;

    @Id
    @Column(name = "ID", nullable = false, precision = 0)
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Basic
    @Column(name = "ORDER_ID", nullable = true, length = 32)
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Basic
    @Column(name = "PAX_ID", nullable = true, length = 64)
    public String getPaxId() {
        return paxId;
    }

    public void setPaxId(String paxId) {
        this.paxId = paxId;
    }

    @Basic
    @Column(name = "PAX_NAME", nullable = true, length = 225)
    public String getPaxName() {
        return paxName;
    }

    public void setPaxName(String paxName) {
        this.paxName = paxName;
    }

    @Basic
    @Column(name = "ID_TYPE", nullable = true, length = 225)
    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    @Basic
    @Column(name = "ID_NO", nullable = true, length = 225)
    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    @Basic
    @Column(name = "SEX", nullable = true, length = 2)
    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    @Basic
    @Column(name = "TELEPHONE", nullable = true, length = 225)
    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    @Basic
    @Column(name = "SEGMENT", nullable = true, length = 225)
    public String getSegment() {
        return segment;
    }

    public void setSegment(String segment) {
        this.segment = segment;
    }

    @Basic
    @Column(name = "ORG_CITY_AIRP", nullable = true, length = 64)
    public String getOrgCityAirp() {
        return orgCityAirp;
    }

    public void setOrgCityAirp(String orgCityAirp) {
        this.orgCityAirp = orgCityAirp;
    }

    @Basic
    @Column(name = "DST_CITY_AIRP", nullable = true, length = 64)
    public String getDstCityAirp() {
        return dstCityAirp;
    }

    public void setDstCityAirp(String dstCityAirp) {
        this.dstCityAirp = dstCityAirp;
    }

    @Basic
    @Column(name = "PAX_STATUS", nullable = true, length = 32)
    public String getPaxStatus() {
        return paxStatus;
    }

    public void setPaxStatus(String paxStatus) {
        this.paxStatus = paxStatus;
    }

    @Basic
    @Column(name = "MAIN_CLASS", nullable = true, length = 32)
    public String getMainClass() {
        return mainClass;
    }

    public void setMainClass(String mainClass) {
        this.mainClass = mainClass;
    }

    @Basic
    @Column(name = "SUB_CLASS", nullable = true, length = 32)
    public String getSubClass() {
        return subClass;
    }

    public void setSubClass(String subClass) {
        this.subClass = subClass;
    }

    @Basic
    @Column(name = "TKT_NO", nullable = true, length = 128)
    public String getTktNo() {
        return tktNo;
    }

    public void setTktNo(String tktNo) {
        this.tktNo = tktNo;
    }

    @Basic
    @Column(name = "WITH_BABY", nullable = true, length = 3)
    public String getWithBaby() {
        return withBaby;
    }

    public void setWithBaby(String withBaby) {
        this.withBaby = withBaby;
    }

    @Basic
    @Column(name = "IS_CHILD", nullable = true, length = 3)
    public String getIsChild() {
        return isChild;
    }

    public void setIsChild(String isChild) {
        this.isChild = isChild;
    }

    @Basic
    @Column(name = "BABY_PAX_NAME", nullable = true, length = 225)
    public String getBabyPaxName() {
        return babyPaxName;
    }

    public void setBabyPaxName(String babyPaxName) {
        this.babyPaxName = babyPaxName;
    }

    @Basic
    @Column(name = "TKT_ISSUE_DATE", nullable = true)
    public String getTktIssueDate() {
        return tktIssueDate;
    }

    public void setTktIssueDate(String  tktIssueDate) {
        this.tktIssueDate = tktIssueDate;
    }

    @Basic
    @Column(name = "SWITCH_OFF", nullable = true, length = 2)
    public String getSwitchOff() {
        return switchOff;
    }

    public void setSwitchOff(String switchOff) {
        this.switchOff = switchOff;
    }

    @Basic
    @Column(name = "ETD_SERVICE_DAY", nullable = true, length = 3)
    public String getEtdServiceDay() {
        return etdServiceDay;
    }

    public void setEtdServiceDay(String etdServiceDay) {
        this.etdServiceDay = etdServiceDay;
    }

    @Basic
    @Column(name = "PNR", nullable = true, length = 128)
    public String getPnr() {
        return pnr;
    }

    public void setPnr(String pnr) {
        this.pnr = pnr;
    }

    @Basic
    @Column(name = "IS_FLAG", nullable = true, length = 2)
    public String getIsFlag() {
        return isFlag;
    }

    public void setIsFlag(String isFlag) {
        this.isFlag = isFlag;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FdHotelPaxInfo that = (FdHotelPaxInfo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(orderId, that.orderId) &&
                Objects.equals(paxId, that.paxId) &&
                Objects.equals(paxName, that.paxName) &&
                Objects.equals(idType, that.idType) &&
                Objects.equals(idNo, that.idNo) &&
                Objects.equals(sex, that.sex) &&
                Objects.equals(telephone, that.telephone) &&
                Objects.equals(segment, that.segment) &&
                Objects.equals(orgCityAirp, that.orgCityAirp) &&
                Objects.equals(dstCityAirp, that.dstCityAirp) &&
                Objects.equals(paxStatus, that.paxStatus) &&
                Objects.equals(mainClass, that.mainClass) &&
                Objects.equals(subClass, that.subClass) &&
                Objects.equals(tktNo, that.tktNo) &&
                Objects.equals(withBaby, that.withBaby) &&
                Objects.equals(isChild, that.isChild) &&
                Objects.equals(babyPaxName, that.babyPaxName) &&
                Objects.equals(tktIssueDate, that.tktIssueDate) &&
                Objects.equals(switchOff, that.switchOff) &&
                Objects.equals(etdServiceDay, that.etdServiceDay) &&
                Objects.equals(pnr, that.pnr) &&
                Objects.equals(isFlag, that.isFlag);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, orderId, paxId, paxName, idType, idNo, sex, telephone, segment, orgCityAirp, dstCityAirp, paxStatus, mainClass, subClass, tktNo, withBaby, isChild, babyPaxName, tktIssueDate, switchOff, etdServiceDay, pnr, isFlag);
    }
}
