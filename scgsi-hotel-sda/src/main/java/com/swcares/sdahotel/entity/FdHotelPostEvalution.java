package com.swcares.sdahotel.entity;/**
 * 作者：徐士昊
 * 时间：2023/1/29 0029 11:04
 **/

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * Date on 2023/1/29 0029  11:04
 */
@Entity
@Table(name = "FD_HOTEL_POST_EVALUTION", schema = "GSSPSC", catalog = "")
public class FdHotelPostEvalution {
    private String id;
    private String hotelName;
    private String wholeFeel;
    private String cleaningService;
    private String serviceAttitude;
    private String roomComfort;
    private String diningEnvironment;
    private String vehiclePickup;
    private String improveItems;
    private String otherItem;
    private Date createTime;
    private String airportName;

    @Id
    @Column(name = "ID", nullable = false, length = 50)
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Basic
    @Column(name = "HOTEL_NAME", nullable = false, length = 300)
    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    @Basic
    @Column(name = "WHOLE_FEEL", nullable = false, length = 4)
    public String getWholeFeel() {
        return wholeFeel;
    }

    public void setWholeFeel(String wholeFeel) {
        this.wholeFeel = wholeFeel;
    }

    @Basic
    @Column(name = "CLEANING_SERVICE", nullable = false, length = 4)
    public String getCleaningService() {
        return cleaningService;
    }

    public void setCleaningService(String cleaningService) {
        this.cleaningService = cleaningService;
    }

    @Basic
    @Column(name = "SERVICE_ATTITUDE", nullable = false, length = 4)
    public String getServiceAttitude() {
        return serviceAttitude;
    }

    public void setServiceAttitude(String serviceAttitude) {
        this.serviceAttitude = serviceAttitude;
    }

    @Basic
    @Column(name = "ROOM_COMFORT", nullable = false, length = 4)
    public String getRoomComfort() {
        return roomComfort;
    }

    public void setRoomComfort(String roomComfort) {
        this.roomComfort = roomComfort;
    }

    @Basic
    @Column(name = "DINING_ENVIRONMENT", nullable = true, length = 4)
    public String getDiningEnvironment() {
        return diningEnvironment;
    }

    public void setDiningEnvironment(String diningEnvironment) {
        this.diningEnvironment = diningEnvironment;
    }

    @Basic
    @Column(name = "VEHICLE_PICKUP", nullable = true, length = 4)
    public String getVehiclePickup() {
        return vehiclePickup;
    }

    public void setVehiclePickup(String vehiclePickup) {
        this.vehiclePickup = vehiclePickup;
    }

    @Basic
    @Column(name = "IMPROVE_ITEMS", nullable = true, length = 300)
    public String getImproveItems() {
        return improveItems;
    }

    public void setImproveItems(String improveItems) {
        this.improveItems = improveItems;
    }

    @Basic
    @Column(name = "OTHER_ITEM", nullable = true, length = 300)
    public String getOtherItem() {
        return otherItem;
    }

    public void setOtherItem(String otherItem) {
        this.otherItem = otherItem;
    }

    @Basic
    @Column(name = "CREATE_TIME", nullable = false)
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "AIRPORT_NAME", nullable = false, length = 300)
    public String getAirportName() {
        return airportName;
    }

    public void setAirportName(String airportName) {
        this.airportName = airportName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FdHotelPostEvalution that = (FdHotelPostEvalution) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(hotelName, that.hotelName) &&
                Objects.equals(wholeFeel, that.wholeFeel) &&
                Objects.equals(cleaningService, that.cleaningService) &&
                Objects.equals(serviceAttitude, that.serviceAttitude) &&
                Objects.equals(roomComfort, that.roomComfort) &&
                Objects.equals(diningEnvironment, that.diningEnvironment) &&
                Objects.equals(vehiclePickup, that.vehiclePickup) &&
                Objects.equals(improveItems, that.improveItems) &&
                Objects.equals(otherItem, that.otherItem) &&
                Objects.equals(createTime, that.createTime) &&
                Objects.equals(airportName, that.airportName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, hotelName, wholeFeel, cleaningService, serviceAttitude, roomComfort, diningEnvironment, vehiclePickup, improveItems, otherItem, createTime, airportName);
    }
}
