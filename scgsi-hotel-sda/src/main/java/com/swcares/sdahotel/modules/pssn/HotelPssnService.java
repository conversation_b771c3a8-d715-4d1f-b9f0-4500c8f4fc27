package com.swcares.sdahotel.modules.pssn;/**
 * 作者：徐士昊
 * 时间：2022/09/23 10:36
 **/

import com.swcares.exception.MessageCode;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.dao.HotelPssnDao;
import com.swcares.sdahotel.vo.HotelPssnVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * Date on 2022/09/23  10:36
 */
@Service
@Transactional
public class HotelPssnService {

    @Autowired
    HotelPssnDao pssnDao;
    public RenderResult getPssnListByAmdNo(String amdno){
      List<HotelPssnVo> list= pssnDao.getPssnListByAmdNo(amdno);
      return  RenderResult.build(MessageCode.SUCCESS.getCode(),"获取成功",list);
    }


}
