package com.swcares.sdahotel.modules.login;/**
 * 作者：徐士昊
 * 时间：2022/06/01 15:48
 **/

import com.jfinal.kit.StrKit;
import com.swcares.scgsi.common.utils.JwtTokenUtils;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.config.SdaConstantConfig;
import com.swcares.sdahotel.common.exception.HotelException;
import com.swcares.sdahotel.common.global.Ret;
import com.swcares.sdahotel.common.kit.Md5Kit;
import com.swcares.sdahotel.dao.HotelUserDao;
import com.swcares.sdahotel.entity.FdHotelUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * Date on 2022/06/01  15:48
 */
@Service
@Component("hotelLoginService")
@Slf4j
public class LoginService {

    @Autowired
    HotelUserDao hotelUserDao;

    @Autowired
    RedisService redisService;

    private static final String LOGIN_ERROR_ACC_PREFIX = "loginerroraccprefix";

    @Transactional
    public RenderResult login(String account, String password) throws HotelException {
        String uniKey = LOGIN_ERROR_ACC_PREFIX + account;
        Optional<Object> uniValueObj = Optional.ofNullable(redisService.get(uniKey));
        int uniValue = (int) uniValueObj.orElse(0);

        FdHotelUserInfo user = hotelUserDao.getByAccount(account);

        if (user!=null&&!StrKit.equals(user.getUserStatus(), "0")) {
            throw new HotelException("用户已被禁用，请联系管理员");
        }

        if (uniValue >= SdaConstantConfig.LOGIN_ERROR_LIMITS) {
            if(user!=null) {
                user.setUserStatus("1");
                user.setStatereason("登录次数过多，系统自动禁用");
                hotelUserDao.update(user);
                redisService.deleteKey(uniKey);
            }
            throw new HotelException("账户已被禁用，请联系管理员");
        }
        if (user == null) {
            uniValue = uniValue + 1;
            redisService.set(uniKey, uniValue, 60 * 60 * 2);
            throw new HotelException("用户名或密码错误");
        }
        if (!Md5Kit.md5(password).equals(user.getPassword())) {
            uniValue = uniValue + 1;
            redisService.set(uniKey, uniValue, 60 * 60 * 2);
            throw new HotelException("用户名或密码错误");
        }

        //更新最后一次登录时间
        user.setLastLoginDate(new Date());
        hotelUserDao.update(user);
        //isRem..设置为为true，这样token的有效期是7天
        String token = JwtTokenUtils.createToken(account, null, user.getId(), true);
        //由于jwt token无法在服务器清除，只能自动过期，因此设置在redis中的标记,取值随意
        String redisKey = SdaConstantConfig.TOKEN_PREFIEX + token;
        redisService.set(redisKey, 10000000000L, 60 * 60 * 24);
        Ret ret = Ret.newInstance();
        ret.put("token", token);
        ret.put("userId", user.getId());
        RenderResult suc = RenderResult.success(ret);
        suc.setMsg("登录成功");
        log.info(user.getAccount() + "登录成功");
        return suc;


    }
}
