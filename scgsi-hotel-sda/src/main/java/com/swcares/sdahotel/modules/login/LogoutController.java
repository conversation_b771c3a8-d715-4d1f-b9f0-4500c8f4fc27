package com.swcares.sdahotel.modules.login;/**
 * 作者：徐士昊
 * 时间：2022/08/24 11:30
 **/

import com.swcares.scgsi.common.utils.JwtTokenUtils;
import com.swcares.scgsi.redis.RedisService;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.config.SdaConstantConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * Date on 2022/08/24  11:30
 */
@RestController
@RequestMapping("/api/sda/hotel/user")
public class LogoutController {

    @Autowired
    RedisService redisService;

    @PostMapping("/logout")
    public RenderResult logout(@RequestHeader("token") String token) {
        String key= SdaConstantConfig.TOKEN_PREFIEX+token;
        redisService.deleteKey(key);
        RenderResult rr = RenderResult.success();
        rr.setMsg("登出成功");
        return rr;
    }

}
