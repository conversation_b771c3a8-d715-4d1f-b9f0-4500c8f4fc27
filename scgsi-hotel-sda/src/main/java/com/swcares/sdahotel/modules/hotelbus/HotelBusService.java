package com.swcares.sdahotel.modules.hotelbus;/**
 * 作者：徐士昊
 * 时间：2022/09/22 15:52
 **/

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.StrKit;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.aop.EncryptFieldAop;
import com.swcares.scgsi.hotel.service.impl.HotelCompensateServiceImpl;
import com.swcares.scgsi.hotel.service.impl.SettleReviewServiceImpl;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.config.SdaConstantConfig;
import com.swcares.sdahotel.common.context.UserContext;
import com.swcares.sdahotel.common.enums.*;
import com.swcares.sdahotel.common.exception.HotelException;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.common.global.Ret;
import com.swcares.sdahotel.dao.*;
import com.swcares.sdahotel.dto.HotelOrderDao;
import com.swcares.sdahotel.entity.*;
import com.swcares.sdahotel.modules.amd.HotelAmdService;
import com.swcares.sdahotel.vo.HotelOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;


/**
 * <AUTHOR>
 * Date on 2022/09/22  15:52
 */
@Service
@Transactional
@Slf4j
public class HotelBusService {

    @Autowired
    HotelOrderDao orderDao;
    @Autowired
    HotelUserDao userDao;
    @Autowired
    HotelPssnDao hotelPssnDao;
    @Autowired
    HotelAmdDao amdDao;

    @Autowired
    HotelRoomDao roomDao;
    @Autowired
    HotelPaxDao hotelPaxDao;
    @Autowired
    HotelAmdService amdService;

    @Autowired
    SdaHotelSettlementDao settlementDao;

    @Autowired
    HotelDao hotelDao;
    @Autowired
    HotelCompensateServiceImpl hotelCompensateService;

    @Autowired
    SettleReviewServiceImpl settleReviewService;
    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmss");


    /**
     * 根据证件号查找有效的住宿单（山航）
     *
     * @param idNo
     * @param userid
     * @return
     */
    public RenderResult findOrdersForAccommodationByIdNo(FdHotelPaxInfo paxInfo, String userid) {
        StringBuilder sb = new StringBuilder();
        String idNo = getEncryptIdNo(paxInfo);
        FdHotelUserInfo userInfo = userDao.getById(userid);
        String hotelId = userInfo.getHotelId();
        FdHotelInfo hotelInfo = new FdHotelInfo();
        if (StrKit.notBlank(hotelId)) {
            hotelInfo = hotelDao.getById(hotelId);
        }
        List<HotelOrderVo> ordersInDb = orderDao.findValidateOrdersByIdNo(idNo, hotelId);
        String ids = "";
        sb.append(String.format("查询%s用户的山航住宿单有%d个;", userid, ordersInDb.size()));
        List<HotelOrderVo> orders = new ArrayList<>();
        //过滤旅客已入住的山航住宿单
        for (HotelOrderVo o : ordersInDb) {
            String orderId = o.getOrderId();
            String paxId = o.getPaxId();
            //查询指定的山航住宿单该旅客是否有入住记录
            FdHotelPssnInfo pssn = hotelPssnDao.getByPaxIdAndOrderId(paxId, orderId);
            if (pssn == null) {
                sb.append(String.format("查询%s用户的山航住宿单有%d个，订单%s旅客没有%s【酒店】的入住记录，满足条件;\n", userid, ordersInDb.size(), hotelInfo.getHotelName(), o.toString()));
                orders.add(o);
            }
            sb.append(String.format("查询%s用户的有效住宿单有%d个，订单%s没有%s【酒店】的入住记录，不满足条件，已被过滤;\n", userid, ordersInDb.size(), hotelInfo.getHotelName(), o.toString()));
        }

        List<JSONObject> amdList = new ArrayList<>();
        for (HotelOrderVo o : orders) {
            FdHotelAccommodation amd = amdDao.getByOrderIdAndHotelId(o.getOrderId(), hotelId);

            String amdStatus = amd.getAccommodationStatus();
            if (amdStatus.equals(AmdStatusEnum.AWAIT_GUARANTEE.getCode()) ||
                    amdStatus.equals(AmdStatusEnum.UNDER_GUARANTEE.getCode())) {
                sb.append(String.format("根据山航住宿单ID:%s和酒店ID:%s，查找到的酒店住宿单信息:%s，且在待保障和保障中,满足要求;\n", o.getOrderId(), hotelId, amd.toString()));
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("hotelAmd", amd);
                jsonObject.put("sdaOrder", o);
                amdList.add(jsonObject);
            } else {
                sb.append(String.format("根据山航住宿单ID:%s和酒店ID:%s，查找到的酒店住宿单信息:%s，且不在待保障和保障中,不满足要求;\n", o.getOrderId(), hotelId, amd.toString()));
            }
        }
        log.info(sb.toString());
        RenderResult rr = RenderResult.build(MessageCode.SUCCESS.getCode(), "获取成功", amdList);
        return rr;
    }


    /**
     * 获取加密后的证件号
     *
     * @param paxInfo
     * @return
     */
    private String getEncryptIdNo(FdHotelPaxInfo paxInfo) {
        //手动加密
        EncryptFieldAop.manualEncrypt(paxInfo);
        String idNo = paxInfo.getIdNo();
        return idNo;
    }


    public RenderResult paxIn(FdHotelPssnInfo pssnInfo, String userid) {
        //pax中的id
        try {
            String paxId = pssnInfo.getHotelPaxId();
            String orderId = pssnInfo.getOrderId();
            String roomId = pssnInfo.getRoomId();

            FdHotelRoomInfo room = roomDao.getById(roomId);
            FdHotelUserInfo userInfo = userDao.getById(userid);
            String hotelId = userInfo.getHotelId();
            FdHotelAccommodation amdInfo = amdDao.getByOrderIdAndHotelId(orderId, hotelId);
            if (amdInfo == null) {
                throw new HotelException("没有查到为酒店生成的住宿单");
            }
            FdHotelPssnInfo pssnInFoDb = hotelPssnDao.getByPaxIdAndOrderId(paxId, orderId);
            if (pssnInFoDb != null) {
                throw new HotelException("旅客的该住宿单已办理入住");
            }
            String amdNo = amdInfo.getAccommodationNo();
            FdHotelOrderInfo order = orderDao.getById(orderId);
            pssnInfo.setId(IdUtil.fastSimpleUUID());
            pssnInfo.setAccommodationNo(amdNo);
            //如果山航保障单中的预计入住日期为空的话，就默认为1天
            pssnInfo.setGuaranteeDayCount(order.getEtdServiceDays() == null ? "1" : order.getEtdServiceDays());
            pssnInfo.setHotelId(hotelId);
            pssnInfo.setRoomId(roomId);
            pssnInfo.setRoomNo(room.getRoomNo());
            pssnInfo.setRoomType(room.getRoomType());
            pssnInfo.setCheckInTime(new Date());
            hotelPssnDao.save(pssnInfo);

            //修改酒店住宿单状态
            if (amdInfo.getAccommodationStatus().equals(AmdStatusEnum.AWAIT_GUARANTEE.getCode())) {
                amdInfo.setAccommodationStatus(AmdStatusEnum.UNDER_GUARANTEE.getCode());
                amdDao.update(amdInfo);
            }
            //修改山航住宿单状态
            if (order.getStatus().equals(OrderStatusEnum.AWAIT_GURANTEE.getCode())) {
                order.setStatus(OrderStatusEnum.UNDER_GURANTEE.getCode());
                orderDao.update(order);
            }


        } catch (HotelException e) {
            return RenderResult.build(MessageCode.FAIL.getCode(), e.getMsg(), null);
        }
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "操作成功", null);
    }


    public RenderResult complete(Req<FdHotelAccommodation> req, String userid) {

        String id = req.getParams().getId();
        FdHotelAccommodation amd = amdDao.findById(id);
        //已完成状态
        amd.setAccommodationStatus(AmdStatusEnum.AWAIT_SETTLE.getCode());
        amd.setModifyId(userid);
        amd.setModifyDate(new Date());
        amd.setGuaranteeComTime(new Date());
        //统计头等舱和经济舱数量
        appendCabins(amd);
        //统计单人间和双人间数量
        appendRooms(amd);
        //统计头等舱和经济舱收费天数
        appendCabinsDays(amd);
        //统计单人间双人间收费天数
        appendRoomDays(amd);
        //预结算金额计算(20221208废弃)
        appendPreSummary2(amd);
        amdDao.update(amd);
        //如果没有旅客,系统已跳过结算环节，自动结束改单
        if (Integer.valueOf(amd.getFirstClassAmount()) == 0 && Integer.valueOf(amd.getEconomyClassAmount()) == 0) {
            String setteId = autoSettle(amd);
            settleReviewService.updateByNoBody(setteId);
            //2023-2-15 状态变为已完成检测并变更服务保障单状态
            hotelCompensateService.completionOrder(amd.getAccommodationNo());
            amd.setSettleTime(new Date());
            amdDao.update(amd);
            return RenderResult.build(MessageCode.SUCCESS.getCode(), "该单没有旅客，自动结束", null);

        } else {
            return RenderResult.build(MessageCode.SUCCESS.getCode(), "操作成功", null);
        }

    }

    private void appendPreSummary2(FdHotelAccommodation amd) {
        String settlementType = amd.getSettlementType();
        //公务舱（单人间）   经济舱（双人间）
        int singlePrice = Integer.valueOf(amd.getSinglePrice() != null ? amd.getSinglePrice() : "0");
        int doublePrice = Integer.valueOf(amd.getDoublePrice() != null ? amd.getDoublePrice() : "0");
        Integer economyClassAmount = Integer.valueOf(amd.getEconomyClassAmount() != null ? amd.getEconomyClassAmount() : "0");
        Integer firstClassAmount = Integer.valueOf(amd.getFirstClassAmount() != null ? amd.getFirstClassAmount() : "0");
        Integer singleDayAmount = Integer.valueOf(amd.getSingleDayAmount() != null ? amd.getSingleDayAmount() : "0");
        Integer doubleDayAmount = Integer.valueOf(amd.getDoubleDayAmount() != null ? amd.getDoubleDayAmount() : "0");
        int total = 0;
        if (settlementType.equals(SettleType.REN.getCode())) {
            total = singlePrice * firstClassAmount + doublePrice * economyClassAmount;
        } else {
            total = singlePrice * singleDayAmount + doubleDayAmount * doublePrice;
        }
        amd.setPreSumMoney(Long.valueOf(total));
    }


    //统计头等舱和经济舱收费总天数
    private void appendCabinsDays(FdHotelAccommodation amd) {
        String amdNo = amd.getAccommodationNo();
        //先获取住宿单中的旅客
        List<FdHotelPssnInfo> pssns = hotelPssnDao.findByAmdNoWithHql(amdNo);
        int firstCabinDays = 0;
        int ecoCabinDays = 0;
        for (FdHotelPssnInfo pssn : pssns) {
            String paxId = pssn.getHotelPaxId();
            FdHotelPaxInfo pax = hotelPaxDao.getById(paxId);
            if (SdaConstantConfig.HIGN_CABIN.contains(pax.getMainClass()) || SdaConstantConfig.HIGN_CABIN.contains(pax.getSubClass())) {
                firstCabinDays += Integer.valueOf(pax.getEtdServiceDay());
            } else {
                ecoCabinDays += Integer.valueOf(pax.getEtdServiceDay());
            }
        }
        amd.setFirstClassDayAmount(String.valueOf(firstCabinDays));
        amd.setEconomyClassDayAmount(String.valueOf(ecoCabinDays));
    }


    /**
     * 20221208废弃
     *
     * @param amd
     */
    @Deprecated
    private void appendPreSummary(FdHotelAccommodation amd) {
        String settlementType = amd.getSettlementType();
        Integer single = Integer.valueOf(amd.getSinglePrice() != null ? amd.getSinglePrice() : "0");
        Integer doub = Integer.valueOf(amd.getDoublePrice() != null ? amd.getDoublePrice() : "0");
        // 0是按间  1是人
        if (settlementType == null || settlementType.equals(SettleType.REN.getCode())) {

            Integer firstClass = Integer.valueOf(amd.getFirstClassAmount());
            Integer ecoClass = Integer.valueOf(amd.getEconomyClassAmount());
            int firstM = firstClass * single;
            int ecoM = doub * ecoClass;
            int sumM = firstM + ecoM;
            amd.setPreSumMoney(Long.valueOf(sumM));
        } else {
            Integer singleAmount = Integer.valueOf(amd.getSingleAmount());
            Integer doubleAmount = Integer.valueOf(amd.getDoubleAmount());
            int singleM = singleAmount * single;
            int doubM = doubleAmount * doub;
            int sumM = singleM + doubM;
            amd.setPreSumMoney(Long.valueOf(sumM));
        }
    }


    /**
     * 公务舱（含高经） 和经济舱的入住人数
     *
     * @param amd
     */
    private void appendCabins(FdHotelAccommodation amd) {
        String amdNo = amd.getAccommodationNo();
        //先获取住宿单旅客
        List<FdHotelPssnInfo> pssns = hotelPssnDao.findByAmdNoWithHql(amdNo);
        int highCabin = 0;
        for (FdHotelPssnInfo pssn : pssns) {
            String paxId = pssn.getHotelPaxId();
            FdHotelPaxInfo pax = hotelPaxDao.getById(paxId);
            if (SdaConstantConfig.HIGN_CABIN.contains(pax.getMainClass()) || SdaConstantConfig.HIGN_CABIN.contains(pax.getSubClass())) {
                ++highCabin;
            }
        }
        int lowCabin = pssns.size() - highCabin;
        amd.setFirstClassAmount(String.valueOf(highCabin));
        amd.setEconomyClassAmount(String.valueOf(lowCabin));
    }

    /**
     * 分别计算按人 按间
     *
     * @param amd
     */
    private void appendRoomDays(FdHotelAccommodation amd) {
        String accommodationNo = amd.getAccommodationNo();
        int singleDays = amdDao.countDaysByRoomType(accommodationNo, RoomType.SINGLE.getCode());
        int doubleDays = amdDao.countDaysByRoomType(accommodationNo, RoomType.DOUBLE.getCode());
        amd.setSingleDayAmount(String.valueOf(singleDays));
        amd.setDoubleDayAmount(String.valueOf(doubleDays));

    }

    private void appendRooms(FdHotelAccommodation amd) {
        Set<String> singleSet = new HashSet<>();
        Set<String> doubleSet = new HashSet<>();
        List<FdHotelPssnInfo> pssns = hotelPssnDao.findByAmdNoWithHql(amd.getAccommodationNo());
        for (FdHotelPssnInfo pssn : pssns) {
            //1 单人  2双人
            if (pssn.getRoomType().equals(RoomType.SINGLE.getCode())) {
                if (!singleSet.contains(pssn.getRoomId())) {
                    singleSet.add(pssn.getRoomId());
                }
            } else {
                if (!doubleSet.contains(pssn.getRoomId())) {
                    doubleSet.add(pssn.getRoomId());
                }

            }
        }

        amd.setSingleAmount(String.valueOf(singleSet.size()));
        amd.setDoubleAmount(String.valueOf(doubleSet.size()));

    }

    //统计4种状态
    public RenderResult total(Req req, String userid) {
        FdHotelUserInfo userInfo = userDao.getById(userid);
        String hotelId = userInfo.getHotelId();
        //统计改酒店不同状态住宿单的数量
        int awaitGuarantee = amdDao.getStatusCount(AmdStatusEnum.AWAIT_GUARANTEE.getCode(), hotelId);
        int underGuarantee = amdDao.getStatusCount(AmdStatusEnum.UNDER_GUARANTEE.getCode(), hotelId);
        int waitSettle = amdDao.getStatusCount(AmdStatusEnum.AWAIT_SETTLE.getCode(), hotelId);
        int completeSettle = amdDao.getStatusCount(AmdStatusEnum.COMPLETE_SETTLE.getCode(), hotelId);
        int completed = amdDao.getStatusCount(AmdStatusEnum.COMPELETED.getCode(), hotelId);
        Ret ret = Ret.newInstance();
        ret.put("awaitGuarantee", awaitGuarantee);
        ret.put("underGuarantee", underGuarantee);
        ret.put("waitSettle", waitSettle);
        ret.put("completeSettle", completeSettle);
        ret.put("completed", completed);

        return RenderResult.build(MessageCode.SUCCESS.getCode(), "获取成功", ret);
    }


    public RenderResult startSettle(FdHotelAccommodation params, String userid) {
        String userId = UserContext.getUserId();
        String id = params.getId();
        FdHotelAccommodation amd = amdDao.findById(id);
        amd.setAccommodationStatus(AmdStatusEnum.UNDER_SETTLE.getCode());
        amd.setRealSumMoney(params.getRealSumMoney());
        amd.setNoteInfo(params.getNoteInfo());

        amd.setModifyDate(new Date());
        amd.setModifyId(userid);
        //结算表插入数据

       String  amdNo=amd.getAccommodationNo();
        List<FdHotelSettlementInfo> list= settlementDao.findByAmdNo(amdNo);
        if(list!=null&&list.size()>0){
            FdHotelSettlementInfo  settlementInfo=list.get(0);
            if(settlementInfo.getIsAuthFlag().equals(AmdVerifycationEnum.BACK.getCode())) {
                amd.setIsVerification(AmdVerifycationEnum.AWAIT.getCode());
                settlementInfo.setIsAuthFlag(AmdVerifycationEnum.AWAIT.getCode());
            }
            settlementInfo.setUpdateTime(new Date());
            settlementDao.update(settlementInfo);

        }else {

            FdHotelSettlementInfo settlementInfo = new FdHotelSettlementInfo();
            settlementInfo.setId(IdUtil.fastSimpleUUID());
            settlementInfo.setAccommodationNo(amd.getAccommodationNo());
            settlementInfo.setAccommodationId(amd.getId());
            settlementInfo.setIsAuthFlag(AmdVerifycationEnum.AWAIT.getCode());
            settlementInfo.setCreateId(userid);
            settlementInfo.setCreateTime(new Date());
            settlementInfo.setStatus(SettleStatusEnum.AWAIT.getCode());
            settlementInfo.setHotelId(amd.getHotelId());
            settlementInfo.setOrderId(amd.getOrderId());
            //20221205增加审核单号和更新日期
            settlementInfo.setAuditNo(generateAuditNo());
            settlementInfo.setUpdateTime(new Date());
            settlementDao.insert(settlementInfo);
        }
        amdDao.update(amd);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "成功", null);
    }


    private String autoSettle(FdHotelAccommodation params) {
        String userId = UserContext.getUserId();
        String id = params.getId();
        FdHotelAccommodation amd = amdDao.findById(id);
        amd.setAccommodationStatus(AmdStatusEnum.UNDER_SETTLE.getCode());
        amd.setRealSumMoney(params.getRealSumMoney());
        amd.setNoteInfo(params.getNoteInfo());
        //审核中
        amd.setSettleStatus("1");
        amd.setModifyDate(new Date());
        amd.setModifyId(userId);
        //结算表插入数据
        FdHotelSettlementInfo settlementInfo = new FdHotelSettlementInfo();
        settlementInfo.setId(IdUtil.fastSimpleUUID());
        settlementInfo.setAccommodationNo(amd.getAccommodationNo());
        settlementInfo.setAccommodationId(amd.getId());
        settlementInfo.setIsAuthFlag(AmdVerifycationEnum.AWAIT.getCode());
        settlementInfo.setCreateId(userId);
        settlementInfo.setCreateTime(new Date());
        settlementInfo.setStatus(SettleStatusEnum.AWAIT.getCode());
        settlementInfo.setHotelId(amd.getHotelId());
        settlementInfo.setOrderId(amd.getOrderId());
        //20221205增加审核单号和更新日期
        settlementInfo.setAuditNo(generateAuditNo());
        settlementInfo.setUpdateTime(new Date());
        settlementDao.insert2(settlementInfo);

        return settlementInfo.getId();
    }

    private static String generateAuditNo() {
        String format = sdf.format(new Date());
        Random random = new Random();
        int rInt = random.nextInt(9999 - 1000 + 1) + 1000;
        String sInt = String.valueOf(rInt);
        return format + sInt;
    }


    public static void main(String[] args) {
        System.out.println(Integer.parseInt(null));
    }
}
