package com.swcares.sdahotel.modules.city;/**
 * 作者：徐士昊
 * 时间：2022/08/26 9:32
 **/

import com.swcares.sdahotel.dao.CityDao;
import com.swcares.sdahotel.entity.CityCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * Date on 2022/08/26  9:32
 */
@Service
@Transactional
public class CityService {

    @Autowired
    CityDao cityDao;
    public List<CityCode> listAll(){
      return   cityDao.listAll();
    }

}
