package com.swcares.sdahotel.modules.pax;/**
 * 作者：徐士昊
 * 时间：2022/09/23 14:09
 **/

import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.entity.FdHotelPaxInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * Date on 2022/09/23  14:09
 */
@RestController
@RequestMapping("/api/sda/hotel/pax")
public class HotelPaxController extends BaseController {


    @Autowired
    HotelPaxService paxService;


    @PostMapping("/detail")
    public RenderResult detail(@RequestBody Req<FdHotelPaxInfo> req, HttpServletRequest request){
        FdHotelPaxInfo params = req.getParams();
        String id = params.getId();
        return paxService.getById(id);
    }

}
