package com.swcares.sdahotel.modules.pssn;/**
 * 作者：徐士昊
 * 时间：2022/09/26 15:55
 **/

import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.entity.FdHotelAccommodation;
import com.swcares.sdahotel.entity.FdHotelPssnInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * Date on 2022/09/26  15:55
 */
@RestController
@RequestMapping("/api/sda/hotel/pssn")
public class HotelPssnController {

    @Autowired
    HotelPssnService hotelPssnService;

    @RequestMapping("/getListByAmdNo")
    public RenderResult getByAmdNo(@RequestBody Req<FdHotelPssnInfo> params){
        return  hotelPssnService.getPssnListByAmdNo(params.getParams().getAccommodationNo());
    }
}
