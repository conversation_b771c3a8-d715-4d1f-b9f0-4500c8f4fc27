package com.swcares.sdahotel.modules.login;/**
 * 作者：徐士昊
 * 时间：2022/06/01 15:48
 **/


import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.exception.HotelException;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.entity.FdHotelUserInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * Date on 2022/06/01  15:48
 */
@RestController
@RequestMapping("/api/sda/hotel/user")
@Component("HotelLoginController")
public class LoginController {

    @Autowired
    @Qualifier("hotelLoginService")
    LoginService loginService;

    @PostMapping("/login")
    public RenderResult login(@RequestBody Req<FdHotelUserInfo> data) throws HotelException {
        FdHotelUserInfo params = data.getParams();
        String account = params.getAccount();
        String password = params.getPassword();
        return loginService.login(account, password);
    }
}
