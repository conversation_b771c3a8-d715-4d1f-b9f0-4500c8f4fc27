package com.swcares.sdahotel.modules.evalution;/**
 * 作者：徐士昊
 * 时间：2023/1/29 0029 11:08
 **/

import cn.hutool.core.util.IdUtil;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.dao.EvalutionDao;
import com.swcares.sdahotel.entity.FdHotelPostEvalution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 * Date on 2023/1/29 0029  11:08
 */
@Service
@Transactional
public class EvalutionService {


    @Autowired
    private EvalutionDao evalutionDao;

    public RenderResult save(Req<FdHotelPostEvalution> params) {
        FdHotelPostEvalution evalution = params.getParams();
        evalution.setId(IdUtil.fastSimpleUUID());
        evalution.setCreateTime(new Date());
        evalutionDao.create(evalution);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "获取成功", null);
    }
}
