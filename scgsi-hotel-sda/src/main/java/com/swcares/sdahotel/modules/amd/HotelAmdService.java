package com.swcares.sdahotel.modules.amd;/**
 * 作者：徐士昊
 * 时间：2022/09/23 15:04
 **/

import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.StrKit;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hotel.service.impl.HotelCompensateServiceImpl;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.enums.AmdStatusEnum;
import com.swcares.sdahotel.common.global.Ret;
import com.swcares.sdahotel.dao.HotelAmdDao;
import com.swcares.sdahotel.dao.HotelPssnDao;
import com.swcares.sdahotel.dao.HotelUserDao;
import com.swcares.sdahotel.dto.HotelOrderDao;
import com.swcares.sdahotel.dto.ListDto;
import com.swcares.sdahotel.entity.*;
import com.swcares.sdahotel.modules.order.HotelOrderService;
import com.swcares.sdahotel.vo.HotelAmdVo;
import com.swcares.sdahotel.vo.HotelPssnVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * Date on 2022/09/23  15:04
 */
@Service
@Transactional
@Slf4j
public class HotelAmdService {
    @Autowired
    HotelAmdDao amdDao;
    @Autowired
    HotelUserDao userDao;

    @Autowired
    HotelOrderDao orderDao;

    @Autowired
    HotelPssnDao pssnDao;

    @Autowired
    HotelOrderService orderService;


    @Autowired
    HotelCompensateServiceImpl   hotelCompensateService;

    public RenderResult list(ListDto params, String userid) {
        //如果当前登录用户是酒店用户，那么重新设置params中的HotelId,防止酒店用户传参为空获取全部酒店住宿单
        FdHotelUserInfo user = userDao.getById(userid);
        String hotelId = user.getHotelId();
        JSONObject query = params.getQuery();
        if (StrKit.notBlank(hotelId)) {
            query.put("hotelId", hotelId);
        }
        QueryResults qr = amdDao.list(params);
        //补充客户数量统计
        appendPssnCount(qr);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "获取成功", qr);
    }


    public List<HotelAmdVo> listExcel(ListDto params, String userid) {
        //如果当前登录用户是酒店用户，那么重新设置params中的HotelId,防止酒店用户传参为空获取全部酒店住宿单
        FdHotelUserInfo user = userDao.getById(userid);
        String hotelId = user.getHotelId();
        JSONObject query = params.getQuery();
        if (StrKit.notBlank(hotelId)) {
            query.put("hotelId", hotelId);
        }
        List<HotelAmdVo> list = amdDao.listAll(params);
        //补充客户数量统计
        appendPssnCountForExcel(list);
        return list;
    }


    private void appendPssnCountForExcel(List<HotelAmdVo> list) {
        for (HotelAmdVo amd : list) {
            String amdNo = amd.getAccommodationNo();
            List<FdHotelPssnInfo> list1 = pssnDao.findByAmdNoWithHql(amdNo);
            Optional<List> optional = Optional.of(list1);
            optional.ifPresent(l -> {
                amd.setPssnCount(String.valueOf(l.size()));
            });

            // arr.add(amd);

        }
    }

    private void appendPssnCount(QueryResults qr) {

        List<HotelAmdVo> list = (List<HotelAmdVo>) qr.getList();
        List<HotelAmdVo> arr = new ArrayList<>();
        for (HotelAmdVo amd : list) {
            String amdNo = amd.getAccommodationNo();
            List<FdHotelPssnInfo> list1 = pssnDao.findByAmdNoWithHql(amdNo);
            Optional<List> optional = Optional.of(list1);
            optional.ifPresent(l -> {
                amd.setPssnCount(String.valueOf(l.size()));
            });

            // arr.add(amd);

        }

    }

    public RenderResult getAmdDetail(FdHotelAccommodation params, String userid) {
        String id = params.getId();
        FdHotelAccommodation amd = amdDao.findById(id);
        String orderId = amd.getOrderId();
        FdHotelOrderInfo order = orderDao.getById(orderId);
        List<HotelPssnVo> pssnList = pssnDao.getPssnListByAmdNo(amd.getAccommodationNo());
        Ret ret = Ret.newInstance();
        ret.put("amd", amd);
        ret.put("order", order);
        ret.put("pssns", pssnList);
        ret.put("settle", null);
        ret.put("audit",new ArrayList<>());
        RenderResult rr = orderService.getAuditList(amd.getAccommodationNo());
        Map map = (HashMap) rr.getData();
        Optional optional = Optional.ofNullable(map.get("settle"));
        if (optional.isPresent()) {
            ret.put("settle", (FdHotelSettlementInfo) optional.get());
        }
        List audit = (List) map.get("audit");
        ret.put("audit", audit);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "获取成功", ret);
    }


    public RenderResult changeAmdStatus(FdHotelAccommodation params, String userid) {
        String id = params.getId();
        FdHotelAccommodation amd = amdDao.findById(id);
        amd.setAccommodationStatus(params.getAccommodationStatus());
        amd.setModifyDate(new Date());
        amd.setModifyId(userid);
        //2023-2-15  如果状态变为已完成(AmdStatusEnum.COMPELETED)检测并变更服务保障单状态
        if(amd.getAccommodationStatus().equals( AmdStatusEnum.COMPELETED.getCode())){
            hotelCompensateService.completionOrder(amd.getAccommodationNo());
            amd.setSettleTime(new Date());
        }
        amdDao.update(amd);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "操作成功", null);

    }
}
