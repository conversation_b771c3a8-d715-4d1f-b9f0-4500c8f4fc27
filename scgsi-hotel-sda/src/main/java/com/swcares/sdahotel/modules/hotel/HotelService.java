package com.swcares.sdahotel.modules.hotel;/**
 * 作者：徐士昊
 * 时间：2022/05/31 14:49
 **/

import cn.hutool.core.util.IdUtil;
import cn.hutool.db.Page;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.StrKit;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Ret;
import com.swcares.sdahotel.dao.HotelDao;
import com.swcares.sdahotel.dto.ListDto;
import com.swcares.sdahotel.entity.FdHotelInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Date on 2022/05/31  14:49
 */
@Service
@Transactional
public class HotelService {
    @Autowired
    HotelDao hotelDao;







    public RenderResult addHotel(FdHotelInfo hotel, String username) {
        hotel.setId(IdUtil.fastSimpleUUID());
        hotel.setCreateId(username);
        hotel.setCreateDate(new Date());
        hotel.setHotelStatus("0");
        hotel.setHotelDeleted("0");
        hotelDao.addHotel(hotel);
        Ret ret = Ret.newInstance();
        ret.put("id", hotel.getId());
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "保存成功", ret);
    }

    public RenderResult updateHotel(FdHotelInfo hotel, String userid) {
        String id = hotel.getId();
        FdHotelInfo hotelInDb = hotelDao.getById(id);
        hotel.setCreateId(hotelInDb.getCreateId());
        hotel.setCreateDate(hotelInDb.getCreateDate());
        hotel.setModifyId(userid);
        hotel.setModifyDate(new Date());
        //如果状态传过来为空，那么认为状态不变
        if (StrKit.isBlank(hotel.getHotelStatus())) {
            hotel.setHotelStatus(hotelInDb.getHotelStatus());
        }
        //如果被删除的状态传递过来为空，那么就认为删除状态不变
        if (StrKit.isBlank(hotel.getHotelDeleted())) {
            hotel.setHotelDeleted(hotelInDb.getHotelDeleted());
        }
        hotelDao.update(hotel);
        Ret ret = Ret.newInstance();
        ret.put("id", hotel.getId());
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "更新成功", ret);
    }


    public RenderResult deleteHotel(String hotelId, String username) {
        FdHotelInfo hotel = hotelDao.getById(hotelId);
        hotel.setHotelDeleted("1");
        hotel.setModifyDate(new Date());
        hotel.setModifyId(username);
        hotelDao.update(hotel);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "删除成功", null);
    }

    public RenderResult listHotelWithNoPage(FdHotelInfo hotelInfo) {
        List<FdHotelInfo> hotelInfos = hotelDao.listNoPage(hotelInfo);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "获取成功", hotelInfos);
    }

    public RenderResult listHotel(ListDto dto) {
        Page page = dto.getPage();
        JSONObject query = dto.getQuery();
        QueryResults qr = hotelDao.list(page, query);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "获取成功", qr);
    }

    public RenderResult getHotelById(String id) {
        FdHotelInfo hotel = hotelDao.getById(id);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "获取成功", hotel);
    }

    public RenderResult changeStatus(FdHotelInfo params, String userid) {
        String hotelId = params.getId();
        String status=params.getHotelStatus();
        FdHotelInfo hotel = hotelDao.getById(hotelId);
        hotel.setHotelStatus(status);
        hotel.setModifyDate(new Date());
        hotel.setModifyId(userid);
        hotelDao.update(hotel);
        return RenderResult.build(MessageCode.SUCCESS.getCode(),"成功",null);

    }
}
