package com.swcares.sdahotel.modules.hotelbus;/**
 * 作者：徐士昊
 * 时间：2022/09/22 11:26
 **/

import com.alibaba.fastjson.JSONObject;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.entity.FdHotelAccommodation;
import com.swcares.sdahotel.entity.FdHotelPaxInfo;
import com.swcares.sdahotel.entity.FdHotelPssnInfo;
import com.swcares.sdahotel.modules.amd.HotelAmdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * Date on 2022/09/22  11:26
 */
@RestController
@RequestMapping("/api/sda/hotel/hotelbus")
public class HotelBusController {
    @Autowired
    HotelBusService  hotelBusService;

    @Autowired
    HotelAmdService hotelAmdService;


    @PostMapping("/findOrders")
    public RenderResult findOrders(@RequestBody Req<FdHotelPaxInfo> req, HttpServletRequest request){
        String userid = request.getAttribute("userid").toString();
        RenderResult rr = hotelBusService.findOrdersForAccommodationByIdNo(req.getParams(), userid);
        return  rr;
    }

    /**
     * 办理入住
     * @param req
     * @param request
     * @return
     */
    @PostMapping("/paxIn")
    public RenderResult paxIn(@RequestBody Req<FdHotelPssnInfo> req, HttpServletRequest request){
        String userid = request.getAttribute("userid").toString();
        RenderResult rr=hotelBusService.paxIn(req.getParams(),userid);

        return  rr;
    }

    /**
     * 保障完成
     * @param req
     * @param request
     * @return
     */

    @RequestMapping("/completeService")
    public RenderResult completeService(@RequestBody Req<FdHotelAccommodation> req, HttpServletRequest request){
        String userid = request.getAttribute("userid").toString();
        RenderResult rr=hotelBusService.complete(req,userid);
        return  rr;
    }
    /**
     *
     */
    @RequestMapping("/total")
    public RenderResult total(@RequestBody Req req, HttpServletRequest request){
        String userid = request.getAttribute("userid").toString();
        RenderResult rr=hotelBusService.total(req,userid);
        return  rr;
    }


    @RequestMapping("/startSettle")
    public RenderResult startSettle(@RequestBody Req<FdHotelAccommodation> req, HttpServletRequest request){
        String userid = request.getAttribute("userid").toString();
        RenderResult rr=hotelBusService.startSettle(req.getParams(),userid);
        return  rr;
    }

}
