package com.swcares.sdahotel.modules.evalution;/**
 * 作者：徐士昊
 * 时间：2023/1/29 0029 11:06
 **/

import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.entity.FdHotelPostEvalution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * Date on 2023/1/29 0029  11:06
 */
@RestController
@RequestMapping("/api/sda/hotel/evalution")
public class EvalutionController {

    @Autowired
    EvalutionService  evalutionService;
    @PostMapping("add")
    RenderResult  add(@RequestBody Req<FdHotelPostEvalution> params){
       return evalutionService.save(params);
    }
}
