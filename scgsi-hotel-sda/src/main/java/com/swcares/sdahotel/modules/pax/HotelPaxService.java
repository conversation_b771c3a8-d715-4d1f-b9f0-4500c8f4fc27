package com.swcares.sdahotel.modules.pax;/**
 * 作者：徐士昊
 * 时间：2022/09/23 14:17
 **/

import com.swcares.exception.MessageCode;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Ret;
import com.swcares.sdahotel.dao.HotelPaxDao;
import com.swcares.sdahotel.entity.FdHotelOrderInfo;
import com.swcares.sdahotel.entity.FdHotelPaxInfo;
import com.swcares.sdahotel.dto.HotelOrderDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * Date on 2022/09/23  14:17
 */
@Service
@Transactional
public class HotelPaxService {


    @Autowired
    HotelPaxDao paxDao;

    @Autowired
    HotelOrderDao hotelOrderDao;

    public RenderResult getById(String id) {
        FdHotelPaxInfo pax = paxDao.getById(id);
        String orderId = pax.getOrderId();
        FdHotelOrderInfo orderInfo = hotelOrderDao.getById(orderId);
        Ret flight = Ret.newInstance();
        flight.put("flightNo", orderInfo.getFlightNo());
        flight.put("flightDate", orderInfo.getFlightDate());
        Ret rr = Ret.newInstance();
        rr.put("pax", pax);
        rr.put("flight", flight);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "获取成功", rr);

    }
}
