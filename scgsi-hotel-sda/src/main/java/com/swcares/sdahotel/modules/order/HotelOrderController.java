package com.swcares.sdahotel.modules.order;/**
 * 作者：徐士昊
 * 时间：2022/11/25 0025 17:11
 **/

import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.entity.FdHotelSettlementInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * Date on 2022/11/25 0025  17:11
 */
@RestController
@RequestMapping("/api/sda/hotel/order")
public class HotelOrderController {
    @Autowired
    HotelOrderService hotelOrderService;
    @RequestMapping("auditList")
    public RenderResult auditList(@RequestBody Req<FdHotelSettlementInfo> req) {
        return hotelOrderService.getAuditList(req.getParams().getAccommodationNo());

    }
}
