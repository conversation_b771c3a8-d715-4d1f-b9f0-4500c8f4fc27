package com.swcares.sdahotel.modules.user;/**
 * 作者：徐士昊
 * 时间：2022/05/11 11:07
 **/

import com.alibaba.fastjson.JSONObject;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.dto.ListDto;
import com.swcares.sdahotel.entity.FdHotelUserInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * Date on 2022/05/11  11:07
 */
@RestController
@RequestMapping("/api/sda/hotel/user")
public class HotelUserController {


    @Autowired
    HotelUserService userService;

    @PostMapping("/add")
    public RenderResult add(@RequestBody Req<FdHotelUserInfo> data, HttpServletRequest request) {
        String userid = request.getAttribute("userid").toString();
        FdHotelUserInfo hotelUserInfo = data.getParams();
        return userService.addHotelUser(hotelUserInfo, userid);

    }

    @PostMapping("/update")
    public RenderResult update(@RequestBody Req<FdHotelUserInfo> data, HttpServletRequest request) {
        String userid = request.getAttribute("userid").toString();
        FdHotelUserInfo hotelUserInfo = data.getParams();
        return userService.updateHotelUser(hotelUserInfo, userid);

    }

    @PostMapping("/detail")
    public RenderResult detail(@RequestBody Req<FdHotelUserInfo> data) {
        FdHotelUserInfo params = data.getParams();
        return userService.getUserById(params.getId());

    }

    @PostMapping("/batchDelete")
    public RenderResult batchDelete(@RequestBody Req<String[]> data) {
        String[] ids = data.getParams();
        return userService.deleteUserByIds(ids);

    }

    @PostMapping("/delete")
    public RenderResult delete(@RequestBody Req<JSONObject> data) {
        JSONObject params = data.getParams();
        String id = params.get("id").toString();
        return userService.deleteUserById(id);

    }

    @PostMapping("/changeStatus")
    public RenderResult changeStatus(@RequestBody Req<FdHotelUserInfo> data, HttpServletRequest request) {
        String username = request.getAttribute("username").toString();
        FdHotelUserInfo params = data.getParams();
        String userId = params.getId();
        String status = params.getUserStatus();
        return userService.changeStatus(userId, status, username);

    }

    @PostMapping("/changePwd")
    public RenderResult changePwd(@RequestBody Req<FdHotelUserInfo> data, HttpServletRequest request) {
        String username = request.getAttribute("username").toString();
        FdHotelUserInfo params = data.getParams();
        String userId = params.getId();
        String password = params.getPassword();
        return userService.changePassword(userId, password, username);

    }

    @PostMapping("/resetPwd")
    public RenderResult resetPwd(@RequestBody Req<FdHotelUserInfo> data, HttpServletRequest request) {
        String username = request.getAttribute("username").toString();
        FdHotelUserInfo params = data.getParams();
        String userId = params.getId();
        //为空表示重置为默认密码
        return userService.changePassword(userId, params.getPassword(), username);

    }

    @PostMapping("/list")

    public  RenderResult list(@RequestBody Req<ListDto> dao){
        ListDto listDao = dao.getParams();
        return userService.list(listDao);
    }

    @PostMapping("/listAll")
    public  RenderResult listAll(@RequestBody Req<FdHotelUserInfo> dao){
        FdHotelUserInfo hotelUserInfo = dao.getParams();
        return userService.listWithNoPage(hotelUserInfo);
    }
}
