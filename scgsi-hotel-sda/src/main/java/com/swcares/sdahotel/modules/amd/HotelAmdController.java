package com.swcares.sdahotel.modules.amd;/**
 * 作者：徐士昊
 * 时间：2022/09/23 15:03
 **/

import com.swcares.scgsi.util.ExcelImportExportUtil;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.context.UserContext;
import com.swcares.sdahotel.common.enums.AmdStatusEnum;
import com.swcares.sdahotel.common.exception.HotelException;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.common.global.Ret;
import com.swcares.sdahotel.dto.ListDto;
import com.swcares.sdahotel.entity.FdHotelAccommodation;
import com.swcares.sdahotel.vo.HotelAmdVo;
import com.swcares.sdahotel.vo.HotelAmdVoForExcel;
import com.swcares.sdahotel.vo.HotelPssnVo;
import com.swcares.sdahotel.vo.HotelPssnVoForExcel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Date on 2022/09/23  15:03
 */
@RestController
@RequestMapping("/api/sda/hotel/amd")
public class HotelAmdController {

    @Autowired
    HotelAmdService amdService;

    @RequestMapping("/list")
    public RenderResult list(@RequestBody Req<ListDto> req, HttpServletRequest request) {
        String userid = request.getAttribute("userid").toString();
        RenderResult rr = amdService.list(req.getParams(), userid);
        return rr;
    }


    @RequestMapping("listExport")
    public void listExport(@RequestBody Req<ListDto> req, HttpServletRequest request, HttpServletResponse response) throws HotelException {
        List<HotelAmdVo> list = amdService.listExcel(req.getParams(), UserContext.getUserId());
        String[] columns = new String[]{"酒店住宿单号", "山航住宿单号", "所属酒店", "住宿单状态", "保障完成时间", "结算时间", "单人间数量", "双人间数量", "单人间头等舱单价",
                "双人间经济舱单价", "预结算金额", "实际结算金额", "结算备注", "核验状态", "结算状态", "头等舱人数", "经济舱人数", "航班号", "航班日期", "服务航站", "航程", "入住数量", "创建时间"};
        String[] keys = new String[]{"accommodationNo", "orderNo", "hotelName", "accommodationStatus", "guaranteeComTime", "settleTime", "singleAmount",
                "doubleAmount", "singlePrice", "doublePrice", "preSumMoney", "realSumMoney", "noteInfo", "isVerification", "settleStatus", "firstClassAmount",
                "economyClassAmount", "flightNo", "flightDate", "serviceCity", "choiceSegment", "pssnCount", "createDate"};
        List<HotelAmdVoForExcel> voList = new ArrayList<>();
        for (HotelAmdVo vo : list) {
            HotelAmdVoForExcel excel = new HotelAmdVoForExcel();
            BeanUtils.copyProperties(vo, excel);
            voList.add(excel);
        }
        try {
            ExcelImportExportUtil.exportExcel(response, "amdList", voList, columns, keys);
        } catch (IOException e) {
            throw new HotelException("导出异常");
        }

    }

    @RequestMapping("/detail")
    public RenderResult detail(@RequestBody Req<FdHotelAccommodation> req, HttpServletRequest request) {
        String userid = request.getAttribute("userid").toString();
        RenderResult rr = amdService.getAmdDetail(req.getParams(), userid);
        return rr;
    }


    @RequestMapping("/detail/export")
    public void detailExport(@RequestBody Req<FdHotelAccommodation> req, HttpServletRequest request, HttpServletResponse response) throws HotelException {
        String userid = request.getAttribute("userid").toString();
        RenderResult rr = amdService.getAmdDetail(req.getParams(), userid);
        Ret ret = (Ret) rr.getData();
        List<HotelPssnVo> pssnList = (List<HotelPssnVo>) ret.get("pssns");
        String[] keys = new String[]{"accommodationNo", "hotelName", "paxName", "roomNo", "roomType", "checkInMode",
                "guaranteeDayCount", "checkInTime", "idNo", "sex", "telephone", "segment"};
        String[] clolumns = new String[]{"酒店住宿单号", "酒店名称", "旅客名称", "房间号", "房间类型", "入住类型", "保障天数", "入住时间",
                "证件号", "性别", "手机号", "航程"};
        List<HotelPssnVoForExcel> voList = new ArrayList<>();
        for (HotelPssnVo vo : pssnList) {
            HotelPssnVoForExcel excel = new HotelPssnVoForExcel();
            BeanUtils.copyProperties(vo, excel);
            voList.add(excel);
        }
        try {
            ExcelImportExportUtil.exportExcel(response, "pssnList", voList, clolumns, keys);
        } catch (IOException e) {
            throw new HotelException("导出异常");
        }

    }

    @RequestMapping("/complete")
    RenderResult completed(@RequestBody Req<FdHotelAccommodation> amd){
        amd.getParams().setAccommodationStatus(AmdStatusEnum.COMPELETED.getCode());
        return     amdService.changeAmdStatus(amd.getParams(),UserContext.getUserId());

    }


}
