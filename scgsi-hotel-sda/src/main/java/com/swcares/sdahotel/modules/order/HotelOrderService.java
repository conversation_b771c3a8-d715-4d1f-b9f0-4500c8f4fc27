package com.swcares.sdahotel.modules.order;/**
 * 作者：徐士昊
 * 时间：2022/09/22 14:41
 **/

import com.swcares.exception.MessageCode;
import com.swcares.scgsi.hotel.model.vo.HotelOrderAuditRecordVO;
import com.swcares.scgsi.hotel.service.HotelAuditService;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.dao.SdaHotelSettlementDao;
import com.swcares.sdahotel.entity.FdHotelOrderInfo;
import com.swcares.sdahotel.entity.FdHotelSettlementInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Date on 2022/09/22  14:41
 */
@Service
@Transactional
public class HotelOrderService {

    @Autowired
    HotelAuditService hotelAuditService;

    @Autowired
    SdaHotelSettlementDao sdaHotelSettlementDao;

    public List<FdHotelOrderInfo> findOrdersByIdNo(String idNo) {

        return null;

    }

    public RenderResult getAuditList(String amdNo) {
        Map res = new HashMap<>();
        List<HotelOrderAuditRecordVO> list = null;
        FdHotelSettlementInfo settle = null;
        List<FdHotelSettlementInfo> slist = null;
        try {
            slist = sdaHotelSettlementDao.findByAmdNo(amdNo);
            if (slist == null || slist.size() == 0) {
                res.put("audit", Collections.emptyList());
                res.put("settle", null);
                return RenderResult.build(MessageCode.SUCCESS.getCode(), "没有查询到审核流", res);
            }
            String id = slist.get(0).getId();
            settle = slist.get(0);
            list = hotelAuditService.auditOperationRecord(id);
            res.put("audit", list);
            res.put("settle", slist.get(0));
            return RenderResult.build(MessageCode.SUCCESS.getCode(), "成功", res);
        } catch (Exception e) {
            res.put("audit", Collections.emptyList());
            res.put("settle", slist.get(0));
            return RenderResult.build(MessageCode.SUCCESS.getCode() , "没有流程实例", res);
        }

    }


}
