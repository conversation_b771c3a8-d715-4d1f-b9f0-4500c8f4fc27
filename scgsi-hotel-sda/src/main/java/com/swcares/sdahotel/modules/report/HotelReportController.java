package com.swcares.sdahotel.modules.report;/**
 * 作者：徐士昊
 * 时间：2022/10/12 10:28
 **/

import com.alibaba.fastjson.JSONObject;
import com.swcares.scgsi.web.BaseController;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Req;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * Date on 2022/10/12  10:28
 */
@RestController
@RequestMapping("/api/sda/hotel/report")
public class HotelReportController extends BaseController {


    @Autowired
    HotelReportService reportService;

    @RequestMapping("/amdForHotel")
    public RenderResult amdForHotel(@RequestBody Req<JSONObject> req, HttpServletRequest request){

        String userid = request.getAttribute("userid").toString();
       return  reportService.amdForHotelReport(req,userid);

    }

}
