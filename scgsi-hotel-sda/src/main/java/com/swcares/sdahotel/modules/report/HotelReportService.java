package com.swcares.sdahotel.modules.report;/**
 * 作者：徐士昊
 * 时间：2022/10/12 10:31
 **/

import com.alibaba.fastjson.JSONObject;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.dao.HotelAmdDao;
import com.swcares.sdahotel.dao.HotelUserDao;
import com.swcares.sdahotel.entity.FdHotelUserInfo;
import com.swcares.sdahotel.vo.AmdReportVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * Date on 2022/10/12  10:31
 */
@Service
@Transactional
public class HotelReportService {


    @Autowired
    HotelUserDao userDao;

    @Autowired
    HotelAmdDao hotelAmdDao;
    public RenderResult amdForHotelReport(Req<JSONObject> req, String userid) {
        JSONObject params = req.getParams();
        String startDate = params.getString("startDate");
        String endDate = params.getString("endDate");
        FdHotelUserInfo user = userDao.getById(userid);
       List<AmdReportVo>  list=hotelAmdDao.computeAmdForHotelReport(startDate,endDate,user.getHotelId());
        return RenderResult.build(MessageCode.SUCCESS.getCode(),"提取成功",list);

    }
}
