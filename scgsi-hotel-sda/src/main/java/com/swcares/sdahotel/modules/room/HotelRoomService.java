package com.swcares.sdahotel.modules.room;/**
 * 作者：徐士昊
 * 时间：2022/06/07 11:09
 **/

import cn.hutool.core.util.IdUtil;
import com.jfinal.kit.Kv;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.dao.HotelRoomDao;
import com.swcares.sdahotel.entity.FdHotelRoomInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Date on 2022/06/07  11:09
 */
@Service
@Transactional
public class HotelRoomService {

    @Autowired
    HotelRoomDao hotelRoomDao;

    public RenderResult addRoom(String userid, Req<FdHotelRoomInfo> req) {
//        String id = IdUtil.fastSimpleUUID();
//        FdHotelRoomInfo room = req.getParams();
//
//        String roomNo = room.getRoomNo();
//
//       FdHotelRoomInfo roomInDb= hotelRoomDao.findRoomByHotelIdAndRoomNo(room.getHotelId(),roomNo);
//
//       if(roomInDb!=null){
//           return RenderResult.build(MessageCode.FAIL.getCode(), "房间号已存在", null);
//       }
//
//        room.setCreateDate(new Date());
//        room.setCreateId(userid);
//        room.setModifyId(userid);
//        room.setModifyDate(new Date());
//        room.setId(id);
//        room.setRoomDeleted("0");
//        hotelRoomDao.add(room);
//        Kv kv = Kv.create().set("id", id);
        FdHotelRoomInfo room = req.getParams();

        String roomNoString = room.getRoomNo();

        String[] roomNos = roomNoString.split(",");
        for (String roomNo : roomNos){
            FdHotelRoomInfo roomInDb= hotelRoomDao.findRoomByHotelIdAndRoomNo(room.getHotelId(),roomNo);

            if(roomInDb!=null){
                return RenderResult.build(MessageCode.FAIL.getCode(), "存在房间号重复添加", null);
            }
        }
        Kv kv = Kv.create();

        for (String roomNo : roomNos){
            FdHotelRoomInfo newRoom = new FdHotelRoomInfo();
            BeanUtils.copyProperties(room,newRoom);
            String id = IdUtil.fastSimpleUUID();
            newRoom.setRoomNo(roomNo);
            newRoom.setCreateDate(new Date());
            newRoom.setCreateId(userid);
            newRoom.setModifyId(userid);
            newRoom.setModifyDate(new Date());
            newRoom.setId(id);
            newRoom.setRoomDeleted("0");
            hotelRoomDao.add(newRoom);
            kv.set(roomNo, id);
        }
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "添加成功", kv);
    }

    public RenderResult updateRoom(String userid, Req<FdHotelRoomInfo> req) {
        FdHotelRoomInfo room = req.getParams();
        String id = room.getId();
        FdHotelRoomInfo roomInDb = hotelRoomDao.getById(id);
        roomInDb.setModifyId(userid);
        roomInDb.setModifyDate(new Date());
        roomInDb.setRoomType(room.getRoomType());
        roomInDb.setRoomStatus(room.getRoomStatus());
        hotelRoomDao.update(roomInDb);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "修改成功", id);
    }

    public RenderResult getById(String id) {
        FdHotelRoomInfo room = hotelRoomDao.getById(id);
        return  RenderResult.build(MessageCode.SUCCESS.getCode(),"获取成功",room);
    }
    public RenderResult listAll(FdHotelRoomInfo params) {
      List<FdHotelRoomInfo> list= hotelRoomDao.listAll(params.getHotelId(),params.getRoomNo(),params.getRoomStatus());
      return  RenderResult.build(MessageCode.SUCCESS.getCode(),"获取成功",list);
    }

    public RenderResult deleteById(String id) {
        FdHotelRoomInfo room = hotelRoomDao.getById(id);
        room.setRoomDeleted("1");
        hotelRoomDao.update(room);
        return  RenderResult.build(MessageCode.SUCCESS.getCode(),"删除成功",null);
    }
}
