package com.swcares.sdahotel.modules.user;/**
 * 作者：徐士昊
 * 时间：2022/05/31 10:31
 **/

import cn.hutool.core.util.IdUtil;
import cn.hutool.db.Page;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.StrKit;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.kit.Md5Kit;
import com.swcares.sdahotel.dao.HotelUserDao;
import com.swcares.sdahotel.dto.ListDto;
import com.swcares.sdahotel.entity.FdHotelUserInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Date on 2022/05/31  10:31
 */
@Service
@Transactional
public class HotelUserService {
    @Autowired
    HotelUserDao hotelUserDao;


    static String DEFAULT_PASSWORD = "123456";

    public RenderResult getUserById(String id) {
        FdHotelUserInfo hotelUserInfo = hotelUserDao.getById(id);
        FdHotelUserInfo hotelUserInfo1=new FdHotelUserInfo();
        BeanUtils.copyProperties(hotelUserInfo,hotelUserInfo1);
        hotelUserInfo1.setPassword("");
        RenderResult<FdHotelUserInfo> renderResult = RenderResult.build(MessageCode.SUCCESS.getCode(), "获取成功", hotelUserInfo1);
        return renderResult;
    }

    public RenderResult deleteUserByIds(String[] ids) {
        hotelUserDao.deleteByIds(ids);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "删除成功", null);
    }

    public RenderResult addHotelUser(FdHotelUserInfo hotelUserInfo, String userid) {

        hotelUserInfo.setCreateDate(new Date());
        hotelUserInfo.setCreateId(userid);
        //密码MD5加密
        hotelUserInfo.setPassword(Md5Kit.md5(hotelUserInfo.getPassword()));
        hotelUserInfo.setId(IdUtil.fastSimpleUUID());
        hotelUserInfo.setUserStatus("0");
        hotelUserDao.add(hotelUserInfo);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "添加成功", "");

    }


    public RenderResult updateHotelUser(FdHotelUserInfo hotelUserInfo, String userid) {
        String id = hotelUserInfo.getId();

        FdHotelUserInfo userInDb = hotelUserDao.getById(id);

        hotelUserInfo.setPassword(userInDb.getPassword());
        hotelUserInfo.setModifyId(userid);
        hotelUserInfo.setModifyDate(new Date());
        hotelUserInfo.setCreateId(userInDb.getCreateId());
        hotelUserInfo.setCreateDate(userInDb.getCreateDate());
        //同步用户状态
        if (StrKit.isBlank(hotelUserInfo.getUserStatus())) {
            hotelUserInfo.setUserStatus(userInDb.getUserStatus());
        }
        hotelUserDao.update(hotelUserInfo);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "修改成功", "");
    }

    /**
     * @param userId
     * @param status   0 正常  1 禁用
     * @param username
     * @return
     */
    public RenderResult changeStatus(String userId, String status, String username) {
        String msg = "用户已禁用";
        if (status.equals("0")) {
            msg = "用户已解除禁用";
        }
        FdHotelUserInfo hotelUserInfo = hotelUserDao.getById(userId);
        hotelUserInfo.setUserStatus(status);
        hotelUserInfo.setModifyId(username);
        hotelUserInfo.setModifyDate(new Date());
        hotelUserDao.update(hotelUserInfo);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), msg, "");

    }

    /**
     * @param userId
     * @param password
     * @param username
     * @function 如果密码为空则设置为默认密码123456
     */

    public RenderResult changePassword(String userId, String password, String username) {
        String msg = "密码修改成功";
        if (StrKit.isBlank(password)) {
            password = DEFAULT_PASSWORD;
            msg = "密码重置成功";
        }
        String encryptPwd = Md5Kit.md5(password);
        FdHotelUserInfo hotelUserInfo = hotelUserDao.getById(userId);
        hotelUserInfo.setPassword(encryptPwd);
        hotelUserInfo.setModifyId(username);
        hotelUserInfo.setModifyDate(new Date());
        hotelUserDao.update(hotelUserInfo);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), msg, "");

    }

    public RenderResult deleteUserById(String id) {
        hotelUserDao.deleteById(id);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "已删除", "");
    }


    public  RenderResult listWithNoPage(FdHotelUserInfo hotelUserInfo){
        List list = hotelUserDao.listWithNoPage(hotelUserInfo);
        RenderResult result=RenderResult.build(MessageCode.SUCCESS.getCode(), "获取成功",list);
        return result;
    }

    public RenderResult list(ListDto listDao) {
        Page page = listDao.getPage();
        JSONObject query = listDao.getQuery();
        int pageNumber = page.getPageNumber();
        int pageSize = page.getPageSize();
        QueryResults qr = hotelUserDao.list(pageSize, pageNumber, query);
        return RenderResult.build(MessageCode.SUCCESS.getCode(), "获取成功", qr);
    }

}
