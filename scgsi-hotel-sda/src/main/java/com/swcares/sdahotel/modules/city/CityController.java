package com.swcares.sdahotel.modules.city;/**
 * 作者：徐士昊
 * 时间：2022/08/26 8:54
 **/

import com.swcares.exception.MessageCode;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.entity.CityCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * Date on 2022/08/26  8:54
 */
@RestController
@RequestMapping("/api/sda/hotel/city")
public class CityController {

    @Autowired
    CityService cityService;
    @RequestMapping("/list")
    public RenderResult  list(@RequestBody Req req){
        List<CityCode> list = cityService.listAll();
        RenderResult rr=     RenderResult.build(MessageCode.SUCCESS.getCode(),"获取成功",list);
        return  rr;
    }

}
