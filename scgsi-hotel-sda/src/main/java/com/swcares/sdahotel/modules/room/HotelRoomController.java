package com.swcares.sdahotel.modules.room;/**
 * 作者：徐士昊
 * 时间：2022/06/07 11:07
 **/


import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.entity.FdHotelRoomInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * Date on 2022/06/07  11:07
 */
@RestController
@RequestMapping("/api/sda/hotel/room")
public class HotelRoomController {

    @Autowired
    HotelRoomService roomService;

    @PostMapping("/add")
    public RenderResult add(HttpServletRequest request, @RequestBody Req<FdHotelRoomInfo> room) {

        String userid = request.getAttribute("userid").toString();
        return  roomService.addRoom(userid,room);

    }

    @PostMapping("/update")
    public RenderResult update(HttpServletRequest request, @RequestBody Req<FdHotelRoomInfo> room) {
        String userid = request.getAttribute("userid").toString();
        return  roomService.updateRoom(userid,room);

    }

    @PostMapping("/detail")
    public RenderResult detail(HttpServletRequest request, @RequestBody Req<FdHotelRoomInfo> room) {
        FdHotelRoomInfo params = room.getParams();
        String id = params.getId();
        return  roomService.getById(id);

    }
    @PostMapping("/delete")
    public RenderResult delete(HttpServletRequest request, @RequestBody Req<FdHotelRoomInfo> room) {
        FdHotelRoomInfo params = room.getParams();
        String id = params.getId();
        return  roomService.deleteById(id);

    }

    @PostMapping("/listAll")
    public   RenderResult listAll(HttpServletRequest request, @RequestBody Req<FdHotelRoomInfo> room){
        FdHotelRoomInfo params = room.getParams();
        return roomService.listAll(params);
    }


}
