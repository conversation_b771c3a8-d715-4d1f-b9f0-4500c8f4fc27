package com.swcares.sdahotel.modules.hotel;/**
 * 作者：徐士昊
 * 时间：2022/05/31 14:46
 **/


import com.swcares.scgsi.web.RenderResult;
import com.swcares.sdahotel.common.global.Req;
import com.swcares.sdahotel.entity.FdHotelInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * Date on 2022/05/31  14:46
 */
@RestController
@RequestMapping("/api/sda/hotel/hotel")
public class HotelController {

    @Autowired
    HotelService hotelService;

    @PostMapping("/add")
    public RenderResult add(@RequestBody Req<FdHotelInfo> hotel, HttpServletRequest req) {
        String username = req.getAttribute("username").toString();
        return hotelService.addHotel(hotel.getParams(), username);

    }

    @PostMapping("/update")
    public RenderResult update(@RequestBody Req<FdHotelInfo> hotel, HttpServletRequest req) {
        String userid = req.getAttribute("userid").toString();
        RenderResult result = hotelService.updateHotel(hotel.getParams(), userid);
        return result;

    }

    @PostMapping("/changeStatus")

    public  RenderResult changeStatus(@RequestBody Req<FdHotelInfo> hotel, HttpServletRequest req){
        String userid = req.getAttribute("userid").toString();
        FdHotelInfo params = hotel.getParams();
        RenderResult result=hotelService.changeStatus(params,userid);
        return  result;

    }



    @PostMapping("/delete")
    public RenderResult delete(@RequestBody Req<FdHotelInfo> hotel, HttpServletRequest req) {
        String username = req.getAttribute("username").toString();
        RenderResult result = hotelService.deleteHotel(hotel.getParams().getId(), username);
        return result;

    }

    @PostMapping("/detail")
    public RenderResult detail(@RequestBody Req<FdHotelInfo> hotel) {
        RenderResult result = hotelService.getHotelById(hotel.getParams().getId());
        return result;

    }

    @PostMapping("/listAll")
   // @NoSecuriyAnnotation
    public RenderResult list(@RequestBody Req<FdHotelInfo> hotel, HttpServletRequest req) {
        RenderResult result = hotelService.listHotelWithNoPage(hotel.getParams());
        return result;

    }




}
