package com.swcares.quartz.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * ClassName：com.swcares.quartz.entity.QuartzJob <br/>
 * Description：定时任务实体对象 <br/>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br/>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br/>
 *
 * <AUTHOR> <br/>
 * @version v1.0 <br/>
 * @date 2019年1月21日 上午9:57:32 <br/>
 */
@Entity
@Table(name = "SYS_QUARTZ_JOB")
@Data
public class QuartzJob implements Serializable {
    public static final String JOB_KEY = "JOB_KEY";

    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    /**
     * Bean名称
     */
    @Column(name = "BEAN_NAME")
    private String beanName;

    /**
     * cro表达式
     */
    @Column(name = "CRON_EXPRESSION")
    private String cronExpression;

    /**
     * 运行状态1停止0运行
     */
    @Column(name = "IS_PAUSE")
    private int isPause;

    /**
     * 任务名称
     */
    @Column(name = "JOB_NAME")
    private String jobName;

    /**
     * 执行方法
     */
    @Column(name = "METHOD_NAME")
    private String methodName;

    /**
     * 执行方法参数
     */
    @Column(name = "PARAMS")
    private String params;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    private String remark;
    /**
     * 创建日期
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;
    /**
     * 更新日期
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;
    /**
     * 定时任务作用域
     */
    @Column(name = "RUN_SCOPE")
    private String scope;
}
