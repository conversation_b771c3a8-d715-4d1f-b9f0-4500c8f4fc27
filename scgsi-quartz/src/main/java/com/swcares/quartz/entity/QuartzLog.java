package com.swcares.quartz.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 
 * ClassName：com.swcares.quartz.entity.QuartzLog <br/>
 * Description：定时任务日志实体<br/>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br/>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br/>
 * <AUTHOR> <br/>
 * @date 2019年1月21日 上午10:00:47 <br/>
 * @version v1.0 <br/>
 */
@Entity
@Table(name = "SYS_QUARTZ_LOG")
@Data
public class QuartzLog implements Serializable {
  /**
   * 主键id
   */
  @Id
  @Column(name = "ID")
  @GeneratedValue(generator = "uuid2")
  @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
  private String id;

  /**
   * Bean名称
   */
  @Column(name = "BEAN_NAME")
  private String beanName;

  /**
   * 创建时间
   */
  @Column(name = "CREATE_TIME")
  private Date createTime;

  /**
   * cro表达式
   */
  @Column(name = "CRON_EXPRESSION")
  private String cronExpression;

  /**
   * 异常详情
   */
  @Column(name = "EXCEPTION_DETAIL")
  private String exceptionDetail;

  /**
   * 状态 0成功1失败
   */
  @Column(name = "IS_SUCCESS")
  private int isSuccess;

  /**
   * 任务名称
   */
  @Column(name = "JOB_NAME")
  private String jobName;

  /**
   * 执行方法
   */
  @Column(name = "METHOD_NAME")
  private String methodName;

  /**
   * 方法参数
   */
  @Column(name = "PARAMS")
  private String params;

  /**
   * 耗时(毫秒)
   */
  @Column(name = "SPEND_TIME")
  private Long spendTime;

  /**
   * 服务器IP
   */
  @Column(name = "IP")
  private String ip;
}
