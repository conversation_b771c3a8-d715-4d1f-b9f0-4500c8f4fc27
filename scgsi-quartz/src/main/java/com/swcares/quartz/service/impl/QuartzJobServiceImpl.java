package com.swcares.quartz.service.impl;

import com.swcares.quartz.dao.QuartzJobDao;
import com.swcares.quartz.entity.QuartzJob;
import com.swcares.quartz.handler.QuartzManage;
import com.swcares.quartz.service.QuartzJobService;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import lombok.extern.slf4j.Slf4j;
import org.quartz.CronExpression;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;

@Service
@Slf4j
public class QuartzJobServiceImpl implements QuartzJobService {
    @Resource
    private QuartzJobDao quartzJobDao;
    @Resource
    private BaseDAO baseDAO;
    @Resource
    private QuartzManage quartzManage;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createJob(QuartzJob quartzJob) {
        if (!CronExpression.isValidExpression(quartzJob.getCronExpression())) {
            log.error("{}cron表达式格式错误:{}",quartzJob.getMethodName(),quartzJob.getCronExpression());
        } else {
            quartzJob.setCreateTime(new Date());
            quartzJobDao.save(quartzJob);
            quartzManage.addJob(quartzJob);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIsPause(String id) {
        QuartzJob quartzJob = baseDAO.findById(id,QuartzJob.class);
        if(quartzJob.getIsPause()==0){
            quartzManage.pauseJob(quartzJob);
            quartzJob.setIsPause(1);
        }else{
            quartzManage.resumeJob(quartzJob);
            quartzJob.setIsPause(0);
        }
        baseDAO.update(quartzJob);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(QuartzJob quartzJob) {
        quartzManage.updateJobCron(quartzJob);
        baseDAO.update(quartzJob);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        QuartzJob quartzJob = baseDAO.findById(id,QuartzJob.class);
        quartzManage.deleteJob(quartzJob);
        quartzJobDao.deleteById(id);
    }

    @Override
    public QueryResults queryPage(String beanName) {
        return baseDAO.queryEntityPage(" FROM SYS_QUARTZ_JOB",new HashMap<>(),QuartzJob.class,1,10);
    }

    @Override
    public void runNow(String id) {
        QuartzJob quartzJob = baseDAO.findById(id,QuartzJob.class);
        quartzManage.runAJobNow(quartzJob);
    }
}
