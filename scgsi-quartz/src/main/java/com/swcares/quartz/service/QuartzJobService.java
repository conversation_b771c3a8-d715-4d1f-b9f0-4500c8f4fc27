package com.swcares.quartz.service;

import com.swcares.quartz.entity.QuartzJob;
import com.swcares.scgsi.base.QueryResults;

/**
 * ClassName：com.swcares.quartz.service <br>
 * Description：定时任务新增编辑，立即执行 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月02日 13:25 <br>
 * @version v1.0 <br>
 */
public interface QuartzJobService {
    /**
     *
     * Title：createJob <br/>
     * Description：新增定时任务,默认停止<br/>
     * author：王建文 <br/>
     * date：2019年1月21日 上午10:07:25 <br/>
     * @param quartzJob <br/>
     */
    public void createJob(QuartzJob quartzJob);

    /**
     *
     * Title：updateIsPause <br/>
     * Description：暂停,运行定时任务 <br/>
     * author：王建文 <br/>
     * date：2019年1月21日 上午10:07:51 <br/>
     * @param id <br/>
     */
    public void updateIsPause(String id);

    /**
     *
     * Title：update <br/>
     * Description：T更新定时任务 <br/>
     * author：王建文 <br/>
     * date：2019年1月21日 上午10:08:16 <br/>
     * @param quartzJob <br/>
     */
    public void update(QuartzJob quartzJob);

    /**
     *
     * Title：delete <br/>
     * Description：删除定时任务 <br/>
     * author：王建文 <br/>
     * date：2019年1月21日 上午10:08:34 <br/>
     * @param id <br/>
     */
    public void delete(String id);
    /**
     * Title：queryPage <br>
     * Description： TODO(用一句话描述该方法做什么)<br>
     * author：王建文 <br>
     * date：2020-3-2 15:32 <br>
     * @param  beanName 定时任务beanName
     * @return QueryResults
     */
    QueryResults queryPage(String beanName);
    /**
     * Title：runNow <br>
     * Description： 立即执行定时任务<br>
     * author：王建文 <br>
     * date：2020-3-2 15:51 <br>
     * @param  id 主键id
     * @return
     */
    public void runNow(String id);
}
