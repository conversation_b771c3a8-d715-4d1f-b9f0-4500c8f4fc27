package com.swcares.quartz.config;

import com.swcares.quartz.dao.QuartzJobDao;
import com.swcares.quartz.entity.QuartzJob;
import com.swcares.quartz.handler.QuartzManage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 
 * ClassName：com.swcares.quartz.config.JobRunner <br/>
 * Description：初始化加载定时job <br/>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br/>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br/>
 * <AUTHOR> <br/>
 * @date 2019年1月21日 上午9:53:27 <br/>
 * @version v1.0 <br/>
 */
@Component
@Slf4j
@PropertySource("classpath:properties/quartzJob.properties")
public class JobRunner implements ApplicationRunner {
    @Autowired
    private QuartzManage quartzManage;
    @Resource
    private QuartzJobDao quartzJobDao;
    /**
     * 定时任务作用域
     */
    @Value("${scope}")
    private String scope;
    /**
     * 
     * Title：run <br/>
     * Description： 项目启动时重新激活启用的定时任务
     * <AUTHOR> <br/>
     * @date 2019年1月21日 上午9:54:08 <br/>
     * @param applicationArguments <br/>
     * @see ApplicationRunner#run(ApplicationArguments) <br/>
     */
    @Override
    public void run(ApplicationArguments applicationArguments){
        //查询出所有未停止的任务
        List<QuartzJob> jobList=quartzJobDao.getQuartzJobByIsPauseAndAndScope(0,scope);
        if(jobList.size()>0){
            log.info("--------------------注入定时任务"+jobList.size()+"个"+"---------------------");
            StringBuffer taskSb=new StringBuffer();
            jobList.forEach(quartzJob -> {
                taskSb.append(quartzJob.getJobName()+",");
                quartzManage.addJob(quartzJob);
            });
            System.out.println(taskSb.toString());
            log.info("--------------------定时任务注入完成---------------------");
        }
    }
}
