package com.swcares.quartz.config;

import org.quartz.Scheduler;
import org.quartz.spi.TriggerFiredBundle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.quartz.AdaptableJobFactory;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Component;

/**
 * 
 * ClassName：com.swcares.quartz.config.QuartzConfig <br/>
 * Description：定时任务配置 <br/>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br/>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br/>
 * <AUTHOR> <br/>
 * @date 2019年1月21日 上午9:55:26 <br/>
 * @version v1.0 <br/>
 */
@Configuration
@PropertySource("classpath:properties/quartzJob.properties")
public class QuartzConfig {
  /**
   * 定时任务延迟执行时间
   */
  @Value("${jobDelayTime}")
  private int jobDelayTime;
  /**
   * 定时任务开关
   */
  @Value("${autoStartup}")
  private boolean autoStartup;

  /**
   *
   * ClassName：com.swcares.core.quartz.config.QuartzJobFactory <br>
   * Description：解决Job中注入Spring Bean为null的问题 <br>
   * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br>
   * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
   * <AUTHOR> <br>
   * date 2019年1月22日 上午9:34:07 <br>
   * @version v1.0 <br>
   */
  @Component("quartzJobFactory")
  public class QuartzJobFactory extends AdaptableJobFactory {
    @Autowired
    private AutowireCapableBeanFactory capableBeanFactory;

    @Override
    protected Object createJobInstance(TriggerFiredBundle bundle) throws Exception {
      // 调用父类的方法
      Object jobInstance = super.createJobInstance(bundle);
      capableBeanFactory.autowireBean(jobInstance);
      return jobInstance;
    }
  }

  /**
   *
   * Title：scheduler <br>
   * Description：注入scheduler到spring <br>
   * author：王建文 <br>
   * date：2019年1月22日 上午9:36:13 <br>
   * @param quartzJobFactory quartzJobFactory
   * @return rt
   * @throws Exception Exception<br>
   */
  @Bean(name = "scheduler")
  public Scheduler scheduler(QuartzJobFactory quartzJobFactory) throws Exception {
    SchedulerFactoryBean factoryBean = new SchedulerFactoryBean();
    factoryBean.setJobFactory(quartzJobFactory);
    factoryBean.afterPropertiesSet();
    Scheduler scheduler = factoryBean.getScheduler();
    // 设置延迟加载
    factoryBean.setStartupDelay(jobDelayTime);
    //设置定时任务开关
    factoryBean.setAutoStartup(autoStartup);
    if(autoStartup) {
      scheduler.start();
    }
    return scheduler;
  }
  @Bean
  public SpringContextHolder springContextHolder() {
    return new SpringContextHolder();
  }
}
