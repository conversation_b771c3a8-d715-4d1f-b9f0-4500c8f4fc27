package com.swcares.quartz.dao;

import com.swcares.quartz.entity.QuartzJob;
import com.swcares.scgsi.base.BaseJpaDao;

import java.util.List;

/**
 * ClassName：com.swcares.quartz.dao <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月02日 14:09 <br>
 * @version v1.0 <br>
 */
public interface QuartzJobDao extends BaseJpaDao<QuartzJob, String> {
    /**
     * Title： getQuartzJobByIsPause<br>
     * Description： 查询出未停用的所有定时任务<br>
     * author：王建文 <br>
     * date：2020-3-2 14:16 <br>
     *
     * @param isPause 0未暂停使用1暂停使用
     * @param scope web,pssn
     * @return List
     */
    List<QuartzJob> getQuartzJobByIsPauseAndAndScope(int isPause,String scope);
}
