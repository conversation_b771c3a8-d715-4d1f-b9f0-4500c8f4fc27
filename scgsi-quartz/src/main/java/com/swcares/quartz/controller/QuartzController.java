package com.swcares.quartz.controller;

import com.swcares.quartz.entity.QuartzJob;
import com.swcares.quartz.service.QuartzJobService;
import com.swcares.scgsi.web.RenderResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ClassName：com.swcares.quartz.controller <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 *         date 2020年 03月02日 15:52 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/quartz")
public class QuartzController {
    @Resource
    private QuartzJobService quartzJobService;
    /**
     *
     * Title：create <br>
     * Description：创建定时任务 <br>
     * author：王建文 <br>
     * date：2019年1月23日 下午5:40:36 <br>
     * @param quartzJob
     * @return <br>
     */
    @PostMapping("create")
    public RenderResult create(QuartzJob quartzJob) {
        try {
            quartzJob.setUpdateTime(new Date());
            quartzJobService.createJob(quartzJob);
            return new RenderResult("1","success");
        } catch (Exception e) {
            e.printStackTrace();
            return new RenderResult("0",e.getMessage());
        }
    }

    /**
     *
     * Title：update <br>
     * Description：更新定时任务 <br>
     * author：王建文 <br>
     * date：2019年1月23日 下午5:40:53 <br>
     * @param quartzJob
     * @return <br>
     */
    @PostMapping("update")
    public RenderResult update(QuartzJob quartzJob) {
        try {
            quartzJobService.update(quartzJob);
            return new RenderResult("1","success");
        } catch (Exception e) {
            e.printStackTrace();
            return new RenderResult("0", e.getMessage());
        }
    }

    /**
     *
     * Title：updateIsPause <br>
     * Description：暂停,执行<br>
     * author：王建文 <br>
     * date：2019年1月23日 下午5:41:19 <br>
     * @param id jobId
     * @return <br>
     */
    @PostMapping("/updateIsPause")
    public RenderResult updateIsPause(String id) {
        try {
            quartzJobService.updateIsPause(id);
            return new RenderResult("1", "success");
        } catch (Exception e) {
            return new RenderResult("0", e.getMessage());
        }
    }

    /**
     *
     * Title：delete <br>
     * Description：删除定时任务 <br>
     * author：王建文 <br>
     * date：2019年1月23日 下午5:43:04 <br>
     * @param id
     * @return <br>
     */
    @PostMapping("/delete")
    public RenderResult delete(String id) {
        try {
            quartzJobService.delete(id);
            return new RenderResult("1", "success");
        } catch (Exception e) {
            return new RenderResult("0", e.getMessage());
        }
    }
    @PostMapping("/runNow")
    public RenderResult runNow(String id) {
        try {
            quartzJobService.runNow(id);
            return new RenderResult("1", "success");
        } catch (Exception e) {
            return new RenderResult("0", e.getMessage());
        }
    }
}