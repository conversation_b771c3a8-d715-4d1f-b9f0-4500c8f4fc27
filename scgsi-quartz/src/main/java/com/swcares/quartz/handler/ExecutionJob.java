package com.swcares.quartz.handler;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.swcares.quartz.dao.QuartzJobDao;
import com.swcares.quartz.dao.QuartzLogDao;
import com.swcares.quartz.entity.QuartzJob;
import com.swcares.quartz.entity.QuartzLog;
import com.swcares.scgsi.base.BaseDAO;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Date;
import java.util.concurrent.*;

/**
 * 
 * ClassName：com.swcares.quartz.utils.ExecutionJob <br/>
 * Description：定时任务执行处理 <br/>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br/>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br/>
 * <AUTHOR> <br/>
 * @date 2019年1月21日 上午10:13:10 <br/>
 * @version v1.0 <br/>
 */
@Async
@Slf4j
public class ExecutionJob extends QuartzJobBean {
  @Autowired
  private QuartzManage quartzManage;
  @Resource
  private QuartzLogDao quartzLogDao;
  @Resource
  private BaseDAO baseDAO;
  @Override
  protected void executeInternal(JobExecutionContext context) {
    QuartzJob quartzJob = (QuartzJob) context.getMergedJobDataMap().get(QuartzJob.JOB_KEY);
    QuartzLog jobLog = new QuartzLog();
    //主键ID待加入
    jobLog.setJobName(quartzJob.getJobName());
    jobLog.setBeanName(quartzJob.getBeanName());
    jobLog.setMethodName(quartzJob.getMethodName());
    jobLog.setParams(quartzJob.getParams());
    jobLog.setCreateTime(new Date());
    long startTime = System.currentTimeMillis();
    jobLog.setCronExpression(quartzJob.getCronExpression());
    try {
      InetAddress inetAddress = InetAddress.getLocalHost();
      String ip = inetAddress.getHostAddress().toString();
     if(StringUtils.isEmpty(ip)){
       jobLog.setIp("未获取到服务器IP");
      }else{
       jobLog.setIp(ip);
     }
    } catch (UnknownHostException e) {
      log.error("获取服务器IP失败");
    }
    try {
      // 执行任务
      log.info("任务准备执行，任务名称：{}",quartzJob.getJobName());
      QuartzRunnable task = new QuartzRunnable(quartzJob.getBeanName(), quartzJob.getMethodName(),
          quartzJob.getParams());
      ThreadFactory namedThreadFactory =
              new ThreadFactoryBuilder().setNameFormat("Quartz-"+quartzJob.getBeanName()+"-"+quartzJob.getJobName()+"pool-%d").build();
      ExecutorService executorService = new ThreadPoolExecutor(1, 1, 60L, TimeUnit.SECONDS,
              new LinkedBlockingQueue<Runnable>(1024), namedThreadFactory,
              new ThreadPoolExecutor.AbortPolicy());
      Future<?> future = executorService.submit(task);
      future.get();
      long times = System.currentTimeMillis() - startTime;
      jobLog.setSpendTime(times);
      // 任务状态
      jobLog.setIsSuccess(0);
      executorService.shutdown();
      log.info("任务执行完毕，任务名称：{} 总共耗时：{} 毫秒",quartzJob.getJobName(),times);
    } catch (Exception e) {
      log.error("任务执行失败:{}，任务名称：{}" ,e.getMessage() , quartzJob.getJobName());
      long times = System.currentTimeMillis() - startTime;
      jobLog.setSpendTime(times);
      // 任务状态 0：成功 1：失败
      jobLog.setIsSuccess(1);
      jobLog.setExceptionDetail(e.getMessage());
      // 出错就暂停任务
      quartzManage.pauseJob(quartzJob);
      // 更新状态
      quartzJob.setIsPause(1);
      //更新状态 dao
      baseDAO.update(quartzJob);
    } finally {
      //日志记录dao
      quartzLogDao.save(jobLog);
    }
  }
}
