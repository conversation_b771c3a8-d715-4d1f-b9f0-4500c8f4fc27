package com.swcares.quartz.handler;

import com.swcares.quartz.config.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;

/**
 * 
 * ClassName：com.swcares.quartz.utils.QuartzRunnable <br/>
 * Description：定时任务线程执行<br/>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br/>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br/>
 * <AUTHOR> <br/>
 * @date 2019年1月21日 上午10:15:31 <br/>
 * @version v1.0 <br/>
 */
@Slf4j
public class QuartzRunnable implements Runnable {
  private Object target;
  private Method method;
  private String params;

  QuartzRunnable(String beanName, String methodName, String params)
      throws NoSuchMethodException, SecurityException {
    this.target = SpringContextHolder.getBean(beanName);
    this.params = params;

    if (StringUtils.isNotBlank(params)) {
      this.method = target.getClass().getDeclaredMethod(methodName, String.class);
    } else {
      this.method = target.getClass().getDeclaredMethod(methodName);
    }
  }

  @Override
  public void run() {
    try {
      ReflectionUtils.makeAccessible(method);
      if (StringUtils.isNotBlank(params)) {
        method.invoke(target, params);
      } else {
        method.invoke(target);
      }
    } catch (Exception e) {
      log.error("定时任务执行失败:{}",e);
    }
  }

}
