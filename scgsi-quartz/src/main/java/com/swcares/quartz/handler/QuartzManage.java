package com.swcares.quartz.handler;

import com.swcares.quartz.entity.QuartzJob;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

import static org.quartz.TriggerBuilder.newTrigger;

/**
 * 
 * ClassName：com.swcares.quartz.utils.QuartzManage <br/>
 * Description：定时任务管理器<br/>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br/>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br/>
 * <AUTHOR> <br/>
 * @date 2019年1月21日 上午10:14:31 <br/>
 * @version v1.0 <br/>
 */
@Component
@Slf4j
public class QuartzManage {
  private static final String JOB_NAME = "TASK_";

  @Resource(name = "scheduler")
  private Scheduler scheduler;

  public void addJob(QuartzJob quartzJob) {
    try {
      // 构建job信息
      JobDetail jobDetail =
          JobBuilder.newJob(ExecutionJob.class).withIdentity(JOB_NAME + quartzJob.getId()).build();
      // 通过触发器名和cron 表达式创建 Trigger
      Trigger cronTrigger = newTrigger().withIdentity(JOB_NAME + quartzJob.getId()).startNow()
          .withSchedule(CronScheduleBuilder.cronSchedule(quartzJob.getCronExpression())).build();

      cronTrigger.getJobDataMap().put(QuartzJob.JOB_KEY, quartzJob);
      // 重置启动时间
      ((CronTriggerImpl) cronTrigger).setStartTime(new Date());
      // 执行定时任务
      scheduler.scheduleJob(jobDetail, cronTrigger);
      // 暂停任务
      if (quartzJob.getIsPause() == 1) {
        pauseJob(quartzJob);
      }
    } catch (Exception e) {
      log.error("创建定时任务{}失败:{}",quartzJob.getJobName(),e.getMessage());
    }
  }

  /**
   * 更新job cron表达式
   * @param quartzJob
   * @throws SchedulerException
   */
  public void updateJobCron(QuartzJob quartzJob) {
    try {
      TriggerKey triggerKey = TriggerKey.triggerKey(JOB_NAME + quartzJob.getId());
      CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
      if(null==trigger&&quartzJob.getIsPause()==0){
        runAJobNow(quartzJob);
      }else{
      CronScheduleBuilder scheduleBuilder =
          CronScheduleBuilder.cronSchedule(quartzJob.getCronExpression());
      trigger = trigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(scheduleBuilder)
          .build();
      // 重置启动时间
      ((CronTriggerImpl) trigger).setStartTime(new Date());

      scheduler.rescheduleJob(triggerKey, trigger);
      // 暂停任务
      if (quartzJob.getIsPause() == 1) {
        pauseJob(quartzJob);
      }
      }
    } catch (Exception e) {
      log.error("更新定时任务{}失败:{}" , quartzJob.getJobName(),e.getMessage());
    }

  }

  /**
   * 删除一个job
   * @param quartzJob
   * @throws SchedulerException
   */
  public void deleteJob(QuartzJob quartzJob) {
    try {
      JobKey jobKey = JobKey.jobKey(JOB_NAME + quartzJob.getId());
      scheduler.deleteJob(jobKey);
    } catch (Exception e) {
      log.error("删除定时任务{}失败:{}",quartzJob.getJobName(),e.getMessage());
    }
  }

  /**
   * 恢复一个job
   * @param quartzJob
   * @throws SchedulerException
   */
  public void resumeJob(QuartzJob quartzJob) {
    try {
      TriggerKey triggerKey = TriggerKey.triggerKey(JOB_NAME + quartzJob.getId());
      CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
      // 如果不存在则创建一个定时任务
      if (trigger == null) {
        addJob(quartzJob);
      }
      JobKey jobKey = JobKey.jobKey(JOB_NAME + quartzJob.getId());
      scheduler.resumeJob(jobKey);
    } catch (Exception e) {
      log.error("恢复定时任务{}失败:{}",quartzJob.getJobName(),e.getMessage());
    }
  }

  /**
   * 立即执行job
   * @param quartzJob
   * @throws SchedulerException
   */
  public void runAJobNow(QuartzJob quartzJob) {
    try {
      TriggerKey triggerKey = TriggerKey.triggerKey(JOB_NAME + quartzJob.getId());
      CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
      // 如果不存在则创建一个定时任务
      if (trigger == null) {
        addJob(quartzJob);
      }
      JobDataMap dataMap = new JobDataMap();
      dataMap.put(QuartzJob.JOB_KEY, quartzJob);
      JobKey jobKey = JobKey.jobKey(JOB_NAME + quartzJob.getId());
      scheduler.triggerJob(jobKey, dataMap);
    } catch (Exception e) {
      log.error("定时任务{}执行失败{}",quartzJob.getJobName(),e.getMessage());
    }
  }

  /**
   * 暂停一个job
   * @param quartzJob
   * @throws SchedulerException
   */
  public void pauseJob(QuartzJob quartzJob) {
    try {
      JobKey jobKey = JobKey.jobKey(JOB_NAME + quartzJob.getId());
      scheduler.pauseJob(jobKey);
    } catch (Exception e) {
      log.error("定时任务{}暂停失败:{}",quartzJob.getJobName(),e.getMessage());
    }
  }
}
