-- 最终优化的SQL结构
-- 结合了您的建议：在内层查询中直接处理城市名称转换和函数逻辑集成

SELECT P.* FROM (
    SELECT DISTINCT(DPI.ID) AS id,
           O.FLIGHT_NO AS flightNo,
           O.FLIGHT_DATE AS flightDate,
           -- 直接在查询中转换城市名称（采用您的建议）
           get_city_name(DPI.ORG_CITY_AIRP) AS orgCity,
           get_city_name(DPI.DST_CITY_AIRP) AS dstCity,
           O.SERVICE_CITY AS serviceCity,
           F.SEGMENT AS segment,
           F.AC_TYPE AS acType,
           DECODE(O.PAY_TYPE,'0','不正常航班','1','异常行李','2','旅客超售') AS payType,
           F.LATE_REASON AS lateReson,
           F.DELAY_TIME AS delayTime,
           O.ORDER_ID AS orderId,
           DPI.PAX_ID AS paxId,
           DECODE(O.STATUS,'2','通过','3','生效','4','关闭') AS orderStatus,
           DPI.PAX_NAME AS paxName,
           DPI.TKT_NO AS tktNo,
           DPI.CURRENT_AMOUNT AS totalPay,
           (DPI.CURRENT_AMOUNT-NVL(OI.PRICE_SPREAD,'0')) AS payMoney,
           OI.PRICE_SPREAD AS tktPriceDiff,
           DECODE(DPI.RECEIVE_STATUS,'0','待领取','1','已领取','2','处理中','3','已逾期') AS receiveStatus,
           DECODE(DPI.RECEIVE_CHANNEL,'0','','1',DECODE(DAO.APPLY_STATUS,'0','待审核','1','通过','2','未通过','3','关闭','4','冻结'),'2','') AS actStatus,
           DECODE(DPI.RECEIVE_CHANNEL,'0','普通','1','代领','2','现金') AS receiveChannel,
           DECODE(DPI.RECEIVE_WAY,'0','微信','1','银联','2','现金') AS receiveWay,
           DECODE(DPI.RECEIVE_WAY,'0','','1',DAO.APPLY_USER,'2','') AS accountName,
           DECODE(DPI.RECEIVE_WAY,'0','','1',DAO.OPEN_BANK_NAME,'2','') AS openBankName,
           DECODE(DPI.RECEIVE_WAY,'2','',DAO.GET_MONEY_ACCOUNT) AS getAccount,
           DPI.ID_NO AS idNo,
           DAO.TELEPHONE AS telephone,
           O.REMARK AS remark,
           TO_CHAR(DAO.PAY_DATE,'YYYY-MM-DD hh24:mi:ss') AS payDate,
           TO_CHAR(add_months(TO_DATE(O.FLIGHT_DATE, 'YYYY-MM-DD'),12*1),'YYYY-MM-DD hh24:mi:ss') AS expireTime,
           E.TU_CNAME AS applyUser,
           DPI.IS_FLAG AS isFlag,
           DPI.RECEIVE_STATUS AS receiveOrder,
           DAO.PAY_STATUS,
           
           -- 集成get_dp_pax_paystatus函数逻辑
           NVL((SELECT PAY_STATUS FROM ( 
             SELECT DAO2.PAY_STATUS FROM DP_APPLY_ORDER DAO2 
             LEFT JOIN DP_APPLY_PAX DAP2 ON DAO2.APPLY_CODE=DAP2.APPLY_CODE 
             WHERE DAP2.PAX_ID=DPI.PAX_ID AND DPI.ORDER_ID IN 
             (SELECT REGEXP_SUBSTR(DAP2.ORDER_ID,'[^,]+', 1, LEVEL) FROM DUAL 
             connect by regexp_substr(DAP2.ORDER_ID, '[^,]+', 1, level) is not null) 
             ORDER BY DAO2.CREATE_TIME DESC 
           ) WHERE ROWNUM=1), DAO.PAY_STATUS) AS finalPayStatus,
           
           -- 集成get_dp_pax_payfailcontent函数逻辑
           (SELECT ERR_CODE_DES FROM ( 
             SELECT DATR.ERR_CODE_DES FROM DP_AO_TRANS_RECORD DATR 
             LEFT JOIN DP_APPLY_PAX DAP3 ON DATR.APPLY_CODE=DAP3.APPLY_CODE 
             WHERE DAP3.PAX_ID=DPI.PAX_ID AND DPI.ORDER_ID IN 
             (SELECT REGEXP_SUBSTR(DAP3.ORDER_ID,'[^,]+', 1, LEVEL) FROM DUAL 
             connect by regexp_substr(DAP3.ORDER_ID, '[^,]+', 1, level) is not null) 
             ORDER BY DATR.CREATE_TIME DESC 
           ) WHERE ROWNUM=1) AS payFailRemark,
           
           -- 集成最后审核人查询
           GET_USER_NAME(get_dp_Order_latest_auditor(DPI.ORDER_ID)) AS lastAuditor,
           
           ROW_NUMBER() OVER(partition by DPI.PAX_ID,DPI.ORDER_ID ORDER by DAO.CREATE_TIME DESC) AS exitCount
           
      FROM DP_PAX_INFO DPI
      LEFT JOIN DP_ORDER_INFO O ON O.ORDER_ID=DPI.ORDER_ID
      LEFT JOIN DP_FLIGHT_INFO F ON F.ORDER_ID=O.ORDER_ID
      LEFT JOIN DP_OVER_INFO OI ON OI.ORDER_ID=O.ORDER_ID
      LEFT JOIN DP_APPLY_PAX DAP ON DAP.PAX_ID=DPI.PAX_ID
        AND DPI.ORDER_ID in (
            SELECT REGEXP_SUBSTR(DAP.ORDER_ID,'[^,]+', 1, LEVEL) FROM DUAL
            connect by regexp_substr(DAP.ORDER_ID, '[^,]+', 1, level) is not null
        )
      LEFT JOIN DP_APPLY_ORDER DAO ON DAO.APPLY_CODE=DAP.APPLY_CODE
      LEFT JOIN EMPLOYEE E ON E.TUNO=O.CREATE_ID
     WHERE 1=1
       AND (O.STATUS='3' OR O.STATUS='2' OR DPI.RECEIVE_STATUS='1')
       -- 这里会添加各种查询条件...
       
    ORDER BY flightDate DESC, receiveOrder DESC, payDate ASC
) P 
WHERE P.exitCount=1
  -- 可以基于新字段进行过滤
  -- AND P.finalPayStatus = :payStatus
;

-- 优化总结：
-- 1. 采用了您建议的更简洁结构，避免了双层嵌套
-- 2. 在内层查询中直接调用get_city_name函数转换城市名称
-- 3. 在内层查询中集成了两个Oracle函数的逻辑
-- 4. 保持了原有的ROW_NUMBER()窗口函数和exitCount过滤
-- 5. 实现了从1+N次查询到1次查询的性能优化

-- 性能对比：
-- 优化前：主查询1次 + 每条记录调用handSpecialReportFiled 1次 = 1+N次查询
-- 优化后：单次复合查询 = 1次查询
-- 性能提升：在大数据量场景下显著减少数据库交互次数
