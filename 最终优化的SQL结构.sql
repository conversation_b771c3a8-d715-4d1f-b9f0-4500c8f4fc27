-- 最终优化的SQL结构
-- 结合了您的建议：在内层查询中直接处理城市名称转换和函数逻辑集成

SELECT P.* FROM (
    SELECT DISTINCT(DPI.ID) AS id,
           O.FLIGHT_NO AS flightNo,
           O.FLIGHT_DATE AS flightDate,
           -- 直接在查询中转换城市名称（采用您的建议）
           get_city_name(DPI.ORG_CITY_AIRP) AS orgCity,
           get_city_name(DPI.DST_CITY_AIRP) AS dstCity,
           O.SERVICE_CITY AS serviceCity,
           F.SEGMENT AS segment,
           F.AC_TYPE AS acType,
           DECODE(O.PAY_TYPE,'0','不正常航班','1','异常行李','2','旅客超售') AS payType,
           F.LATE_REASON AS lateReson,
           F.DELAY_TIME AS delayTime,
           O.ORDER_ID AS orderId,
           DPI.PAX_ID AS paxId,
           DECODE(O.STATUS,'2','通过','3','生效','4','关闭') AS orderStatus,
           DPI.PAX_NAME AS paxName,
           DPI.TKT_NO AS tktNo,
           DPI.CURRENT_AMOUNT AS totalPay,
           (DPI.CURRENT_AMOUNT-NVL(OI.PRICE_SPREAD,'0')) AS payMoney,
           OI.PRICE_SPREAD AS tktPriceDiff,
           DECODE(DPI.RECEIVE_STATUS,'0','待领取','1','已领取','2','处理中','3','已逾期') AS receiveStatus,
           DECODE(DPI.RECEIVE_CHANNEL,'0','','1',DECODE(DAO.APPLY_STATUS,'0','待审核','1','通过','2','未通过','3','关闭','4','冻结'),'2','') AS actStatus,
           DECODE(DPI.RECEIVE_CHANNEL,'0','普通','1','代领','2','现金') AS receiveChannel,
           DECODE(DPI.RECEIVE_WAY,'0','微信','1','银联','2','现金') AS receiveWay,
           DECODE(DPI.RECEIVE_WAY,'0','','1',DAO.APPLY_USER,'2','') AS accountName,
           DECODE(DPI.RECEIVE_WAY,'0','','1',DAO.OPEN_BANK_NAME,'2','') AS openBankName,
           DECODE(DPI.RECEIVE_WAY,'2','',DAO.GET_MONEY_ACCOUNT) AS getAccount,
           DPI.ID_NO AS idNo,
           DAO.TELEPHONE AS telephone,
           O.REMARK AS remark,
           TO_CHAR(DAO.PAY_DATE,'YYYY-MM-DD hh24:mi:ss') AS payDate,
           TO_CHAR(add_months(TO_DATE(O.FLIGHT_DATE, 'YYYY-MM-DD'),12*1),'YYYY-MM-DD hh24:mi:ss') AS expireTime,
           E.TU_CNAME AS applyUser,
           DPI.IS_FLAG AS isFlag,
           DPI.RECEIVE_STATUS AS receiveOrder,
           DAO.PAY_STATUS,
           
           -- 修复后：处理DAO.PAY_STATUS可能为空的情况
           COALESCE(DAO.PAY_STATUS, '0') AS finalPayStatus,

           -- 修复后：处理DAO.APPLY_CODE可能为空的情况
           (CASE WHEN DAO.APPLY_CODE IS NOT NULL THEN
             (SELECT ERR_CODE_DES FROM (
               SELECT DATR.ERR_CODE_DES FROM DP_AO_TRANS_RECORD DATR
               WHERE DATR.APPLY_CODE=DAO.APPLY_CODE
               ORDER BY DATR.CREATE_TIME DESC
             ) WHERE ROWNUM=1)
            ELSE NULL END) AS payFailRemark,
           
           -- 集成最后审核人查询
           GET_USER_NAME(get_dp_Order_latest_auditor(DPI.ORDER_ID)) AS lastAuditor,
           
           ROW_NUMBER() OVER(partition by DPI.PAX_ID,DPI.ORDER_ID ORDER by DAO.CREATE_TIME DESC) AS exitCount
           
      FROM DP_PAX_INFO DPI
      LEFT JOIN DP_ORDER_INFO O ON O.ORDER_ID=DPI.ORDER_ID
      LEFT JOIN DP_FLIGHT_INFO F ON F.ORDER_ID=O.ORDER_ID
      LEFT JOIN DP_OVER_INFO OI ON OI.ORDER_ID=O.ORDER_ID
      LEFT JOIN DP_APPLY_PAX DAP ON DAP.PAX_ID=DPI.PAX_ID
        AND (DAP.ORDER_ID = DPI.ORDER_ID OR DAP.ORDER_ID LIKE '%'||DPI.ORDER_ID||'%')
      LEFT JOIN DP_APPLY_ORDER DAO ON DAO.APPLY_CODE=DAP.APPLY_CODE
      LEFT JOIN EMPLOYEE E ON E.TUNO=O.CREATE_ID
     WHERE 1=1
       AND (O.STATUS='3' OR O.STATUS='2' OR DPI.RECEIVE_STATUS='1')
       -- 这里会添加各种查询条件...
       
    ORDER BY flightDate DESC, receiveOrder DESC, payDate ASC
) P 
WHERE P.exitCount=1
  -- 可以基于新字段进行过滤
  -- AND P.finalPayStatus = :payStatus
;

-- 优化总结：
-- 1. 采用了您建议的更简洁结构，避免了双层嵌套
-- 2. 在内层查询中直接调用get_city_name函数转换城市名称
-- 3. 修复了DPI.ORDER_ID标识符无效的问题：
--    - 原方案：复杂的子查询中引用外层字段导致标识符无效
--    - 修复方案：直接使用已连接的DAO.PAY_STATUS和简化的子查询
-- 4. 保持了原有的ROW_NUMBER()窗口函数和exitCount过滤
-- 5. 实现了从1+N次查询到1次查询的性能优化

-- 关键修复说明：
-- 问题1：在子查询的 FROM DUAL 中无法正确引用外层的 DPI.ORDER_ID
-- 问题2：DAO.APPLY_CODE标识符无效（LEFT JOIN可能为空）
-- 解决方案：
--   1. 简化ORDER_ID匹配逻辑：使用LIKE模糊匹配替代复杂的REGEXP_SUBSTR
--   2. 使用COALESCE和CASE WHEN处理可能为空的字段
--   3. 利用已有的表连接关系，避免复杂的子查询

-- 性能对比：
-- 优化前：主查询1次 + 每条记录调用handSpecialReportFiled 1次 = 1+N次查询
-- 优化后：单次复合查询 = 1次查询
-- 性能提升：在大数据量场景下显著减少数据库交互次数
