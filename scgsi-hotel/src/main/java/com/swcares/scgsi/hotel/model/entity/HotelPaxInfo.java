package com.swcares.scgsi.hotel.model.entity;
import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.component.workflow.FD.entity.HotelPaxInfo <br>
 * Description：酒店保障单-旅客信息表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-09-23 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "FD_HOTEL_PAX_INFO")
@Data
@EncryptionClassz
public class HotelPaxInfo{

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;
    
    @ApiModelProperty(value = "山航保障单id")
    @Column(name ="ORDER_ID")
    private String orderId;

    @ApiModelProperty(value = "旅客id")
    @Column(name ="PAX_ID")
    private String paxId;

    @ApiModelProperty(value = "旅客姓名")
    @Column(name ="PAX_NAME")
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    @Column(name ="ID_TYPE")
    private String idType;

    @ApiModelProperty(value = "证件号")
    @Column(name ="ID_NO")
    @Encryption
    private String idNo;

    @ApiModelProperty(value = "性别C儿童M男F女")
    @Column(name ="SEX")
    private String sex;

    @ApiModelProperty(value = "联系电话")
    @Column(name ="TELEPHONE")
    @Encryption
    private String telephone;

    @ApiModelProperty(value = "航段")
    @Column(name ="SEGMENT")
    private String segment;

    @ApiModelProperty(value = "起始航站三字码")
    @Column(name ="ORG_CITY_AIRP")
    private String orgCityAirp;

    @ApiModelProperty(value = "到达航站三字码")
    @Column(name ="DST_CITY_AIRP")
    private String dstCityAirp;

    @ApiModelProperty(value = "旅客状态AC-值机，CL-订座取消，XR-直接取消")
    @Column(name ="PAX_STATUS")
    private String paxStatus;

    @ApiModelProperty(value = "主舱位")
    @Column(name ="MAIN_CLASS")
    private String mainClass;

    @ApiModelProperty(value = "子舱位")
    @Column(name ="SUB_CLASS")
    private String subClass;

    @ApiModelProperty(value = "票号")
    @Column(name ="TKT_NO")
    private String tktNo;

    @ApiModelProperty(value = "携带婴儿标识")
    @Column(name ="WITH_BABY")
    private String withBaby;

    @ApiModelProperty(value = "儿童标识")
    @Column(name ="IS_CHILD")
    private String isChild;

    @ApiModelProperty(value = "携带婴儿,婴儿名称")
    @Column(name ="BABY_PAX_NAME")
    private String babyPaxName;

    @ApiModelProperty(value = "购票时间")
    @Column(name ="TKT_ISSUE_DATE")
    private String tktIssueDate;

    @ApiModelProperty(value = "旅客资格开关(默认0有资格，1取消领取资格)")
    @Column(name ="SWITCH_OFF")
    private String switchOff;

    @ApiModelProperty(value = "预服务天数")
    @Column(name ="ETD_SERVICE_DAY")
    private String etdServiceDay;

    @ApiModelProperty(value = "PNR")
    @Column(name ="PNR")
    private String pnr;

    @ApiModelProperty(value = "是否标记0未标记1标记")
    @Column(name ="IS_FLAG")
    private String isFlag;

    @ApiModelProperty(value = "创建人")
    @Column(name = "CREATE_ID")
    private String createId;

    @ApiModelProperty(value = "创建日期")
    @Column(name = "CREATE_TIME")
    private Date createTime;


}
