package com.swcares.scgsi.hotel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：OrderSendMessgeResultVo
 * @Description：酒店保障单消息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/10/12 16:39
 * @version： v1.0
 */
@Data
public class OrderSendMessgeResultVo {

    @ApiModelProperty(value = "该流程定义下业务数据实例唯一键",required = false)
    private String businessKey;

    @ApiModelProperty(value = "审核状态- 同意AGREE、拒绝REJECT、驳回BACK")
    private String optionCode;

    @ApiModelProperty(value = "审核备注")
    private String remarks;

}
