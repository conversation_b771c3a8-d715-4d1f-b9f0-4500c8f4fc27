package com.swcares.scgsi.hotel.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hotel.enums.HotelCompensateStatusEnums;
import com.swcares.scgsi.hotel.enums.OrderAuditTypeQueryEnums;
import com.swcares.scgsi.hotel.model.dto.HotelAuditParamsDTO;
import com.swcares.scgsi.hotel.model.dto.HotelCompensateQueryDTO;
import com.swcares.scgsi.hotel.model.dto.HotelOrderAuditParamDTO;
import com.swcares.scgsi.hotel.model.dto.HotelOrderInfoDTO;
import com.swcares.scgsi.hotel.model.entity.HotelOrderInfo;
import com.swcares.scgsi.hotel.model.vo.*;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.util.UserUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;

/**
 * @ClassName：HotelCompensateDaoImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/26 11:03
 * @version： v1.0
 */
@Repository
public class HotelOrderInfoDaoImpl {
    @Resource
    private BaseDAO baseDAO;
    @Resource
    private UserUtil userUtil;

    public QueryResults findCompensateFlightPage(HotelCompensateQueryDTO dto){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        sql.append(" SELECT P.flightNo,P.flightDate,P.segment, ");
        sql.append("         FD_ORDER_STATUS_COUNT (NULL,P.flightDate,P.flightNo) AS applyCount, ");
        sql.append(" FD_ORDER_STATUS_COUNT ('3',P.flightDate,P.flightNo) AS effectCount, ");
        sql.append(" FD_ORDER_STATUS_COUNT ('1',P.flightDate,P.flightNo) AS auditCount, ");
        sql.append(" FD_ORDER_STATUS_COUNT ('9',P.flightDate,P.flightNo) AS closeCount ");
        sql.append(" FROM ( ");
        sql.append("         SELECT F.FLIGHT_NO AS flightNo,F.FLIGHT_DATE AS flightDate,REPLACE(F.SEGMENT,' ', '') AS SEGMENT  ");
        sql.append("         FROM FD_HOTEL_FLIGHT_INFO F ");
        sql.append("         LEFT JOIN FD_HOTEL_ORDER_INFO DOI ON DOI.FLIGHT_NO = F.FLIGHT_NO ");
        sql.append("         AND DOI.FLIGHT_DATE = F.FLIGHT_DATE ");
        sql.append("         LEFT JOIN FD_HOTEL_PAX_INFO DPI ON DPI.ORDER_ID=DOI.ID ");
        sql.append("         WHERE 1 = 1 ");
        String flightNo = dto.getFlightNo();
        if (StringUtils.isNotBlank(flightNo)) {
            paramsMap.put("flightNo", flightNo);
            sql.append(" AND F.FLIGHT_NO= :flightNo ");
        }
        String payType= dto.getPayType();
        if (StringUtils.isNotBlank(payType)) {
            paramsMap.put("payType", payType);
            sql.append(" AND DOI.PAY_TYPE= :payType ");
        }
        String startDate = dto.getStartDate();
        String endDate = dto.getEndDate();
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND F.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }
        if (StringUtils.isNotBlank(endDate) && StringUtils.isBlank(startDate)) {
            paramsMap.put("endDate", endDate);
            sql.append(" AND F.FLIGHT_DATE <= :endDate ");
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
            endDate = DateUtils.parseCurrentDateToStr(DateUtils.YYYY_MM_DD);
            paramsMap.put("endDate", endDate);
            paramsMap.put("startDate", startDate);
            sql.append(" AND F.FLIGHT_DATE  BETWEEN :startDate and :endDate ");
        }
        String orgCityAirp = dto.getOrgCityAirp();
        if (StringUtils.isNotBlank(orgCityAirp)) {
            paramsMap.put("orgCityAirp", orgCityAirp);
            sql.append(" AND DPI.ORG_CITY_AIRP= :orgCityAirp ");
        }
        String dstCityAirp = dto.getDstCityAirp();
        if (StringUtils.isNotBlank(dstCityAirp)) {
            paramsMap.put("dstCityAirp", dstCityAirp);
            sql.append(" AND DPI.DST_CITY_AIRP= :dstCityAirp ");
        }
        String status = dto.getStatus();
        if (StringUtils.isNotBlank(status)) {
            Authentication authentication = AuthenticationUtil.getAuthentication();
            String myOwn=(String) authentication.getPrincipal();
            paramsMap.put("myOwn", myOwn);
            if("0".equals(status)){
                sql.append(" AND DOI.STATUS='0' ");
                sql.append(" AND F.CREATE_ID= :myOwn ");
            }
            if("1".equals(status)){
                sql.append(" AND DOI.STATUS !='0' ");
                sql.append(" AND F.CREATE_ID= :myOwn ");
            }
        }else {
            //全部tab下，草稿状态单子要做私有化
            Authentication authentication = AuthenticationUtil.getAuthentication();
            String myOwn=(String) authentication.getPrincipal();
            sql.append(" AND ( DOI.STATUS !='0' or (DOI.STATUS='0' AND F.CREATE_ID= :myOwn) ) ");
            paramsMap.put("myOwn", myOwn);
        }


        sql.append(" GROUP BY F.FLIGHT_NO,F.FLIGHT_DATE,REPLACE(F.SEGMENT,' ', '') ) P ");
        sql.append(" ORDER BY P.flightDate DESC ,P.flightNo");
        return baseDAO.findBySQLPage_comm(
                sql.toString(),
                dto.getCurrent(),
                dto.getPageSize(),
                paramsMap,
                HotelCompensateFlightVO.class);
    }


    public List<HotelCompensatePageVO> getOrderInfoByFlightInfo(String flightNo, String flightDate, String status){
        if (StringUtils.isEmpty(flightNo) || StringUtils.isEmpty(flightDate)) {
            return new ArrayList<>();
        }
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();

        sql.append(" SELECT O.STATUS ,O.PAY_TYPE payType,O.ORDER_NO orderNo,O.ID as orderId,O.CHOICE_SEGMENT AS choiceSegment, ");
        sql.append("    O.SERVICE_CITY as serviceCity ,O.ETD_SERVICE_DAYS as etdServiceDays,O.SERVICE_NUM as serviceNum, ");
        sql.append("    O.CREATE_ID createId ,get_user_name(O.CREATE_ID) AS applyUser,TO_CHAR(O.LAUNCH_TIME,'YYYY-MM-DD hh24:mi:ss') AS applyTime, ");
        sql.append("    get_user_name(O.CLOSE_USER) AS closeUser,TO_CHAR(O.CLOSE_TIME,'YYYY-MM-DD hh24:mi:ss') AS closeTime, ");
        sql.append("     (SELECT  task_id from WORKFLOW_AUDITOR_ID_INFO where BUSINESS_VALUE = o.id and AUDITOR_ID =:currentUser) taskId ");

        sql.append(" FROM ");
        sql.append(" FD_HOTEL_ORDER_INFO o ");
        paramsMap.put("flightNo", flightNo);
        paramsMap.put("flightDate", flightDate);
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String myOwn=(String) authentication.getPrincipal();
        paramsMap.put("currentUser", myOwn);
        sql.append(" WHERE O.FLIGHT_NO =:flightNo ");
        sql.append(" AND O.FLIGHT_DATE=:flightDate ");
        List<String> payTypeLIst=userUtil.findBussiTypeByEmpId(myOwn);
        if(payTypeLIst.size()>0){
            String queryType1="";
            for(String payType1:payTypeLIst){
                queryType1+=payType1+",";
            }
            sql.append(" AND O.PAY_TYPE IN ");
            sql.append("  (SELECT REGEXP_SUBSTR('"+queryType1+"','[^,]+', 1, LEVEL) FROM DUAL ");
            sql.append(" connect by regexp_substr('"+queryType1+"', '[^,]+', 1, level) is not null) ");
        }
        if (StringUtils.isNotBlank(status)) {
            paramsMap.put("myOwn", myOwn);
            if("0".equals(status)){
                sql.append(" AND O.STATUS='0' ");
                sql.append(" AND O.CREATE_ID= :myOwn ");
            }
            if("1".equals(status)){
                sql.append(" AND O.STATUS !='0' ");
                sql.append(" AND O.CREATE_ID= :myOwn ");
            }
        }else {
            //全部tab下，草稿状态单子要做私有化
            sql.append(" AND (O.STATUS !='0' or (O.STATUS='0' AND O.CREATE_ID= :myOwn)) ");
            paramsMap.put("myOwn", myOwn);
        }

        sql.append(" ORDER BY applyTime DESC ");
        return (List<HotelCompensatePageVO>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, HotelCompensatePageVO.class);
    }


    public HotelCompensateDetailVO getDetailOrderInfo(String orderId){
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser =(String) authentication.getPrincipal();
        sql.append(" SELECT");
        sql.append("     FOI.id orderId,FOI.ORDER_NO orderNo,");
        sql.append("     FOI.PAY_TYPE payType,FOI.FLIGHT_NO flightNo,FOI.FLIGHT_DATE flightDate,");
        sql.append("     FOI.SERVICE_CITY serviceCity,FOI.CHOICE_SEGMENT choiceSegment,");
        sql.append("     FOI.REMARK remark, FOI.STATUS status,");
        sql.append("     FFI.LATE_REASON lateReason,");
        sql.append("     FFI.DELAY_TIME delayTime,");
        sql.append("     FFI.PLANE_CODE planeCode,");
        sql.append("     get_user_name(FOI.CREATE_ID) AS applyUser,FOI.CREATE_ID createId,FOI.CREATE_TIME createTime,");
        sql.append("     FFI.ETD etd,FFI.STD std,FOI.ETD_SERVICE_DAYS etdServiceDays ,");
        sql.append("     (select to_char(wm_concat(HOTEL_ID )) from FD_HOTEL_MIDDLE_INFO WHERE ORDER_ID = FOI. ID ) serviceHotels, ");
        sql.append("     (select to_char(wm_concat(FH.HOTEL_NAME )) from FD_HOTEL_MIDDLE_INFO FM ");
        sql.append("     LEFT JOIN FD_HOTEL_INFO FH ON FM.HOTEL_ID = FH.ID ");
        sql.append("     WHERE FM.ORDER_ID = FOI. ID ) serviceHotelNames, ");
        sql.append("     (SELECT  task_id from WORKFLOW_AUDITOR_ID_INFO where BUSINESS_VALUE = FOI.id and AUDITOR_ID =:currentUser) taskId ");
        sql.append(" FROM");
        sql.append("     FD_HOTEL_ORDER_INFO FOI");
        sql.append(" LEFT JOIN FD_HOTEL_FLIGHT_INFO FFI ON FOI. ID = FFI.ORDER_ID");
        sql.append(" WHERE");
        sql.append(" 1 = 1 ");
        sql.append(" AND FOI.ID = :orderId ");
        paramsMap.put("orderId", orderId);
        paramsMap.put("currentUser", currentUser);
        List<HotelCompensateDetailVO> bySQL_comm = (List<HotelCompensateDetailVO>) baseDAO.findBySQL_comm(sql.toString(), paramsMap, HotelCompensateDetailVO.class);
        if(bySQL_comm.size()>0){
            return bySQL_comm.get(0);
        }
        return new HotelCompensateDetailVO();
    }

    public List<Map<String,Object>> findHotelListByServiceCity(String serviceCity){
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT");
        sql.append("    ID hotelId,");
        sql.append("    HOTEL_NAME hotelName");
        sql.append(" FROM");
        sql.append("         FD_HOTEL_INFO");
        sql.append(" WHERE");
        sql.append("         HOTEL_DELETED = 0");
        sql.append("   AND HOTEL_STATUS = 0 ");
        sql.append(" AND SERVICE_CITY = '"+serviceCity+"'");
        return (List<Map<String,Object>>) baseDAO.findBySQL_comm(sql.toString(),new HashMap<>(),null);
    }

    public void updateOrderStatusByOrderIdAndStatus(String orderId, String status) {
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE FD_HOTEL_ORDER_INFO SET ");
        sql.append(" STATUS=? ");
        //关闭状态执行更新关闭人，关闭时间
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser =(String) authentication.getPrincipal();
        if (status.equals(HotelCompensateStatusEnums.CLOSE.getKey())) {
            Date closeTime = new Date();
            sql.append(" ,CLOSE_TIME=?, ");
            sql.append(" CLOSE_USER=? ");
            sql.append(" WHERE ID=? ");
            baseDAO.batchUpdate(sql.toString(), status, closeTime, currentUser, orderId);
        } else if (status.equals(HotelCompensateStatusEnums.PASS.getKey())) {
            Date updTime = new Date();
            sql.append(" ,APPROVAL_TIME=? ");
            sql.append(" ,UPDATE_TIME=? ");
            sql.append(" ,UPDATE_USER=? ");
            sql.append(" WHERE ID=? ");
            baseDAO.batchUpdate(sql.toString(), status,updTime,updTime,currentUser, orderId);
        }else  {
            Date updTime = new Date();
            sql.append(" ,UPDATE_TIME=? ");
            sql.append(" ,UPDATE_USER=? ");
            sql.append(" WHERE ID=? ");
            baseDAO.batchUpdate(sql.toString(), status,updTime,currentUser, orderId);
        }
    }
    /**
     * @title findOrderInfoById
     * @description h5提交保障单时查询草稿单内容
     * <AUTHOR>
     * @date 2022/10/27 14:08
     * @param orderId
     * @return com.swcares.scgsi.hotel.model.dto.HotelOrderInfoDTO
     */
    public HotelOrderInfoDTO findOrderInfoById(String orderId){
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT fhoi.ID orderId,fhoi.ORDER_NO orderNo,fhoi.FLIGHT_NO flightNo,");
        sql.append("fhoi.PAY_TYPE payType,fhoi.FLIGHT_DATE flightDate,fhoi.SERVICE_CITY serviceCity,");
        sql.append("fhoi.CHOICE_SEGMENT choiceSegment,fhoi.REMARK,fhoi.STATUS,fhoi.FLIGHT_ID flightId,");
        sql.append("fhoi.ETD_SERVICE_DAYS etdServiceDays,fhoi.SERVICE_NUM serviceNum,");
        sql.append("fhfi.PLANE_CODE planeCode,fhfi.AC_TYPE acType,fhfi.STD ,fhfi.ETD ,");
        sql.append("fhfi.DELAY_TIME delayTime,fhfi.LATE_REASON lateReason  ");
        sql.append("FROM FD_HOTEL_ORDER_INFO fhoi ");
        sql.append("LEFT JOIN FD_HOTEL_FLIGHT_INFO fhfi ");
        sql.append("ON fhoi.FLIGHT_ID = fhfi.FLIGHT_ID AND fhoi.id = fhfi.ORDER_ID ");
        sql.append("WHERE fhoi.ID = :id");
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("id",orderId);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,HotelOrderInfoDTO.class);
    }
    /**
     * @title findHotelIdByOrderId
     * @description  获取保障单创建时选择的酒店
     * <AUTHOR>
     * @date 2022/10/27 14:33
     * @param orderId
     * @return java.util.List<java.lang.String>
     */
    public List<HotelInfoVO> findHotelIdByOrderId(String orderId){
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT fhmi.HOTEL_ID as id FROM FD_HOTEL_MIDDLE_INFO fhmi ");
        sql.append("WHERE fhmi.ORDER_ID = :id");
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("id",orderId);
        return (List<HotelInfoVO>) baseDAO.findBySQL_comm(sql.toString(),paramsMap,HotelInfoVO.class);
    }

    public void deleteOrderInfo(String orderId) {
        //1.删除航班信息
        StringBuffer flightSql = new StringBuffer();
        flightSql.append(" DELETE FROM FD_HOTEL_FLIGHT_INFO ");
        flightSql.append(" where ORDER_ID=? ");
        baseDAO.batchUpdate(flightSql.toString(), orderId);
        //2.删除赔付酒店
        StringBuffer hotelSql = new StringBuffer();
        hotelSql.append(" DELETE FROM FD_HOTEL_MIDDLE_INFO ");
        hotelSql.append(" where ORDER_ID=? ");
        baseDAO.batchUpdate(hotelSql.toString(), orderId);
        //3.删除旅客信息
        StringBuffer paxSql = new StringBuffer();
        paxSql.append(" DELETE FROM FD_HOTEL_PAX_INFO ");
        paxSql.append(" where ORDER_ID=? ");
        baseDAO.batchUpdate(paxSql.toString(), orderId);
    }

    public int expireOrderInfo(){
        StringBuffer sql = new StringBuffer();
        sql.append(" update FD_HOTEL_ORDER_INFO set STATUS = '8' ");
        sql.append(" where id in( ");
        sql.append("         SELECT o.id ");
        sql.append("         FROM ");
        sql.append("         FD_HOTEL_ORDER_INFO O ");
        sql.append("         WHERE ");
        sql.append("         1 = 1 ");
        sql.append("         and (o.STATUS = '0' and ");
        sql.append("                 TO_CHAR (O.CREATE_TIME + INTERVAL '30' DAY,'YYYY-MM-DD')<  TO_CHAR (SYSDATE, 'YYYY-MM-DD') ");
		sql.append(" ) or ");
        sql.append("         ( ");
        sql.append("                 o.STATUS = '2' and O.APPROVAL_TIME is not null and ");
        sql.append(" TO_CHAR (O.APPROVAL_TIME + INTERVAL '30' DAY,'YYYY-MM-DD')< TO_CHAR (SYSDATE, 'YYYY-MM-DD') ");
		sql.append(" )) ");
		return baseDAO.batchUpdate(sql.toString(),new ArrayList<>());
    }


    public int completionOrder(String accommodationNo) {
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE FD_HOTEL_ORDER_INFO set STATUS='7' ");
        sql.append(" where id in(select id from ( ");
        sql.append("         select hoi.id ,count(DISTINCT hmi.HOTEL_ID) sumhotel, ");
        sql.append("         count(DISTINCT hsi.HOTEL_ID) reality ");
        sql.append("         from FD_HOTEL_ORDER_INFO hoi ");
        sql.append("         LEFT JOIN FD_HOTEL_MIDDLE_INFO hmi on hoi.id = hmi.ORDER_ID ");
        sql.append("         LEFT JOIN FD_HOTEL_ACCOMMODATION hsi on hoi.id = hsi.ORDER_ID ");
        sql.append("         where hsi.ACCOMMODATION_STATUS = '5'  ");
        if (StringUtils.isNotEmpty(accommodationNo)) {
            sql.append("         AND hsi.ACCOMMODATION_NO =  '" + accommodationNo + "'");
        }
        sql.append("         GROUP BY hoi.id ");
        sql.append(" )where reality=sumhotel) ");
        return baseDAO.batchUpdate(sql.toString(), new ArrayList<>());
    }


    public QueryResults findOrderAuditPage(HotelOrderAuditParamDTO dto) {
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser = (String) authentication.getPrincipal();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        StringBuffer sql = findOrderListByType(dto, dto.getQueryType());

        if (OrderAuditTypeQueryEnums.NOT_AUDIT.getKey().equals(dto.getQueryType())) {
            paramsMap.put("userId", currentUser);
        } else if (OrderAuditTypeQueryEnums.AUDIT.getKey().equals(dto.getQueryType())) {
            paramsMap.put("hasAuditOrderIds", dto.getHasAuditOrderList());
        } else {
            paramsMap.put("userId", currentUser);
            paramsMap.put("hasAuditOrderIds", dto.getHasAuditOrderList());
        }
        if (StringUtils.isNotBlank(dto.getFlightNo())) {
            paramsMap.put("flightNo", dto.getFlightNo());
            sql.append("AND flightNo = :flightNo ");
        }
        if (StringUtils.isNotBlank(dto.getStartDate()) && StringUtils.isNotBlank(dto.getEndDate())) {
            paramsMap.put("startDate", dto.getStartDate());
            paramsMap.put("endDate", dto.getEndDate());
            sql.append("AND flightDate BETWEEN :startDate AND :endDate ");
        }
        String orgCityAirp = dto.getOrgCityAirp();
        if (StringUtils.isNotBlank(orgCityAirp)) {
            paramsMap.put("orgCityAirp", "%" + orgCityAirp + "-%");
            sql.append(" AND choiceSegment like :orgCityAirp ");
        }
        String dstCityAirp = dto.getDstCityAirp();
        if (StringUtils.isNotBlank(dstCityAirp)) {
            paramsMap.put("dstCityAirp", "%-" + dstCityAirp + "%");
            sql.append(" AND choiceSegment like :dstCityAirp ");
        }
        sql.append(" ORDER BY flightDate DESC ,flightNo asc,createTime DESC ");
        return baseDAO.findBySQLPage_comm(
                sql.toString(),
                dto.getCurrent(),
                dto.getPageSize(),
                paramsMap,
                HotelOrderAuditPageVO.class);
    }

    private StringBuffer findOrderListByType(HotelOrderAuditParamDTO dto, String type) {
        StringBuffer sql = new StringBuffer();
        if (StringUtils.isEmpty(type) || OrderAuditTypeQueryEnums.ALL.getKey().equals(type)) {
            sql.append("SELECT * FROM (({0}) UNION({1}) ) WHERE 1=1 ");
            return new StringBuffer(MessageFormat.format(sql.toString(), findOrderListByType(dto, OrderAuditTypeQueryEnums.NOT_AUDIT.getKey())
                    , findOrderListByType(dto, OrderAuditTypeQueryEnums.AUDIT.getKey())));
        }
        if (OrderAuditTypeQueryEnums.NOT_AUDIT.getKey().equals(type)) {
            sql.append("   SELECT * FROM ( ");
            sql.append("    SELECT fhoi.ID ,fhoi.FLIGHT_NO AS flightNo,fhoi.FLIGHT_DATE AS flightDate,fhoi.CHOICE_SEGMENT AS choiceSegment,fhoi.SERVICE_CITY AS serviceCity,fhoi.STATUS status,");
            sql.append("    fhoi.PAY_TYPE payType,fhoi.ORDER_NO AS orderNO,GET_USER_NAME(fhoi.CREATE_ID) AS createUser ,fhoi.LAUNCH_TIME AS createTime, ");
            sql.append("    fhoi.CREATE_ID createId ,fhoi.SERVICE_NUM serviceNum,waii.TASK_ID taskId ");
            sql.append("    FROM FD_HOTEL_ORDER_INFO fhoi ");
            sql.append("    LEFT JOIN WORKFLOW_AUDITOR_ID_INFO waii ON fhoi.ID = waii.BUSINESS_VALUE ");
            sql.append("    WHERE waii.AUDITOR_ID = :userId  AND fhoi.STATUS != '6'");
            sql.append("   ) WHERE 1=1  ");
        }
        if (OrderAuditTypeQueryEnums.AUDIT.getKey().equals(type)) {
            sql.append("   SELECT * FROM ( ");
            sql.append("    SELECT fhoi.ID ,fhoi.FLIGHT_NO AS flightNo,fhoi.FLIGHT_DATE AS flightDate,fhoi.CHOICE_SEGMENT AS choiceSegment,fhoi.SERVICE_CITY AS serviceCity,fhoi.STATUS status,");
            sql.append("    fhoi.PAY_TYPE payType,fhoi.ORDER_NO AS orderNO,GET_USER_NAME(fhoi.CREATE_ID) AS createUser ,fhoi.LAUNCH_TIME AS createTime ,");
            sql.append("    fhoi.CREATE_ID createId ,fhoi.SERVICE_NUM serviceNum ,'' taskId");
            sql.append("    FROM FD_HOTEL_ORDER_INFO fhoi ");
            sql.append("    WHERE 1=1");
            sql.append("    AND  fhoi.ID in (:hasAuditOrderIds) ");

            //全部tab下 待审核和已审核都有数据，只保留待审核数据，前端显示id相同的会有问题，
            if (OrderAuditTypeQueryEnums.ALL.getKey().equals(dto.getQueryType())) {
                sql.append(" and fhoi.ID not in (");
                sql.append("    SELECT fhoi.ID");
                sql.append("    FROM FD_HOTEL_ORDER_INFO fhoi");
                sql.append("    LEFT JOIN WORKFLOW_AUDITOR_ID_INFO waii ON fhoi.ID = waii.BUSINESS_VALUE");
                sql.append("    WHERE waii.AUDITOR_ID = :userId   AND fhoi.STATUS != '6'");
                sql.append("    )");
            }


            sql.append("   ) WHERE 1=1  ");
        }
        return sql;
    }
}
