package com.swcares.scgsi.hotel.enums;

/**
 * @ClassName：HotelAccommodationStatusEnums
 * @Description： 住宿单状态
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/23 10:30
 * @version： v1.0
 */
public enum HotelAccommodationStatusEnums {
    DRAFT("0", "待保障"),
    ENSURING("1", "保障中"),
    READY("2", "待结算"),
    AUDIT("3", "结算审核中"),
    PASS("4", "结算完成"),
    DONE("5", "已完成")
    ;
    private HotelAccommodationStatusEnums(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static HotelAccommodationStatusEnums build(String key) {
        return build(key, true);
    }

    public static HotelAccommodationStatusEnums build(String key, boolean throwEx) {
        HotelAccommodationStatusEnums typeEnum = null;
        for (HotelAccommodationStatusEnums element : HotelAccommodationStatusEnums.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + HotelAccommodationStatusEnums.class.getSimpleName());
        }
        return typeEnum;
    }
}
