package com.swcares.scgsi.hotel.enums;

/**
 * @ClassName：HotelCompensateEnums
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/23 10:30
 * @version： v1.0
 */
public enum HotelCompensateStatusEnums {
    //服务单状态(0草稿、1审核中、2通过、3待保障、4保障中 5未通过 6驳回 7保障完成 8逾期 9关闭
    DRAFT("0", "草稿"),
    AUDIT("1", "审核中"),
    PASS("2", "通过"),
    EFFECT("3", "待保障"),//等同生效
    READY("4", "保障中"),
    NO_PASS("5", "未通过"),
    REJECT("6", "驳回"),
    COMPLETE("7", "保障完成"),
    OVERDUE("8", "逾期"),
    CLOSE("9", "关闭"),
    ;
    private HotelCompensateStatusEnums(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static HotelCompensateStatusEnums build(String key) {
        return build(key, true);
    }

    public static HotelCompensateStatusEnums build(String key, boolean throwEx) {
        HotelCompensateStatusEnums typeEnum = null;
        for (HotelCompensateStatusEnums element : HotelCompensateStatusEnums.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + HotelCompensateStatusEnums.class.getSimpleName());
        }
        return typeEnum;
    }
}
