package com.swcares.scgsi.hotel.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @ClassName：HotelCompensateVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/23 10:32
 * @version： v1.0
 */
@Data
public class HotelCompensatePageVO {

    @ApiModelProperty(value = "服务单状态(0草稿、1审核中、2通过、3生效、4关闭,5未通过6驳回7待审核8逾期9保障完成)")
    private String status;

    @ApiModelProperty(value = "赔偿单id")
    private String orderId;

    @ApiModelProperty(value = "赔偿单号")
    private String orderNo;

    @ApiModelProperty(value = "服务类型 1航延住宿")
    private String payType;

    @ApiModelProperty(value = "航段")
    private String choiceSegment;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "预计保障天数")
    private String etdServiceDays;

    @ApiModelProperty(value = "预计保障人数")
    private String serviceNum;

    @ApiModelProperty(value = "申请人")
    private String applyUser;

    @ApiModelProperty(value = "申请时间")
    private String applyTime;

    @ApiModelProperty(value = "关闭人")
    private String closeUser;

    @ApiModelProperty(value = "关闭时间")
    private String closeTime;

    @ApiModelProperty(value = "申请人id")
    private String createId;

    @ApiModelProperty(value = "审核id")
    private String taskId;



}
