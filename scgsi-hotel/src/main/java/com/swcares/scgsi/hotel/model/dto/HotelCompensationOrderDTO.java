package com.swcares.scgsi.hotel.model.dto;

import com.swcares.scgsi.base.Pager;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName：HotelCompensationOrderDTO
 * @Description：H5保障单筛选DTO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/10/8 14:59
 * @version： v1.0
 */
@Data
public class HotelCompensationOrderDTO extends Pager {

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班开始日期")
    private String flightStartDate;

    @ApiModelProperty(value = "航班结束日期")
    private String flightEndDate;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "服务单状态(0草稿、1审核中、2通过、3待保障、4保障中 5未通过 6驳回 7保障完成 8逾期 9关闭")
    private List<String> status;

    @ApiModelProperty(value = "创建人")
    private String createId;
}
