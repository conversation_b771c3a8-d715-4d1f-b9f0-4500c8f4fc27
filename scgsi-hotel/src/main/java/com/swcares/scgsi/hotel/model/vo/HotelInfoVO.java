package com.swcares.scgsi.hotel.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @ClassName：HotelInfoVO
 * @Description： 酒店信息vo类
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/23 14:14
 * @version： v1.0
 */
@Data
@ApiModel(value = "HotelInfo",description = "酒店信息")
public class HotelInfoVO {

    @ApiModelProperty("酒店id")
    private String id;

    @ApiModelProperty("酒店名称")
    private String hotelName;

    @ApiModelProperty("服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "单人间服务单价",hidden = true)
    @JsonIgnore
    private String singlePrice;

    @ApiModelProperty(value = "双人间服务单价",hidden = true)
    @JsonIgnore
    private String doublePrice;

    @ApiModelProperty(value = "结算类型    0间 1人", hidden = true)
    @JsonIgnore
    private String settlementType;

}
