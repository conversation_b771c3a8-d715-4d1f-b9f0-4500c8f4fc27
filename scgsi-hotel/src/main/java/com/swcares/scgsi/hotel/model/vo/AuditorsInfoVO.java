package com.swcares.scgsi.hotel.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：AuditorsInfoVO
 * @Description： 审核人员信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/10/12 16:55
 * @version： v1.0
 */
@Data
@ApiModel(value = "AuditorsInfoVO" , description = "审核人员信息")
public class AuditorsInfoVO {

    @ApiModelProperty("审核人")
    private String userInfo;

    @ApiModelProperty("电话")
    private String phone;

    @ApiModelProperty("工号")
    private String userNo;
}
