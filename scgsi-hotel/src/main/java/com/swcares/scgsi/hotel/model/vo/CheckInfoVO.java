package com.swcares.scgsi.hotel.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：CheckInfoVO
 * @Description： 结算审核单-核验信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/26 14:46
 * @version： v1.0
 */
@Data
@ApiModel(value = "CheckInfoVO" , description = "核验信息")
public class CheckInfoVO {

    @ApiModelProperty("结算类型   0间 1人")
    private String settlementType;
    @ApiModelProperty("审批单信息")
    List<ReviewCheckInfoVO> reviewList;

//    @ApiModelProperty("是否有不符合要求的服务标准：Y是，N否")
//    private String noAmount;
    /*结算单信息*/
    @ApiModelProperty("结算单-公务舱服务数量")
    private String orderServiceNumMainClass;

    @ApiModelProperty("结算单-经济舱服务数量")
    private String orderServiceNum;

    @ApiModelProperty("结算单-公务舱服务标准")
    private String orderServiceAmountMainClass;;

    @ApiModelProperty("结算单-经济舱服务标准")
    private String orderServiceAmount;

    @ApiModelProperty("核验状态")
    private String checkStatus;

}
