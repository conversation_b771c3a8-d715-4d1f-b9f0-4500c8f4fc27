package com.swcares.scgsi.hotel.service.process.compensate;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.util.StringUtils;
import com.swcares.exception.BusinessException;
import com.swcares.scgsi.hotel.service.HotelWorkflowBaseService;
import com.swcares.scgsi.workflow.enums.WorkflowNodeBusiTypeEnum;
import com.swcares.scgsi.workflow.enums.WorkflowStatusEnum;
import com.swcares.scgsi.workflow.model.dto.NodeExtVarsDTO;
import com.swcares.scgsi.workflow.model.dto.NodeNoticeDTO;
import com.swcares.scgsi.workflow.model.vo.NodeNoticeProcessResult;
import com.swcares.scgsi.workflow.proxy.NodeNoticeProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * ClassName：com.swcares.aps.compensation.impl.irregularflight.workflow.process <br>
 * Description：Submitter通知节点，传入业务参数至工作流引擎，真正开始整个流程的扭转 <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 02月14日 14:52 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class HotelSubmitterNodeProcess implements NodeNoticeProcess {

    @Autowired
    private HotelWorkflowBaseService hotelWorkflowBaseService;




    @Transactional
    @Override
    public NodeNoticeProcessResult process(NodeNoticeDTO noticeDTO) {
        NodeNoticeProcessResult nodeNoticeProcessResult = new NodeNoticeProcessResult();
        try {
            if(WorkflowStatusEnum.REJECT.getKey().equals(noticeDTO.getOptionCode())){
                //审批驳回后-修改状态
                nodeNoticeProcessResult.setData(hotelWorkflowBaseService.nodeBackHandler(noticeDTO));
            }else {
                nodeNoticeProcessResult.setData(hotelWorkflowBaseService.nodeHandler(noticeDTO));
            }
        }catch (Exception e){
            log.error("【scgsi-hotel】提交申领单流程出错，noticeDTO:{"+ JSONUtil.toJsonStr(noticeDTO)+"}",e);
            throw new BusinessException(null,"提交申领单流程出错",null);
        }
        return nodeNoticeProcessResult;
    }

    @Override
    public boolean canProcess(NodeNoticeDTO noticeDTO) {
        boolean result=false;
        JSONObject jsonObject = JSONUtil.parseObj(noticeDTO.getExtVars());
        NodeExtVarsDTO extVarsDTO = JSONUtil.toBean(jsonObject, NodeExtVarsDTO.class);
        if(hotelWorkflowBaseService.isExist(extVarsDTO.getBusiness()+noticeDTO.getNodeBusinessType())
                  && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), WorkflowNodeBusiTypeEnum.SUBMITTER.getType())){
            result=true;
        }
        return result;
    }
}
