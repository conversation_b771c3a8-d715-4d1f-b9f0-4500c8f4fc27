package com.swcares.scgsi.hotel.service.process.compensate;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.util.StringUtils;
import com.swcares.exception.BusinessException;
import com.swcares.scgsi.hotel.service.HotelWorkflowBaseService;
import com.swcares.scgsi.workflow.enums.WorkflowNodeBusiTypeEnum;
import com.swcares.scgsi.workflow.model.dto.NodeExtVarsDTO;
import com.swcares.scgsi.workflow.model.dto.NodeNoticeDTO;
import com.swcares.scgsi.workflow.model.vo.NodeNoticeProcessResult;
import com.swcares.scgsi.workflow.proxy.NodeNoticeProcess;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ClassName：com.swcares.aps.compensation.impl.irregularflight.workflow.process <br>
 * Description：普通审批节点 <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 02月14日 14:52 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class HotelCommonNodeProcess implements NodeNoticeProcess {
    @Autowired
    private HotelWorkflowBaseService hotelWorkflowBaseService;

    @Override
    public NodeNoticeProcessResult process(NodeNoticeDTO noticeDTO) {
        if(CollectionUtils.isEmpty(noticeDTO.getAssignees())){
            log.error("【scgsi-hotel】-航延酒店-普通节点回调异常，节点没有没有执行人,notice:{}", JSONUtil.toJsonStr(noticeDTO));
        }
        NodeNoticeProcessResult nodeNoticeProcessResult = new NodeNoticeProcessResult();
        try {
            nodeNoticeProcessResult.setData(hotelWorkflowBaseService.nodeHandler(noticeDTO));
        } catch (Exception e) {
            log.error("【scgsi-hotel】普通审核节点-处理异常！，noticeDTO:{"+ JSONUtil.toJsonStr(noticeDTO)+"}",e);
            throw new BusinessException(null,"提交申领单流程出错",null);
        }
        return nodeNoticeProcessResult;
    }

    @Override
    public boolean canProcess(NodeNoticeDTO noticeDTO) {
        boolean result=false;
        JSONObject jsonObject = JSONUtil.parseObj(noticeDTO.getExtVars());
        NodeExtVarsDTO extVarsDTO = JSONUtil.toBean(jsonObject, NodeExtVarsDTO.class);
        if(hotelWorkflowBaseService.isExist(extVarsDTO.getBusiness()+noticeDTO.getNodeBusinessType())
                && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), WorkflowNodeBusiTypeEnum.COMMON.getType())){
            result=true;
        }
        return result;
    }


}
