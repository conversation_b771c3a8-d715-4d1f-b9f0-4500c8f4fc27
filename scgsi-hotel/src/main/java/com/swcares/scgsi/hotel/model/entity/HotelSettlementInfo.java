package com.swcares.scgsi.hotel.model.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.component.workflow.FD.entity.HotelSettlementInfo <br>
 * Description：酒店结算信息表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-09-23 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "FD_HOTEL_SETTLEMENT_INFO")
@Data
@ApiModel(value="HotelSettlementInfo对象", description="酒店结算信息表")
public class HotelSettlementInfo{

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @ApiModelProperty(value = "酒店住宿单号")
    @Column(name ="ACCOMMODATION_NO")
    private String accommodationNo;

    @ApiModelProperty(value = "审核单号")
    @Column(name ="AUDIT_NO")
    private String auditNo;

    @ApiModelProperty(value = "保障单id")
    @Column(name ="ORDER_ID")
    private String orderId;

    @ApiModelProperty(value = "服务酒店id")
    @Column(name ="HOTEL_ID")
    private String hotelId;

    @ApiModelProperty(value = "授权单验证状态（0待核验1通过2退回）")
    @Column(name ="IS_AUTH_FLAG")
    private String isAuthFlag;

    @ApiModelProperty(value = "授权单验证备注")
    @Column(name ="CHECK_REMARK")
    private String checkRemark;

    @ApiModelProperty(value = "审核状态 (0待审核1审核中2驳回3通过4不通过）")
    @Column(name ="STATUS")
    private String status;

    @ApiModelProperty(value = "创建人")
    @Column(name ="CREATE_ID")
    private String createId;

    @ApiModelProperty(value = "创建时间")
    @Column(name ="CREATE_TIME")
    private Date createTime;

    @ApiModelProperty(value = "备注")
    @Column(name ="REMARK")
    private String remark;

    @ApiModelProperty(value = "酒店住宿单id")
    @Column(name ="ACCOMMODATION_ID")
    private String accommodationId;

    @ApiModelProperty(value = "提交审核时间")
    @Column(name ="SUBMIT_TIME")
    private Date submitTime;

    @ApiModelProperty(value = "审核通过时间")
    @Column(name = "AUDIT_PASS_TIME")
    private Date auditPassTime;


}
