package com.swcares.scgsi.hotel.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.dict.dao.SysDictDataDao;
import com.swcares.scgsi.dict.entity.SysDictDataInfo;
import com.swcares.scgsi.hotel.common.HotelConstant;
import com.swcares.scgsi.hotel.dao.HotelSettlementDao;
import com.swcares.scgsi.hotel.dao.impl.AccommodationDaoImpl;
import com.swcares.scgsi.hotel.dao.impl.HotelInfoDaoImpl;
import com.swcares.scgsi.hotel.dao.impl.SettleReviewDaoImpl;
import com.swcares.scgsi.hotel.enums.HotelAccommodationStatusEnums;
import com.swcares.scgsi.hotel.enums.HotelWorkflowBusinessEnum;
import com.swcares.scgsi.hotel.enums.SettleOrderStatusEnums;
import com.swcares.scgsi.hotel.enums.SettleReviewStatusEnums;
import com.swcares.scgsi.hotel.model.dto.*;
import com.swcares.scgsi.hotel.model.entity.HotelSettlementInfo;
import com.swcares.scgsi.hotel.model.vo.*;
import com.swcares.scgsi.hotel.service.HotelAuditService;
import com.swcares.scgsi.hotel.service.HotelWorkflowBaseService;
import com.swcares.scgsi.hotel.service.SettleReviewAuditService;
import com.swcares.scgsi.hotel.service.SettleReviewService;
import com.swcares.scgsi.message.common.model.form.MessageSendForm;
import com.swcares.scgsi.message.common.msgenum.MessageTypeEnum;
import com.swcares.scgsi.message.service.MessageService;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.workflow.enums.WorkflowStatusEnum;
import com.swcares.scgsi.workflow.model.dto.ActivityCompleteParamsBaseDTO;
import com.swcares.scgsi.workflow.model.dto.BaseQueryParamDTO;
import com.swcares.scgsi.workflow.model.dto.CurrentTaskActivityDTO;
import com.swcares.scgsi.workflow.model.vo.CurrentTaskActivityVO;
import com.swcares.scgsi.workflow.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName：SettleReviewServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/23 16:03
 * @version： v1.0
 */
@Service
@Slf4j
public class SettleReviewServiceImpl implements SettleReviewService {
    @Autowired
    SettleReviewDaoImpl settleReviewDao;
    @Autowired
    HotelInfoDaoImpl hotelInfoDao;
    @Autowired
    SettleReviewAuditService settleReviewAuditService;
    @Autowired
    HotelWorkflowBaseService hotelWorkflowBaseService;
    @Autowired
    WorkflowService workflowService;
    @Autowired
    HotelAuditService hotelAuditService;
    @Resource
    MessageService messageService;
    @Autowired
    SysDictDataDao sysDictDataDao;
    @Autowired
    HotelSettlementDao hotelSettlementDao;
    @Autowired
    AccommodationDaoImpl accommodationDao;

    final String ECONOMY_CLASS = "economyClass";
    final String MAIN_CLASS = "mainClass";
    final String SYS_DATA_STATUS = "0";



    @Override
    public QueryResults getSettleReviewPage(SettleReviewListDTO dto) {
        log.info("【航延住宿-结算审核单列表】筛选条件：【{}】", JSONUtil.toJsonStr(dto));
        return settleReviewDao.getSettleReviewPage(dto);
    }

    @Override
    public SettleReviewDetailVO getSettleReviewInfo(String settleId) throws Exception{
        log.info("【航延住宿-结算审核单详情】结算审核单id：【{}】",settleId);
        SettleReviewDetailVO settleReviewInfo = settleReviewDao.getSettleReviewInfo(settleId);
        log.info("【航延住宿-结算审核单详情】结算审核单基本信息：【{}】",JSONUtil.toJsonStr(settleReviewInfo));

        List<ChoosePaxVO> paxList = settleReviewDao.getChoosePaxList(settleReviewInfo.getOrderId(),settleReviewInfo.getHotelId());
        log.info("【航延住宿-结算审核单详情】结算审核单已选旅客列表：【{}】",JSONUtil.toJsonStr(paxList));
        settleReviewInfo.setPaxList(paxList);

        //添加审核记录
        if(StringUtils.isNotEmpty(settleReviewInfo.getStatus()) && !SettleReviewStatusEnums.DRAFT.getKey().equals(settleReviewInfo.getStatus())){
            List<HotelOrderAuditRecordVO> hotelOrderAuditRecordVOS = hotelAuditService.auditOperationRecord(settleId);
            settleReviewInfo.setAuditList(hotelOrderAuditRecordVOS);
            log.info("【航延住宿-结算审核单详情】结算审核单审核记录：【{}】",JSONUtil.toJsonStr(hotelOrderAuditRecordVOS));
        }
        return settleReviewInfo;
    }

    @Override
    public List<HotelInfoVO> getHotelInfo(List<String> serviceCities) {
        log.info("【航延住宿-结算审核单获取酒店下拉框】已选择的服务航站：【{}】", JSONUtil.toJsonStr(serviceCities));
        return hotelInfoDao.getHotelInfo(serviceCities);
    }

    @Override
    public CheckInfoVO getCheckInfo(String reviewNo, String auditNo) {
        log.info("【航延住宿-获取核验信息】审批单号：【{}】，结算审核单号：【{}】",reviewNo,auditNo);
        //获取结算单信息
        CheckInfoVO result = settleReviewDao.getCheckInfo(auditNo);
        if(ObjectUtils.isEmpty(result)){
            throw new BusinessException(MessageCode.HOTEL_SETTLE_NOT_FIND.getCode());
        }
        log.info("【航延住宿-获取核验信息】审批单号：【{}】，结算审核单号：【{}】,获取结算单信息：【{}】", reviewNo, auditNo, JSONUtil.toJsonStr(result));

        //通过审批单号获取审批单信息
        List<ReviewCheckInfoVO>  list = settleReviewDao.getReviewCheckInfo(reviewNo);
        if(ObjectUtils.isEmpty(list)){
            throw new BusinessException(MessageCode.HOTEL_REVIEW_NOT_FIND.getCode());
        }
        log.info("【航延住宿-获取核验信息】审批单号：【{}】，结算审核单号：【{}】,获取审批单信息集合：【{}】", reviewNo, auditNo, JSONUtil.toJsonStr(list));
        result.setReviewList(list);
        /*
        //封装审批单信息
        List<SysDictDataInfo> l1 = sysDictDataDao.findByTypeAndStatus(MAIN_CLASS,SYS_DATA_STATUS);
        List<SysDictDataInfo> l2 = sysDictDataDao.findByTypeAndStatus(ECONOMY_CLASS,SYS_DATA_STATUS);
        if(ObjectUtils.isEmpty(l1) || ObjectUtils.isEmpty(l2)){
            log.error("【航延住宿-获取核验信息】获取舱位类型出错，请检查数据字典舱位类型！");
        }
        String mainClass = l1.get(0).getValue();
        String economyClass = l2.get(0).getValue();
        list.forEach(e->{
            if(e.getServiceName().contains(mainClass) &&
                    ObjectUtils.isEmpty(result.getReviewServiceAmountMainClass())){
                result.setReviewServiceAmountMainClass(e.getResultMoney());
                result.setReviewServiceNumMainClass(e.getAmount());
            }else if(e.getServiceName().contains(economyClass) &&
                    ObjectUtils.isEmpty(result.getReviewServiceAmount())){
                result.setReviewServiceAmount(e.getResultMoney());
                result.setReviewServiceNum(e.getAmount());
            }
        });*/
        log.info("【航延住宿-获取核验信息】审批单号：【{}】，结算审核单号：【{}】，获取最终结果：【{}】", reviewNo, auditNo, JSONUtil.toJsonStr(result));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitSettleReviewNo(String auditNo) throws Exception{
        log.info("【航延住宿-提交结算审核】结算审核单号：【{}】",auditNo);
        //核验完成
        doneCheck(auditNo);

        SettleReviewSubmitVO settleReviewSubmitInfo = settleReviewDao.getSettleReviewSubmitInfo(auditNo);
        if(ObjectUtils.isEmpty(settleReviewSubmitInfo)){
            log.error("【航延住宿-提交结算审核】结算审核不存在！单号：【{}】",auditNo);
            throw new BusinessException(MessageCode.HOTEL_SETTLE_REVIEW_NOT_FIND.getCode());
        }else if(!SettleOrderStatusEnums.PASS.getKey().equals(settleReviewSubmitInfo.getCheckStatus())){
            log.error("【航延住宿-提交结算审核】结算审核单当前核验状态不支持提交操作！核验状态【{}】，应该是1",settleReviewSubmitInfo.getCheckStatus());
            throw new BusinessException(MessageCode.HOTEL_SETTLE_STATUS_NOT_SUPPORT.getCode());
        }
        log.info("【航延住宿-提交结算审核】提交的审核信息：【{}】", JSONUtil.toJsonStr(settleReviewSubmitInfo));

        //发起审核：当前状态为待核验
        if(SettleReviewStatusEnums.DRAFT.getKey().equals(settleReviewSubmitInfo.getStatus()) ){
            hotelWorkflowBaseService.startAuditProcess(
                    WorkflowStartBaseDTO.builder()
                            .business(HotelWorkflowBusinessEnum.FD_SETTLEMENT.getKey())
                            .businessKey(settleReviewSubmitInfo.getId())
                            .businessObj(settleReviewSubmitInfo)
                            .build()
            );
        }//重新提交：当前状态为驳回
        else if(SettleReviewStatusEnums.REJECT.getKey().equals(settleReviewSubmitInfo.getStatus()) ){
            hotelWorkflowBaseService.submitterAuditProcess(
                    WorkflowStartBaseDTO.builder()
                            .business(HotelWorkflowBusinessEnum.FD_SETTLEMENT.getKey())
                            .businessKey(settleReviewSubmitInfo.getId())
                            .businessObj(settleReviewSubmitInfo)
                            .build()
            );
        }

        //发起成功后推送消息给保障单提交人
        MessageSendForm sendForm = getMessageSendForm(settleReviewSubmitInfo,"submit");
        messageService.sendMsg(sendForm);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkBack(String auditNo,String remark){
        log.info("【航延住宿-核验退回】结算审核单号：【{}】",auditNo);
        SettleReviewSubmitVO settleReviewSubmitInfo = settleReviewDao.getSettleReviewSubmitInfo(auditNo);
        if(ObjectUtils.isEmpty(settleReviewSubmitInfo)){
            throw new BusinessException(MessageCode.HOTEL_SETTLE_REVIEW_NOT_FIND.getCode());
        }else if (!SettleOrderStatusEnums.DRAFT.getKey().equals(settleReviewSubmitInfo.getCheckStatus())){
            throw new BusinessException(MessageCode.HOTEL_SETTLE_STATUS_NOT_SUPPORT.getCode());
        }
        log.info("【航延住宿-核验退回】提交的审核信息：【{}】", JSONUtil.toJsonStr(settleReviewSubmitInfo));
        //修改结算审核单以及住宿单的状态
        settleReviewAuditService.updateSettleReviewStatus(settleReviewSubmitInfo.getId(),SettleReviewStatusEnums.DRAFT.getKey(), SettleOrderStatusEnums.REJECT.getKey());
        settleReviewAuditService.updateSettleOrderStatus(settleReviewSubmitInfo.getAccommodationId(),SettleReviewStatusEnums.DRAFT.getKey(),
                SettleOrderStatusEnums.REJECT.getKey(), HotelAccommodationStatusEnums.READY.getKey());

        //修改结算审核单核验备注
        updSettleCheckRemark(settleReviewSubmitInfo.getCheckRemark(),remark,settleReviewSubmitInfo.getId());
    }

    public void doneCheck(String auditNo){
        log.info("【航延住宿-核验完成】结算审核单号：【{}】",auditNo);
        SettleReviewSubmitVO settleReviewSubmitInfo = settleReviewDao.getSettleReviewSubmitInfo(auditNo);
        if(ObjectUtils.isEmpty(settleReviewSubmitInfo)){
            log.error("【航延住宿-核验完成】结算单不存在！");
            throw new BusinessException(MessageCode.HOTEL_SETTLE_REVIEW_NOT_FIND.getCode());
        }else if (!SettleOrderStatusEnums.DRAFT.getKey().equals(settleReviewSubmitInfo.getCheckStatus())){
            log.error("【航延住宿-核验完成】结算单当前状态【{}】不支持核验状态！--状态应该是0才对",settleReviewSubmitInfo.getCheckStatus());
            throw new BusinessException(MessageCode.HOTEL_SETTLE_STATUS_NOT_SUPPORT.getCode());
        }
        log.info("【航延住宿-核验完成】提交的审核信息：【{}】", JSONUtil.toJsonStr(settleReviewSubmitInfo));
        //修改结算审核单以及住宿单的状态

        settleReviewAuditService.updateSettleReviewStatus(settleReviewSubmitInfo.getId(),settleReviewSubmitInfo.getStatus(), SettleOrderStatusEnums.PASS.getKey());
        settleReviewAuditService.updateSettleOrderStatus(settleReviewSubmitInfo.getAccommodationId(),SettleReviewStatusEnums.DRAFT.getKey(),
                SettleOrderStatusEnums.PASS.getKey(),HotelAccommodationStatusEnums.READY.getKey());
    }

    @Override
    public List<AuditorsInfoVO> getAuditors(String businessKey, String userInfo) {
        CurrentTaskActivityVO currentTaskActivityVO = workflowService.currentUserTask(BaseQueryParamDTO.builder().businessKey(businessKey).build());
        /**查询审核用户集合**/
        CurrentTaskActivityDTO currentTaskActivityDTO = currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0);
        List<String> assignees = currentTaskActivityDTO.getAssignees();
        log.info("【结算审核单-查询审核人员】查询结果(处理前-配置信息):【{}】",JSONUtil.toJsonStr(assignees));
        Set<String> auditorList = new HashSet<>();
        if(ObjectUtils.isNotEmpty(assignees)) {
            auditorList = hotelAuditService.findAuditorList(assignees);
            log.info("【结算审核单-查询审核人员】查询结果(处理后-工号集):【{}】",JSONUtil.toJsonStr(auditorList));
        }else{
            return null;
        }
        return settleReviewAuditService.getAuditors(auditorList,userInfo);
    }

    @Override
    public void saveAuditors(String businessKey, List<String> auditors) {
        hotelAuditService.saveTaskAuditor(HotelWorkflowBusinessEnum.FD_SETTLEMENT.getKey(),
                businessKey,auditors);
    }

    @Override
    public void auditOperation(SettleReviewAuditDTO dto) throws Exception{
        ActivityCompleteParamsBaseDTO baseDTO = BeanUtil.copyProperties(dto, ActivityCompleteParamsBaseDTO.class);
        baseDTO.setBusinessKey(dto.getOrderId());
        baseDTO.setBusiness(HotelWorkflowBusinessEnum.FD_SETTLEMENT.getKey());
        hotelAuditService.auditOperation(baseDTO);
        //若是驳回到第一节点（即是保障单提交人）需要消息通知
        CurrentTaskActivityVO currentTaskActivityVO = workflowService.currentUserTask(BaseQueryParamDTO.builder().businessKey(dto.getOrderId()).build());
        //查询当前任务节点信息
        CurrentTaskActivityDTO currentTaskActivityDTO = currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0);
        SettleReviewSubmitVO settleReviewSubmitInfo = settleReviewDao.getSettleReviewSubmitInfo(dto.getOrderId());
        if(WorkflowStatusEnum.REJECT.getKey().equals(dto.getOptionCode()) && ObjectUtils.isNotEmpty(currentTaskActivityDTO.getAssignees())
                && currentTaskActivityDTO.getAssignees().get(0).split(":")[1].equals(settleReviewSubmitInfo.getUserNo())){
            //推送消息给保障单提交人
            MessageSendForm sendForm = getMessageSendForm(settleReviewSubmitInfo, dto.getRemarks());
            messageService.sendMsg(sendForm);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByNoBody(String settleId) {
        //需要修改FD_HOTEL_ACCOMMODATION以及FD_HOTEL_SETTLEMENT_INFO的 审核单状态和核验状态 为通过
        //还需修改FD_HOTEL_ACCOMMODATION 的住宿单状态为已完成
        HotelSettlementInfo settlementInfo = hotelSettlementDao.findTById(settleId);
        if(ObjectUtils.isEmpty(settlementInfo)){
            log.error("【无人入住保障场景】修改无人入住结算单状态出错--结算单id【{}】不存在",settleId);
            throw new BusinessException(MessageCode.HOTEL_SETTLE_REVIEW_NOT_FIND.getCode());
        }
        log.info("【无人入住保障场景】结算单信息【{}】",JSONUtil.toJsonStr(settlementInfo));
        settleReviewDao.updateSettleReviewStatus(settleId,
                SettleReviewStatusEnums.PASS.getKey(),
                SettleOrderStatusEnums.PASS.getKey());

        accommodationDao.updateAccommodationStatus(settlementInfo.getAccommodationId(),
                SettleReviewStatusEnums.PASS.getKey(),
                SettleOrderStatusEnums.PASS.getKey(),
                HotelAccommodationStatusEnums.DONE.getKey());

    }


    @Override
    public QueryResults getPaxAccommodationReportPage(PaxAccommodationReportDTO dto) {
        QueryResults paxAccommodationReportPage = settleReviewDao.getPaxAccommodationReportPage(dto);
        //取出分页数据中的List数据集合
        List<PaxAccommodationReportChildVO> list = (List<PaxAccommodationReportChildVO>) paxAccommodationReportPage.getList();
        if(ObjectUtils.isEmpty(list)) return paxAccommodationReportPage;
        //将List中徐要查询审核历史记录的单子进行去重转换成list集合，避免重复的id反复调用审核历史记录接口
        List<String> orderIdList = list.stream().map(PaxAccommodationReportChildVO::getOrderId).distinct().collect(Collectors.toList());
        List<String> settleIdList = list.stream().map(PaxAccommodationReportChildVO::getSettleId).distinct().collect(Collectors.toList());
        //将每个单子对应的审核记录中的最后一个审核节点转换成Map对象
        Map<String, HotelOrderAuditRecordVO> orderAuditRecordVOMap = orderIdList.stream().map(order -> {
            try {
                List<HotelOrderAuditRecordVO> hotelOrderAuditRecordVOS = hotelAuditService.auditOperationRecord(order);
                return hotelOrderAuditRecordVOS.get(hotelOrderAuditRecordVOS.size() - 1);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toMap(HotelOrderAuditRecordVO::getOrderId, Function.identity()));
        Map<String, HotelOrderAuditRecordVO> orderSettleAuditRecordVOMap = settleIdList.stream().map(settleId -> {
            try {
                List<HotelOrderAuditRecordVO> hotelSettleOrderAuditRecordVOS = hotelAuditService.auditOperationRecord(settleId);
                return hotelSettleOrderAuditRecordVOS.get(hotelSettleOrderAuditRecordVOS.size() - 1);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toMap(HotelOrderAuditRecordVO::getOrderId, Function.identity()));
        //遍历list集合，取出每个单子对应的审核人和结算审核人
        list.forEach(vo -> {
            vo.setExamineUser(orderAuditRecordVOMap.get(vo.getOrderId()).getAuditor());
            HotelOrderAuditRecordVO hotelOrderAuditRecordVO = orderSettleAuditRecordVOMap.get(vo.getSettleId());
            vo.setSettleReviewUser(hotelOrderAuditRecordVO.getAuditor());
            vo.setSettleReviewTime(DateUtils.parseStrToDate(hotelOrderAuditRecordVO.getAuditTime(),DateUtils.YYYY_MM_DD_HH_MM_SS));
        });
        paxAccommodationReportPage.setList(list);
        return paxAccommodationReportPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFlag(List<String> paxIds, String flag) {
        settleReviewDao.updateFlag(paxIds,flag);
    }

    /**
     * @title getMessageSendForm
     * @description 获取消息内容
     * <AUTHOR>
     * @date 2022/11/9 16:00
     * @param settleReviewSubmitInfo
     * @param key
     * @return com.swcares.scgsi.message.common.model.form.MessageSendForm
     */
    private MessageSendForm getMessageSendForm(SettleReviewSubmitVO settleReviewSubmitInfo,String key){
        if(ObjectUtils.isEmpty(settleReviewSubmitInfo)){
            throw new BusinessException(MessageCode.HOTEL_ORDER_NOT_NULL.getCode());
        }
        StringBuilder content=new StringBuilder();
        MessageSendForm sendForm = getBaseMessageSendForm(settleReviewSubmitInfo.getId());
        if("submit".equals(key)){
            content.append(HotelConstant.TO_DO_MSG_CONTENT);
            sendForm.setMsgTitle(HotelConstant.TO_DO_MSG_TITLE);
        }else {
            content.append(String.format(HotelConstant.BACK_MSG_CONTENT,key));
            sendForm.setMsgTitle(HotelConstant.BACK_MSG_TITLE);
        }
        sendForm.setMsgContent(content.toString());
        sendForm.setMsgUser("0");//系统发送
        sendForm.setFlightNo(settleReviewSubmitInfo.getFlightNo());
        sendForm.setFlightDate(settleReviewSubmitInfo.getFlightDate());
        sendForm.setMsgReplyUser(settleReviewSubmitInfo.getUserNo().split(","));

        return sendForm;
    }
    /**
     * @title getBaseMessageSendForm
     * @description 封装消息基本内容
     * <AUTHOR>
     * @date 2022/11/9 15:59
     * @param settleId
     * @return com.swcares.scgsi.message.common.model.form.MessageSendForm
     */
    private MessageSendForm getBaseMessageSendForm(String settleId){
        MessageSendForm sendForm = new MessageSendForm();
        sendForm.setMsgType(MessageTypeEnum.HOTEL_SETTLE_AUDIT.getType());
        sendForm.setMsgTypeName(MessageTypeEnum.HOTEL_SETTLE_AUDIT.getTypeName());
        sendForm.setMsgChildType(MessageTypeEnum.HOTEL_SETTLE_AUDIT.getChildType()+"");
        sendForm.setMsgChildTypeName(MessageTypeEnum.HOTEL_SETTLE_AUDIT.getChildTypeName());
        sendForm.setPcUrl(MessageFormat.format(MessageTypeEnum.HOTEL_SETTLE_AUDIT.getPcUrl(),settleId));
        sendForm.setMobileUrl(MessageFormat.format(MessageTypeEnum.HOTEL_SETTLE_AUDIT.getMobileUrl(),settleId));
        sendForm.setIsAudit(MessageTypeEnum.HOTEL_SETTLE_AUDIT.getIsAudit());
        sendForm.setMsgDate(new Date());
        return sendForm;
    }

    public void updSettleCheckRemark(String oldRemark,String newRemark,String settleId){
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String re =ObjectUtils.isNotEmpty(oldRemark)? oldRemark:"";
        StringBuffer remark = new StringBuffer(re);
        remark.append(df.format(new Date()))
                .append("\t")
                .append(newRemark)
                .append("\n");
        settleReviewAuditService.updateSettleCheckRemark(settleId,remark.toString());
    }
}
