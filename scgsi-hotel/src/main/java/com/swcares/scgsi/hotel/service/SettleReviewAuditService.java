package com.swcares.scgsi.hotel.service;

import com.swcares.scgsi.hotel.model.vo.AuditorsInfoVO;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @ClassName：SettleReviewAuditService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/10/11 16:24
 * @version： v1.0
 */
public interface SettleReviewAuditService {
    /**
     * @title updateSettleReviewStatus
     * @description  修改结算审核单的状态
     * <AUTHOR>
     * @date 2022/10/11 17:18
     * @param settleId 结算审核单id或单号
     * @param auditStatus 审核状态
     * @param status 核验状态
     * @return void
     */
    void updateSettleReviewStatus(String settleId,String auditStatus,String status);

    /**
     * @title updateSettleOrderStatus
     * @description  修改住宿单的状态--酒店端
     * <AUTHOR>
     * @date 2022/10/11 17:19
     * @param accommodationId 住宿单id或单号
     * @param auditStatus 审核状态
     * @param status 核验状态
     * @param accommodationStatus 住宿单状态
     * @return void
     */
    void updateSettleOrderStatus(String accommodationId,String auditStatus,String status,String accommodationStatus);

    /**
     * @title updateSettleRemark
     * @description 修改结算审核单的备注（仅驳回到发起人时修改，作为驳回的操作记录使用）
     * <AUTHOR>
     * @date 2022/10/19 11:11
     * @param remark  备注
     * @param settleId 结算单id
     * @return void
     */
    void updateSettleRemark(String remark,String settleId);

    /**
     * @title updateSettleCheckRemark
     * @description 修改结算审核单的核验备注（仅点击退回按钮才会记录，追加方式）
     * <AUTHOR>
     * @date 2022/12/2 15:37
     * @param settleId
     * @param remark
     * @return void
     */
    void updateSettleCheckRemark(String settleId,String remark);

    /**
     * @title updateSettleSubTime
     * @description  修改结算审核单的提交审核时间（只保存最近一次提交时间）
     * <AUTHOR>
     * @date 2022/10/26 14:35
     * @param subTime 提交时间
     * @param settleId 结算单id
     * @return void
     */
    void updateSettleSubTime(Date subTime,String settleId);

    /**
     * @title getAuditors
     * @description 获取可审核人员
     * <AUTHOR>
     * @date 2022/10/13 9:18
     * @param auditorIds
     * @param userInfo
     * @return java.util.List<com.swcares.scgsi.hotel.model.vo.AuditorsInfoVO>
     */
    List<AuditorsInfoVO> getAuditors(Set<String> auditorIds, String userInfo);

}
