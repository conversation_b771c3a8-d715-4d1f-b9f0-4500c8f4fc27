package com.swcares.scgsi.hotel.service;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hotel.model.dto.AuditCompleteParamsDTO;
import com.swcares.scgsi.hotel.model.dto.PaxAccommodationReportDTO;
import com.swcares.scgsi.hotel.model.dto.SettleReviewAuditDTO;
import com.swcares.scgsi.hotel.model.dto.SettleReviewListDTO;
import com.swcares.scgsi.hotel.model.vo.AuditorsInfoVO;
import com.swcares.scgsi.hotel.model.vo.CheckInfoVO;
import com.swcares.scgsi.hotel.model.vo.HotelInfoVO;
import com.swcares.scgsi.hotel.model.vo.SettleReviewDetailVO;
import com.swcares.scgsi.web.RenderResult;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @ClassName：SettleReviewService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/23 13:45
 * @version： v1.0
 */
public interface SettleReviewService {
    /**
     * @title getSettleReviewPage
     * @description 分页查询结算审核列表
     * <AUTHOR>
     * @date 2022/9/23 14:31
     * @param dto 结算审核列表查询参数封装对象
     * @return com.swcares.scgsi.base.QueryResults
     */
    QueryResults getSettleReviewPage(SettleReviewListDTO dto);

    /***
     * @title getSettleReviewInfo
     * @description 获取结算审核详情
     * <AUTHOR>
     * @date 2022/9/23 13:48
     * @param settleId 结算审核单id
     * @return java.lang.Object
     */
    SettleReviewDetailVO getSettleReviewInfo(String settleId) throws Exception;

    /**
     * @title getHotelInfo
     * @description 下拉获取酒店
     * <AUTHOR>
     * @date 2022/9/23 16:26
     * @param serviceCities 已选航站
     * @return java.util.List<com.swcares.scgsi.hotel.model.vo.HotelInfoVO>
     */
    List<HotelInfoVO> getHotelInfo(List<String> serviceCities);

    /**
     * @title getCheckInfo
     * @description @TODO
     * <AUTHOR>
     * @date 2022/9/26 14:58
     * @param reviewNo 审批单号
     * @param auditNo   结算审核单号
     * @return com.swcares.scgsi.hotel.model.vo.CheckInfoVO
     */
    CheckInfoVO getCheckInfo(String reviewNo, String auditNo);

    /**
     * @title submitSettleReviewNo
     * @description 提交结算审核-进入审核
     * <AUTHOR>
     * @date 2022/10/11 9:49
     * @param auditNo
     * @return void
     */
    void submitSettleReviewNo (String auditNo) throws Exception;

    /**
     * @title checkBack
     * @description 核验退回
     * <AUTHOR>
     * @date 2022/10/12 9:51
     * @param auditNo 结算审核单号
     * @param remark 备注
     * @return void
     */
    void checkBack(String auditNo,String remark);

//    /**
//     * @title doneCheck
//     * @description  完成核验
//     * <AUTHOR>
//     * @date 2022/10/19 11:20
//     * @param auditNo 结算审核单号
//     * @return void
//     */
//    void doneCheck(String auditNo);


        /**
         * @title getAuditors
         * @description  查找可审核的人员
         * <AUTHOR>
         * @date 2022/10/13 9:35
         * @param businessKey 审核单id
         * @param userInfo 搜索条件（审核人姓名/工号）
         * @return java.util.List<com.swcares.scgsi.hotel.model.vo.AuditorsInfoVO>
         */
    List<AuditorsInfoVO> getAuditors(String businessKey,String userInfo);

    /**
     * @title saveAuditors
     * @description 保存所选的审核人
     * <AUTHOR>
     * @date 2022/10/13 9:45
     * @param businessKey 审核单id
     * @param auditors 已选审核人工号
     * @return void
     */
    void saveAuditors(String businessKey,List<String> auditors);

    /**
     * @title auditOperation
     * @description 结算审核单-审核操作
     * <AUTHOR>
     * @date 2022/10/13 11:30
     * @param dto
     * @return void
     */
    void auditOperation(SettleReviewAuditDTO dto) throws Exception;

    /**
     * @title updateByNoBody
     * @description  酒店发起结算时，因酒店无人入住，直接将此结算单通过
     * <AUTHOR>
     * @date 2023/1/6 15:48
     * @param settleId 结算单id
     * @return void
     */
    void updateByNoBody(String settleId);


        /**
         * @title getPaxAccommodationReportPage
         * @description 报表分页查询
         * <AUTHOR>
         * @date 2022/9/26 14:07
         * @param dto
         * @return List<PaxAccommodationReportVO>
         */
    QueryResults getPaxAccommodationReportPage(PaxAccommodationReportDTO dto);

    /**
     * @title updateFlag
     * @description 修改旅客标记
     * <AUTHOR>
     * @date 2022/9/27 11:27
     * @param paxIds
     * @param flag
     * @return java.lang.Boolean
     */
    void updateFlag(List<String> paxIds, String flag);

}
