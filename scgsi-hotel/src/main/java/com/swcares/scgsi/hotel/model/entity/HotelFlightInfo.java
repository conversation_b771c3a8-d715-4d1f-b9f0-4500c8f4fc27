package com.swcares.scgsi.hotel.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.component.workflow.FD.entity.HotelFlightInfo <br>
 * Description：酒店保障单-航班信息表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-09-23 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "FD_HOTEL_FLIGHT_INFO")
@ApiModel(value="HotelFlightInfo对象", description="酒店保障单-航班信息表")
public class HotelFlightInfo {

    private static final long serialVersionUID = 1L;
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @ApiModelProperty(value = "保障单id")
    @Column(name ="ORDER_ID")
    private String orderId;

    @ApiModelProperty(value = "航班ID")
    @Column(name ="FLIGHT_ID")
    private String flightId;

    @ApiModelProperty(value = "航班号")
    @Column(name ="FLIGHT_NO")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    @Column(name ="FLIGHT_DATE")
    private String flightDate;

    @ApiModelProperty(value = "航段(LXA_CTU)")
    @Column(name ="SEGMENT")
    private String segment;

    @ApiModelProperty(value = "飞机号")
    @Column(name ="PLANE_CODE")
    private String planeCode;

    @ApiModelProperty(value = "机型")
    @Column(name = "AC_TYPE")
    private String acType;

    @ApiModelProperty(value = "计划起飞时间")
    @Column(name ="STD")
    private String std;

    @ApiModelProperty(value = "预计起飞时")
    @Column(name ="ETD")
    private String etd;

    @ApiModelProperty(value = "延误时长")
    @Column(name ="DELAY_TIME")
    private String delayTime;

    @ApiModelProperty(value = "延误原因")
    @Column(name ="LATE_REASON")
    private String lateReason;

    @ApiModelProperty(value = "创建人ID（P1003_USER_INFO）")
    @Column(name ="CREATE_ID")
    private String createId;

    @ApiModelProperty(value = "创建时间")
    @Column(name ="CREATE_TIME")
    private Date createTime;


}
