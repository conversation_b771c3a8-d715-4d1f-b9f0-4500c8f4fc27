package com.swcares.scgsi.hotel.service;

import com.swcares.scgsi.audit.vo.ActSendMessgeVo;
import com.swcares.scgsi.hotel.model.dto.HotelOrderSaveParamDTO;
import com.swcares.scgsi.hotel.model.dto.OrderSendMessgeResultVo;
import com.swcares.scgsi.hotel.model.entity.HotelOrderInfo;
import com.swcares.scgsi.message.common.model.form.MessageSendForm;

/**
 * @ClassName：HotelOrderInfoService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/26 16:54
 * @version： v1.0
 */
public interface HotelOrderInfoService {

    /***
     * @title saveOrderInfo
     * @description 保存航延酒店保障单
     * <AUTHOR>
     * @date 2022/9/26 17:15
     * @param dto
     * @return java.lang.String
     */
    String saveOrderInfo(HotelOrderSaveParamDTO dto);


    /***
     * @title assembleResultSendMessage
     * @description 审核结果通知
     * <AUTHOR>
     * @date 2022/10/12 16:50
     * @param sendMessage
     * @return com.swcares.scgsi.message.common.model.form.MessageSendForm
     */
    MessageSendForm assembleResultSendMessage(OrderSendMessgeResultVo sendMessage);

    /***
     * @title assembleSendMessage
     * @description 下一个节点审核通知
     * <AUTHOR>
     * @date 2022/10/12 16:50
     * @param sendMessage
     * @param userId
     * @return com.swcares.scgsi.message.common.model.form.MessageSendForm
     */
    MessageSendForm assembleSendMessage(OrderSendMessgeResultVo sendMessage, String userId);
}
