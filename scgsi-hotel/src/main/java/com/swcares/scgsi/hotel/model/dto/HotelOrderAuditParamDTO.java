package com.swcares.scgsi.hotel.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：HotelOrderAuditParamDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/12/13 10:46
 * @version： v1.0
 */
@Data
public class HotelOrderAuditParamDTO {

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班开始日期")
    private String startDate;

    @ApiModelProperty(value = "航班结束日期")
    private String endDate;

    @ApiModelProperty(value = "起始")
    private String orgCityAirp;

    @ApiModelProperty(value = "到达")
    private String dstCityAirp;

    @ApiModelProperty(value = "已审核补偿单单号（后端使用，前端不传）")
    @JsonIgnore
    private List<String> hasAuditOrderList;

    @ApiModelProperty(value = "查询类型 0或者空全部 1-待审核 2-已审核")
    private String queryType;

    /**
     * 当前页数，默认为第一页
     **/
    private int current = 1;

    /**
     * 每页显示记录数，默认为10条
     **/
    private int pageSize = 10;


}
