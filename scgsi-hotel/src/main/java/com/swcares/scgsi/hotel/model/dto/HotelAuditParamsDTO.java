package com.swcares.scgsi.hotel.model.dto;

import com.swcares.scgsi.base.Pager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName：HotelAuditParamsDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/11/9 11:17
 * @version： v1.0
 */
@Data
@ApiModel(value="HotelAuditParamsDTO对象", description="审核列表筛选对象")
public class HotelAuditParamsDTO extends Pager {

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "0 全部 1待审核 2已审核{必填}")
    @NotNull
    private String type;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班开始时间")
    private String startDate;

    @ApiModelProperty(value = "航班结束时间")
    private String endDate;

    /**
     * 赔偿类型 不正常航班赔偿0、异常行李1、超售旅客2
     */
    /*  private String payType;*/

    @ApiModelProperty(value = " 服务单状态(0草稿、1审核中、2通过、3待保障、4保障中 5未通过 6驳回 7保障完成 8逾期 9关闭 ")
    private List<String> status;

    @ApiModelProperty(value = "审核类型：航延补偿")
    private String auditStatus;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "已审核补偿单单号（后端使用，前端不传）")
    private List<String> hasAuditOrderList;

}
