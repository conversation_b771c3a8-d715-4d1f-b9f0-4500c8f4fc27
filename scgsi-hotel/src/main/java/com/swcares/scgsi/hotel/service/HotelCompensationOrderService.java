package com.swcares.scgsi.hotel.service;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hotel.model.dto.HotelAuditParamsDTO;
import com.swcares.scgsi.hotel.model.dto.HotelCompensationOrderDTO;
import com.swcares.scgsi.hotel.model.dto.HotelCompensationPaxDTO;
import com.swcares.scgsi.hotel.model.vo.HotelCompensateDetailVO;
import com.swcares.scgsi.hotel.model.vo.HotelCompensatePaxVO;
import com.swcares.scgsi.hotel.model.vo.HotelOrderInfoVO;

import java.util.List;

/**
 * @ClassName：HotelCompensationOrderService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/10/8 14:28
 * @version： v1.0
 */
public interface HotelCompensationOrderService {
    /**
     * @title getGuaranteeOrderList
     * @description 保障住宿单列表
     * <AUTHOR>
     * @date 2022/10/10 15:43
     * @param dto
     * @return java.util.List<com.swcares.scgsi.hotel.model.vo.HotelOrderInfoVO>
     */
    QueryResults getGuaranteeOrderList(HotelCompensationOrderDTO dto);

    /**
     * @title getGuaranteeOrderDetail
     * @description 旅客保障住宿单详情
     * <AUTHOR>
     * @date 2022/10/10 15:42
     * @param orderId
     * @return com.swcares.scgsi.hotel.model.vo.HotelCompensateDetailVO
     */
    HotelCompensateDetailVO getGuaranteeOrderDetail(String orderId) throws Exception;
    /**
     * @title getPersonnelDetails
     * @description 获取保障旅客详情
     * <AUTHOR>
     * @date 2022/10/10 15:42
     * @param dto
     * @return java.util.List<com.swcares.scgsi.hotel.model.vo.HotelCompensatePaxVO>
     */
    List<HotelCompensatePaxVO> getPersonnelDetails(HotelCompensationPaxDTO dto);

    /**
     * @title getOrderExamineList
     * @description 查询保障住宿单审核列表
     * <AUTHOR>
     * @date 2022/11/9 11:41
     * @param dto
     * @return com.swcares.scgsi.base.QueryResults
     */
    QueryResults getOrderExamineList(HotelAuditParamsDTO dto);

    /**
     * @title submitOrder
     * @description  h5提交保障单审核
     * <AUTHOR>
     * @date 2022/10/27 14:00
     * @param orderId 保障单id
     * @return java.lang.String 新的保障单id
     */
    String submitOrder(String orderId);
}
