package com.swcares.scgsi.hotel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import com.swcares.scgsi.hotel.enums.HotelCompensateStatusEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * @ClassName：PaxAccommodationReportVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/9/23 16:16
 * @version： v1.0
 */
@ApiModel(value = "旅客住宿报表VO")
@EncryptionClassz
public class PaxAccommodationReportVO {


    @ApiModelProperty(value = "标记")
    private String isFlag;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "服务类型")
    private String payType;

    @ApiModelProperty(value = "酒店名称")
    private String hotelName;

    @ApiModelProperty(value = "服务天数")
    private String guaranteeDayCount;

    @ApiModelProperty(value = "服务保障单单号")
    private String orderNo;

    @ApiModelProperty(value = "服务单发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "审核单号")
    private String auditNo;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "证件号")
    @Encryption
    private String idNo;

    @ApiModelProperty(value = "手机号")
    @Encryption
    private String telephone;

    @ApiModelProperty(value = "房间类型")
    private String roomType;

    @ApiModelProperty(value = "房间号")
    private String roomNo;

    @ApiModelProperty(value = "入住时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date checkInTime;

    @ApiModelProperty(value = "入住办理方式")
    private String checkInMode;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "出发航站")
    private String orgCityAirport;

    @ApiModelProperty(value = "到达航站")
    private String dstCityAirport;

    @ApiModelProperty(value = "服务单状态")
    private String status;

    @ApiModelProperty(value = "机型")
    private String acType;

    @ApiModelProperty(value = "创建人/发起人")
    private String createUser;

    @ApiModelProperty(value = "审核人")
    private String examineUser;

    @ApiModelProperty(value = "结算审核人")
    private String settleReviewUser;

    @ApiModelProperty(value = "结算审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settleReviewTime;

    @ApiModelProperty(value = "完成结算人")
    private String settleUser;

    @ApiModelProperty(value = "完成结算日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date settleTime;

    public String getIsFlag() {
        return isFlag;
    }

    public void setIsFlag(String isFlag) {
        if("1".equals(isFlag)) {
            this.isFlag = "是";
        }else {
            this.isFlag = "否";
        }
    }

    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate;
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    public String getSegment() {
        return segment;
    }

    public void setSegment(String segment) {
        this.segment = segment;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        if (StringUtils.isNotEmpty(payType)) {
            switch (payType) {
                case "1":
                    this.payType = "航延住宿";
                    break;
            }
        }
    }

    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    public String getGuaranteeDayCount() {
        return guaranteeDayCount;
    }

    public void setGuaranteeDayCount(String guaranteeDayCount) {
        this.guaranteeDayCount = guaranteeDayCount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getAuditNo() {
        return auditNo;
    }

    public void setAuditNo(String auditNo) {
        this.auditNo = auditNo;
    }

    public String getPaxName() {
        return paxName;
    }

    public void setPaxName(String paxName) {
        this.paxName = paxName;
    }

    public String getTktNo() {
        return tktNo;
    }

    public void setTktNo(String tktNo) {
        this.tktNo = tktNo;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getRoomType() {
        return roomType;
    }

    public void setRoomType(String roomType) {
        if (StringUtils.isNotEmpty(roomType)) {
            switch (roomType) {
                case "1":
                    this.roomType = "单人间";
                    break;
                case "2":
                    this.roomType = "双人间";
                    break;
            }
        }
    }

    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    public Date getCheckInTime() {
        return checkInTime;
    }

    public void setCheckInTime(Date checkInTime) {
        this.checkInTime = checkInTime;
    }

    public String getCheckInMode() {
        return checkInMode;
    }

    public void setCheckInMode(String checkInMode) {
        if (StringUtils.isNotEmpty(checkInMode)) {
            switch (checkInMode) {
                case "0":
                    this.checkInMode = "自动";
                    break;
                case "1":
                    this.checkInMode = "手动";
                    break;
            }
        }
    }

    public String getServiceCity() {
        return serviceCity;
    }

    public void setServiceCity(String serviceCity) {
        this.serviceCity = serviceCity;
    }

    public String getOrgCityAirport() {
        return orgCityAirport;
    }

    public void setOrgCityAirport(String orgCityAirport) {
        this.orgCityAirport = orgCityAirport;
    }

    public String getDstCityAirport() {
        return dstCityAirport;
    }

    public void setDstCityAirport(String dstCityAirport) {
        this.dstCityAirport = dstCityAirport;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        if(StringUtils.isNotEmpty(status))
        this.status = HotelCompensateStatusEnums.build(status).getValue();
    }

    public String getAcType() {
        return acType;
    }

    public void setAcType(String acType) {
        this.acType = acType;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getExamineUser() {
        return examineUser;
    }

    public void setExamineUser(String examineUser) {
        this.examineUser = examineUser;
    }

    public String getSettleReviewUser() {
        return settleReviewUser;
    }

    public void setSettleReviewUser(String settleReviewUser) {
        this.settleReviewUser = settleReviewUser;
    }

    public Date getSettleReviewTime() {
        return settleReviewTime;
    }

    public void setSettleReviewTime(Date settleReviewTime) {
        this.settleReviewTime = settleReviewTime;
    }

    public String getSettleUser() {
        return settleUser;
    }

    public void setSettleUser(String settleUser) {
        this.settleUser = settleUser;
    }

    public Date getSettleTime() {
        return settleTime;
    }

    public void setSettleTime(Date settleTime) {
        this.settleTime = settleTime;
    }
}
