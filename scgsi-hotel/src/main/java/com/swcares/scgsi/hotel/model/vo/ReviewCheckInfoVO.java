package com.swcares.scgsi.hotel.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：ReviewCheckInfoVO
 * @Description： 审批单-核验信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/26 14:46
 * @version： v1.0
 */
@Data
@ApiModel(value = "ReviewCheckInfoVO" , description = "审批单核验信息")
public class ReviewCheckInfoVO {

    @ApiModelProperty("服务数量")
    private String amount;

    @ApiModelProperty("服务标准名")
    private String serviceName;

    @ApiModelProperty("服务标准")
    private String serviceMoney;

    @ApiModelProperty("服务标准表达式")
    private String serviceMoneyExpr;

    @ApiModelProperty("最终服务标准")
    private String resultMoney;

    @ApiModelProperty("单位名称")
    private String companyName;

}
