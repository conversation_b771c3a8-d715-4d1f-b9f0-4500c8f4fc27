package com.swcares.scgsi.hotel.common;

/**
 * @ClassName：HotelConstant
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/27 11:03
 * @version： v1.0
 */
public interface HotelConstant {

    String LOCK_NAME="HOTEL_LOCK_SAVE_NAME";
    //redis分布式锁过期时间
    Long SAVE_REDIS_LOCK_TIME = 120L;


    //----------------------------workflow使用---------------------------------------
    //文件夹
    String COMPENSATION_CODE="COMPENSATION_ORDER_AUDIT:";
    //赔偿单审核-审核操作人（部门、角色）
    String COMPENSATION_AUDITOR_KEY = COMPENSATION_CODE+"{}";

    //同意AGREE、拒绝REJECT、驳回BACK
    //审核状态-同意
    String AUDIT_AGREE = "AGREE";
    //审核状态-不同意
    String AUDIT_DISAGREE = "REJECT";
    //审核状态-驳回
    String AUDIT_REJECT = "BACK";
    //审核节点-发起人节点
    String NODE_KEY_SUBMITTER = "submitter";
    //不正常航班-审核流程key【TODO 可变】
    String AUDIT_PROCESS_KEY="Fltdelayt1";
    //节点是否为end
    String NODE_END="end";
    //节点是否为CANCEL
    String NODE_CANCEL="cancel";
    //admin结束异常流程，备注
    String ADMIN_END_REMARKS="当前审核节点未配置审核人员";
    //admin结束异常流程，备注
    String ADMIN_END="ADMIN";
    //admin结束异常流程，前端提示
    String ADMIN_PROMPT_MESSAGE="未匹配到下一节点审核人员，系统已自动为您结束审核流程";

    //工作流业务申领单业务类型
    String IRREGULARFLIGHT_WORKFLOW_BUSINESS="H";

    String SUBMITTER_COMMON="发起";

    //----------------------------workflow使用---------------------------------------

    //-----------------------------结算审批单站内信--------------------------------------
    String TO_DO_MSG_CONTENT = "<p>您有一条结算审核单待审核，请前往处理</p>";
    String BACK_MSG_CONTENT = "<p>您有一条服务保障单被驳回，备注：%s ，请前往查看</p>";
    String TO_DO_MSG_TITLE = "结算审核单审核提醒";
    String BACK_MSG_TITLE = "结算审核单驳回提醒";

    //-----------------------------结算审批单站内信--------------------------------------

    //-----------------------------酒店保障单--------------------------------------
    //保障类型：航延住宿
    String PAY_TYPE_HOTEL = "1";
}
