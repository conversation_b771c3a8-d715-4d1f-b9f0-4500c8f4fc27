package com.swcares.scgsi.hotel.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * ClassName：com.swcares.component.workflow.FD.entity.HotelRoomInfo <br>
 * Description：酒店房间信息表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-09-23 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "FD_HOTEL_ROOM_INFO")
@ApiModel(value="HotelRoomInfo对象", description="酒店房间信息表")
public class HotelRoomInfo {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @ApiModelProperty(value = "酒店ID")
    @Column(name ="HOTEL_ID")
    private String hotelId;

    @ApiModelProperty(value = "房间号")
    @Column(name ="ROOM_NO")
    private String roomNo;

    @ApiModelProperty(value = "房间类型（1单人间，2双人间）")
    @Column(name ="ROOM_TYPE")
    private String roomType;

    @ApiModelProperty(value = "房间状态（0不可用，1可用）")
    @Column(name ="ROOM_STATUS")
    private String roomStatus;

    @ApiModelProperty(value = "添加日期")
    @Column(name ="CREATE_DATE")
    private Date createDate;

    @ApiModelProperty(value = "修改日期")
    @Column(name ="MODIFY_DATE")
    private Date modifyDate;

    @ApiModelProperty(value = "添加人ID")
    @Column(name ="CREATE_ID")
    private String createId;

    @ApiModelProperty(value = "修改人ID")
    @Column(name ="MODIFY_ID")
    private String modifyId;

    @ApiModelProperty(value = "0 正常  1被删除")
    @Column(name ="ROOM_DELETED")
    private String roomDeleted;


}
