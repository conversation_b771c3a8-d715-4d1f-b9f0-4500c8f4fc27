package com.swcares.scgsi.hotel.model.dto;

import com.swcares.scgsi.base.Pager;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * @ClassName：PaxAccommodationReportDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/9/23 15:10
 * @version： v1.0
 */
@Data
@ApiModel(value = "旅客住宿报表DTO")
public class PaxAccommodationReportDTO extends Pager {

    @ApiModelProperty(value = "航班开始时间")
    private String flightStartDate;

    @ApiModelProperty(value = "航班结束时间")
    private String flightEndDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "服务航站")
    private List<String> serviceCities;

    @ApiModelProperty(value = "服务酒店")
    private List<String> serviceHotels;

    @ApiModelProperty(value = "建单开始时间")
    private String createStartTime;

    @ApiModelProperty(value = "建单结束时间")
    private String createEndTime;

    @ApiModelProperty(value = "服务单号")
    @Size(max = 50,message = "服务单号超出限制，最大50字符")
    private String orderNo;

    @ApiModelProperty(value = "旅客姓名")
    @Size(max = 20,message = "姓名长度超出限制，最大20字符")
    private String paxName;

    @ApiModelProperty(value = "证件号")
    @Size(max = 50,message = "证件号超出限制，最大50字符")
    private String idNO;

    @ApiModelProperty(value = "电话号码")
    @Size(max = 20,message = "电话号码长度超出限制，最大20字符")
    private String telephone;

    @ApiModelProperty(value = "出发航站")
    private List<String> orgCityAirports;

    @ApiModelProperty(value = "到达航站")
    private List<String> dstCityAirports;

    @ApiModelProperty(value = "服务单状态")
    private List<String> status;

    @ApiModelProperty(value = "服务类型")
    private List<String> payTypes;

    @ApiModelProperty(value = "标记")
    private String isFlag;

}
