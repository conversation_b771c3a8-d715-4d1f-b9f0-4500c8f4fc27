package com.swcares.scgsi.hotel.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：HotelOrderInfoDTO
 * @Description：保存赔偿单-订单信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/26 10:04
 * @version： v1.0
 */
@Data
@ApiModel(value = "HotelOrderInfoDTO",description = "保存赔偿单-航班&赔偿单信息")
public class HotelOrderInfoDTO{


    @ApiModelProperty(value = "保障单号")
    private String orderId;

    @ApiModelProperty(value = "保障单号")
    private String orderNo;

    @ApiModelProperty(value = "保障类型 1酒店")
    private String payType;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "所选航段")
    private String choiceSegment;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "服务单状态(0草稿、1审核中、2通过、3待保障、4保障中 5未通过 6驳回 7保障完成 8逾期 9关闭")
    private String status;

    @ApiModelProperty(value = "航班ID")
    private String flightId;

    @ApiModelProperty(value = "预计保障天数")
    private String etdServiceDays;

    @ApiModelProperty(value = "预计保障人数")
    private String serviceNum;

    @ApiModelProperty(value = "保障酒店，多选逗号分隔")
    private String serviceHotels;

    //-------------以下是航班信息-----------------

    @ApiModelProperty(value = "飞机号")
    private String planeCode;

    @ApiModelProperty(value = "机型")
    private String acType;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "预计起飞时")
    private String etd;

    @ApiModelProperty(value = "延误时长")
    private String delayTime;

    @ApiModelProperty(value = "延误原因")
    private String lateReason;

}
