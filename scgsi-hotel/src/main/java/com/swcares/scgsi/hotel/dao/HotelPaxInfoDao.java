package com.swcares.scgsi.hotel.dao;

import com.swcares.scgsi.base.BaseJpaDao;
import com.swcares.scgsi.encryption.DecryptMethod;
import com.swcares.scgsi.hotel.model.entity.HotelPaxInfo;

import java.util.List;

/**
 * @ClassName：HotelCompensateDao
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/23 10:30
 * @version： v1.0
 */
public interface HotelPaxInfoDao extends BaseJpaDao<HotelPaxInfo,String> {


    @DecryptMethod
    List<HotelPaxInfo> findAllByOrderId(String orderId);
}
