package com.swcares.scgsi.hotel.service.process.compensate;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.util.StringUtils;
import com.swcares.exception.BusinessException;
import com.swcares.scgsi.hotel.service.HotelWorkflowBaseService;
import com.swcares.scgsi.workflow.enums.WorkflowNodeBusiTypeEnum;
import com.swcares.scgsi.workflow.model.dto.NodeExtVarsDTO;
import com.swcares.scgsi.workflow.model.dto.NodeNoticeDTO;
import com.swcares.scgsi.workflow.model.vo.NodeNoticeProcessResult;
import com.swcares.scgsi.workflow.proxy.NodeNoticeProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ClassName：com.swcares.aps.compensation.impl.irregularflight.workflow.process <br>
 * Description：申领单END通知节点处理；直接将申领单改为审核通过或者审核不通过<br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 02月14日 14:52 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class HotelEndNodeNoticeProcess implements NodeNoticeProcess {

    @Autowired
    private HotelWorkflowBaseService hotelWorkflowBaseService;

    @Override
    public NodeNoticeProcessResult process(NodeNoticeDTO noticeDTO) {
        try {
            hotelWorkflowBaseService.nodeHandler(noticeDTO);
        }catch (Exception e){
            log.error("【scgsi-hotel】结束流程申领单流程出错，noticeDTO:{"+ JSONUtil.toJsonStr(noticeDTO)+"}",e);
            throw new BusinessException(null,"提交申领单流程出错",null);
        }
        return new NodeNoticeProcessResult();
    }

    @Override
    public boolean canProcess(NodeNoticeDTO noticeDTO) {
        boolean result=false;
        JSONObject jsonObject = JSONUtil.parseObj(noticeDTO.getExtVars());
        NodeExtVarsDTO extVarsDTO = JSONUtil.toBean(jsonObject, NodeExtVarsDTO.class);
        if(hotelWorkflowBaseService.isExist(extVarsDTO.getBusiness()+noticeDTO.getNodeBusinessType())
                && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), WorkflowNodeBusiTypeEnum.END.getType())){
            result=true;
        }

        return result;
    }

}
