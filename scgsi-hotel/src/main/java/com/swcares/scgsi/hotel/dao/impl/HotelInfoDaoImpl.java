package com.swcares.scgsi.hotel.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.hotel.model.vo.HotelInfoVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：HotelInfoDao
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/26 11:29
 * @version： v1.0
 */
@Repository
public class HotelInfoDaoImpl {

    @Resource
    private BaseDAO baseDAO;


    public List<HotelInfoVO> getHotelInfo(List<String> serviceCities) {
        StringBuffer sql = getHotelInfoSql();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        if(ObjectUtils.isNotEmpty(serviceCities)){
            sql.append("and fhi.SERVICE_CITY IN (:serviceCity)");
            paramsMap.put("serviceCity",serviceCities);
        }
        return (List<HotelInfoVO>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,HotelInfoVO.class);
    }

    public List<HotelInfoVO> getHotelInfoByOrderId(String orderId){
        StringBuffer sql = getHotelInfoByOrderIdSql();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("orderId",orderId);
        return (List<HotelInfoVO>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,HotelInfoVO.class);
    }

    private StringBuffer getHotelInfoSql(){
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT fhi.id,fhi.HOTEL_NAME as hotelName,fhi.SERVICE_CITY as serviceCity FROM FD_HOTEL_INFO fhi ");
        sql.append("WHERE HOTEL_DELETED = 0 ");
        return sql;
    }

    private StringBuffer getHotelInfoByOrderIdSql(){
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT fhi.ID ,fhi.SINGLE_PRICE singlePrice,fhi.DOUBLE_PRICE doublePrice, ");
        sql.append("fhi.SETTLEMENT_TYPE settlementType   ");
        sql.append("FROM FD_HOTEL_INFO fhi  ");
        sql.append("LEFT JOIN FD_HOTEL_MIDDLE_INFO fhmi ON fhi.id = fhmi.HOTEL_ID  ");
        sql.append("WHERE fhmi.ORDER_ID = :orderId");
        return sql;
    }


}
