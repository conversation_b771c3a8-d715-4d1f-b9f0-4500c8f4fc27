package com.swcares.scgsi.hotel.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.hotel.model.vo.HotelOrderAuditRecordVO;
import com.swcares.scgsi.hotel.service.HotelAuditService;
import com.swcares.scgsi.hum.employee.entity.Employee;
import com.swcares.scgsi.user.dao.UserJpaDao;
import com.swcares.scgsi.util.Asserts;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.workflow.dao.WorkflowAuditorIdInfoDao;
import com.swcares.scgsi.workflow.dao.impl.WorkflowAuditorIdInfoDaoImpl;
import com.swcares.scgsi.workflow.model.dto.*;
import com.swcares.scgsi.workflow.model.entity.WorkflowAuditorIdInfoDO;
import com.swcares.scgsi.workflow.model.vo.CurrentTaskActivityVO;
import com.swcares.scgsi.workflow.model.vo.HistoryTaskAuditActivityVO;
import com.swcares.scgsi.workflow.proxy.NodeNoticeProcessProxy;
import com.swcares.scgsi.workflow.service.WorkflowNodeDriveService;
import com.swcares.scgsi.workflow.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * ClassName：com.swcares.scgsi.hotel.service <br>
 * Description：航延 <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 09月30日 15:50 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class HotelAuditServiceImpl implements HotelAuditService {
    @Autowired
    WorkflowService workflowService;
    @Autowired
    NodeNoticeProcessProxy nodeNoticeProcessProxy;
    @Autowired
    WorkflowNodeDriveService workflowNodeDriveService;
    @Autowired
    WorkflowAuditorIdInfoDaoImpl workflowAuditorIdInfoDaoImpl;
    @Autowired
    WorkflowAuditorIdInfoDao workflowAuditorIdInfoDao;
    @Autowired(required = false)
    private Redisson redisson;
    @Autowired
    UserJpaDao userJpaDao;

    String ROLE_ID="roleId";
    String USER_ID="userId";
    private static final String HOTEL_REDISSON_PRE_KEY = "HOTEL_AUDIT_";
    private static final Long REDISSON_KEY_REDIS_LOCK_TIME = 100L;
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditOperation(ActivityCompleteParamsBaseDTO dto){
        RLock lock = redisson.getLock(HOTEL_REDISSON_PRE_KEY + dto.getBusinessKey()+dto.getTaskId());
        try {
            boolean resLock = lock.tryLock();
            if (resLock) {
                log.info("【scgsi-hotel】-赔偿单工作流-start节点，begin:{}", JSONUtil.toJsonStr(dto));

                if(StringUtils.isEmpty(dto.getBusiness())){
                    log.info("---【scgsi-hotel】--方法[auditOperation]------business业务标记不能为空！----");
                    throw new BusinessException(MessageCode.HOTEL_BUSINESS_NOT_NULL.getCode());
                }
                List<WorkflowAuditorIdInfoDO> allByBusinessValue = workflowAuditorIdInfoDao.findAllByBusinessValue(dto.getBusinessKey());
                log.info("---【scgsi-hotel】--方法[auditOperation]------businessKey:[{}]-查询结果为【{}】----", dto.getBusinessKey(), JSON.toJSONString(allByBusinessValue));


                Authentication authentication = AuthenticationUtil.getAuthentication();
                String createUser = (String) authentication.getPrincipal();
                boolean isAuth = false;
                for (WorkflowAuditorIdInfoDO infoDO : allByBusinessValue) {
                    if (infoDO.getAuditorId().equals(createUser) && infoDO.getTaskId().equals(dto.getTaskId())) {
                        isAuth = true;
                    }
                }
                if (ObjectUtil.isEmpty(allByBusinessValue) || !isAuth) {
                    log.info("---【scgsi-hotel】--方法[auditOperation]------businessKey:[{}]-审批任务不存在，抛出提示！----", dto.getBusinessKey());
                    throw new BusinessException(MessageCode.HOTEL_ORDER_AUDIT_COMPLETE.getCode());
                }

                CompleteProcessParamsDTO processParamsDTO = new CompleteProcessParamsDTO();
                processParamsDTO.setBusinessKey(dto.getBusinessKey());
                processParamsDTO.setComment(dto.getRemarks());
                processParamsDTO.setOptionCode(dto.getOptionCode());
                processParamsDTO.setTaskId(dto.getTaskId());
                processParamsDTO.setUserId(createUser);
                processParamsDTO.setNextAssignee(dto.getNextAssignee());
                dto.setUserId(createUser);
                //删除待审核,此处删除后，节点common、end就不用再做删除操作了
                workflowAuditorIdInfoDao.deleteByBusinessValue(dto.getBusinessKey());
                CurrentTaskActivityVO currentTaskActivityVO = workflowService.complementTask(processParamsDTO);
                //节点驱动
                NodeExtVarsDTO nodeExtVarsDTO = workflowNodeDriveService.createNodeExtVarsDTO(dto.getBusiness());
                workflowNodeDriveService.triggerNextNodeNotice(currentTaskActivityVO,nodeExtVarsDTO,dto);
            } else {
                log.error("【scgsi-hotel】-赔偿单工作流-start节点，获取分布式锁失败：{}", JSONUtil.toJsonStr(dto));
                throw new BusinessException(MessageCode.HOTEL_ORDER_AUDIT_COMPLETE.getCode());
            }
        } finally {
            lock.unlock();
        }
    }

    @Override
    public List<HotelOrderAuditRecordVO> auditOperationRecord(String businessKey) throws Exception {
        Asserts.isNotEmpty(businessKey,MessageCode.HOTEL_BUSINESS_KEY_NOT_NULL.getCode());
        HistoryTaskAuditActivityVO historyTaskAuditActivityVO = workflowService.historyTaskAuditActivity(
                BaseQueryParamDTO.builder().businessKey(businessKey).build());
        List<HistoryTaskAuditActivityDTO> taskAuditActivityDTOS = historyTaskAuditActivityVO.getHistoryTaskAuditActivityDTOS();

        List<HotelOrderAuditRecordVO> compensateAuditRecordVO = new ArrayList<>();
        //组装对象
        for(HistoryTaskAuditActivityDTO activityDTO : taskAuditActivityDTOS){

            HotelOrderAuditRecordVO recordVO = new HotelOrderAuditRecordVO();
            if(null != activityDTO.getTaskEndTime()){
                recordVO.setAuditTime(DateUtils.parseDateToStr(activityDTO.getTaskEndTime(),DateUtils.YYYY_MM_DD_HH_MM_SS));
            }
            recordVO.setNodeKey(activityDTO.getNodeKey());
            recordVO.setOpinionCode(activityDTO.getOptionCode());
            recordVO.setOrderId(businessKey);
            recordVO.setRemark(activityDTO.getComment());
            recordVO.setTaskStatus(activityDTO.getTaskStatus());
            //查用户信息
            /***待处理节点-审核用户来源：从待审核表中获取*/
            if(!activityDTO.getTaskStatus()){
                Set<String> auditorSet = new HashSet<>();
                List<String> auditorList = new ArrayList<>();
                List<Map<String, Object>> auditorListMap = workflowAuditorIdInfoDaoImpl.getAuditorByBusinessKey(businessKey);
                addAuditorList(auditorListMap,auditorSet);
                for(String tuno :auditorSet){
                    Employee employee = userJpaDao.findByTuNo(tuno);
                    if(null == employee){continue;}
                    auditorList.add(employee.getTuCname());
                }
                recordVO.setAuditor(String.join(",", auditorList));
            }else {
                String[] split1 = activityDTO.getAssignee().split(":");
                String userId = split1[1];
                List<Map<String, Object>> employeeByTuno = workflowAuditorIdInfoDaoImpl.getEmployeeByTuno(userId);
                if(employeeByTuno.size()>0){
                    Map<String, Object> objectMap = employeeByTuno.get(0);
                    String tucname = (String) objectMap.get("TUCNAME");
                    String tuno = (String) objectMap.get("TUNO");
                    String job = (String) objectMap.get("JOB");
                    recordVO.setAuditor(tucname);
                    recordVO.setJob(job);
                }
            }
            compensateAuditRecordVO.add(recordVO);
        }
        return compensateAuditRecordVO;
    }


    @Override
    public List<WorkflowAuditorIdInfoDO> findTaskAuditorList(String businessKey) {
        Asserts.isNotEmpty(businessKey,MessageCode.HOTEL_BUSINESS_KEY_NOT_NULL.getCode());
        return workflowAuditorIdInfoDao.findAllByBusinessValue(businessKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTaskAuditor(String business,String businessKey, List<String> assignees) {
        Asserts.isNotEmpty(business,MessageCode.HOTEL_BUSINESS_NOT_NULL.getCode());
        Asserts.isNotEmpty(businessKey,MessageCode.HOTEL_BUSINESS_KEY_NOT_NULL.getCode());
        List<WorkflowAuditorIdInfoDO> allByBusinessValue = workflowAuditorIdInfoDao.findAllByBusinessValue(businessKey);
        if(ObjectUtils.isEmpty(allByBusinessValue)){
            throw new BusinessException(MessageCode.TASK_NOT_EXIST.getCode());
        }
        String taskId = allByBusinessValue.get(0).getTaskId();
        List<WorkflowAuditorIdInfoDO> auditorIdInfoDOList = new ArrayList<>();
        /**设置任务候选人**/
        workflowService.addNextAssignee(businessKey,assignees);
        /**删除**/
        workflowAuditorIdInfoDao.deleteByBusinessValue(businessKey);
        /**添加待审核用户**/
        for(String auditorId:assignees){
            WorkflowAuditorIdInfoDO auditorIdInfoDO = new WorkflowAuditorIdInfoDO();
            auditorIdInfoDO.setAuditorId(auditorId);
            auditorIdInfoDO.setTaskId(taskId);
            auditorIdInfoDO.setBusinessValue(businessKey);
            auditorIdInfoDO.setBusiness(business);
            auditorIdInfoDOList.add(auditorIdInfoDO);
        }
        workflowAuditorIdInfoDao.saveAll(auditorIdInfoDOList);
    }


    @Override
    public Set<String> findAuditorList(List<String> assignees) {
        if(CollectionUtils.isEmpty(assignees)){
            return Collections.EMPTY_SET;
        }
        //roleId:航延住宿角色001/航延住宿角色002,userId:30001,deptId:1002
        Map<String,String> auditorMap=new HashMap<>();
        assignees.forEach(t->{
            String[] split = t.split(":");
            auditorMap.put(split[0],split[1].replace("/",","));
        });
        Set<String> auditorList = new HashSet<>();
        List<Map<String,Object>> auditorListRole = workflowAuditorIdInfoDaoImpl.getAuditorOndutyByRoleName(auditorMap.get(ROLE_ID), false);
        List<Map<String,Object>> auditorListTuno = workflowAuditorIdInfoDaoImpl.getAuditorOndutyByTuNo(auditorMap.get(USER_ID), false);
        addAuditorList(auditorListRole,auditorList);
        addAuditorList(auditorListTuno,auditorList);
        return auditorList;
    }

    private void addAuditorList(List<Map<String,Object>> auditorListMap,Set<String> auditorList){
        if(auditorListMap.size()>0){
            String userId = (String) auditorListMap.get(0).get("USERID");
            if(StringUtils.isNotEmpty(userId)){
                auditorList.addAll(Arrays.asList(userId.split(",")));
            }
        }
    }

}
