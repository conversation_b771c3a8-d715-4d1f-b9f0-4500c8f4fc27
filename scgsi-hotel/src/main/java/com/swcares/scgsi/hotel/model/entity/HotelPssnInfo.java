package com.swcares.scgsi.hotel.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * ClassName：com.swcares.component.workflow.FD.entity.HotelPssnInfo <br>
 * Description：酒店住宿单-旅客信息表（酒店入住单） <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-09-23 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name ="FD_HOTEL_PSSN_INFO")
@ApiModel(value="HotelPssnInfo对象", description="酒店住宿单-旅客信息表（酒店入住单）")
public class HotelPssnInfo{

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @ApiModelProperty(value = "酒店住宿单单号")
    @Column(name ="ACCOMMODATION_NO")
    private String accommodationNo;

    @ApiModelProperty(value = "酒店ID（冗余--酒店住宿单中有）")
    @Column(name ="HOTEL_ID")
    private String hotelId;

    @ApiModelProperty(value = "旅客ID")
    @Column(name ="HOTEL_PAX_ID")
    private String hotelPaxId;

    @ApiModelProperty(value = "房间酒店")
    @Column(name ="ROOM_ID")
    private String roomId;

    @ApiModelProperty(value = "办理入住方式（0自动，1手动）")
    @Column(name ="CHECK_IN_MODE")
    private String checkInMode;

    @ApiModelProperty(value = "保障天数")
    @Column(name ="GUARANTEE_DAY_COUNT")
    private String guaranteeDayCount;

    @ApiModelProperty(value = "1 单人间  2 双人间")
    @Column(name ="ROOM_TYPE")
    private String roomType;

    @ApiModelProperty(value = "山航保障单ID")
    @Column(name ="ORDER_ID")
    private String orderId;


}
