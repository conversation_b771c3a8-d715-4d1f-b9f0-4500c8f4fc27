package com.swcares.scgsi.hotel.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 * @ClassName：HotelAccommodationVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/10/25 11:49
 * @version： v1.0
 */
@Data
public class HotelAccommodationVO {

    @ApiModelProperty(value = "酒店住宿单号")
    private String accommodationNo;

    @ApiModelProperty(value = "保障单id")
    private String orderId;

    @ApiModelProperty(value = "服务酒店id")
    private String hotelId;

    @ApiModelProperty(value = "酒店住宿单id")
    private String accommodationId;

    @ApiModelProperty(value = "授权单验证状态（0待核验1通过2退回）")
    private String isAuthFlag;

}
