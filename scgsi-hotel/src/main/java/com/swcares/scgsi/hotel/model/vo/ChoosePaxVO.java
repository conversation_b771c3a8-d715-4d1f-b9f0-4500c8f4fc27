package com.swcares.scgsi.hotel.model.vo;

import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：ChoosePaxVO
 * @Description： 保障单已选的旅客VO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/23 15:44
 * @version： v1.0
 */
@EncryptionClassz
@Data
@ApiModel(value = "ChoosePaxVO",description = "保障单已选的旅客")
public class ChoosePaxVO {
    /**
     * 旅客基础信息
     */
    @ApiModelProperty(value = "山航保障单id")
    private String orderId;

    @ApiModelProperty(value = "旅客id")
    private String paxId;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    private String idType;

    @ApiModelProperty(value = "证件号")
    @Encryption
    private String idNo;

    @ApiModelProperty(value = "联系电话")
    @Encryption
    private String telephone;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "旅客状态AC-值机，CL-订座取消，XR-直接取消")
    private String paxStatus;

    @ApiModelProperty(value = "主舱位")
    private String mainClass;

    @ApiModelProperty(value = "子舱位")
    private String subClass;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "购票时间")
    private String tktIssueDate;

    @ApiModelProperty(value = "PNR")
    private String pnr;
    /**
     * 旅客保障酒店信息
     */
    @ApiModelProperty(value = "服务天数")
    private String guaranteeDayCount;

    @ApiModelProperty(value = "房间号")
    private String roomNo;

    @ApiModelProperty(value = "房间类型（1单人间，2双人间）")
    private String roomType;

    @ApiModelProperty(value = "入住日期")
    private String checkInTime;

    @ApiModelProperty(value = "入住方式（0自动，1手动）")
    private String checkInMode;
}
