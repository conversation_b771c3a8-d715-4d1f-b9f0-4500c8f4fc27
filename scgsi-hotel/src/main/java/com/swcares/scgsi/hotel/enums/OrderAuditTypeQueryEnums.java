package com.swcares.scgsi.hotel.enums;

/**
 * @ClassName：OrderAuditTypeEnums
 * @Description：审核查询类型
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/12/14 9:49
 * @version： v1.0
 */
public enum OrderAuditTypeQueryEnums {
    ALL("0", "全部"),
    NOT_AUDIT("1", "待审核"),
    AUDIT("2", "已审核");

    private OrderAuditTypeQueryEnums(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static OrderAuditTypeQueryEnums build(String key) {
        return build(key, true);
    }

    public static OrderAuditTypeQueryEnums build(String key, boolean throwEx) {
        OrderAuditTypeQueryEnums typeEnum = null;
        for (OrderAuditTypeQueryEnums element : OrderAuditTypeQueryEnums.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + OrderAuditTypeQueryEnums.class.getSimpleName());
        }
        return typeEnum;
    }
}
