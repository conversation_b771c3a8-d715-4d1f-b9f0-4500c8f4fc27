package com.swcares.scgsi.hotel.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hotel.common.HotelConstant;
import com.swcares.scgsi.hotel.dao.HotelOrderInfoDao;
import com.swcares.scgsi.hotel.dao.HotelPaxInfoDao;
import com.swcares.scgsi.hotel.dao.impl.HotelOrderInfoDaoImpl;
import com.swcares.scgsi.hotel.enums.HotelCompensateStatusEnums;
import com.swcares.scgsi.hotel.enums.HotelWorkflowBusinessEnum;
import com.swcares.scgsi.hotel.model.dto.*;
import com.swcares.scgsi.hotel.model.entity.HotelOrderInfo;
import com.swcares.scgsi.hotel.model.entity.HotelPaxInfo;
import com.swcares.scgsi.hotel.model.vo.*;
import com.swcares.scgsi.hotel.service.*;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.workflow.model.dto.ActivityCompleteParamsBaseDTO;
import com.swcares.scgsi.workflow.model.dto.BaseQueryParamDTO;
import com.swcares.scgsi.workflow.model.vo.CurrentTaskActivityVO;
import com.swcares.scgsi.workflow.model.vo.HistoryTaskAuditBusiInfoVO;
import com.swcares.scgsi.workflow.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName：HotelCompensateServiceImpl
 * @Description：航延酒店保障单
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/23 10:21
 * @version： v1.0
 */
@Service
@Slf4j
public class HotelCompensateServiceImpl implements HotelCompensateService {


    @Resource
    HotelOrderInfoDaoImpl hotelOrderInfoDaoImpl;
    @Autowired
    HotelPaxInfoDao hotelPaxInfoDao;
    @Resource
    HotelOrderInfoService hotelOrderInfoService;
    @Autowired
    HotelWorkflowBaseService hotelWorkflowBaseService;
    @Autowired
    HotelOrderInfoDao hotelOrderInfoDao;
    @Autowired
    HotelAuditService hotelAuditService;
    @Autowired
    HotelAccommodationService hotelAccommodationService;
    @Autowired
    private RedissonClient redisson;

    @Autowired
    WorkflowService workflowService;

    private Map<String, List<String>> allowTargetStatusMp=new HashMap<>();

    @PostConstruct
    private void init() {
        //酒店保障单状态变更

        //审核中->审核不通过
        //审核中->驳回
        //审核中->通过
        //通过->发放
        //通过->关闭
        allowTargetStatusMp.put(HotelCompensateStatusEnums.AUDIT.getKey(), Lists.newArrayList(HotelCompensateStatusEnums.NO_PASS.getKey(),
                HotelCompensateStatusEnums.REJECT.getKey(),HotelCompensateStatusEnums.PASS.getKey()));
        allowTargetStatusMp.put(HotelCompensateStatusEnums.PASS.getKey(), Lists.newArrayList(HotelCompensateStatusEnums.EFFECT.getKey()
                ,HotelCompensateStatusEnums.CLOSE.getKey()));
    }

        @Override
    public QueryResults findCompensateFlightPage(HotelCompensateQueryDTO dto) {
        return hotelOrderInfoDaoImpl.findCompensateFlightPage(dto);
    }

    @Override
    public List<HotelCompensatePageVO> getOrderInfoByFlightInfo(String flightNo, String flightDate, String status) {
        return hotelOrderInfoDaoImpl.getOrderInfoByFlightInfo(flightNo,flightDate,status);
    }

    @Override
    public List<HotelOrderAuditRecordVO> getOrderAuditRecord(String orderId) throws Exception {
        return hotelAuditService.auditOperationRecord(orderId);
    }

    @Override
    public HotelCompensateDetailBaselVO getDetailOrderInfoByOrderId(String orderId){

        HotelCompensateDetailBaselVO detailBaselVO = new HotelCompensateDetailBaselVO();
        HotelCompensateDetailVO detailOrderInfo = hotelOrderInfoDaoImpl.getDetailOrderInfo(orderId);
        List<HotelPaxInfo> allByOrderId = hotelPaxInfoDao.findAllByOrderId(orderId);
        List<HotelOrderAuditRecordVO> hotelOrderAuditRecordVOS = null;
        try {
            hotelOrderAuditRecordVOS = hotelAuditService.auditOperationRecord(orderId);
        } catch (Exception e) {
            log.error("--【scgsi-hotel】->getDetailOrderInfoByOrderId---查询订单审核记录报错【可能因为草稿订单是流程实例不存在】，不影响查询逻辑。",e);
            e.printStackTrace();
        }

        detailBaselVO.setCompensateDetailVO(detailOrderInfo);
        detailBaselVO.setCompensatePaxVO(allByOrderId);
        detailBaselVO.setCompensateAuditRecordVO(hotelOrderAuditRecordVOS);
        return detailBaselVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOrderInfo(HotelOrderSaveParamDTO dto) throws Exception {
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String createUser =(String) authentication.getPrincipal();
        HotelOrderInfoDTO hotelOrderInfoDTO = dto.getHotelOrderInfoDTO();
        HotelOrderInfo dbOrderInfo = null;
        if(StringUtils.isNotEmpty(hotelOrderInfoDTO.getOrderId())){
            dbOrderInfo = hotelOrderInfoDao.findTById(hotelOrderInfoDTO.getOrderId());
        }
        //保存航延住宿保障单
        RLock lock = redisson.getLock(HotelConstant.LOCK_NAME+createUser);
        // 尝试加锁 返回boolean类型
        boolean resLock = false;
        try {
            resLock = lock.tryLock(HotelConstant.SAVE_REDIS_LOCK_TIME, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new BusinessException(MessageCode.HOTEL_SAVE_REDIS_LOCK_ERROR.getCode());
        }
        try {
            if (resLock) {
                hotelOrderInfoService.saveOrderInfo(dto);
            }else{
                log.error("---【scgsi-hotel】-----------save保存航延酒店补偿单，获取分布式锁失败：{}", JSONUtil.toJsonStr(dto));
                throw new BusinessException(MessageCode.HOTEL_SAVE_REDIS_LOCK_ERROR.getCode());
            }
        }finally {
            lock.unlock();
        }


        CurrentTaskActivityVO taskActivityVO = null;
        if(null !=dbOrderInfo){
            try {
                taskActivityVO = workflowService.currentUserTask(BaseQueryParamDTO.builder().businessKey(dbOrderInfo.getId()).build());
            }catch (Exception e){
                log.error("---【scgsi-hotel】-----------save保存航延酒店补偿单-查询流程实例结果：不存在流程实例--",e);
            }
        }

        //发起审核：当前状态为审核中 没有流程实例
        if(HotelCompensateStatusEnums.AUDIT.getKey().equals(hotelOrderInfoDTO.getStatus()) && null == taskActivityVO){
            hotelWorkflowBaseService.startAuditProcess(
                    WorkflowStartBaseDTO.builder()
                    .business(HotelWorkflowBusinessEnum.FD_HOTEL.getKey())
                    .businessKey(dto.getHotelOrderInfoDTO().getOrderId())
                    .businessObj(dto)
                    .build());
        }//重新提交：当前状态为审核中 有流程实例
        if(HotelCompensateStatusEnums.AUDIT.getKey().equals(hotelOrderInfoDTO.getStatus()) && null != taskActivityVO){
            hotelWorkflowBaseService.submitterAuditProcess(
                    WorkflowStartBaseDTO.builder()
                            .business(HotelWorkflowBusinessEnum.FD_HOTEL.getKey())
                            .businessKey(dto.getHotelOrderInfoDTO().getOrderId())
                            .businessObj(dto)
                            .build());


        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatus(String orderId, String status) {
        HotelOrderInfo tById = hotelOrderInfoDao.findTById(orderId);
        if(null == tById){
            return;
        }
        List<String> targetStatusList = allowTargetStatusMp.get(tById.getStatus());
        if(CollectionUtils.isEmpty(targetStatusList) || !targetStatusList.contains(status)){
            return;
        }

        hotelOrderInfoDaoImpl.updateOrderStatusByOrderIdAndStatus(orderId,status);
        if(HotelCompensateStatusEnums.EFFECT.getKey().equals(status)){
            // 发放保障时 保存酒店住宿单
            hotelAccommodationService.saveAccommodation(orderId);
        }
    }

    @Override
    public List<Map<String, Object>> findHotelListByServiceCity(String serviceCity) {
        return hotelOrderInfoDaoImpl.findHotelListByServiceCity(serviceCity);
    }

    @Override
    public void auditOperation(AuditCompleteParamsDTO dto){
        ActivityCompleteParamsBaseDTO baseDTO = BeanUtil.copyProperties(dto, ActivityCompleteParamsBaseDTO.class);
        baseDTO.setBusinessKey(dto.getOrderId());
        baseDTO.setBusiness(HotelWorkflowBusinessEnum.FD_HOTEL.getKey());
        hotelAuditService.auditOperation(baseDTO);
    }

    @Override
    public HotelOrderInfoDTO findOrderInfoById(String orderId) {
        HotelOrderInfoDTO hotelOrderInfoDTO = hotelOrderInfoDaoImpl.findOrderInfoById(orderId);
        List<HotelInfoVO> hotelIdList = hotelOrderInfoDaoImpl.findHotelIdByOrderId(orderId);
        StringBuffer hotelId = new StringBuffer();
        if(ObjectUtils.isNotEmpty(hotelIdList)){
            for (int i = 0; i < hotelIdList.size(); i++) {
                hotelId.append(hotelIdList.get(i).getId());
                if(i<hotelIdList.size()-1){
                    hotelId.append(",");
                }
            }
        }
        hotelOrderInfoDTO.setServiceHotels(hotelId.toString());
        return hotelOrderInfoDTO;
    }

    @Override
    public int expireOrderInfo() {
        return hotelOrderInfoDaoImpl.expireOrderInfo();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int completionOrder(String accommodationNo) {
        log.info("--【scgsi-web】--【航延酒店-酒店端结算数据-更新保障单状态】--请求参数：【{}】", accommodationNo);
        int i = hotelOrderInfoDaoImpl.completionOrder(accommodationNo);
        log.info("--【scgsi-web】--【航延酒店-酒店端结算数据-更新保障单状态】--请求参数：【{}】--处理保障完成订单共计：【{}】条", i);
        return i;
    }

    @Override
    public QueryResults findOrderAuditPage(HotelOrderAuditParamDTO dto) {
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String createUser = (String) authentication.getPrincipal();

        List<HistoryTaskAuditBusiInfoVO> historyTaskAuditBusiInfoVOS = workflowService.historyTaskAuditByAssignee(createUser);
        if (ObjectUtils.isNotEmpty(historyTaskAuditBusiInfoVOS)) {
            List<String> list = historyTaskAuditBusiInfoVOS.stream().map(HistoryTaskAuditBusiInfoVO::getBusiKey).distinct().collect(Collectors.toList());
            dto.setHasAuditOrderList(list);
        }

        return hotelOrderInfoDaoImpl.findOrderAuditPage(dto);
    }
}
