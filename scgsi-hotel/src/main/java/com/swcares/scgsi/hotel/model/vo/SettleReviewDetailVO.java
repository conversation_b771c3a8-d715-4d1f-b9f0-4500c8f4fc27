package com.swcares.scgsi.hotel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName：SettleReviewDetailVO
 * @Description： 结算审核详情对象
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/23 13:51
 * @version： v1.0
 */
@Data
@ApiModel(value = "SettleReviewDetailVO",description = "结算审核详情")
public class SettleReviewDetailVO {
    /**
     * 结算审核单信息
     */
    @ApiModelProperty("结算审核单id")
    private String id;

    @ApiModelProperty("酒店名")
    private String hotelName;

    @ApiModelProperty("公务舱/单人间 服务数量")
    private String orderServiceNumMainClass;

    @ApiModelProperty("经济舱/双人间 服务数量")
    private String orderServiceNum;

    @ApiModelProperty("公务舱/单人间 服务标准")
    private String orderServiceAmountMainClass;

    @ApiModelProperty("经济舱/双人间 服务标准")
    private String orderServiceAmount;

    @ApiModelProperty("预结算金额")
    private String preSumMoney;

    @ApiModelProperty("实际结算金额")
    private String realSumMoney;

    @ApiModelProperty("备注")
    private String noteInfo;

    @ApiModelProperty("实际服务人数")
    private Integer payPaxNumber;

    @ApiModelProperty("审核单状态")
    private String status;

    @ApiModelProperty("审核单号")
    private String auditNo;

    @ApiModelProperty("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date submitTime;

    @ApiModelProperty("结算类型  0间 1人")
    private String settlementType;
    /**
     * 服务保障单信息
     */
    @ApiModelProperty("保障单id")
    private String orderId;

    @ApiModelProperty("航班日期")
    private String flightDate;

    @ApiModelProperty("航班号")
    private String flightNo;

    @ApiModelProperty("服务航站")
    private String serviceCity;

    @ApiModelProperty("服务单号")
    private String orderNo;

    @ApiModelProperty("预计服务人数")
    private String serviceNum;

    @ApiModelProperty("保障单状态(0草稿1审核中2通过3待保障4保障中5未通过6驳回7保障完成8逾期9关闭)")
    private String orderStatus;

    @ApiModelProperty("服务类型：1航延住宿")
    private String payType;
    /**
     * 旅客信息
     */
    @ApiModelProperty("旅客列表")
    private List<ChoosePaxVO> paxList;
    /**
     * 审核记录
     */
    @ApiModelProperty("审核记录")
    private List<HotelOrderAuditRecordVO> auditList;

    @ApiModelProperty("当前任务id")
    private String taskId;

    @ApiModelProperty(value = "审核备注，用于记录驳回记录",hidden = true)
    @JsonIgnore
    private String remark;

    @ApiModelProperty(value = "结算单对应的酒店id",hidden = true)
    @JsonIgnore
    private String hotelId;


    @ApiModelProperty(value = "酒店住宿单号")
    private String accommodationNo;
}
