package com.swcares.scgsi.hotel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import com.swcares.scgsi.hotel.model.entity.HotelPaxInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 * @ClassName：HotelCompensatePaxVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/26 10:00
 * @version： v1.0
 */
@Data
@EncryptionClassz
public class HotelCompensatePaxVO {

    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "山航保障单id")
    private String orderId;

    @ApiModelProperty(value = "旅客id")
    private String paxId;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    private String idType;

    @ApiModelProperty(value = "证件号")
    @Encryption
    private String idNo;

    @ApiModelProperty(value = "性别C儿童M男F女")
    private String sex;

    @ApiModelProperty(value = "联系电话")
    @Encryption
    private String telephone;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "起始航站三字码")
    private String orgCityAirp;

    @ApiModelProperty(value = "到达航站三字码")
    private String dstCityAirp;

    @ApiModelProperty(value = "旅客状态AC-值机，CL-订座取消，XR-直接取消")
    private String paxStatus;

    @ApiModelProperty(value = "主舱位")
    private String mainClass;

    @ApiModelProperty(value = "子舱位")
    private String subClass;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "携带婴儿标识")
    private String withBaby;

    @ApiModelProperty(value = "儿童标识")
    private String isChild;

    @ApiModelProperty(value = "携带婴儿,婴儿名称")
    private String babyPaxName;

    @ApiModelProperty(value = "购票时间")
    private String tktIssueDate;

    @ApiModelProperty(value = "旅客资格开关(默认0有资格，1取消领取资格)")
    private String switchOff;

    @ApiModelProperty(value = "预服务天数")
    private String etdServiceDay;

    @ApiModelProperty(value = "PNR")
    private String pnr;

    @ApiModelProperty(value = "是否标记0未标记1标记")
    private String isFlag;

    @ApiModelProperty(value = "创建人")
    private String createId;

    @ApiModelProperty(value = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
}
