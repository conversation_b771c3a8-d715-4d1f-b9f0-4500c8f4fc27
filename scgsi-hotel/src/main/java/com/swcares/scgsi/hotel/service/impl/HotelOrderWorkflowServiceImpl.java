package com.swcares.scgsi.hotel.service.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.hotel.common.HotelConstant;
import com.swcares.scgsi.hotel.enums.HotelCompensateStatusEnums;
import com.swcares.scgsi.hotel.enums.HotelWorkflowBusinessEnum;
import com.swcares.scgsi.hotel.model.dto.HotelOrderSaveParamDTO;
import com.swcares.scgsi.hotel.model.dto.OrderSendMessgeResultVo;
import com.swcares.scgsi.hotel.service.HotelAuditService;
import com.swcares.scgsi.hotel.service.HotelCompensateService;
import com.swcares.scgsi.hotel.service.HotelOrderInfoService;
import com.swcares.scgsi.hotel.service.HotelOrderWorkflowService;
import com.swcares.scgsi.message.common.model.form.MessageSendForm;
import com.swcares.scgsi.message.service.MessageService;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.workflow.dao.WorkflowAuditorIdInfoDao;
import com.swcares.scgsi.workflow.enums.WorkflowStatusEnum;
import com.swcares.scgsi.workflow.model.dto.*;
import com.swcares.scgsi.workflow.model.entity.WorkflowAuditorIdInfoDO;
import com.swcares.scgsi.workflow.model.vo.CurrentTaskActivityVO;
import com.swcares.scgsi.workflow.service.WorkflowNodeDriveService;
import com.swcares.scgsi.workflow.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @ClassName：HotelOrderWorkflowServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/27 17:37
 * @version： v1.0
 */
@Slf4j
@Service
public class HotelOrderWorkflowServiceImpl implements HotelOrderWorkflowService {

    @Autowired
    WorkflowService workflowService;
    @Autowired
    WorkflowNodeDriveService workflowNodeDriveService;
    @Autowired
    WorkflowAuditorIdInfoDao workflowAuditorIdInfoDao;
    @Autowired
    HotelAuditService hotelAuditService;
    @Autowired
    HotelCompensateService hotelCompensateService;
    @Autowired
    HotelOrderInfoService hotelOrderInfoService;
    @Resource
    private MessageService messageService;

    @Override
    public Map<String, Object> submitterWorkflow(NodeNoticeDTO nodeNoticeDTO){
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String createUser =(String) authentication.getPrincipal();
        CurrentTaskActivityVO currentTaskActivityVO = workflowService.currentUserTask(BaseQueryParamDTO.builder().businessKey(nodeNoticeDTO.getBusiKey()).build());
        CompleteProcessParamsDTO processParamsDTO = new CompleteProcessParamsDTO();
        processParamsDTO.setBusinessKey(nodeNoticeDTO.getBusiKey());
        processParamsDTO.setComment(HotelConstant.SUBMITTER_COMMON);
        processParamsDTO.setOptionCode(WorkflowStatusEnum.AGREE.getKey());
        processParamsDTO.setTaskId(currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0).getTaskId());
        processParamsDTO.setUserId(createUser);
        CurrentTaskActivityVO taskActivityVO = workflowService.complementTask(processParamsDTO);
        workflowNodeDriveService.triggerNextNodeNotice(taskActivityVO,nodeNoticeDTO.getExtVars());

        return Collections.EMPTY_MAP;
    }

    @Override
    public Map<String, Object> commonWorkflow(NodeNoticeDTO nodeNoticeDTO) {

        workflowAuditorIdInfoDao.deleteByBusinessValue(nodeNoticeDTO.getBusiKey());
        CurrentTaskActivityVO currentTaskActivityVO = workflowService.currentUserTask(BaseQueryParamDTO.builder().businessKey(nodeNoticeDTO.getBusiKey()).build());
        /**查询审核用户集合**/
        CurrentTaskActivityDTO currentTaskActivityDTO = currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0);
        List<String> assignees = currentTaskActivityDTO.getAssignees();
        List<WorkflowAuditorIdInfoDO> auditorIdInfoDOList = new ArrayList<>();
        Set<String> auditorList = new HashSet<>();
        if(ObjectUtils.isNotEmpty(assignees.size())){
                auditorList = hotelAuditService.findAuditorList(assignees);
                for(String auditorId:auditorList){
                    WorkflowAuditorIdInfoDO auditorIdInfoDO = new WorkflowAuditorIdInfoDO();
                    auditorIdInfoDO.setAuditorId(auditorId);
                    auditorIdInfoDO.setTaskId(currentTaskActivityDTO.getTaskId());
                    auditorIdInfoDO.setBusinessValue(nodeNoticeDTO.getBusiKey());
                    auditorIdInfoDO.setBusiness(HotelWorkflowBusinessEnum.FD_HOTEL.getKey());
                    auditorIdInfoDOList.add(auditorIdInfoDO);
                }
            }
        workflowAuditorIdInfoDao.saveAll(auditorIdInfoDOList);
        //通知下一个审批节点
        sendMessageNext(nodeNoticeDTO,StringUtils.join(auditorList, ","));

        return Collections.EMPTY_MAP;
    }

    @Override
    public Map<String, Object> endWorkflow(NodeNoticeDTO nodeNoticeDTO){
        /**1.更新赔偿单状态**/
        this.updHotelCompensateStatus(nodeNoticeDTO);
        return Collections.EMPTY_MAP;
    }

    @Override
    public Object nodeBackHandler(NodeNoticeDTO nodeNoticeDTO) {
        List<WorkflowAuditorIdInfoDO> auditorIdInfoDOList = new ArrayList<>();
        List<String> assignees = nodeNoticeDTO.getAssignees();
        Set<String> auditorList = new HashSet<>();
        if(ObjectUtils.isNotEmpty(assignees.size())){
            auditorList = hotelAuditService.findAuditorList(assignees);
            for(String auditorId:auditorList){
                WorkflowAuditorIdInfoDO auditorIdInfoDO = new WorkflowAuditorIdInfoDO();
                auditorIdInfoDO.setAuditorId(auditorId);
                auditorIdInfoDO.setTaskId(nodeNoticeDTO.getTaskId());
                auditorIdInfoDO.setBusinessValue(nodeNoticeDTO.getBusiKey());
                auditorIdInfoDO.setBusiness(HotelWorkflowBusinessEnum.FD_HOTEL.getKey());
                auditorIdInfoDOList.add(auditorIdInfoDO);
            }
        }
        workflowAuditorIdInfoDao.saveAll(auditorIdInfoDOList);
        updHotelCompensateStatus(nodeNoticeDTO);
        return null;
    }



    public void updHotelCompensateStatus(NodeNoticeDTO nodeNoticeDTO) {
        boolean isUpd = false;//更新标识
        String status = "";
        switch (nodeNoticeDTO.getOptionCode()) {
            //同意
            case HotelConstant.AUDIT_AGREE:
                if (HotelConstant.NODE_END.equals(nodeNoticeDTO.getNodeKey())) {
                    //判断审核节点是否为最后一个节点
                    isUpd = true;
                    status = HotelCompensateStatusEnums.PASS.getKey();
                }
                break;
            //不同意-结束审核、更新赔偿单-不通过
            case HotelConstant.AUDIT_DISAGREE:
                isUpd = true;
                status = HotelCompensateStatusEnums.NO_PASS.getKey();
                break;
            //驳回
            case HotelConstant.AUDIT_REJECT:
                //判断审核节点是否驳回到发起人
                if (HotelConstant.NODE_KEY_SUBMITTER.equals(nodeNoticeDTO.getNodeKey())) {
                    isUpd = true;
                    status = HotelCompensateStatusEnums.REJECT.getKey();
                }
                break;
        }
        if (isUpd && StringUtils.isNotEmpty(status)) {
            hotelCompensateService.updateOrderStatus(nodeNoticeDTO.getBusiKey(), status);
            //发送审核结果消息给订单发起人
            sendMessageResult(nodeNoticeDTO);
        }
    }

    private void sendMessageResult(NodeNoticeDTO nodeNoticeDTO){
        log.info("【scgsi-hotel】-赔偿单向发起人发送审核结果-sendMessageResult ->orderId:{}，begin:{}", nodeNoticeDTO.getBusiKey(), JSONUtil.toJsonStr(nodeNoticeDTO));

        Object extVars1 = nodeNoticeDTO.getExtVars().getExtVars();
        if(extVars1 instanceof ActivityCompleteParamsBaseDTO){
            OrderSendMessgeResultVo resultVo = new OrderSendMessgeResultVo();
            ActivityCompleteParamsBaseDTO extVars = (ActivityCompleteParamsBaseDTO) extVars1;
            resultVo.setBusinessKey(extVars.getBusinessKey());
            resultVo.setOptionCode(extVars.getOptionCode());
            resultVo.setRemarks(extVars.getRemarks());
            MessageSendForm sendForm = hotelOrderInfoService.assembleResultSendMessage(resultVo);
            //向发起人
            try {
                messageService.sendMsg(sendForm);
            } catch (Exception e) {
                throw new BusinessException(MessageCode.AUDIT_MESSAGE_ERROR.getCode());
            }

        }
    }

    private void sendMessageNext(NodeNoticeDTO nodeNoticeDTO, String userId){
        log.info("【scgsi-hotel】-赔偿单向下一个节点用户发送审核通知-sendMessageNext ->审批用户:{}，begin:{}", userId, JSONUtil.toJsonStr(nodeNoticeDTO));

        if(StringUtils.isEmpty(userId)){
            return;
        }
        Object extVars1 = nodeNoticeDTO.getExtVars().getExtVars();

        if(extVars1 instanceof ActivityCompleteParamsBaseDTO){
            log.info("【scgsi-hotel】-赔偿单向下一个节点用户发送审核通知-sendMessageNext，ActivityCompleteParamsBaseDTO ->审批用户:{}，begin:{}", userId, JSONUtil.toJsonStr(nodeNoticeDTO));

            OrderSendMessgeResultVo resultVo = new OrderSendMessgeResultVo();
            ActivityCompleteParamsBaseDTO extVars = (ActivityCompleteParamsBaseDTO) extVars1;
            resultVo.setBusinessKey(nodeNoticeDTO.getBusiKey());
            resultVo.setOptionCode(extVars.getOptionCode());
            resultVo.setRemarks(extVars.getRemarks());
            MessageSendForm sendForm = hotelOrderInfoService.assembleSendMessage(resultVo,userId);
            try {
                messageService.sendMsg(sendForm);
            } catch (Exception e) {
                throw new BusinessException(MessageCode.AUDIT_MESSAGE_ERROR.getCode());
            }
        } else {
            OrderSendMessgeResultVo resultVo = new OrderSendMessgeResultVo();
            resultVo.setBusinessKey(nodeNoticeDTO.getBusiKey());
            MessageSendForm sendForm = hotelOrderInfoService.assembleSendMessage(resultVo, userId);
            try {
                messageService.sendMsg(sendForm);
            } catch (Exception e) {
                throw new BusinessException(MessageCode.AUDIT_MESSAGE_ERROR.getCode());
            }
        }
    }
}
