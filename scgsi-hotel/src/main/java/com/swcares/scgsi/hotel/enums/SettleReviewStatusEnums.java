package com.swcares.scgsi.hotel.enums;

/**
 * @ClassName：SettleReviewStatusEnums
 * @Description： 结算单审核状态
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/23 10:30
 * @version： v1.0
 */
public enum SettleReviewStatusEnums {
    DRAFT("0", "待核验"),
    AUDIT("1", "审核中"),
    PASS("3", "通过"),
    REJECT("2", "驳回")
    ;
    private SettleReviewStatusEnums(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static SettleReviewStatusEnums build(String key) {
        return build(key, true);
    }

    public static SettleReviewStatusEnums build(String key, boolean throwEx) {
        SettleReviewStatusEnums typeEnum = null;
        for (SettleReviewStatusEnums element : SettleReviewStatusEnums.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + SettleReviewStatusEnums.class.getSimpleName());
        }
        return typeEnum;
    }
}
