package com.swcares.scgsi.hotel.enums;

/**
 * @ClassName：SettleOrderStatusEnums
 * @Description： 结算单核验状态
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/23 10:30
 * @version： v1.0
 */
public enum SettleOrderStatusEnums {
    DRAFT("0", "待核验"),
    PASS("1", "通过"),
    REJECT("2", "退回")
    ;
    private SettleOrderStatusEnums(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static SettleOrderStatusEnums build(String key) {
        return build(key, true);
    }

    public static SettleOrderStatusEnums build(String key, boolean throwEx) {
        SettleOrderStatusEnums typeEnum = null;
        for (SettleOrderStatusEnums element : SettleOrderStatusEnums.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + SettleOrderStatusEnums.class.getSimpleName());
        }
        return typeEnum;
    }
}
