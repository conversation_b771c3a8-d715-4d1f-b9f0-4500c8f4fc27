package com.swcares.scgsi.hotel.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.component.workflow.FD.entity.HotelInfo <br>
 * Description：酒店信息表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-09-23 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "FD_HOTEL_INFO")
@ApiModel(value="HotelInfo对象", description="酒店信息表")
public class HotelInfo{

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @ApiModelProperty(value = "酒店名称")
    @Column(name ="HOTEL_NAME")
    private String hotelName;

    @ApiModelProperty(value = "联系人姓名")
    @Column(name ="CON_NAME")
    private String conName;

    @ApiModelProperty(value = "联系方式")
    @Column(name ="HOTEL_PHONE")
    private String hotelPhone;

    @ApiModelProperty(value = "邮箱")
    @Column(name ="HOTEL_EMAIL")
    private String hotelEmail;

    @ApiModelProperty(value = "地址")
    @Column(name ="HOTEL_ADDR")
    private String hotelAddr;

    @ApiModelProperty(value = "酒店状态（0正常，1锁定）")
    @Column(name ="HOTEL_STATUS")
    private String hotelStatus;

    @ApiModelProperty(value = "添加日期")
    @Column(name ="CREATE_DATE")
    private Date createDate;

    @ApiModelProperty(value = "修改日期")
    @Column(name ="MODIFY_DATE")
    private Date modifyDate;

    @ApiModelProperty(value = "添加人ID")
    @Column(name ="CREATE_ID")
    private String createId;

    @ApiModelProperty(value = "修改人ID")
    @Column(name ="MODIFY_ID")
    private String modifyId;

    @ApiModelProperty(value = "0  正常 1 被删除")
    @Column(name ="HOTEL_DELETED")
    private String hotelDeleted;

    @ApiModelProperty(value = "所在航站")
    @Column(name ="SERVICE_CITY")
    private String serviceCity;


}
