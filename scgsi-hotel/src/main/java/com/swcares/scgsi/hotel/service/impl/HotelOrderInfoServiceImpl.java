package com.swcares.scgsi.hotel.service.impl;

import com.alibaba.fastjson.JSON;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.aop.EncryptFieldAop;
import com.swcares.scgsi.common.model.form.FlightInfoListForm;
import com.swcares.scgsi.common.model.view.OriginalSegmentView;
import com.swcares.scgsi.hotel.common.HotelConstant;
import com.swcares.scgsi.hotel.dao.HotelFlightInfoDao;
import com.swcares.scgsi.hotel.dao.HotelMiddleInfoDao;
import com.swcares.scgsi.hotel.dao.HotelOrderInfoDao;
import com.swcares.scgsi.hotel.dao.HotelPaxInfoDao;
import com.swcares.scgsi.hotel.dao.impl.HotelOrderInfoDaoImpl;
import com.swcares.scgsi.hotel.enums.HotelCompensateStatusEnums;
import com.swcares.scgsi.hotel.model.dto.HotelOrderInfoDTO;
import com.swcares.scgsi.hotel.model.dto.HotelOrderSaveParamDTO;
import com.swcares.scgsi.hotel.model.dto.OrderSendMessgeResultVo;
import com.swcares.scgsi.hotel.model.entity.HotelFlightInfo;
import com.swcares.scgsi.hotel.model.entity.HotelMiddleInfo;
import com.swcares.scgsi.hotel.model.entity.HotelOrderInfo;
import com.swcares.scgsi.hotel.model.entity.HotelPaxInfo;
import com.swcares.scgsi.hotel.service.HotelOrderInfoService;
import com.swcares.scgsi.message.common.model.form.MessageSendForm;
import com.swcares.scgsi.message.common.msgenum.MessageTypeEnum;
import com.swcares.scgsi.service.FlightInfoService;
import com.swcares.scgsi.util.Asserts;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.util.DateUtils;
import com.swcares.scgsi.workflow.enums.WorkflowStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @ClassName：HotelOrderInfoServiceImpl
 * @Description：航延酒店赔偿单服务类
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/26 16:55
 * @version： v1.0
 */
@Service
@Slf4j
public class HotelOrderInfoServiceImpl implements HotelOrderInfoService {
    @Resource
    HotelOrderInfoDaoImpl hotelOrderInfoDaoImpl;
    @Resource
    HotelOrderInfoDao hotelOrderInfoDao;
    @Resource
    HotelMiddleInfoDao hotelMiddleInfoDao;
    @Resource
    HotelFlightInfoDao hotelFlightInfoDao;
    @Resource
    HotelPaxInfoDao hotelPaxInfoDao;
    @Autowired
    FlightInfoService flightInfoService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveOrderInfo(HotelOrderSaveParamDTO dto) {
        log.info("----【scgsi-hotel】----------航延酒店住宿保障单--save参数：【{}】", JSON.toJSONString(dto));
        HotelOrderInfoDTO orderInfoDTO = dto.getHotelOrderInfoDTO();
        List<HotelPaxInfo> paxInfoList = dto.getPaxInfoList();
        try {
            /**生成订单号 & 创建人 & 创建时间**/
            String orderNo = handleServiceOrderNo();
            Authentication authentication = AuthenticationUtil.getAuthentication();
            String createUser =(String) authentication.getPrincipal();
            Date createTime = new Date();
            HotelOrderInfo orderInfoDb = null;
            //保障单是否存在
            if(StringUtils.isNotEmpty(orderInfoDTO.getOrderId()) && StringUtils.isNotEmpty(orderInfoDTO.getOrderNo())){
                orderInfoDb = hotelOrderInfoDao.findTById(orderInfoDTO.getOrderId());
                orderNo = orderInfoDTO.getOrderNo();
                //删除保障单相关数据
                hotelOrderInfoDaoImpl.deleteOrderInfo(orderInfoDTO.getOrderId());
            }
            orderInfoDTO.setServiceNum(String.valueOf(paxInfoList.size()));
            /**保存保障单信息 & 返回orderId**/
            saveOrderInfo(createTime, createUser, orderNo, orderInfoDTO, orderInfoDb);
            /**保存保障单-旅客信息表**/
            savePaxInfo(createTime,createUser,orderInfoDTO,paxInfoList);
            /**保存保障单-酒店中间表**/
            saveHotelMiddleInfo(orderInfoDTO);
            /**保存保障单-航班信息表**/
            saveHotelFlightInfo(createTime,createUser,orderInfoDTO);
            return orderNo;
        }catch (Exception e){
            log.error("---【scgsi-hotel】-----------save保存航延酒店补偿单异常！！！",e);
            throw new BusinessException(MessageCode.HOTEL_SAVE_ERROR.getCode());
        }
    }



    public void savePaxInfo(Date createTime, String createUser,HotelOrderInfoDTO dto,List<HotelPaxInfo> paxInfoList) {
        //加密旅客信息证件号和电话号码
        EncryptFieldAop.manualEncrypt(paxInfoList);
        List<HotelPaxInfo> savePaxList = new ArrayList<>();
        for(HotelPaxInfo paxInfo : paxInfoList){
            paxInfo.setOrderId(dto.getOrderId());
            paxInfo.setCreateId(createUser);
            paxInfo.setCreateTime(createTime);
            savePaxList.add(paxInfo);
        }
        hotelPaxInfoDao.saveAll(savePaxList);
    }

    public void saveOrderInfo(Date createTime, String createUser, String orderNo, HotelOrderInfoDTO dto, HotelOrderInfo orderInfoDb) {

        HotelOrderInfo orderInfo = new HotelOrderInfo();
        BeanUtils.copyProperties(dto, orderInfo);
        orderInfo.setOrderNo(orderNo);
        orderInfo.setPayType(HotelConstant.PAY_TYPE_HOTEL);
        orderInfo.setId(dto.getOrderId());

        orderInfo.setLaunchUser(null);
        orderInfo.setLaunchTime(null);
        if (null != orderInfoDb) {
            orderInfo.setCreateId(orderInfoDb.getCreateId());
            orderInfo.setCreateTime(orderInfoDb.getCreateTime());
        } else {
            orderInfo.setCreateId(createUser);
            orderInfo.setCreateTime(createTime);
        }

        if (HotelCompensateStatusEnums.AUDIT.getKey().equals(dto.getStatus())) {
            orderInfo.setLaunchUser(createUser);
            orderInfo.setLaunchTime(createTime);
        }
        HotelOrderInfo orderInfoDB = hotelOrderInfoDao.save(orderInfo);
        //保存后设置order主键id
        dto.setOrderId(orderInfoDB.getId());
    }
    
    
    /** 
     * @title saveHotelMiddleInfo
     * @description 保存航延酒店保障单-服务酒店信息， 
     * <AUTHOR>
     * @date 2022/9/26 17:20
     * @return void
     */
    private void saveHotelMiddleInfo(HotelOrderInfoDTO orderInfoDTO){
        List<HotelMiddleInfo> hotelMiddleInfoList = new ArrayList<>();
        List<String> stringList = Arrays.asList(orderInfoDTO.getServiceHotels().split(","));
        for(String hotelId : stringList){
            HotelMiddleInfo hotelMiddleInfo = new HotelMiddleInfo();
            hotelMiddleInfo.setHotelId(hotelId);
            hotelMiddleInfo.setOrderId(orderInfoDTO.getOrderId());
            hotelMiddleInfoList.add(hotelMiddleInfo);
        }
        hotelMiddleInfoDao.saveAll(hotelMiddleInfoList);
    }

    private void saveHotelFlightInfo(Date createTime, String createUser,HotelOrderInfoDTO dto) {
        HotelFlightInfo flightInfo = new HotelFlightInfo();
        flightInfo.setOrderId(dto.getOrderId());
        flightInfo.setFlightNo(dto.getFlightNo());
        flightInfo.setFlightDate(dto.getFlightDate());
        FlightInfoListForm form1 = new FlightInfoListForm();
        form1.setFlightNum(dto.getFlightNo());
        form1.setFlightDate(dto.getFlightDate().replaceAll("-", "/"));
        OriginalSegmentView originalSegmentView = flightInfoService.getOriginalSegment(form1);
        flightInfo.setSegment(originalSegmentView.getSegment().replace(" ", ""));
        flightInfo.setCreateTime(createTime);
        flightInfo.setCreateId(createUser);
        flightInfo.setAcType(dto.getAcType());
        flightInfo.setFlightId(dto.getFlightId());
        flightInfo.setStd(dto.getStd());
        flightInfo.setEtd(dto.getEtd());
        flightInfo.setLateReason(dto.getLateReason());
        flightInfo.setPlaneCode(dto.getPlaneCode());
        hotelFlightInfoDao.save(flightInfo);
    }




    /***
     * @title handleServiceOrderNo
     * @description 服务保障单编号生成规则：年月日时分秒+4位随机码
     * <AUTHOR>
     * @date 2022/9/26 17:12

     * @return java.lang.String
     */
    private String handleServiceOrderNo() {
        // 精确时间到秒14位
        String orderId = DateUtils.parseCurrentDateToStr(DateUtils.YYYYMMDDHHMMSS);
        // 随机4位数
        int random5 = (int) ((Math.random() * 9 + 1) * 1000);
        orderId += String.valueOf(random5);
        return orderId;
    }



    /**
     * Title： assembleSendMessage<br>
     * Description： 组装消息内容 向发起人发送审核结果<br>
     * author：傅欣荣 <br>
     * date：2020/4/11 16:59 <br>
     * @param  sendMessage
     * @return
     */
    @Override
    public MessageSendForm assembleResultSendMessage(OrderSendMessgeResultVo sendMessage){
        HotelOrderInfo orderInfo = hotelOrderInfoDao.findTById(sendMessage.getBusinessKey());
        Asserts.notNull(orderInfo,MessageCode.HOTEL_ORDER_NOT_NULL.getCode());
        String[] userIds = orderInfo.getCreateId().split(",");
        StringBuffer content=new StringBuffer();
//        您有一条服务保障单被驳回，备注：XXXXX，请前往查看
//        您有一条服务保障单审核不通过，备注：XXXXX，请前往查看
//        您有一条服务保障单已审核通过，请前往进行后续处理

        content.append("<p>").append("您有一条服务保障单");
        if(WorkflowStatusEnum.AGREE.getKey().equals(sendMessage.getOptionCode())){
            content.append("已审核通过，请前往进行后续处理");
        }else if(WorkflowStatusEnum.REJECT.getKey().equals(sendMessage.getOptionCode())){
            content.append("被驳回");
        }else if(WorkflowStatusEnum.DISAGREE.getKey().equals(sendMessage.getOptionCode())){
            content.append("审核不通过");
        }
        if(!WorkflowStatusEnum.AGREE.getKey().equals(sendMessage.getOptionCode()) && StringUtils.isNotEmpty(sendMessage.getRemarks()) ){
            content.append("备注：").append(sendMessage.getRemarks()).append("，请前往查看");
        }
        content.append("</p>");

        MessageSendForm sendForm = getSendFormCommon(sendMessage.getBusinessKey(),orderInfo);
        sendForm.setMsgTitle("服务保障单审核提醒");
        sendForm.setMsgContent(content.toString());
        sendForm.setMsgUser("0");//系统发送
        sendForm.setFlightNo(orderInfo.getFlightNo());
        sendForm.setFlightDate(orderInfo.getFlightDate());
        sendForm.setMsgReplyUser(userIds);
        return sendForm;
    }


    /**
     * Title： assembleSendMessage<br>
     * Description： 组装消息内容 向审核人发起消息<br>
     * author：傅欣荣 <br>
     * date：2020/4/11 16:59 <br>
     * @param  sendMessage
     * @param  userId 收消息人
     * @return
     */
    @Override
    public MessageSendForm assembleSendMessage(OrderSendMessgeResultVo sendMessage, String userId){
        HotelOrderInfo orderInfo = hotelOrderInfoDao.findTById(sendMessage.getBusinessKey());
        Asserts.notNull(orderInfo,MessageCode.HOTEL_ORDER_NOT_NULL.getCode());
        String[] userIds = userId.split(",");
        StringBuffer content=new StringBuffer();
        content.append("<p>").append("您有一条服务保障单待审核，请前往处理").append("</p>");
        MessageSendForm sendForm = getSendFormCommon(sendMessage.getBusinessKey(),orderInfo);
        sendForm.setMsgTitle("服务保障单审核提醒");
        sendForm.setMsgContent(content.toString());
        sendForm.setMsgUser("0");//系统发送
        sendForm.setFlightNo(orderInfo.getFlightNo());
        sendForm.setFlightDate(orderInfo.getFlightDate());
        sendForm.setMsgReplyUser(userIds);

        return sendForm;
    }

    /**
     * Title： getSendFormCommon<br>
     * Description：公共发送消息实体 <br>
     * author：傅欣荣 <br>
     * date：2020/4/11 17:12 <br>
     * @param
     * @return
     */
    public MessageSendForm getSendFormCommon(String orderId,HotelOrderInfo orderInfo){
        MessageSendForm sendForm = new MessageSendForm();
        sendForm.setMsgType(MessageTypeEnum.HOTEL_ORDER_AUDIT.getType());
        sendForm.setMsgTypeName(MessageTypeEnum.HOTEL_ORDER_AUDIT.getTypeName());
        sendForm.setMsgChildType(MessageTypeEnum.HOTEL_ORDER_AUDIT.getChildType()+"");
        sendForm.setMsgChildTypeName(MessageTypeEnum.HOTEL_ORDER_AUDIT.getChildTypeName());
        sendForm.setPcUrl(MessageFormat.format(MessageTypeEnum.HOTEL_ORDER_AUDIT.getPcUrl(),orderId,orderInfo.getStatus()));
        sendForm.setMobileUrl(MessageFormat.format(MessageTypeEnum.HOTEL_ORDER_AUDIT.getMobileUrl(),orderId));
        sendForm.setIsAudit(MessageTypeEnum.HOTEL_ORDER_AUDIT.getIsAudit());
        sendForm.setMsgDate(new Date());
        return sendForm;
    }
}
