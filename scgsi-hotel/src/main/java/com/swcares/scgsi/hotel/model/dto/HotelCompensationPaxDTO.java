package com.swcares.scgsi.hotel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @ClassName：HotelCompensationPaxDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/10/10 15:09
 * @version： v1.0
 */
@Data
public class HotelCompensationPaxDTO {

    @ApiModelProperty(value = "赔偿旅客主键Id")
    private String id;

    @ApiModelProperty(value = "保障单Id")
    @NotNull
    private String orderId;

    @ApiModelProperty(value = "旅客姓名")
    @Size(max = 20,message = "姓名长度超出限制，最大20字符")
    private String paxName;

    @ApiModelProperty(value = "旅客所在航段")
    private List<String> segment;


}
