package com.swcares.scgsi.hotel.service.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.hotel.dao.HotelAccommodationDao;
import com.swcares.scgsi.hotel.dao.HotelOrderInfoDao;
import com.swcares.scgsi.hotel.dao.impl.HotelInfoDaoImpl;
import com.swcares.scgsi.hotel.enums.HotelAccommodationStatusEnums;
import com.swcares.scgsi.hotel.enums.SettleOrderStatusEnums;
import com.swcares.scgsi.hotel.enums.SettleReviewStatusEnums;
import com.swcares.scgsi.hotel.model.entity.HotelAccommodation;
import com.swcares.scgsi.hotel.model.entity.HotelOrderInfo;
import com.swcares.scgsi.hotel.model.vo.HotelInfoVO;
import com.swcares.scgsi.hotel.service.HotelAccommodationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * @ClassName：HotelAccommodationServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/11/8 11:38
 * @version： v1.0
 */
@Service
@Slf4j
public class HotelAccommodationServiceImpl implements HotelAccommodationService {
    @Autowired
    HotelAccommodationDao hotelAccommodationDao;
    @Autowired
    HotelOrderInfoDao hotelOrderInfoDao;
    @Autowired
    HotelInfoDaoImpl hotelInfoDaoImpl;
    @Autowired
    private RedissonClient redisson;

    private String LOCK_NAME="ACCOMMODATION_LOCK_";

    @Override
    public void saveAccommodation(String orderId) {
        log.info("【发放保障单-保存酒店住宿单】当前发放的保障单{}",orderId);
        HotelOrderInfo orderInfo = hotelOrderInfoDao.findTById(orderId);
        if(ObjectUtils.isEmpty(orderInfo)){
            log.error("【发放保障单-保存酒店住宿单】发放失败,当前保障单【{}】不存在！",orderId);
            throw new BusinessException(MessageCode.HOTEL_ORDER_NOT_NULL.getCode());
        }
        List<HotelInfoVO> list = hotelInfoDaoImpl.getHotelInfoByOrderId(orderId);
        log.info("【发放保障单-保存酒店住宿单】需要下发的酒店信息【{}】", JSONUtil.toJsonStr(list));
        RLock lock = redisson.getLock(LOCK_NAME+orderId);
        try {
            boolean resLock = lock.tryLock();
            if (resLock) {
                list.forEach(e -> {
                    Date date = new Date();
                    HotelAccommodation hotelAccommodation = new HotelAccommodation();
                    hotelAccommodation.setAccommodationNo(createSettleReviewNo());
                    hotelAccommodation.setOrderId(orderId);
                    hotelAccommodation.setCreateDate(date);
                    hotelAccommodation.setModifyDate(date);
                    //住宿单状态、核验状态、结算审核状态 全部初始化
                    hotelAccommodation.setAccommodationStatus(HotelAccommodationStatusEnums.DRAFT.getKey());
                    hotelAccommodation.setIsVerification(SettleOrderStatusEnums.DRAFT.getKey());
                    hotelAccommodation.setSettleStatus(SettleReviewStatusEnums.DRAFT.getKey());
                    hotelAccommodation.setSinglePrice(e.getSinglePrice());
                    hotelAccommodation.setDoublePrice(e.getDoublePrice());
                    hotelAccommodation.setHotelId(e.getId());
                    hotelAccommodation.setSettlementType(e.getSettlementType());
                    hotelAccommodationDao.save(hotelAccommodation);
                });
            }
        }catch(Exception e){
            log.error("【发放保障单-保存酒店住宿单】发放失败，住宿单保存出错！");
            throw new BusinessException(MessageCode.HOTEL_ACCOMMODATION_SAVE_ERROR.getCode());
        }finally {
            lock.unlock();
        }
    }

    /**
     * @title createSettleReviewNo
     * @description  生成单号，当前时间+随机三位数
     * <AUTHOR>
     * @date 2022/10/26 13:54
     * @return java.lang.String
     */
    private String createSettleReviewNo(){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        StringBuffer stringBuffer = new StringBuffer(LocalDateTime.now().format(formatter));
        stringBuffer.append(String.format("%03d", new Random().nextInt(999)));
        return stringBuffer.toString();
    }
}
