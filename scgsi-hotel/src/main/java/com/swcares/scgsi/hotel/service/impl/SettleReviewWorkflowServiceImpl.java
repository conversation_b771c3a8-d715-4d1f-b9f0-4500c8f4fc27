package com.swcares.scgsi.hotel.service.impl;

import com.swcares.scgsi.hotel.common.HotelConstant;
import com.swcares.scgsi.hotel.dao.impl.SettleReviewDaoImpl;
import com.swcares.scgsi.hotel.enums.HotelAccommodationStatusEnums;
import com.swcares.scgsi.hotel.enums.HotelWorkflowBusinessEnum;
import com.swcares.scgsi.hotel.enums.SettleOrderStatusEnums;
import com.swcares.scgsi.hotel.enums.SettleReviewStatusEnums;
import com.swcares.scgsi.hotel.model.dto.WorkflowStartBaseDTO;
import com.swcares.scgsi.hotel.model.vo.SettleReviewDetailVO;
import com.swcares.scgsi.hotel.model.vo.SettleReviewSubmitVO;
import com.swcares.scgsi.hotel.service.*;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.workflow.dao.WorkflowAuditorIdInfoDao;
import com.swcares.scgsi.workflow.enums.WorkflowStatusEnum;
import com.swcares.scgsi.workflow.model.dto.*;
import com.swcares.scgsi.workflow.model.entity.WorkflowAuditorIdInfoDO;
import com.swcares.scgsi.workflow.model.vo.CurrentTaskActivityVO;
import com.swcares.scgsi.workflow.service.WorkflowNodeDriveService;
import com.swcares.scgsi.workflow.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName： SettleReviewWorkflowServiceImpl
 * @Description： 结算审核具体审核流程流转实现类
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/27 17:37
 * @version： v1.0
 */
@Slf4j
@Service
public class SettleReviewWorkflowServiceImpl implements SettleReviewWorkflowService {

    @Autowired
    WorkflowService workflowService;
    @Autowired
    WorkflowNodeDriveService workflowNodeDriveService;
    @Autowired
    SettleReviewAuditService settleReviewAuditService;
    @Autowired
    WorkflowAuditorIdInfoDao workflowAuditorIdInfoDao;
    @Autowired
    SettleReviewDaoImpl settleReviewDao;
    @Autowired
    HotelAuditService hotelAuditService;



    @Override
    public Map<String, Object> submitterWorkflow(NodeNoticeDTO nodeNoticeDTO) {
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String createUser =(String) authentication.getPrincipal();
        CurrentTaskActivityVO currentTaskActivityVO = workflowService.currentUserTask(BaseQueryParamDTO.builder().businessKey(nodeNoticeDTO.getBusiKey()).build());
        CompleteProcessParamsDTO processParamsDTO = new CompleteProcessParamsDTO();
        processParamsDTO.setBusinessKey(nodeNoticeDTO.getBusiKey());
        processParamsDTO.setComment(HotelConstant.SUBMITTER_COMMON);
        processParamsDTO.setOptionCode(WorkflowStatusEnum.AGREE.getKey());
        processParamsDTO.setTaskId(currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0).getTaskId());
        processParamsDTO.setUserId(createUser);
        //设置第一节点审核人---只能是保障单的创建人
        WorkflowStartBaseDTO workflowStartBaseDTO =(WorkflowStartBaseDTO) nodeNoticeDTO.getExtVars().getExtVars();
        SettleReviewSubmitVO settleReviewSubmitInfo =(SettleReviewSubmitVO)workflowStartBaseDTO.getBusinessObj();
        List<String> reviewers = new ArrayList<>();
        reviewers.add(settleReviewSubmitInfo.getUserNo());
        processParamsDTO.setNextAssignee(reviewers);

        CurrentTaskActivityVO taskActivityVO = workflowService.complementTask(processParamsDTO);
        workflowNodeDriveService.triggerNextNodeNotice(taskActivityVO,nodeNoticeDTO.getExtVars());

//        hotelAuditService.saveTaskAuditor(HotelWorkflowBusinessEnum.FD_SETTLEMENT.getKey(),
//                settleReviewSubmitInfo.getId(),reviewers);
        //修改状态
        nodeNoticeDTO.setOptionCode(WorkflowStatusEnum.AGREE.getKey());
        nodeNoticeDTO.setNodeKey(HotelConstant.NODE_KEY_SUBMITTER);
        this.updSettleAndAccommodationStatus(nodeNoticeDTO);
        //修改提交时间
        settleReviewAuditService.updateSettleSubTime(new Date(),nodeNoticeDTO.getBusiKey());

        return Collections.EMPTY_MAP;
    }

    @Override
    public Map<String, Object> commonWorkflow(NodeNoticeDTO nodeNoticeDTO) {
        workflowAuditorIdInfoDao.deleteByBusinessValue(nodeNoticeDTO.getBusiKey());
        CurrentTaskActivityVO currentTaskActivityVO = workflowService.currentUserTask(BaseQueryParamDTO.builder().businessKey(nodeNoticeDTO.getBusiKey()).build());
        /**查询审核用户集合**/
        CurrentTaskActivityDTO currentTaskActivityDTO = currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0);
        List<String> assignees = currentTaskActivityDTO.getAssignees();
        List<WorkflowAuditorIdInfoDO> auditorIdInfoDOList = new ArrayList<>();
        Set<String> auditorList = new HashSet<>();
        if(ObjectUtils.isNotEmpty(assignees.size())){
            auditorList = hotelAuditService.findAuditorList(assignees);
            for(String auditorId:auditorList){
                WorkflowAuditorIdInfoDO auditorIdInfoDO = new WorkflowAuditorIdInfoDO();
                auditorIdInfoDO.setAuditorId(auditorId);
                auditorIdInfoDO.setTaskId(currentTaskActivityDTO.getTaskId());
                auditorIdInfoDO.setBusinessValue(nodeNoticeDTO.getBusiKey());
                auditorIdInfoDO.setBusiness(HotelWorkflowBusinessEnum.FD_SETTLEMENT.getKey());
                auditorIdInfoDOList.add(auditorIdInfoDO);
            }
        }
        workflowAuditorIdInfoDao.saveAll(auditorIdInfoDOList);
        //是否发送消息

        //返回审核人列表
       /* Map<String, Object> map = new HashMap<>();
        map.put("auditors",auditorList);
        map.put("business",HotelWorkflowBusinessEnum.FD_SETTLEMENT.getKey());
        map.put("businessKey",nodeNoticeDTO.getBusiKey());*/
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> endWorkflow(NodeNoticeDTO nodeNoticeDTO) {

        /**1.更新赔偿单状态**/
        this.updSettleAndAccommodationStatus(nodeNoticeDTO);
        /**2.发送消息**/
        return Collections.EMPTY_MAP;
    }

    public void updSettleAndAccommodationStatus(NodeNoticeDTO nodeNoticeDTO) {
        boolean isUpd = false;//更新标识
        String status = SettleOrderStatusEnums.PASS.getKey();//核验状态
        String accommodationStatus = HotelAccommodationStatusEnums.AUDIT.getKey();//住宿单状态
        String auditStatus = "";//审核状态
        switch (nodeNoticeDTO.getOptionCode()) {
            //同意
            case HotelConstant.AUDIT_AGREE:
                if (HotelConstant.NODE_END.equals(nodeNoticeDTO.getNodeKey())) {
                    //判断审核节点是否为最后一个节点
                    isUpd = true;
                    auditStatus = SettleReviewStatusEnums.PASS.getKey();
                    accommodationStatus = HotelAccommodationStatusEnums.PASS.getKey();
                }//判断是否是提交节点
                else if(HotelConstant.NODE_KEY_SUBMITTER.equals(nodeNoticeDTO.getNodeKey())){
                    isUpd = true;
                    auditStatus = SettleReviewStatusEnums.AUDIT.getKey();
                }
                break;
            //驳回
            case HotelConstant.AUDIT_REJECT:
                //判断审核节点是否驳回到发起人
                if (HotelConstant.NODE_KEY_SUBMITTER.equals(nodeNoticeDTO.getNodeKey())) {
                    isUpd = true;
                    auditStatus = SettleReviewStatusEnums.REJECT.getKey();
                    status = SettleOrderStatusEnums.REJECT.getKey();
                }
                break;
        }
        //获取审核单和住宿单的信息并修改状态
        SettleReviewSubmitVO settleReviewSubmitInfo = settleReviewDao.getSettleReviewSubmitInfo(nodeNoticeDTO.getBusiKey());
        if (isUpd && StringUtils.isNotEmpty(auditStatus) && ObjectUtils.isNotEmpty(settleReviewSubmitInfo)) {
            settleReviewAuditService.updateSettleReviewStatus(settleReviewSubmitInfo.getId(),auditStatus,status);
            settleReviewAuditService.updateSettleOrderStatus(settleReviewSubmitInfo.getAccommodationId(),auditStatus,status,accommodationStatus);
        }
    }


    @Override
    public Object nodeBackHandler(NodeNoticeDTO nodeNoticeDTO) {
        //保存驳回记录
        List<WorkflowAuditorIdInfoDO> auditorIdInfoDOList = new ArrayList<>();
        List<String> assignees = nodeNoticeDTO.getAssignees();
        Set<String> auditorList = new HashSet<>();
        if(ObjectUtils.isNotEmpty(assignees.size())){
            auditorList = hotelAuditService.findAuditorList(assignees);
            for(String auditorId:auditorList){
                WorkflowAuditorIdInfoDO auditorIdInfoDO = new WorkflowAuditorIdInfoDO();
                auditorIdInfoDO.setAuditorId(auditorId);
                auditorIdInfoDO.setTaskId(nodeNoticeDTO.getTaskId());
                auditorIdInfoDO.setBusinessValue(nodeNoticeDTO.getBusiKey());
                auditorIdInfoDO.setBusiness(HotelWorkflowBusinessEnum.FD_SETTLEMENT.getKey());
                auditorIdInfoDOList.add(auditorIdInfoDO);
            }
        }
        workflowAuditorIdInfoDao.saveAll(auditorIdInfoDOList);
        //修改结算单和住宿单状态
        updSettleAndAccommodationStatus(nodeNoticeDTO);
        //修改备注（每一次驳回都需要记录备注，作为驳回的历史记录）
        updSettleRemark(nodeNoticeDTO);
        return null;
    }

    /**
     * @title updSettleRemark
     * @description  修改结算单的备注
     * <AUTHOR>
     * @date 2022/10/19 10:20
     * @param nodeNoticeDTO
     * @return void
     */
    public void updSettleRemark(NodeNoticeDTO nodeNoticeDTO){
        ActivityCompleteParamsBaseDTO extVar =(ActivityCompleteParamsBaseDTO) nodeNoticeDTO.getExtVars().getExtVars();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SettleReviewDetailVO settleReviewInfo = settleReviewDao.getSettleReviewInfo(nodeNoticeDTO.getBusiKey());
        String re =ObjectUtils.isNotEmpty(settleReviewInfo.getRemark())? settleReviewInfo.getRemark():"";
        StringBuffer remark = new StringBuffer(re);
        remark.append(df.format(new Date()))
                .append("\t")
                .append(extVar.getRemarks())
                .append("\n");
        settleReviewAuditService.updateSettleRemark(remark.toString(),nodeNoticeDTO.getBusiKey());
    }

}
