package com.swcares.scgsi.hotel.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;
/**
 * ClassName：com.swcares.component.workflow.FD.entity.HotelOrderInfo <br>
 * Description：航延酒店保障单 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-09-23 <br>
 * @version v1.0 <br>
 */
@Entity
@Table(name = "FD_HOTEL_ORDER_INFO")
@Data
public class HotelOrderInfo{

    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @ApiModelProperty(value = "保障单号")
    @Column(name ="ORDER_NO")
    private String orderNo;

    @ApiModelProperty(value = "保障类型 1航延住宿")
    @Column(name ="PAY_TYPE")
    private String payType;

    @ApiModelProperty(value = "航班号")
    @Column(name ="FLIGHT_NO")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    @Column(name ="FLIGHT_DATE")
    private String flightDate;

    @ApiModelProperty(value = "服务航站")
    @Column(name ="SERVICE_CITY")
    private String serviceCity;

    @ApiModelProperty(value = "所选航段")
    @Column(name ="CHOICE_SEGMENT")
    private String choiceSegment;

    @ApiModelProperty(value = "备注")
    @Column(name ="REMARK")
    private String remark;

    @ApiModelProperty(value = "服务单状态(0草稿、1审核中、2通过、3待保障、4保障中 5未通过 6驳回 7保障完成 8逾期 9关闭")
    @Column(name ="STATUS")
    private String status;

    @ApiModelProperty(value = "航班ID")
    @Column(name ="FLIGHT_ID")
    private String flightId;

    @ApiModelProperty(value = "创建人")
    @Column(name ="CREATE_ID")
    private String createId;

    @ApiModelProperty(value = "创建日期")
    @Column(name ="CREATE_TIME")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    @Column(name ="UPDATE_USER")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    @Column(name ="UPDATE_TIME")
    private Date updateTime;

    @ApiModelProperty(value = "关闭人")
    @Column(name ="CLOSE_USER")
    private String closeUser;

    @ApiModelProperty(value = "关闭时间")
    @Column(name ="CLOSE_TIME")
    private Date closeTime;

    @ApiModelProperty(value = "发起人")
    @Column(name ="LAUNCH_USER")
    private String launchUser;

    @ApiModelProperty(value = "关闭时间")
    @Column(name ="LAUNCH_TIME")
    private Date launchTime;

    @ApiModelProperty(value = "预计保障天数")
    @Column(name ="ETD_SERVICE_DAYS")
    private String etdServiceDays;

    @ApiModelProperty(value = "预计保障人数")
    @Column(name ="SERVICE_NUM")
    private String serviceNum;

    @ApiModelProperty(value = "审核通过时间")
    @Column(name ="APPROVAL_TIME")
    private Date approvalTime;

}
