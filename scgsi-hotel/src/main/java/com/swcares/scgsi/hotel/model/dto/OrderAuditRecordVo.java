package com.swcares.scgsi.hotel.model.dto;

import lombok.Data;

import java.sql.Clob;
import java.sql.SQLException;

/**
 * ClassName：com.swcares.scgsi.audit.dto <br>
 * Description：服务单审核记录跟踪 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 03月11日 14:29 <br>
 * @version v1.0 <br>
 */
@Data
public class OrderAuditRecordVo {

    /**
     * 服务单id
     */
    private String orderId;

    /**
     *  审核状态 1同意0不同意 2驳回 3发起
     */
    private String opinion;

    /**
     *  审核备注
     */
    private String remark;

    /**
     * 用户姓名
     */
    private String auditor;

    /**
     *  用户职位
     */
    private String job;

    /**
     * 审核时间
     */
    private String auditTime;

    /**
     * 用户头像
     */
    private Clob photo;

    /**
     * Title： getPhoto <br>
     * Description： Clob类型转string <br>
     * author：傅欣荣 <br>
     * date：2020/7/1 13:19 <br>
     * @param
     * @return
     */
    public String getPhoto() {
        String value = null;
        try {
            if (photo != null) {
                value = photo.getSubString((long) 1, (int) photo.length());// 把clob数据转换为string
            }
        } catch (SQLException e) {
        }
        return value;
    }


}
