package com.swcares.scgsi.hotel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @ClassName：WorkflowStartBaseDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/10/11 14:49
 * @version： v1.0
 */
@Data
@Builder
public class WorkflowStartBaseDTO {
    @ApiModelProperty(value = "业务id")
    private String businessKey;

    @ApiModelProperty(value = "业务类型；比如apply")
    private String business;

    @ApiModelProperty(value = "业务对象")
    private Object businessObj;

}
