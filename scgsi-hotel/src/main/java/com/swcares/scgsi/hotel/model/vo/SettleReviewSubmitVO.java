package com.swcares.scgsi.hotel.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：SettleReviewSubmitVO
 * @Description： 结算审核详情对象
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/23 13:51
 * @version： v1.0
 */
@Data
@ApiModel(value = "SettleReviewDetailVO",description = "结算审核详情")
public class SettleReviewSubmitVO {

    @ApiModelProperty("结算审核单id")
    private String id;

    @ApiModelProperty("审核单状态")
    private String status;

    @ApiModelProperty("审核单号")
    private String auditNo;

    @ApiModelProperty("提交时间")
    private String submitTime;

    @ApiModelProperty("保障单id")
    private String orderId;

    @ApiModelProperty("保障单号")
    private String orderNo;

    @ApiModelProperty("保障单状态(0草稿1审核中2通过3待保障4保障中5未通过6驳回7保障完成8逾期9关闭)")
    private String orderStatus;

    @ApiModelProperty("保障单提交人工号")
    private String userNo;

    @ApiModelProperty("住宿单id")
    private String accommodationId;

    @ApiModelProperty("住宿单号")
    private String accommodationNo;

    @ApiModelProperty(value = "航班号",hidden = true)
    @JsonIgnore
    private String flightNo;

    @ApiModelProperty(value = "航班日期",hidden = true)
    @JsonIgnore
    private String flightDate;

    @ApiModelProperty(value = "核验状态",hidden = true)
    @JsonIgnore
    private String checkStatus;

    @ApiModelProperty(value = "核验备注",hidden = true)
    @JsonIgnore
    private String checkRemark;

}
