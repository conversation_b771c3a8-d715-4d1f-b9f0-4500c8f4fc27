package com.swcares.scgsi.hotel.model.dto;

import com.swcares.scgsi.hotel.model.entity.HotelPaxInfo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：HotelOrderSaveParamDTO
 * @Description：酒店赔偿单-保存参数
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/26 10:02
 * @version： v1.0
 */
@Data
@ApiModel(value = "HotelOrderSaveParamDTO",description = "保存赔偿单信息")
public class HotelOrderSaveParamDTO {

    //赔偿单&航班信息
    HotelOrderInfoDTO hotelOrderInfoDTO;

    //选择旅客
    List<HotelPaxInfo> paxInfoList;

}
