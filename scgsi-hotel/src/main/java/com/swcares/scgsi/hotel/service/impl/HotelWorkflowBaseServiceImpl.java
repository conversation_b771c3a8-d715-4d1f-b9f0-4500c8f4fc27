package com.swcares.scgsi.hotel.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.hotel.enums.HotelWorkflowBusinessEnum;
import com.swcares.scgsi.hotel.model.dto.ActStartProcessParamDTO;
import com.swcares.scgsi.hotel.model.dto.WorkflowStartBaseDTO;
import com.swcares.scgsi.hotel.service.HotelOrderWorkflowService;
import com.swcares.scgsi.hotel.service.HotelWorkflowBaseService;
import com.swcares.scgsi.hotel.service.SettleReviewWorkflowService;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.workflow.dao.WorkflowModelCodeInfoDao;
import com.swcares.scgsi.workflow.enums.WorkflowNodeBusiTypeEnum;
import com.swcares.scgsi.workflow.model.dto.AuditStartProcessDTO;
import com.swcares.scgsi.workflow.model.dto.NodeExtVarsDTO;
import com.swcares.scgsi.workflow.model.dto.NodeNoticeDTO;
import com.swcares.scgsi.workflow.model.entity.WorkflowModelCodeInfoDO;
import com.swcares.scgsi.workflow.model.vo.CurrentTaskActivityVO;
import com.swcares.scgsi.workflow.service.WorkflowNodeDriveService;
import com.swcares.scgsi.workflow.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * @ClassName：HotelOrderWorkflowServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/27 17:37
 * @version： v1.0
 */
@Slf4j
@Service
public class HotelWorkflowBaseServiceImpl implements HotelWorkflowBaseService {

    @Autowired
    WorkflowService workflowService;
    @Autowired
    WorkflowNodeDriveService workflowNodeDriveService;
    @Autowired
    WorkflowModelCodeInfoDao workflowModelCodeInfoDao;
    @Autowired
    HotelOrderWorkflowService hotelOrderWorkflowService;
    @Autowired
    SettleReviewWorkflowService settleReviewWorkflowService;
    @Autowired
    private RedissonClient redisson;

    private static final String HOTEL_NODE_REDISSON_PRE_KEY = "HOTEL_AUDIT_NODE_";
    private static final Long REDISSON_KEY_REDIS_LOCK_TIME = 100L;
    final String USER_ID_PF = "userId:";

    private Map<String, Function<NodeNoticeDTO, Object>> businessNodeMap = new HashMap<>();
    private Map<String, Function<NodeNoticeDTO, Object>> businessUpdStatusMap = new HashMap<>();

    @PostConstruct
    private void init() {
        businessNodeMap.put(HotelWorkflowBusinessEnum.FD_HOTEL.getKey() + WorkflowNodeBusiTypeEnum.SUBMITTER.getType()
                , nodeNoticeDTO -> hotelOrderWorkflowService.submitterWorkflow(nodeNoticeDTO));
        businessNodeMap.put(HotelWorkflowBusinessEnum.FD_HOTEL.getKey() + WorkflowNodeBusiTypeEnum.COMMON.getType()
                , nodeNoticeDTO -> hotelOrderWorkflowService.commonWorkflow(nodeNoticeDTO));
        businessNodeMap.put(HotelWorkflowBusinessEnum.FD_HOTEL.getKey() + WorkflowNodeBusiTypeEnum.END.getType()
                , nodeNoticeDTO -> hotelOrderWorkflowService.endWorkflow(nodeNoticeDTO));

        businessNodeMap.put(HotelWorkflowBusinessEnum.FD_SETTLEMENT.getKey() + WorkflowNodeBusiTypeEnum.SUBMITTER.getType()
                , nodeNoticeDTO -> settleReviewWorkflowService.submitterWorkflow(nodeNoticeDTO));
        businessNodeMap.put(HotelWorkflowBusinessEnum.FD_SETTLEMENT.getKey() + WorkflowNodeBusiTypeEnum.COMMON.getType()
                , nodeNoticeDTO -> settleReviewWorkflowService.commonWorkflow(nodeNoticeDTO));
        businessNodeMap.put(HotelWorkflowBusinessEnum.FD_SETTLEMENT.getKey() + WorkflowNodeBusiTypeEnum.END.getType()
                , nodeNoticeDTO -> settleReviewWorkflowService.endWorkflow(nodeNoticeDTO));


        businessUpdStatusMap.put(HotelWorkflowBusinessEnum.FD_HOTEL.getKey()
                , nodeNoticeDTO -> hotelOrderWorkflowService.nodeBackHandler(nodeNoticeDTO));
        businessUpdStatusMap.put(HotelWorkflowBusinessEnum.FD_SETTLEMENT.getKey()
                , nodeNoticeDTO -> settleReviewWorkflowService.nodeBackHandler(nodeNoticeDTO));

    }
    @Override
    public Map<String, Object> startAuditProcess(WorkflowStartBaseDTO dto){
        RLock lock = redisson.getLock(HOTEL_NODE_REDISSON_PRE_KEY + dto.getBusinessKey());
        try {
            boolean resLock = lock.tryLock();
            if (resLock) {
                log.info("【scgsi-hotel】-赔偿单工作流-start节点，begin:{}", JSONUtil.toJsonStr(dto));

                Authentication authentication = AuthenticationUtil.getAuthentication();
                String createUser =(String) authentication.getPrincipal();
                //根据业务查询processKey
                WorkflowModelCodeInfoDO modelCodeInfoDO= workflowModelCodeInfoDao.findByBusiness(dto.getBusiness());
                ActStartProcessParamDTO paramDTO = new ActStartProcessParamDTO();
                paramDTO.setAssignee(USER_ID_PF+createUser);
                Map<String, Object> businessData = BeanUtil.beanToMap(paramDTO);
                AuditStartProcessDTO processDTO =new AuditStartProcessDTO();
                processDTO.setAssignee(createUser);
                processDTO.setBusinessKey(dto.getBusinessKey());
                processDTO.setProcessKey(modelCodeInfoDO.getModelCode());
                processDTO.setBusinessData(businessData);
                workflowService.startProcessResult(processDTO);
                return this.submitterAuditProcess(dto);
            } else {
                log.error("【scgsi-hotel】-赔偿单工作流-start节点，获取分布式锁失败：{}", JSONUtil.toJsonStr(dto));
                throw new BusinessException(MessageCode.HOTEL_RLOCK_ERROR.getCode());
            }
        }  finally {
            lock.unlock();
        }

    }

    @Override
    public Map<String, Object> submitterAuditProcess(WorkflowStartBaseDTO dto) {
        NodeExtVarsDTO nodeExtVarsDTO = workflowNodeDriveService.createNodeExtVarsDTO(dto.getBusiness());
        CurrentTaskActivityVO nextNodeTaskVo = workflowNodeDriveService.getNextNodeTaskVo(dto.getBusinessKey());
        return workflowNodeDriveService.triggerNextNodeNotice(nextNodeTaskVo,nodeExtVarsDTO,dto);
    }

    @Override
    public boolean isExist(String business) {
        return businessNodeMap.containsKey(business);
    }


    @Override
    public Map<String, Object> nodeHandler(NodeNoticeDTO nodeNoticeDTO) {
        RLock lock = redisson.getLock(HOTEL_NODE_REDISSON_PRE_KEY + nodeNoticeDTO.getBusiKey());
        try {
            boolean resLock = lock.tryLock();
            if (resLock) {

                log.info("【scgsi-hotel】-赔偿单工作流-{}节点，begin:{}", nodeNoticeDTO.getNodeKey(),JSONUtil.toJsonStr(nodeNoticeDTO));
                Function<NodeNoticeDTO, Object> function = businessNodeMap.get(nodeNoticeDTO.getExtVars().getBusiness()+nodeNoticeDTO.getNodeBusinessType());
                if(function!=null){
                    return (Map<String, Object>) function.apply(nodeNoticeDTO);
                }

            } else {
                log.error("【scgsi-hotel】-赔偿单工作流-{}节点，获取分布式锁失败：{}", nodeNoticeDTO.getNodeKey(), JSONUtil.toJsonStr(nodeNoticeDTO));
                throw new BusinessException(MessageCode.HOTEL_RLOCK_ERROR.getCode());
            }
        } finally {
            lock.unlock();
        }
        return Collections.EMPTY_MAP;
    }


    @Override
    public Object nodeBackHandler(NodeNoticeDTO nodeNoticeDTO) {
        Function<NodeNoticeDTO, Object> function = businessUpdStatusMap.get(nodeNoticeDTO.getExtVars().getBusiness());
        if(function!=null){
            return function.apply(nodeNoticeDTO);
        }
        return null;
    }
}
