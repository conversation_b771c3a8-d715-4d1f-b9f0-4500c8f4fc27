package com.swcares.scgsi.hotel.model.dto;

import com.swcares.scgsi.base.Pager;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @ClassName：SettleReviewListDTO
 * @Description： 结算审核列表查询参数
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/23 11:52
 * @version： v1.0
 */
@Data
@ApiModel(value = "SettleReviewListDTO",description = "结算审核列表查询参数")
public class SettleReviewListDTO extends Pager {

    @ApiModelProperty("服务酒店，传id")
    private List<String> hotels;

    @ApiModelProperty("航班日期-起始时间")
    @NotNull(message = "航班日期不可为空")
    private String flightStartDate;

    @ApiModelProperty("航班日期-结束时间")
    @NotNull(message = "航班日期不可为空")
    private String flightEndDate;

    @ApiModelProperty("航班号")
    private String flightNo;

    @ApiModelProperty("服务航站")
    private List<String> serviceCity;

    @ApiModelProperty("审核单状态 (0待核验1审核中2驳回3通过4不通过)")
    private List<String> status;

    @ApiModelProperty("提交时间-起始")
    @NotNull(message = "提交时间不可为空")
    private String submitStartTime;

    @ApiModelProperty("提交时间-结束")
    @NotNull(message = "提交时间不可为空")
    private String submitEndTime;

    @ApiModelProperty("服务单号")
    @Size(max = 50,message = "服务单号超出限制，最大50字符")
    private String orderNo;

    @ApiModelProperty("核验状态 (0待核验1通过2退回)")
    private String checkStatus;
}
