package com.swcares.scgsi.hotel.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * ClassName：com.swcares.component.workflow.FD.entity.HotelUserInfo <br>
 * Description：酒店端用户信息表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-09-23 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "FD_HOTEL_USER_INFO")
@ApiModel(value="HotelUserInfo对象", description="酒店端用户信息表")
public class HotelUserInfo{

    private static final long serialVersionUID = 1L;
    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;
    
    @ApiModelProperty(value = "酒店ID")
    @Column(name ="HOTEL_ID")
    private String hotelId;

    @ApiModelProperty(value = "系统登录账号")
    @Column(name ="ACCOUNT")
    private String account;

    @ApiModelProperty(value = "密码")
    @Column(name ="PASSWORD")
    private String password;

    @ApiModelProperty(value = "用户类型（0管理员 1酒店人员）")
    @Column(name ="USER_TYPE")
    private String userType;

    @ApiModelProperty(value = "账号状态（0锁定，1解锁）")
    @Column(name ="USER_STATUS")
    private String userStatus;

    @ApiModelProperty(value = "添加日期")
    @Column(name ="CREATE_DATE")
    private Date createDate;

    @ApiModelProperty(value = "最近修改日期")
    @Column(name ="MODIFY_DATE")
    private Date modifyDate;

    @ApiModelProperty(value = "添加人ID")
    @Column(name ="CREATE_ID")
    private String createId;

    @ApiModelProperty(value = "修改人ID")
    @Column(name ="MODIFY_ID")
    private String modifyId;

    @ApiModelProperty(value = "名字")
    @Column(name ="USER_NAME")
    private String userName;

    @ApiModelProperty(value = "0 正常 1删除")
    @Column(name ="USER_DELETED")
    private String userDeleted;

    @ApiModelProperty(value = "最近一次登录时间")
    @Column(name ="LAST_LOGIN_DATE")
    private Date lastLoginDate;

    @ApiModelProperty(value = "状态修改原因")
    @Column(name ="STATEREASON")
    private String statereason;


}
