package com.swcares.scgsi.hotel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName：SettleReviewListVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/23 10:40
 * @version： v1.0
 */
@Data
@ApiModel(value = "SettleReviewListVO",description = "结算审核列表")
public class SettleReviewListVO {

    @ApiModelProperty("结算审核单id")
    private String id;

    @ApiModelProperty("酒店名")
    private String hotelName;

    @ApiModelProperty("航班日期")
    private String flightDate;

    @ApiModelProperty("航班号")
    private String flightNo;

    @ApiModelProperty("服务航站")
    private String serviceCity;

    @ApiModelProperty("服务类型：1酒店")
    private String payType;

    @ApiModelProperty("预结算金额")
    private String preSumMoney;

    @ApiModelProperty("实际结算金额")
    private String realSumMoney;

    @ApiModelProperty("说明")
    private String noteInfo;

    @ApiModelProperty("实际服务人数")
    private Integer payPaxNumber;

    @ApiModelProperty("公务舱服务标准")
    private String orderServiceAmountMainClass;

    @ApiModelProperty("经济舱服务标准")
    private String orderServiceAmount;

    @ApiModelProperty("标准类型（  0间 1人）")
    private String settlementType;

    @ApiModelProperty("审核单状态")
    private String status;

    @ApiModelProperty("审核单号")
    private String auditNo;

    @ApiModelProperty("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date submitTime;

    @ApiModelProperty("服务单号")
    private String orderNo;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty("核验状态")
    private String checkStatus;

    @ApiModelProperty("是否是保障单创建人（Y:是，N:不是）")
    private String createdBy;

    @ApiModelProperty(value = "酒店住宿单号")
    private String accommodationNo;

}
