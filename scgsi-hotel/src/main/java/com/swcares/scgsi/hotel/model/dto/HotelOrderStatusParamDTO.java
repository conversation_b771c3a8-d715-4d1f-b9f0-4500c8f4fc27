package com.swcares.scgsi.hotel.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName：HotelOrderStatusParamDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/10/18 9:36
 * @version： v1.0
 */
@Data
@ApiModel(value = "HotelOrderStatusParamDTO",description = "")
public class HotelOrderStatusParamDTO {

    @ApiModelProperty(value = "服务单id")
    @NotNull
    private String orderId;

    @ApiModelProperty(value = "服务单状态(0草稿、1审核中、2通过、3待保障、4保障中 5未通过 6驳回 7保障完成 8逾期 9关闭)")
    @NotNull
    private String status;
}
