package com.swcares.scgsi.hotel.service;

import com.swcares.scgsi.hotel.model.dto.WorkflowStartBaseDTO;
import com.swcares.scgsi.workflow.model.dto.NodeNoticeDTO;
import com.swcares.scgsi.workflow.service.WorkflowNodeService;

import java.util.Map;

/**
 * @ClassName：HotelWorkflowService
 * @Description：审核父类
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/27 17:49
 * @version： v1.0
 */
public interface HotelWorkflowBaseService{


    Map<String,Object> nodeHandler(NodeNoticeDTO nodeNoticeDTO);

    Map<String,Object> startAuditProcess(WorkflowStartBaseDTO dto);

    Map<String, Object> submitterAuditProcess(WorkflowStartBaseDTO dto);

    boolean isExist(String business);

    Object nodeBackHandler(NodeNoticeDTO nodeNoticeDTO);

}
