package com.swcares.scgsi.hotel.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hotel.enums.SettleReviewStatusEnums;
import com.swcares.scgsi.hotel.model.dto.PaxAccommodationReportDTO;
import com.swcares.scgsi.hotel.model.dto.SettleReviewListDTO;
import com.swcares.scgsi.hotel.model.vo.*;
import com.swcares.scgsi.util.AesEncryptUtil;
import com.swcares.scgsi.util.AuthenticationUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

/**
 * @ClassName：SettleReviewDaoImpl
 * @Description： 结算审核单dao层
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/26 9:59
 * @version： v1.0
 */
@Repository
public class SettleReviewDaoImpl {

    @Resource
    private BaseDAO baseDAO;

    public QueryResults getSettleReviewPage(SettleReviewListDTO dto){
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser =(String) authentication.getPrincipal();
        StringBuffer sql = getSettleReviewPageSql();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("flightStartDate",dto.getFlightStartDate());
        paramsMap.put("flightEndDate",dto.getFlightEndDate());
        paramsMap.put("submitStartTime",dto.getSubmitStartTime());
        paramsMap.put("submitEndTime",dto.getSubmitEndTime());
        paramsMap.put("currentUser",currentUser);
        if(ObjectUtils.isNotEmpty(dto.getFlightNo())){
            sql.append("and fhfi.FLIGHT_NO = :flightNo ");
            paramsMap.put("flightNo",dto.getFlightNo());
        }
        if(ObjectUtils.isNotEmpty(dto.getHotels())){
            sql.append("and fhsi.HOTEL_ID in (:hotels) ");
            paramsMap.put("hotels",dto.getHotels());
        }
        if(ObjectUtils.isNotEmpty(dto.getStatus())){
            sql.append("and fhsi.STATUS in (:status) ");
            paramsMap.put("status",dto.getStatus());
        }
        if(ObjectUtils.isNotEmpty(dto.getCheckStatus())){
            sql.append("and fhsi.IS_AUTH_FLAG = :checkStatus ");
            paramsMap.put("checkStatus",dto.getCheckStatus());
        }
        if(ObjectUtils.isNotEmpty(dto.getServiceCity())){
            sql.append("and fhoi.SERVICE_CITY in (:serviceCity) ");
            paramsMap.put("serviceCity",dto.getServiceCity());
        }
        if(ObjectUtils.isNotEmpty(dto.getOrderNo())){
            sql.append("and fhoi.ORDER_NO = :orderNo ");
            paramsMap.put("orderNo",dto.getOrderNo());
        }
        sql.append(" order by fhsi.STATUS asc ,fhsi.UPDATE_TIME desc");
        return baseDAO.findBySQLPage_comm(sql.toString(),dto.getCurrent(),dto.getPageSize(),paramsMap, SettleReviewListVO.class);
    }

    public SettleReviewDetailVO getSettleReviewInfo(String settleId) {
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser =(String) authentication.getPrincipal();
        StringBuffer sql = getSettleReviewInfoSql();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("id",settleId);
        paramsMap.put("currentUser",currentUser);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,SettleReviewDetailVO.class);
    }

    public List<ChoosePaxVO> getChoosePaxList(String orderId,String hotelId){
        StringBuffer sql = getChoosePaxListSql();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("id",orderId);
        paramsMap.put("hotelId",hotelId);
        return (List<ChoosePaxVO>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,ChoosePaxVO.class);
    }

    public CheckInfoVO getCheckInfo(String auditNo) {
        StringBuffer sql = getCheckInfoSql();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("auditNo",auditNo);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,CheckInfoVO.class);
    }
    public List<ReviewCheckInfoVO> getReviewCheckInfo(String reviewNo) {
        StringBuffer sql = getReviewCheckInfoSql();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("reviewNo",reviewNo);
        return (List<ReviewCheckInfoVO> )baseDAO.findBySQL_comm(sql.toString(),paramsMap,ReviewCheckInfoVO.class);
    }

    public SettleReviewSubmitVO getSettleReviewSubmitInfo(String auditNo) {
        StringBuffer sql = getSettleReviewSubmitInfoSql();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("auditNo",auditNo);
        return baseDAO.findOneBySql(sql.toString(),paramsMap,SettleReviewSubmitVO.class);
    }

    public void updateSettleReviewStatus(String settleId,String auditStatus,String status){
        StringBuffer sql = updateSettleReviewStatusSql(auditStatus);
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("id",settleId);
        paramsMap.put("auditStatus",auditStatus);
        paramsMap.put("status",status);
        if (SettleReviewStatusEnums.PASS.getKey().equals(auditStatus)) {
            paramsMap.put("auditPassTime", new Date());
        }
        baseDAO.batchUpdate(sql.toString(),paramsMap);
    }

    public void updateSettleRemark(String remark, String settleId){
        StringBuffer sql = updateSettleRemarkSql();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("id",settleId);
        paramsMap.put("remark",remark);
        baseDAO.batchUpdate(sql.toString(),paramsMap);
    }
    public void updateSettleCheckRemark( String settleId,String remark){
        StringBuffer sql = updateSettleCheckRemarkSql();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("id",settleId);
        paramsMap.put("remark",remark);
        baseDAO.batchUpdate(sql.toString(),paramsMap);
    }

    public void updateSettleSubTime(Date subTime, String settleId){
        StringBuffer sql = updateSettleSubTimeSql();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("id",settleId);
        paramsMap.put("time",subTime);
        baseDAO.batchUpdate(sql.toString(),paramsMap);
    }

    public List<AuditorsInfoVO> getAuditors(Set<String> auditorIds,String userInfo){
        StringBuffer sql = getAuditorsSql(userInfo);
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("ids",auditorIds);
        if(ObjectUtils.isNotEmpty(userInfo)) {
            sql.append("and e.TU_CNAME LIKE concat(concat('%',:userInfo),'%') ");
//            sql.append("OR e.TUNO LIKE concat(concat('%',:userInfo),'%') )");
            paramsMap.put("userInfo",userInfo);
        }
        return (List<AuditorsInfoVO>)baseDAO.findBySQL_comm(sql.toString(),paramsMap,AuditorsInfoVO.class);
    }




    private StringBuffer getAuditorsSql(String userInfo){
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT e.TU_CNAME AS userInfo,e.TU_MOBILE AS phone,e.TUNO as userNo " );
        sql.append("FROM EMPLOYEE e " );
        sql.append("WHERE e.TUNO in ( :ids ) " );
        return sql;
    }

    private StringBuffer getSettleReviewPageSql(){
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT fhsi.ID, fhi.HOTEL_NAME hotelName, fhfi.FLIGHT_NO flightNo, fhfi.FLIGHT_DATE flightDate,fhsi.IS_AUTH_FLAG checkStatus,");
        sql.append("fhoi.SERVICE_CITY serviceCity, fhoi.PAY_TYPE payType, fha.PRE_SUM_MONEY preSumMoney,fha.REAL_SUM_MONEY realSumMoney,");
        sql.append("(SELECT COUNT(*) FROM FD_HOTEL_PSSN_INFO f WHERE f.ORDER_ID = fhsi.ORDER_ID AND f.HOTEL_ID = fhsi.HOTEL_ID ) AS payPaxNumber,");
        sql.append("fha.SINGLE_PRICE orderServiceAmountMainClass, fha.DOUBLE_PRICE orderServiceAmount, fha.SETTLEMENT_TYPE settlementType,");
        sql.append("fhsi.STATUS , fhsi.AUDIT_NO auditNo, fhsi.UPDATE_TIME submitTime, fhoi.ORDER_NO orderNo, fha.NOTE_INFO noteInfo,");
        sql.append("(SELECT  task_id from WORKFLOW_AUDITOR_ID_INFO where BUSINESS_VALUE = fhsi.id and AUDITOR_ID =:currentUser) taskId, ");
        sql.append("fhsi.ACCOMMODATION_NO accommodationNo, ");
        sql.append("case when fhoi.CREATE_ID = :currentUser THEN 'Y' ELSE 'N' END AS createdBy ");
        sql.append("FROM FD_HOTEL_SETTLEMENT_INFO fhsi ");
        sql.append("LEFT JOIN FD_HOTEL_INFO fhi ON fhsi.HOTEL_ID = fhi.ID ");
        sql.append("LEFT JOIN FD_HOTEL_ORDER_INFO fhoi ON fhsi.ORDER_ID = fhoi.ID ");
        sql.append("LEFT JOIN FD_HOTEL_FLIGHT_INFO fhfi ON fhoi.ID = fhfi.ORDER_ID ");
        sql.append("LEFT JOIN FD_HOTEL_ACCOMMODATION fha ON fhi.id = fha.HOTEL_ID AND fhoi.id = fha.ORDER_ID ");
        sql.append("WHERE (fhfi.FLIGHT_DATE BETWEEN :flightStartDate AND  :flightEndDate) ");
        sql.append("and (TO_CHAR(fhsi.UPDATE_TIME,'yyyy-MM-dd') BETWEEN :submitStartTime AND :submitEndTime) ");
//        sql.append("and fhoi.CREATE_ID = :currentUser ");
        return sql;
    }

    private StringBuffer getChoosePaxListSql(){
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT fhpi.ORDER_ID orderId,fhpi.PAX_ID paxId,fhpi.PAX_NAME paxName,fhpi.ID_TYPE idType,");
        sql.append("fhpi.ID_NO idNo,fhpi.TELEPHONE ,fhpi.SEGMENT ,fhpi.PAX_STATUS paxStatus, fhpi.PNR,");
        sql.append("fhpi.MAIN_CLASS mainClass,fhpi.SUB_CLASS subClass,fhpi.TKT_NO tktNo,fhpi.TKT_ISSUE_DATE tktIssueDate,");
        sql.append("fhp.GUARANTEE_DAY_COUNT guaranteeDayCount,fhp.ROOM_NO roomNo,fhp.ROOM_TYPE roomType,fhp.CHECK_IN_MODE checkInMode,");
        sql.append("fhp.CHECK_IN_TIME checkInTime ");
        sql.append("FROM FD_HOTEL_PSSN_INFO fhp ");
        sql.append("LEFT JOIN FD_HOTEL_PAX_INFO fhpi ON fhpi.ID = fhp.HOTEL_PAX_ID ");
        sql.append("WHERE fhpi.ORDER_ID = :id AND fhp.HOTEL_ID = :hotelId ");
        return sql;
    }


    private StringBuffer getSettleReviewInfoSql(){
        StringBuffer sql = new StringBuffer();
        //结算审核单信息
        sql.append("SELECT fhsi.ID, fhi.HOTEL_NAME hotelName, fha.PRE_SUM_MONEY preSumMoney, fha.REAL_SUM_MONEY realSumMoney, fha.NOTE_INFO noteInfo,");
        sql.append("(SELECT COUNT(*) FROM FD_HOTEL_PSSN_INFO f WHERE f.ORDER_ID = fhsi.ORDER_ID AND f.HOTEL_ID = fhsi.HOTEL_ID ) AS payPaxNumber,");
        sql.append("fhsi.STATUS, fhsi.AUDIT_NO auditNo, fhsi.UPDATE_TIME submitTime, fhsi.remark, fhsi.HOTEL_ID hotelId, ");
        sql.append("fha.SETTLEMENT_TYPE settlementType, ");
        sql.append("fha.SINGLE_PRICE orderServiceAmountMainClass, fha.DOUBLE_PRICE orderServiceAmount,");
        sql.append("CASE WHEN fha.SETTLEMENT_TYPE = '1' THEN fha.FIRST_CLASS_AMOUNT ");
        sql.append("WHEN fha.SETTLEMENT_TYPE = '0' THEN fha.SINGLE_AMOUNT ");
        sql.append("END AS orderServiceNumMainClass ,");
        sql.append("CASE WHEN fha.SETTLEMENT_TYPE = '1' THEN fha.ECONOMY_CLASS_AMOUNT ");
        sql.append("WHEN fha.SETTLEMENT_TYPE = '0' THEN fha.DOUBLE_AMOUNT ");
        sql.append("END AS orderServiceNum, ");
        sql.append("fhsi.ACCOMMODATION_NO accommodationNo, ");
        //服务保障单信息
        sql.append("fhoi.id orderId, fhfi.FLIGHT_NO flightNo, fhfi.FLIGHT_DATE flightDate, fhoi.SERVICE_CITY serviceCity, fhoi.ORDER_NO orderNo,");
        sql.append("fhoi.PAY_TYPE payType, fhoi.SERVICE_NUM serviceNum, fhoi.STATUS as orderStatus,");
        //
        sql.append("(SELECT  task_id from WORKFLOW_AUDITOR_ID_INFO where BUSINESS_VALUE = fhsi.id and AUDITOR_ID =:currentUser) taskId ");
        sql.append("FROM FD_HOTEL_SETTLEMENT_INFO fhsi ");
        sql.append("LEFT JOIN FD_HOTEL_INFO fhi ON fhsi.HOTEL_ID = fhi.ID ");
        sql.append("LEFT JOIN FD_HOTEL_ORDER_INFO fhoi ON fhsi.ORDER_ID = fhoi.ID ");
        sql.append("LEFT JOIN FD_HOTEL_FLIGHT_INFO fhfi ON fhoi.ID = fhfi.ORDER_ID ");
        sql.append("LEFT JOIN FD_HOTEL_ACCOMMODATION fha ON fhi.id = fha.HOTEL_ID AND fhoi.id = fha.ORDER_ID ");
        sql.append("WHERE fhsi.id = :id  ");
        return sql;
    }

    private StringBuffer getCheckInfoSql(){
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT fha.SETTLEMENT_TYPE settlementType ,fha.IS_VERIFICATION checkStatus,");
        sql.append("fha.SINGLE_PRICE orderServiceAmountMainClass, fha.DOUBLE_PRICE orderServiceAmount,");
        sql.append("CASE WHEN fha.SETTLEMENT_TYPE = '1' THEN fha.FIRST_CLASS_AMOUNT ");
        sql.append("WHEN fha.SETTLEMENT_TYPE = '0' THEN fha.SINGLE_AMOUNT ");
        sql.append("END AS orderServiceNumMainClass ,");
        sql.append("CASE WHEN fha.SETTLEMENT_TYPE = '1' THEN fha.ECONOMY_CLASS_AMOUNT ");
        sql.append("WHEN fha.SETTLEMENT_TYPE = '0' THEN fha.DOUBLE_AMOUNT ");
        sql.append("END AS orderServiceNum ");
        sql.append("FROM FD_HOTEL_ACCOMMODATION fha ");
        sql.append("LEFT JOIN FD_HOTEL_SETTLEMENT_INFO fhsi ON fhsi.ACCOMMODATION_ID  = fha.ID ");
        sql.append("WHERE fhsi.AUDIT_NO = :auditNo ");
        return sql;
    }
    private StringBuffer getReviewCheckInfoSql(){
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT  ssb.F_SL amount ,ssb.F_BZ serviceMoney,s.bz serviceMoneyExpr,s.F_FYMC serviceName,");
        sql.append("nvl(TO_CHAR(ssb.F_BZ),s.bz) resultMoney  ,sgd.F_DWQC  companyName ");
        sql.append("FROM SDADSSNEW_SH_BZCHB ssb  " );
        sql.append(" LEFT JOIN SDADSSNEW_GG_DWXXB sgd on ssb.F_DWID = sgd.F_DWID ");
        sql.append("LEFT JOIN ( " );
        sql.append(" SELECT scs.F_FYID,scs.F_FYMC, " );
        sql.append(" CASE  " );
        sql.append(" WHEN nvl(LTRIM(scs.F_BZBDS,'0123456789.'),'false') = 'false' THEN scs.F_BZBDS " );
        sql.append(" ELSE 'false' END AS bz , " );
        sql.append(" ROW_NUMBER () over(partition by scs.F_FYID ORDER BY scs.F_RQ DESC) rnum " );
        sql.append(" FROM SDADSSNEW_CN_SHBZ scs   " );
        sql.append(" WHERE  scs.F_FYMC LIKE '%住宿%' ORDER BY rnum ) s " );
        sql.append("ON  ssb.F_FYMXID =  s.F_FYID " );
        sql.append("WHERE s.rnum = 1 and ssb.F_ZFBZ=0 " );
        sql.append("AND ssb.F_BH =:reviewNo ");
        return sql;
    }

    private StringBuffer getSettleReviewSubmitInfoSql(){
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT fhsi.ID, fhsi.STATUS, fhsi.AUDIT_NO auditNo, fhsi.SUBMIT_TIME submitTime, ");
        sql.append("fhoi.id orderId, fhoi.ORDER_NO orderNo, fhoi.STATUS as orderStatus, fhoi.CREATE_ID as userNo,");
        sql.append("fhoi.FLIGHT_NO flightNo, fhoi.FLIGHT_DATE flightDate,fhsi.CHECK_REMARK checkRemark, ");
        sql.append("fha.ACCOMMODATION_NO accommodationNo, fha.id as accommodationId,fha.IS_VERIFICATION checkStatus  ");
        sql.append("FROM FD_HOTEL_SETTLEMENT_INFO fhsi ");
        sql.append("LEFT JOIN FD_HOTEL_ORDER_INFO fhoi ON fhsi.ORDER_ID = fhoi.ID ");
        sql.append("LEFT JOIN FD_HOTEL_ACCOMMODATION fha ON fhsi.ORDER_ID = fha.ORDER_ID and fhsi.HOTEL_ID = fha.HOTEL_ID ");
        sql.append("WHERE (fhsi.AUDIT_NO = :auditNo or fhsi.id = :auditNo) ");
        return sql;
    }

    private StringBuffer updateSettleReviewStatusSql(String auditStatus) {

        StringBuffer sql = new StringBuffer();
        sql.append("UPDATE FD_HOTEL_SETTLEMENT_INFO fhsi ");
        sql.append("set fhsi.STATUS = :auditStatus ,fhsi.IS_AUTH_FLAG = :status " );
        if (SettleReviewStatusEnums.PASS.getKey().equals(auditStatus)) {
            sql.append(",fhsi.AUDIT_PASS_TIME =  :auditPassTime ");
        }
        sql.append("WHERE (fhsi.ID = :id or fhsi.AUDIT_NO = :id) ");
        return sql;
    }


    public QueryResults getPaxAccommodationReportPage(PaxAccommodationReportDTO dto) {
        StringBuffer sql = getPaxAccommodationReportPageSql(dto);
        Map<String,Object> paramsMap = new HashMap<>();
        paramsMap.put("flightStartDate",dto.getFlightStartDate());
        paramsMap.put("flightEndDate",dto.getFlightEndDate());
        paramsMap.put("createStartTime",dto.getCreateStartTime());
        paramsMap.put("createEndTime",dto.getCreateEndTime());
        if(ObjectUtils.isNotEmpty(dto.getFlightNo())){
            paramsMap.put("flightNo",dto.getFlightNo());
            sql.append("AND fhoi.FLIGHT_NO = :flightNo ");
        }
        if(ObjectUtils.isNotEmpty(dto.getServiceCities())){
            paramsMap.put("serviceCities",dto.getServiceCities());
            sql.append("AND fhoi.SERVICE_CITY in (:serviceCities) ");
        }
        if(ObjectUtils.isNotEmpty(dto.getServiceHotels())){
            paramsMap.put("serviceHotels",dto.getServiceHotels());
            sql.append("AND fhi.ID in (:serviceHotels) ");
        }
        if(ObjectUtils.isNotEmpty(dto.getOrderNo())){
            paramsMap.put("orderNo",dto.getOrderNo());
            sql.append("AND fhoi.ORDER_NO = :orderNo ");
        }
        if(ObjectUtils.isNotEmpty(dto.getPaxName())){
            paramsMap.put("paxName",dto.getPaxName());
            sql.append("AND fhpi.PAX_NAME = :paxName ");
        }
        if(ObjectUtils.isNotEmpty(dto.getIdNO())){
            paramsMap.put("idNo", AesEncryptUtil.aesEncryptScgsi(dto.getIdNO()));
            sql.append("AND fhpi.ID_NO = :idNo ");
        }
        if(ObjectUtils.isNotEmpty(dto.getTelephone())){
            paramsMap.put("telephone",dto.getTelephone());
            sql.append("AND fhpi.TELEPHONE = :telephone ");
        }
        if(ObjectUtils.isNotEmpty(dto.getOrgCityAirports())){
            paramsMap.put("orgCityAirports",dto.getOrgCityAirports());
            sql.append("AND fhpi.ORG_CITY_AIRP in (:orgCityAirports) ");
        }
        if(ObjectUtils.isNotEmpty(dto.getDstCityAirports())){
            paramsMap.put("dstCityAirports",dto.getDstCityAirports());
            sql.append("AND fhpi.DST_CITY_AIRP in (:dstCityAirports) ");
        }
        if(ObjectUtils.isNotEmpty(dto.getStatus())){
            paramsMap.put("status",dto.getStatus());
            sql.append("AND fhoi.STATUS in (:status) ");
        }
        if(ObjectUtils.isNotEmpty(dto.getPayTypes())){
            paramsMap.put("payTypes",dto.getPayTypes());
            sql.append("AND fhoi.PAY_TYPE in (:payTypes) ");
        }
        if(ObjectUtils.isNotEmpty(dto.getIsFlag())){
            paramsMap.put("isFlag",dto.getIsFlag());
            sql.append("AND fhpi2.IS_FLAG = :isFlag ");
        }
        sql.append("ORDER BY fha.GUARANTEE_COM_TIME DESC,fhpi.TKT_ISSUE_DATE ");
        return baseDAO.findBySQLPage_comm(sql.toString(), dto.getCurrent(), dto.getPageSize(),paramsMap, PaxAccommodationReportChildVO.class);
    }

    private StringBuffer updateSettleRemarkSql(){
        StringBuffer sql = new StringBuffer();
        sql.append("UPDATE FD_HOTEL_SETTLEMENT_INFO fhsi ");
        sql.append("set fhsi.remark = :remark " );
        sql.append("WHERE (fhsi.ID = :id or fhsi.AUDIT_NO = :id) ");
        return sql;
    }

    private StringBuffer updateSettleCheckRemarkSql(){
        StringBuffer sql = new StringBuffer();
        sql.append("UPDATE FD_HOTEL_SETTLEMENT_INFO fhsi ");
        sql.append("set fhsi.CHECK_REMARK = :remark " );
        sql.append("WHERE (fhsi.ID = :id or fhsi.AUDIT_NO = :id) ");
        return sql;
    }

    private StringBuffer updateSettleSubTimeSql(){
        StringBuffer sql = new StringBuffer();
        sql.append("UPDATE FD_HOTEL_SETTLEMENT_INFO fhsi ");
        sql.append("set fhsi.SUBMIT_TIME = :time " );
        sql.append("WHERE (fhsi.ID = :id or fhsi.AUDIT_NO = :id) ");
        return sql;
    }

    private StringBuffer getPaxAccommodationReportPageSql(PaxAccommodationReportDTO dto) {
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT fhpi2.ID AS paxId, fhpi2.IS_FLAG AS isFlag, fhoi.FLIGHT_DATE AS flightDate , fhoi.FLIGHT_NO AS flightNo , fhfi.SEGMENT AS segment, fhoi.PAY_TYPE AS payType,");
        sql.append("fhsi.ID AS settleId,fhoi.ID AS orderId, fhi.HOTEL_NAME AS hotelName, fhpi2.GUARANTEE_DAY_COUNT AS guaranteeDayCount, fhoi.ORDER_NO AS orderNo, fhoi.CREATE_TIME AS createTime,");
        sql.append("fhsi.AUDIT_NO AS auditNo, fhpi.PAX_NAME AS paxName, fhpi.TKT_NO AS tktNo, fhpi.ID_NO AS idNo, fhpi.TELEPHONE AS telephone, fhpi2.ROOM_NO AS roomNo,");
        sql.append("fhpi2.ROOM_TYPE AS roomType, fhpi2.CHECK_IN_TIME AS checkInTime, fhpi2.CHECK_IN_MODE AS checkInMode, fhoi.SERVICE_CITY AS serviceCity,");
        sql.append("(SELECT cc.CITY_CH_NAME FROM CITY_CODE cc WHERE cc.CITY_3CODE =  fhpi.ORG_CITY_AIRP) AS orgCityAirport,");
        sql.append("(SELECT cc.CITY_CH_NAME FROM CITY_CODE cc WHERE cc.CITY_3CODE =  fhpi.DST_CITY_AIRP) AS dstCityAirport,");
        sql.append("fhoi.STATUS, fhfi.AC_TYPE acType,");
        sql.append("fhsi.ACCOMMODATION_NO accommodationNo, ");
        sql.append("GET_USER_NAME(fhoi.CREATE_ID) AS createUser,GET_USER_NAME(fha.CREATE_ID) AS examineUser,");
        sql.append("GET_USER_NAME(fhsi.CREATE_ID) AS settleReviewUser,GET_USER_NAME(fha.SETTLE_ID) AS settleUser,");
        sql.append("fhsi.AUDIT_PASS_TIME AS settleReviewTime,fha.SETTLE_TIME AS settleTime ");
        sql.append("FROM FD_HOTEL_ORDER_INFO fhoi ");
        sql.append("LEFT JOIN FD_HOTEL_PAX_INFO fhpi ON fhoi.ID = fhpi.ORDER_ID ");
        sql.append("LEFT JOIN FD_HOTEL_FLIGHT_INFO fhfi ON fhoi.ID = fhfi.ORDER_ID ");
        sql.append("LEFT JOIN FD_HOTEL_PSSN_INFO fhpi2 ON fhpi.ID = fhpi2.HOTEL_PAX_ID ");
        sql.append("LEFT JOIN FD_HOTEL_ACCOMMODATION fha  ON fha.ACCOMMODATION_NO  = fhpi2.ACCOMMODATION_NO ");
        sql.append("LEFT JOIN FD_HOTEL_INFO fhi  ON fha.HOTEL_ID  = fhi.ID ");
//        sql.append("LEFT JOIN FD_HOTEL_ROOM_INFO fhri  ON fhpi2.ROOM_ID  = fhri.ID ");
        sql.append("LEFT JOIN FD_HOTEL_SETTLEMENT_INFO fhsi  ON fhpi2.ACCOMMODATION_NO = fhsi.ACCOMMODATION_NO ");
        sql.append("WHERE fha.ACCOMMODATION_STATUS = '5' AND fhpi2.ID IS NOT NULL ");
        sql.append("AND fhoi.FLIGHT_DATE BETWEEN :flightStartDate AND :flightEndDate ");
        sql.append("AND TO_CHAR(fhoi.CREATE_TIME,'yyyy-MM-dd') BETWEEN :createStartTime AND :createEndTime ");
        return sql;
    }

    public void updateFlag(List<String> paxIds, String flag) {
        StringBuffer sql = new StringBuffer();
        Map<String,Object> paramsMap = new HashMap<>();
        paramsMap.put("paxIds",paxIds);
        paramsMap.put("flag",flag);
        sql.append("update FD_HOTEL_PSSN_INFO fhpi2 set fhpi2.IS_FLAG = :flag ");
        sql.append("where fhpi2.ID in (:paxIds) ");
        baseDAO.batchUpdate(sql.toString(),paramsMap);
    }
}
