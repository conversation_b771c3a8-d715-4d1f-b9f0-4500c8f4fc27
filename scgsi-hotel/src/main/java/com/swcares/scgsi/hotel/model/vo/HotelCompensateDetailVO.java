package com.swcares.scgsi.hotel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName：HotelCompensateDetailVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/26 9:27
 * @version： v1.0
 */
@Data
public class HotelCompensateDetailVO {

    @ApiModelProperty(value = "保障单Id")
    private String orderId;

    @ApiModelProperty(value = "保障类型 1酒店")
    private String payType;

    @ApiModelProperty(value = "保障单号")
    private String orderNo;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "所选航段")
    private String choiceSegment;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "服务单状态(0草稿、1审核中、2通过、3待保障、4保障中 5未通过 6驳回 7保障完成 8逾期 9关闭")
    private String status;

    @ApiModelProperty(value = "预服务人数")
    private String serviceNum;

    @ApiModelProperty(value = "预服务天数")
    private String etdServiceDays;

    @ApiModelProperty(value = "预服务酒店")
    private String hotelName;

    @ApiModelProperty(value = "发起人")
    private String applyUser;

    @ApiModelProperty(value = "发起人id")
    private String createId;

    @ApiModelProperty(value = "发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "延误原因")
    private String lateReason;

    @ApiModelProperty(value = "延误时长")
    private String delayTime;

    @ApiModelProperty(value = "飞机号")
    private String planeCode;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "保障酒店，多选逗号分隔")
    private String serviceHotels;

    @ApiModelProperty(value = "保障酒店，多选逗号分隔")
    private String serviceHotelNames;

    @ApiModelProperty(value = "审核任务id，此字段有值显示审核按钮")
    private String taskId;

    @ApiModelProperty(value = "审核记录")
    private List<HotelOrderAuditRecordVO> hotelOrderAuditRecordVOList;

}
