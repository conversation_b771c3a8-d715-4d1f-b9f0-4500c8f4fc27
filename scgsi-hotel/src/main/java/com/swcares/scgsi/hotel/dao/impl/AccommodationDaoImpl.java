package com.swcares.scgsi.hotel.dao.impl;

import com.swcares.scgsi.base.BaseDAO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName：AccommodationDaoImpl
 * @Description： 酒店端住宿单dao层
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/10/12 9:30
 * @version： v1.0
 */
@Repository
public class AccommodationDaoImpl {
    @Resource
    private BaseDAO baseDAO;

    public void updateAccommodationStatus(String accommodationId,String auditStatus,String status,String accommodationStatus){
        StringBuffer sql = updateAccommodationStatusSql();
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        paramsMap.put("id",accommodationId);
        paramsMap.put("auditStatus",auditStatus);
        paramsMap.put("status",status);
        paramsMap.put("accommodationStatus",accommodationStatus);
        baseDAO.batchUpdate(sql.toString(),paramsMap);
    }





    private StringBuffer updateAccommodationStatusSql(){
        StringBuffer sql = new StringBuffer();
        sql.append("UPDATE FD_HOTEL_ACCOMMODATION fha ");
        sql.append("set fha.SETTLE_STATUS = :auditStatus ,fha.IS_VERIFICATION = :status, " );
        sql.append("fha.ACCOMMODATION_STATUS = :accommodationStatus " );
        sql.append("WHERE (fha.ID = :id or fha.ACCOMMODATION_NO = :id)");
        return sql;
    }
}
