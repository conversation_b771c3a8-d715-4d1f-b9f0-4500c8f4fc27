package com.swcares.scgsi.hotel.service;

import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hotel.model.dto.*;
import com.swcares.scgsi.hotel.model.entity.HotelOrderInfo;
import com.swcares.scgsi.hotel.model.vo.HotelCompensateDetailBaselVO;
import com.swcares.scgsi.hotel.model.vo.HotelCompensatePageVO;
import com.swcares.scgsi.hotel.model.vo.HotelOrderAuditRecordVO;
import com.swcares.scgsi.hotel.model.vo.HotelOrderInfoVO;
import com.swcares.scgsi.workflow.model.dto.ActivityCompleteParamsBaseDTO;

import java.util.List;
import java.util.Map;

/**
 * @ClassName：HotelCompensateService
 * @Description：航延酒店保障单
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/23 10:22
 * @version： v1.0
 */
public interface HotelCompensateService {


    QueryResults findCompensateFlightPage(HotelCompensateQueryDTO dto);

    List<HotelCompensatePageVO> getOrderInfoByFlightInfo(String flightNo, String flightDate, String status);

    List<HotelOrderAuditRecordVO> getOrderAuditRecord(String orderId) throws Exception;

    HotelCompensateDetailBaselVO getDetailOrderInfoByOrderId(String orderId);

    String saveOrderInfo(HotelOrderSaveParamDTO dto) throws Exception;

    void updateOrderStatus(String orderId, String status);

    /***
     * @title findHotelListByServiceCity
     * @description 根据服务航站查酒店
     * <AUTHOR>
     * @date 2022/11/9 10:46
     * @param serviceCity
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     */
    List<Map<String,Object>> findHotelListByServiceCity(String serviceCity);

    /***
     * @title auditOperation
     * @description 航延酒店审批
     * <AUTHOR>
     * @date 2022/11/9 10:46
     * @param dto
     * @return void
     */
    void auditOperation(AuditCompleteParamsDTO dto);

    /**
     * @title findOrderInfoById
     * @description  h5-查询保障单信息
     * <AUTHOR>
     * @date 2022/10/27 14:11
     * @param orderId 保障单id
     * @return com.swcares.scgsi.hotel.model.dto.HotelOrderInfoDTO
     */
    HotelOrderInfoDTO findOrderInfoById(String orderId);

    /***
     * @title expireOrderInfo
     * @description 逾期订单处理
     * <AUTHOR>
     * @date 2022/11/9 10:46

     * @return int
     */
    int expireOrderInfo();


    /***
     * @title completionOrder
     * @description 处理保障完成的航延酒店赔付单
     * <AUTHOR>
     * @date 2022/11/9 10:49

     * @return int
     */
    int completionOrder(String accommodationNo);


    /***
     * @title findOrderAuditPage
     * @description web航延酒店审核列表
     * <AUTHOR>
     * @date 2022/12/14 9:29
     * @param dto
     * @return java.util.List<com.swcares.scgsi.hotel.model.vo.HotelOrderInfoVO>
     */
    QueryResults findOrderAuditPage(HotelOrderAuditParamDTO dto);

}
