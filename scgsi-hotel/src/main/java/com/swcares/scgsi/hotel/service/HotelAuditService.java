package com.swcares.scgsi.hotel.service;

import com.swcares.scgsi.hotel.model.vo.HotelOrderAuditRecordVO;
import com.swcares.scgsi.workflow.model.dto.ActivityCompleteParamsBaseDTO;
import com.swcares.scgsi.workflow.model.entity.WorkflowAuditorIdInfoDO;

import java.util.List;
import java.util.Set;

/**
 * ClassName：com.swcares.scgsi.hotel.service <br>
 * Description：航延住宿酒店审核模块 <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 09月30日 15:45 <br>
 * @version v1.0 <br>
 */
public interface HotelAuditService {

    /**
     * @title auditOperation
     * @description 审核 -其他模块调用需设置业务标记
     * <AUTHOR>
     * @date 2022/10/11 9:21
     * @param dto
     * @return void
     */
    void auditOperation(ActivityCompleteParamsBaseDTO dto);

    /***
     * @title auditOperationRecord
     * @description 审批记录
     * <AUTHOR>
     * @date 2022/10/11 9:20
     * @param businessKey
     * @return void
     */
    List<HotelOrderAuditRecordVO> auditOperationRecord(String businessKey) throws Exception;


    /***
     * @title findTaskAuditorList
     * @description 获取任务审核人列表
     * <AUTHOR>
     * @date 2022/10/10 17:57
     * @param businessKey
     * @return void
     */
    List<WorkflowAuditorIdInfoDO> findTaskAuditorList(String businessKey);

    /***
     * @title saveTaskAuditor
     * @description 保存审核人
     * <AUTHOR>
     * @date 2022/10/11 9:30
     * @param assignees
     * @return void
     */
    void saveTaskAuditor(String business,String businessKey,List<String> assignees);


    /***
     * @title findAuditorList
     * @description 节点驱动内：查询审核用户集合
     * <AUTHOR>
     * @date 2022/10/9 14:40
     * @param assignees
     * @return void
     */
    Set<String> findAuditorList(List<String> assignees);
}
