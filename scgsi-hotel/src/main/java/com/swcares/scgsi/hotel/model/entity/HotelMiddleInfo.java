package com.swcares.scgsi.hotel.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * ClassName：com.swcares.component.workflow.FD.entity.HotelMiddleInfo <br>
 * Description：航延酒店保障单酒店信息表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-09-23 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name = "FD_HOTEL_MIDDLE_INFO")
@ApiModel(value="HotelMiddleInfo对象", description="航延酒店保障单酒店信息表")
public class HotelMiddleInfo{

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @ApiModelProperty(value = "酒店保障单id")
    @Column(name ="ORDER_ID")
    private String orderId;

    @ApiModelProperty(value = "酒店id")
    @Column(name ="HOTEL_ID")
    private String hotelId;


}
