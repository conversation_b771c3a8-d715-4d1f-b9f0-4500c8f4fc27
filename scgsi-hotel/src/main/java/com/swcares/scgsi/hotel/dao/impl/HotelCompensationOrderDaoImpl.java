package com.swcares.scgsi.hotel.dao.impl;

import com.swcares.scgsi.audit.vo.OrderAuditQuertListVo;
import com.swcares.scgsi.base.BaseDAO;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hotel.model.dto.HotelAuditParamsDTO;
import com.swcares.scgsi.hotel.model.dto.HotelCompensationOrderDTO;
import com.swcares.scgsi.hotel.model.dto.HotelCompensationPaxDTO;
import com.swcares.scgsi.hotel.model.vo.HotelCompensateDetailVO;
import com.swcares.scgsi.hotel.model.vo.HotelCompensatePaxVO;
import com.swcares.scgsi.hotel.model.vo.HotelOrderInfoVO;
import com.swcares.scgsi.util.AuthenticationUtil;
import com.swcares.scgsi.workflow.model.vo.HistoryTaskAuditBusiInfoVO;
import com.swcares.scgsi.workflow.service.WorkflowService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：HotelCompensationOrderDaoImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/10/8 14:33
 * @version： v1.0
 */
@Repository
public class HotelCompensationOrderDaoImpl {

    @Resource
    private BaseDAO baseDAO;


    private static final String ALL="0";
    /*待审核*/
    private static final String NOT_AUDIT="1";
    /*已审核*/
    private static final String AUDIT="2";

    public QueryResults getGuaranteeOrderList(HotelCompensationOrderDTO dto) {
        StringBuffer sql = new StringBuffer();
        Map<String,Object> paramsMap = new HashMap<>();
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser = (String) authentication.getPrincipal();
        sql.append("SELECT fhoi.ID ,fhoi.FLIGHT_DATE AS flightDate,fhoi.FLIGHT_NO AS flightNo,fhoi.SERVICE_CITY AS serviceCity,fhoi.STATUS ,fhoi.PAY_TYPE payType ,fhoi.ORDER_NO AS orderNo,fhoi.CREATE_ID AS createdId,GET_USER_NAME(fhoi.CREATE_ID) AS createUser,fhoi.LAUNCH_TIME AS createTime FROM FD_HOTEL_ORDER_INFO fhoi ");
        sql.append("WHERE 1=1 ");
        if(ObjectUtils.isNotEmpty(dto.getFlightStartDate()) && ObjectUtils.isNotEmpty(dto.getFlightEndDate())){
            paramsMap.put("flightStartDate",dto.getFlightStartDate());
            paramsMap.put("flightEndDate",dto.getFlightEndDate());
            sql.append("AND fhoi.FLIGHT_DATE BETWEEN :flightStartDate AND :flightEndDate ");
        }
        if(ObjectUtils.isNotEmpty(dto.getFlightNo())){
            paramsMap.put("flightNo",dto.getFlightNo());
            sql.append("AND fhoi.FLIGHT_NO = :flightNo ");
        }
        if(ObjectUtils.isNotEmpty(dto.getServiceCity())){
            paramsMap.put("serviceCity",dto.getServiceCity());
            sql.append("AND fhoi.SERVICE_CITY = :serviceCity ");
        }
        if(ObjectUtils.isNotEmpty(dto.getStatus())){
            paramsMap.put("status",dto.getStatus());
            sql.append("AND fhoi.STATUS in (:status) ");
        }
        if(ObjectUtils.isNotEmpty(dto.getCreateId())){
            paramsMap.put("createdId",dto.getCreateId());
            sql.append("AND fhoi.CREATE_ID = :createdId ");
        } else {
            paramsMap.put("createdId", currentUser);
            sql.append(" AND(fhoi.STATUS !=0 or (fhoi.STATUS = 0 and  fhoi.CREATE_ID = :createdId)) ");
        }
        sql.append("ORDER BY fhoi.STATUS ASC,fhoi.CREATE_TIME DESC ");
        return baseDAO.findBySQLPage_comm(sql.toString(),
                dto.getCurrent(),
                dto.getPageSize(),
                paramsMap, HotelOrderInfoVO.class);
    }

    public List<HotelCompensateDetailVO> getGuaranteeOrderDetail(String orderId) {
        StringBuffer sql = new StringBuffer();
        Map<String,Object> paramMap = new HashMap<>();
        Authentication authentication = AuthenticationUtil.getAuthentication();
        String currentUser =(String) authentication.getPrincipal();
        paramMap.put("orderId",orderId);
        paramMap.put("currentUser",currentUser);
        sql.append("SELECT fhoi.ID orderId,fhoi.ORDER_NO orderNo,fhoi.SERVICE_NUM serviceNum,fhoi.FLIGHT_NO flightNo,fhoi.FLIGHT_DATE flightDate,fhoi.CHOICE_SEGMENT choiceSegment,fhoi.SERVICE_CITY serviceCity,");
        sql.append("fhoi.STATUS ,fhoi.PAY_TYPE payType,fhoi.ETD_SERVICE_DAYS etdServiceDays,fhi.HOTEL_NAME hotelName,fhoi.REMARK ,GET_USER_NAME(fhoi.CREATE_ID) AS applyUser ,fhoi.CREATE_ID createId ,fhoi.LAUNCH_TIME createTime, ");
        sql.append("GET_USER_NAME(fhoi.UPDATE_USER) AS updateUser,fhoi.UPDATE_TIME updateTime,(SELECT  task_id from WORKFLOW_AUDITOR_ID_INFO where BUSINESS_VALUE = fhoi.ID and AUDITOR_ID =:currentUser) AS taskId ");
        sql.append("FROM FD_HOTEL_ORDER_INFO fhoi ");
        sql.append("LEFT JOIN FD_HOTEL_MIDDLE_INFO fhmi ON fhoi.ID = fhmi.ORDER_ID ");
        sql.append("LEFT JOIN FD_HOTEL_INFO fhi ON fhmi.HOTEL_ID = fhi.ID ");
        sql.append("WHERE fhoi.ID = :orderId");
        return (List<HotelCompensateDetailVO>) baseDAO.findBySQL_comm(sql.toString(), paramMap,HotelCompensateDetailVO.class);
    }

    public List<HotelCompensatePaxVO> getPersonnelDetails(HotelCompensationPaxDTO dto) {
        StringBuffer sql = new StringBuffer();
        Map<String,Object> paramMap = new HashMap<>();
        sql.append("SELECT fhpi.ID AS id ,fhpi.ORDER_ID AS orderId,fhpi.PAX_ID paxId,fhpi.PAX_NAME paxName,fhpi.ID_TYPE idType,fhpi.ID_NO idNo,fhpi.SEX sex,fhpi.TELEPHONE telephone,fhpi.SEGMENT segment,");
        sql.append("fhpi.ORG_CITY_AIRP orgCityAirp,fhpi.DST_CITY_AIRP dstCityAirp,fhpi.PAX_STATUS paxStatus,fhpi.MAIN_CLASS mainClass,fhpi.SUB_CLASS subClass,fhpi.TKT_NO tktNo,");
        sql.append("CASE WHEN fhpi.WITH_BABY IS NULL THEN '0' ELSE '1' END AS withBaby,CASE WHEN fhpi.IS_CHILD IS NULL THEN '0' ELSE '1' END AS isChild,");
        sql.append("fhpi.BABY_PAX_NAME babyPaxName,fhpi.TKT_ISSUE_DATE tktIssueDate,fhpi.SWITCH_OFF switchOff,fhpi.ETD_SERVICE_DAY etdServiceDay,fhpi.PNR pnr,fhpi.IS_FLAG isFlag,GET_USER_NAME(fhpi.CREATE_ID) AS createId ,fhpi.CREATE_TIME createTime ");
        sql.append("FROM FD_HOTEL_PAX_INFO fhpi WHERE 1=1 ");
        if(ObjectUtils.isNotEmpty(dto.getOrderId())){
            paramMap.put("orderId",dto.getOrderId());
            sql.append("AND fhpi.ORDER_ID = :orderId ");
        }
        if(ObjectUtils.isNotEmpty(dto.getPaxName())){
            paramMap.put("paxName",dto.getPaxName());
            sql.append("AND fhpi.PAX_NAME = :paxName ");
        }
        if(ObjectUtils.isNotEmpty(dto.getSegment())){
            paramMap.put("segment",dto.getSegment());
            sql.append("AND fhpi.SEGMENT in (:segment) ");
        }
        sql.append("ORDER BY fhpi.TKT_ISSUE_DATE ");
        return (List<HotelCompensatePaxVO>) baseDAO.findBySQL_comm(sql.toString(),paramMap,HotelCompensatePaxVO.class);
    }

    public QueryResults getOrderExamineList(HotelAuditParamsDTO dto) {
        Map<String, Object> paramsMap = new HashMap<String, Object>();
        StringBuffer sql = findOrderListByType(dto,dto.getType());

        if (NOT_AUDIT.equals(dto.getType())){
            paramsMap.put("userId",dto.getUserId());
        } else if (AUDIT.equals(dto.getType())) {
            paramsMap.put("hasAuditOrderIds",dto.getHasAuditOrderList());
        }
        else{
            paramsMap.put("userId",dto.getUserId());
            paramsMap.put("hasAuditOrderIds",dto.getHasAuditOrderList());
        }
        if(StringUtils.isNotBlank(dto.getFlightNo())){
            paramsMap.put("flightNo",dto.getFlightNo());
            sql.append("AND flightNo = :flightNo ");
        }
        if(StringUtils.isNotBlank(dto.getStartDate()) && StringUtils.isNotBlank(dto.getEndDate())){
            paramsMap.put("startDate",dto.getStartDate());
            paramsMap.put("endDate",dto.getEndDate());
            sql.append("AND flightDate BETWEEN :startDate AND :endDate ");
        }
        if(StringUtils.isNotBlank(dto.getServiceCity())){
            paramsMap.put("serviceCity",dto.getServiceCity());
            sql.append("AND serviceCity = :serviceCity ");
        }
        if(ObjectUtils.isNotEmpty(dto.getStatus())){
            paramsMap.put("status",dto.getStatus());
            sql.append("AND status in (:status) ");
        }
        //按照保障单发起时间倒序排序
        sql.append("ORDER BY createTime DESC ");
        return baseDAO.findBySQLPage_comm(sql.toString(),
                dto.getCurrent(),
                dto.getPageSize(),
                paramsMap, HotelOrderInfoVO.class);
    }

    private StringBuffer findOrderListByType(HotelAuditParamsDTO dto,String type) {
        StringBuffer sql = new StringBuffer();
        if(ALL.equals(type)){
            sql.append("SELECT * FROM (({0}) UNION({1}) ) WHERE 1=1 ");
            return new StringBuffer(MessageFormat.format(sql.toString(),findOrderListByType(dto,NOT_AUDIT)
                    , findOrderListByType(dto,AUDIT)));
        }
        if(NOT_AUDIT.equals(type)){
            //嵌套一层Select * 使得查出的数据为别名，便于后面使用别名作为判断条件
            sql.append("SELECT * FROM (");
            sql.append(" SELECT fhoi.ID ,fhoi.FLIGHT_NO AS flightNo,fhoi.FLIGHT_DATE AS flightDate,fhoi.CHOICE_SEGMENT AS choiceSegment,fhoi.SERVICE_CITY AS serviceCity,fhoi.STATUS status,");
            sql.append(" fhoi.PAY_TYPE payType,fhoi.ORDER_NO AS orderNO,GET_USER_NAME(fhoi.CREATE_ID) AS createUser ,fhoi.CREATE_TIME AS createTime ,");
            sql.append(" fhoi.SERVICE_NUM serviceNum,fhoi.ETD_SERVICE_DAYS endServiceDays,");
            sql.append(" (select to_char(wm_concat(FH.HOTEL_NAME )) from FD_HOTEL_MIDDLE_INFO FM");
            sql.append(" LEFT JOIN FD_HOTEL_INFO FH ON FM.HOTEL_ID = FH.ID");
            sql.append(" WHERE FM.ORDER_ID = fhoi. ID ) hotelName");
            sql.append(" FROM FD_HOTEL_ORDER_INFO fhoi ");
            sql.append(" LEFT JOIN WORKFLOW_AUDITOR_ID_INFO waii ON fhoi.ID = waii.BUSINESS_VALUE ");
            sql.append(" WHERE waii.AUDITOR_ID = :userId AND fhoi.STATUS != '6') ");
            sql.append(" WHERE 1=1 ");
        }
        if (AUDIT.equals(type)) {
            //嵌套一层Select * 使得查出的数据为别名，便于后面使用别名作为判断条件
            sql.append("SELECT * FROM (");
            sql.append(" SELECT fhoi.ID ,fhoi.FLIGHT_NO AS flightNo,fhoi.FLIGHT_DATE AS flightDate,fhoi.CHOICE_SEGMENT AS choiceSegment,fhoi.SERVICE_CITY AS serviceCity,fhoi.STATUS status,");
            sql.append(" fhoi.PAY_TYPE payType,fhoi.ORDER_NO AS orderNO,GET_USER_NAME(fhoi.CREATE_ID) AS createUser ,fhoi.CREATE_TIME AS createTime ,");
            sql.append(" fhoi.SERVICE_NUM serviceNum,fhoi.ETD_SERVICE_DAYS endServiceDays,");
            sql.append(" (select to_char(wm_concat(FH.HOTEL_NAME )) from FD_HOTEL_MIDDLE_INFO FM");
            sql.append(" LEFT JOIN FD_HOTEL_INFO FH ON FM.HOTEL_ID = FH.ID");
            sql.append(" WHERE FM.ORDER_ID = fhoi. ID ) hotelName");
            sql.append(" FROM FD_HOTEL_ORDER_INFO fhoi ");
            sql.append(" WHERE fhoi.ID in (:hasAuditOrderIds)) ");
            sql.append(" WHERE 1=1 ");

        }
        return sql;
    }
}
