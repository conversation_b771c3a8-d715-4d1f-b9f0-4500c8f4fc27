package com.swcares.scgsi.hotel.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * ClassName：com.swcares.component.workflow.FD.entity.HotelAccommodation <br>
 * Description：酒店住宿单表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-09-23 <br>
 * @version v1.0 <br>
 */
@Data
@Entity
@Table(name ="FD_HOTEL_ACCOMMODATION")
@ApiModel(value="HotelAccommodation对象", description="酒店住宿单表")
public class HotelAccommodation{

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @ApiModelProperty(value = "酒店住宿单单号(系统生成比如根据时间)")
    @Column(name ="ACCOMMODATION_NO")
    private String accommodationNo;

    @ApiModelProperty(value = "酒店ID")
    @Column(name ="HOTEL_ID")
    private String hotelId;

    @ApiModelProperty(value = "酒店住宿单状态（0待保障、1保障中、2待结算、3结算审核中、4已完成、5结算完成）")
    @Column(name ="ACCOMMODATION_STATUS")
    private String accommodationStatus;

    @ApiModelProperty(value = "酒店人员确认保障完成时间")
    @Column(name ="GUARANTEE_COM_TIME")
    private LocalDateTime guaranteeComTime;

    @ApiModelProperty(value = "完成结算时间")
    @Column(name ="SETTLE_TIME")
    private LocalDateTime settleTime;

    @ApiModelProperty(value = "完成结算人ID")
    @Column(name ="SETTLE_ID")
    private String settleId;

    @ApiModelProperty(value = "单人间数量")
    @Column(name ="SINGLE_AMOUNT")
    private String singleAmount;

    @ApiModelProperty(value = "双人间数量")
    @Column(name ="DOUBLE_AMOUNT")
    private String doubleAmount;

    @ApiModelProperty(value = "单人间服务单价")
    @Column(name ="SINGLE_PRICE")
    private String singlePrice;

    @ApiModelProperty(value = "双人间服务单价")
    @Column(name ="DOUBLE_PRICE")
    private String doublePrice;

    @ApiModelProperty(value = "山航服务保障单号（ID或单号，暂定数据类型为255）")
    @Column(name ="ORDER_ID")
    private String orderId;

    @ApiModelProperty(value = "预结算总价")
    @Column(name ="PRE_SUM_MONEY")
    private BigDecimal preSumMoney;

    @ApiModelProperty(value = "建单日期")
    @Column(name ="CREATE_DATE")
    private Date createDate;

    @ApiModelProperty(value = "修改日期")
    @Column(name ="MODIFY_DATE")
    private Date modifyDate;

    @ApiModelProperty(value = "建单人ID")
    @Column(name ="CREATE_ID")
    private String createId;

    @ApiModelProperty(value = "修改人ID")
    @Column(name ="MODIFY_ID")
    private String modifyId;

    @ApiModelProperty(value = "实结算总价")
    @Column(name ="REAL_SUM_MONEY")
    private BigDecimal realSumMoney;

    @ApiModelProperty(value = "备注")
    @Column(name ="NOTE_INFO")
    private String noteInfo;

    @ApiModelProperty(value = "核验状态 0未核验 1通过 2退回 ")
    @Column(name ="IS_VERIFICATION")
    private String isVerification;

    @ApiModelProperty(value = "结算审核状态（0待审核，1审核中，2通过，3驳回）")
    @Column(name ="SETTLE_STATUS")
    private String settleStatus;

    @ApiModelProperty(value = "结算类型    0间 1人")
    @Column(name ="SETTLEMENT_TYPE")
    private String settlementType;

    @ApiModelProperty(value = "公务舱人数")
    @Column(name ="FIRST_CLASS_AMOUNT")
    private String firstClassAmount;

    @ApiModelProperty(value = "经济舱人数")
    @Column(name ="ECONOMY_CLASS_AMOUNT")
    private String economyClassAmount;


}
