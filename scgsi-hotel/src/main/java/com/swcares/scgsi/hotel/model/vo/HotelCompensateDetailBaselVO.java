package com.swcares.scgsi.hotel.model.vo;

import com.swcares.scgsi.hotel.model.entity.HotelPaxInfo;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：HotelCompensateDetaiBaselVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/26 9:54
 * @version： v1.0
 */
@Data
public class HotelCompensateDetailBaselVO {

    //赔偿单信息
    private HotelCompensateDetailVO compensateDetailVO;
    //审核信息
    private List<HotelOrderAuditRecordVO> compensateAuditRecordVO;
    //旅客信息
    private List<HotelPaxInfo> compensatePaxVO;


}
