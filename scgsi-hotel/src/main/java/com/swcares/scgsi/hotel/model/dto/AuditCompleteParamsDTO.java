package com.swcares.scgsi.hotel.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;

import javax.validation.constraints.NotEmpty;

/**
 * ClassName：com.swcares.scgsi.hotel.model.dto <br>
 * Description：流程审批参数<br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 09月30日 16:14 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="AuditCompleteParamsDTO对象", description="赔偿单审核处理对象")
public class AuditCompleteParamsDTO{

    @ApiModelProperty(value = "该流程定义下业务数据实例唯一键")
    @NotEmpty
    private String orderId;

    @ApiModelProperty(value = "审核状态- 同意AGREE、拒绝REJECT、驳回BACK")
    @NotEmpty
    private String optionCode;

    @ApiModelProperty(value = "审核备注")
    private String remarks;

    @ApiModelProperty(value = "审核taskId")
    @NotEmpty
    private String taskId;


}
