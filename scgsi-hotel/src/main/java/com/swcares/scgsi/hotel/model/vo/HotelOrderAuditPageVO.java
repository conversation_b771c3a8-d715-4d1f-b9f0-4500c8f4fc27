package com.swcares.scgsi.hotel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName：HotelOrderInfoVO
 * @Description： 保障单VO类
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/9/23 14:57
 * @version： v1.0
 */
@Data
@ApiModel(value = "HotelOrderInfoVO", description = "保障单信息")
public class HotelOrderAuditPageVO {
    @ApiModelProperty("审核id")
    private String taskId;

    @ApiModelProperty("保障单id")
    private String id;

    @ApiModelProperty("航班日期")
    private String flightDate;

    @ApiModelProperty("航班号")
    private String flightNo;

    @ApiModelProperty("服务航站")
    private String serviceCity;

    @ApiModelProperty("服务单号")
    private String orderNo;

    @ApiModelProperty(value = "服务单状态(0草稿、1审核中、2通过、3待保障、4保障中 5未通过 6驳回 7保障完成 8逾期 9关闭")
    private String status;

    @ApiModelProperty(value = "保障类型 1航延住宿")
    private String payType;

    @ApiModelProperty(value = "航段")
    private String choiceSegment;

    @ApiModelProperty(value = "预服务人数")
    private String serviceNum;

    @ApiModelProperty(value = "创建人Id")
    private String createId;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
