package com.swcares.scgsi.hotel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @ClassName：ActStartProcessParamDto
 * @Description：启动审核流程
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/8/1 14:41
 * @version： v1.0
 */
//@Builder
@Data
public class ActStartProcessParamDTO {

    private String assignee;

   /* @ApiModelProperty(value = "下级处理人组",required = false)
    private String candidateGroups;

    @ApiModelProperty(value = "下级候选处理人",required = false)
    private String candidates;*/

}
