package com.swcares.scgsi.hotel.service.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.exception.BusinessException;
import com.swcares.exception.MessageCode;
import com.swcares.scgsi.base.QueryResults;
import com.swcares.scgsi.hotel.dao.HotelPaxInfoDao;
import com.swcares.scgsi.hotel.dao.impl.HotelCompensationOrderDaoImpl;
import com.swcares.scgsi.hotel.enums.HotelCompensateStatusEnums;
import com.swcares.scgsi.hotel.model.dto.*;
import com.swcares.scgsi.hotel.model.entity.HotelPaxInfo;
import com.swcares.scgsi.hotel.model.vo.HotelCompensateDetailVO;
import com.swcares.scgsi.hotel.model.vo.HotelCompensatePaxVO;
import com.swcares.scgsi.hotel.model.vo.HotelOrderAuditRecordVO;
import com.swcares.scgsi.hotel.model.vo.HotelOrderInfoVO;
import com.swcares.scgsi.hotel.service.HotelCompensateService;
import com.swcares.scgsi.hotel.service.HotelAuditService;
import com.swcares.scgsi.hotel.service.HotelCompensationOrderService;
import com.swcares.scgsi.web.RenderResult;
import com.swcares.scgsi.workflow.model.vo.HistoryTaskAuditBusiInfoVO;
import com.swcares.scgsi.workflow.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName：HotelCompensationOrderServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/10/8 14:29
 * @version： v1.0
 */
@Service
@Slf4j
public class HotelCompensationOrderServiceImpl implements HotelCompensationOrderService {

    @Resource
    private HotelCompensationOrderDaoImpl hotelCompensationOrderDao;
    @Autowired
    HotelCompensateService hotelCompensateService;
    @Autowired
    HotelPaxInfoDao hotelPaxInfoDao;

    @Autowired
    HotelAuditService hotelAuditService;
    @Autowired
    WorkflowService workflowService;


    @Override
    public QueryResults getGuaranteeOrderList(HotelCompensationOrderDTO dto) {
        return hotelCompensationOrderDao.getGuaranteeOrderList(dto);
    }

    @Override
    public HotelCompensateDetailVO getGuaranteeOrderDetail(String orderId) throws Exception {
        log.info("【H5航延住宿-旅客服务详情】保障单id：【{}】",orderId);
        List<HotelCompensateDetailVO> guaranteeOrderDetailList = hotelCompensationOrderDao.getGuaranteeOrderDetail(orderId);
        if(ObjectUtils.isEmpty(guaranteeOrderDetailList)){
            throw new BusinessException(MessageCode.ORDER_NOT_EXIST.getCode());
        }
        String hotelNames = guaranteeOrderDetailList.stream().map(HotelCompensateDetailVO::getHotelName).distinct().collect(Collectors.joining("，"));
        HotelCompensateDetailVO hotelCompensateDetailVO = guaranteeOrderDetailList.get(0);
        hotelCompensateDetailVO.setServiceHotels(hotelNames);
        List<HotelOrderAuditRecordVO> hotelOrderAuditRecordVOS = null;
        if(!HotelCompensateStatusEnums.DRAFT.getKey().equals(hotelCompensateDetailVO.getStatus())){
            hotelOrderAuditRecordVOS = hotelAuditService.auditOperationRecord(orderId);
        }
        log.info("【H5航延住宿-旅客服务详情】当前保障单的审核记录：【{}】", JSONUtil.toJsonStr(hotelOrderAuditRecordVOS));
        //待保障，保障中，保障完成  都处于发放之后的状态，需展示发放人
        if(HotelCompensateStatusEnums.COMPLETE.getKey().equals(hotelCompensateDetailVO.getStatus()) ||
           HotelCompensateStatusEnums.READY.getKey().equals(hotelCompensateDetailVO.getStatus()) ||
           HotelCompensateStatusEnums.EFFECT.getKey().equals(hotelCompensateDetailVO.getStatus())){
            HotelOrderAuditRecordVO hotelOrderAuditRecordVO = new HotelOrderAuditRecordVO();
            hotelOrderAuditRecordVO.setOrderId(orderId);
            hotelOrderAuditRecordVO.setOpinionCode("GRANT");
            hotelOrderAuditRecordVO.setTaskStatus(true);
            hotelOrderAuditRecordVO.setAuditTime(hotelCompensateDetailVO.getUpdateTime().toString());
            hotelOrderAuditRecordVO.setAuditor(hotelCompensateDetailVO.getUpdateUser());
            hotelOrderAuditRecordVOS.add(hotelOrderAuditRecordVO);
        }
        hotelCompensateDetailVO.setHotelOrderAuditRecordVOList(hotelOrderAuditRecordVOS);
        return hotelCompensateDetailVO;
    }

    @Override
    public List<HotelCompensatePaxVO> getPersonnelDetails(HotelCompensationPaxDTO dto) {
        //如果传入航段作为筛选条件，需要在"-"两边加个空格" - "进行匹配。
        List<String> segmentList = dto.getSegment();
        if(ObjectUtils.isNotEmpty(segmentList)){
            List<String> list = segmentList.stream().map(str -> {
                return str.replace("-", " - ");
            }).collect(Collectors.toList());
            dto.setSegment(list);
        }
        return hotelCompensationOrderDao.getPersonnelDetails(dto);
    }

    @Override
    public QueryResults getOrderExamineList(HotelAuditParamsDTO dto) {
        List<HistoryTaskAuditBusiInfoVO> historyTaskAuditBusiInfoVOS = workflowService.historyTaskAuditByAssignee(dto.getUserId());
        if(ObjectUtils.isNotEmpty(historyTaskAuditBusiInfoVOS)){
            List<String> list = historyTaskAuditBusiInfoVOS.stream().map(HistoryTaskAuditBusiInfoVO::getBusiKey).distinct().collect(Collectors.toList());
            dto.setHasAuditOrderList(list);
        }
        return hotelCompensationOrderDao.getOrderExamineList(dto);
    }

    @Override
    public String submitOrder(String orderId) {
        //h5只需要通过保障单id查询，再继续走web端逻辑--h5只会草稿提交，不支持编辑
        HotelOrderSaveParamDTO dto = new HotelOrderSaveParamDTO();
        //封装保障单信息
        HotelOrderInfoDTO hotelOrderInfoDTO = hotelCompensateService.findOrderInfoById(orderId);
        log.info("【h5提交保障单】查询的保障单结果信息{}，需查询的保障单id{}", JSONUtil.toJsonStr(hotelOrderInfoDTO),orderId);
        if(!HotelCompensateStatusEnums.DRAFT.getKey().equals(hotelOrderInfoDTO.getStatus())){
            throw  new BusinessException(MessageCode.HOTEL_BUSINESS_ORDER_STATUS_NOT_SUPPORT.getCode());
        }
        hotelOrderInfoDTO.setStatus(HotelCompensateStatusEnums.AUDIT.getKey());
        dto.setHotelOrderInfoDTO(hotelOrderInfoDTO);
        //封装旅客信息
        List<HotelPaxInfo> paxInfoList = hotelPaxInfoDao.findAllByOrderId(orderId);
        dto.setPaxInfoList(paxInfoList);
        String orderNewId = null;
        try {
            orderNewId = hotelCompensateService.saveOrderInfo(dto);
        }catch (Exception exception){
            log.error("【h5提交保障单】提交出错，保障单id：【{}】，错误原因：【{}】",orderId,exception.getMessage());
            throw new BusinessException(MessageCode.HOTEL_BUSINESS_ORDER_SAVE_ERROR.getCode());
        }
        return orderNewId;
    }


}
