package com.swcares.scgsi.hotel.service.impl;

import com.swcares.scgsi.hotel.dao.impl.AccommodationDaoImpl;
import com.swcares.scgsi.hotel.dao.impl.SettleReviewDaoImpl;
import com.swcares.scgsi.hotel.model.vo.AuditorsInfoVO;
import com.swcares.scgsi.hotel.service.SettleReviewAuditService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @ClassName：SettleReviewAuditServiceImpl
 * @Description： 结算审核审核操作相关接口实现类
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/10/11 16:25
 * @version： v1.0
 */
@Slf4j
@Service
public class SettleReviewAuditServiceImpl implements SettleReviewAuditService {
    @Autowired
    private SettleReviewDaoImpl settleReviewDao;
    @Autowired
    private AccommodationDaoImpl accommodationDao;



    @Override
    public void updateSettleReviewStatus(String settleId,String auditStatus,String status){
        settleReviewDao.updateSettleReviewStatus(settleId,auditStatus, status);
    }


    @Override
    public void updateSettleOrderStatus(String accommodationId,String auditStatus,String status,String accommodationStatus){
        accommodationDao.updateAccommodationStatus(accommodationId, auditStatus, status,accommodationStatus);
    }

    @Override
    public void updateSettleRemark(String remark, String settleId) {
        settleReviewDao.updateSettleRemark(remark,settleId);
    }

    @Override
    public void updateSettleCheckRemark(String settleId, String remark) {
        settleReviewDao.updateSettleCheckRemark(settleId, remark);
    }

    @Override
    public void updateSettleSubTime(Date subTime, String settleId) {
        settleReviewDao.updateSettleSubTime(subTime,settleId);
    }

    @Override
    public List<AuditorsInfoVO> getAuditors(Set<String> auditorIds, String userInfo) {
        if (ObjectUtils.isEmpty(auditorIds)){
            return null;
        }
        return settleReviewDao.getAuditors(auditorIds, userInfo);
    }

}
