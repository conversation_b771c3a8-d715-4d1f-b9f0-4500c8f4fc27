package com.swcares.scgsi.hotel.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Clob;
import java.sql.SQLException;

/**
 * ClassName：com.swcares.scgsi.audit.dto <br>
 * Description：审批记录 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 10月11日 14:29 <br>
 * @version v1.0 <br>
 */
@Data
public class HotelOrderAuditRecordVO {

    @ApiModelProperty("服务单id")
    private String orderId;

    @ApiModelProperty("节点 submitter-发起，common-其他普通节点")
    private String nodeKey;

    @ApiModelProperty("审核状态 AGREE 同意 REJECT不同意 BACK驳回")
    private String opinionCode;

    @ApiModelProperty("审核备注")
    private String remark;

    @ApiModelProperty("用户姓名")
    private String auditor;

    @ApiModelProperty("用户职位")
    private String job;

    @ApiModelProperty("审核时间")
    private String auditTime;

    @ApiModelProperty("用户头像")
    private Clob photo;

    @ApiModelProperty(value = "任务状态 true已处理 false未处理")
    private boolean taskStatus;

    /**
     * Title： getPhoto <br>
     * Description： Clob类型转string <br>
     * author：傅欣荣 <br>
     * date：2022/10/11 13:19 <br>
     * @param
     * @return
     */
    public String getPhoto() {
        String value = null;
        try {
            if (photo != null) {
                value = photo.getSubString((long) 1, (int) photo.length());// 把clob数据转换为string
            }
        } catch (SQLException e) {
        }
        return value;
    }


}
