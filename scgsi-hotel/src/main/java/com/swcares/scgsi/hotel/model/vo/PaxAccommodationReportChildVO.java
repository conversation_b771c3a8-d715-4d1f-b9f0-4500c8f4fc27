package com.swcares.scgsi.hotel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.scgsi.encryption.Encryption;
import com.swcares.scgsi.encryption.EncryptionClassz;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName：PaxAccommodationReportChildVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/11/15 15:23
 * @version： v1.0
 */
@Data
@EncryptionClassz
@ApiModel(value = "旅客住宿报表子类VO")
public class PaxAccommodationReportChildVO{

    @ApiModelProperty(value = "旅客Id")
    private String paxId;

    @ApiModelProperty(value = "保障单Id")
    private String orderId;

    @ApiModelProperty(value = "结算审核Id")
    private String settleId;

    @ApiModelProperty(value = "标记")
    private String isFlag;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "服务类型")
    private String payType;

    @ApiModelProperty(value = "酒店名称")
    private String hotelName;

    @ApiModelProperty(value = "服务天数")
    private String guaranteeDayCount;

    @ApiModelProperty(value = "服务保障单单号")
    private String orderNo;

    @ApiModelProperty(value = "服务单发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "审核单号")
    private String auditNo;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @Encryption
    @ApiModelProperty(value = "证件号")
    private String idNo;

    @Encryption
    @ApiModelProperty(value = "手机号")
    private String telephone;

    @ApiModelProperty(value = "房间类型")
    private String roomType;

    @ApiModelProperty(value = "房间号")
    private String roomNo;

    @ApiModelProperty(value = "入住时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date checkInTime;

    @ApiModelProperty(value = "入住办理方式")
    private String checkInMode;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "出发航站")
    private String orgCityAirport;

    @ApiModelProperty(value = "到达航站")
    private String dstCityAirport;

    @ApiModelProperty(value = "服务单状态")
    private String status;

    @ApiModelProperty(value = "机型")
    private String acType;

    @ApiModelProperty(value = "创建人/发起人")
    private String createUser;

    @ApiModelProperty(value = "审核人")
    private String examineUser;


    @ApiModelProperty(value = "结算审核人")
    private String settleReviewUser;

    @ApiModelProperty(value = "结算审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settleReviewTime;

    @ApiModelProperty(value = "完成结算人")
    private String settleUser;


    @ApiModelProperty(value = "完成结算日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date settleTime;

    @ApiModelProperty(value = "酒店住宿单号")
    private String accommodationNo;
}
