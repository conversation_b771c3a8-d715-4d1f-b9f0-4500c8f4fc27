package com.swcares.scgsi.hotel.enums;

/**
 * @ClassName：HotelWorkflowBusinessEnum
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/8/2 11:57
 * @version： v1.0
 */
public enum HotelWorkflowBusinessEnum {
    FD_HOTEL("hotel", "航延酒店业务类型"),
    FD_SETTLEMENT("settle", "航延酒店结算审核");

    private HotelWorkflowBusinessEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static HotelWorkflowBusinessEnum build(String key) {
        return build(key, true);
    }

    public static HotelWorkflowBusinessEnum build(String key, boolean throwEx) {
        HotelWorkflowBusinessEnum typeEnum = null;
        for (HotelWorkflowBusinessEnum element : HotelWorkflowBusinessEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + HotelWorkflowBusinessEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
