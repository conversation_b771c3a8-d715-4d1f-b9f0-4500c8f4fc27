package com.swcares.scgsi.hotel.model.dto;

import lombok.Data;

/**
 * @ClassName：HotelCompensateDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/23 10:31
 * @version： v1.0
 */
@Data
public class HotelCompensateQueryDTO {


    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 赔付类型 1航延住宿
     */
    private String payType;

    /**
     * 航班开始日期
     */
    private String startDate;
    /**
     * 航班结束日期
     */
    private String endDate;
    /**
     * 出发航站
     */
    private String orgCityAirp;

    /**
     * 到达航站
     */
    private String dstCityAirp;
    /**
     * 赔付单状态 null 全部  0草稿   1我发起的
     */
    private String status;


    /** 当前页数，默认为第一页 **/
    private int current = 1;

    /** 每页显示记录数，默认为10条 **/
    private int pageSize = 10;
}
