package com.swcares.exception;


/**
 * 
 * ClassName：com.swcares.core.exception.GenericException <br>
 * Description：通用异常处理类 <br>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2019年2月1日 上午11:21:47 <br>
 * @version v1.0 <br>
 */
public class GenericException extends RuntimeException {

  private static final long serialVersionUID = -7911482301292075834L;

  /** 错误代码 */
  private String code;
  
  /** 补充参数，比如{}为空，这个param可以设置进去 */
  private String[] params;

  /** 实际数据 */
  private Object data;

  public GenericException(String code) {
    this.code = code;
  }

  public GenericException(String code, String msg) {
    super(msg);
    this.code = code;
  }
  
  public GenericException(String code, String[] params) {
    this.code = code;
    this.params = params;
  }
  
  public GenericException(String code, String msg, String... params) {
    super(msg);
    this.code = code;
    this.params = params;
  }
  
  public GenericException(String code, String[] params, Object data) {
    this.code = code;
    this.params = params;
    this.data = data;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }


  /**
   * 
   * Title：getParams <br>
   * Description：TODO(这里用一句话描述这个方法的作用) <br>
   * author：姚文兵[winiger][wbyao@travelsky] <br>
   * date：2019年3月4日 上午10:45:35 <br>
   * @return rt<br>
   */
  public String[] getParams() {
    return params;
  }

  /**
   * 
   * Title：setParams <br>
   * Description：TODO(这里用一句话描述这个方法的作用) <br>
   * author：姚文兵[winiger][wbyao@travelsky] <br>
   * date：2019年3月4日 上午10:45:16 <br>
   * @param params params<br>
   */
  public void setParams(String[] params) {
    this.params = params;
  }

  /**
   * 
   * Title：getData <br>
   * Description：TODO(这里用一句话描述这个方法的作用) <br>
   * author：姚文兵[winiger][wbyao@travelsky] <br>
   * date：2019年3月4日 上午10:45:03 <br>
   * @return rt<br>
   */
  public Object getData() {
    return data;
  }

  /**
   * 
   * Title：setData <br>
   * Description：TODO(这里用一句话描述这个方法的作用) <br>
   * author：姚文兵[winiger][wbyao@travelsky] <br>
   * date：2019年3月4日 上午10:44:36 <br>
   * @param data data<br>
   */
  public void setData(Object data) {
    this.data = data;
  }

}
