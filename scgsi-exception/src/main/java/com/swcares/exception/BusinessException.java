package com.swcares.exception;


/**   
 * ClassName：com.swcares.core.exception.BusinessException <br>
 * Description：自定义业务异常类<br>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2019年1月23日 下午5:15:08 <br>
 * @version v1.0 <br>  
 */
public class BusinessException extends GenericException {

  private static final long serialVersionUID = -8277858945324956928L;

  public BusinessException(String code) {
    super(code);
  }
  
  public BusinessException(String code, String... param) {
    super(code, param);
  }
  
  public BusinessException(String code, String msg, String... param) {
    super(code, msg, param);
  }
  
  public BusinessException(String code, String[] param, Object data) {
    super(code, param, data);
  }
  
}
