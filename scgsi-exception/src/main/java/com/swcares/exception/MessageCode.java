package com.swcares.exception;

import lombok.Getter;

/**
 * 
 * ClassName：com.swcares.common.exception.BusinessExcepCode <br>
 * Description：异常值<br>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2019年3月4日 下午2:05:13 <br>
 * @version v1.0 <br>
 */
@Getter
public enum MessageCode {
  
  /*------------------------系统基础----------------------*/
  SUCCESS("0"),FAIL("-1"),
  UN_KNOWN("0000"),NO_AUTH("0001"),
  REFRESH("0002"),PARAM_EXCEPTION("0003"),LOGIN_AGAIN("0004"),
  PARAM_IS_NULL("0008"),
  /** 短期内同一用户或ip访问过多提示 */
  VISIT_EXCEED_THRESHOLD("0009"),
  /*------------------------部门模块----------------------*/
  DEPARTMENT_SAVE_FAIL("1120"),
  /** 部门信息为空 */
  DEPARTMENT_NULL("1121"),
  /** 资源为空 */
  RESOURCE_NULL("1122"),
  /** 当前角色类型至多绑定一个用户 */
  ROLE_USER_EXCESS("1123"),
  /** 当前角色绑定了用户 */
  ROLE_BIND_USER("1124"),
  /** 分配用户应为当前角色部门下及其子部门*/
  DEPARTMENT_ERROR("1125"),
  /** 当前选择的用户已拥有一个管理员身份 */
  USER_BIND_ERROR("1126"),
  /*------------------------消息模块----------------------*/
  
  /*---------------------用户登录模块-------------------*/
  /** code: 100100, msg: 验证码已过期 */
  SYS_CODE_STALE("100100"),
  /** code: 100101, msg: 验证码不正确 */
  SYS_CODE_ERROR("100101"),
  /** code: 100102, msg: 用户已被冻结 */
  SYS_USER_FREEZED("100102"),
  /** code: 100103, msg: 用户不存在或密码错误 */
  SYS_USER_NOT_EXIST_AND_PWD_ERROE("100103"),
  /** code: 100104, msg: 用户状态为禁用 */
  SYS_USER_STATE("100104"),
  /** code: 100105, msg: 密码错误（暂时不使用） */
  SYS_PWD_ERROR("100105"),
  /** code: 100106, msg: 初始密码，请立即修改密码 */
  SYS_PWD_INIT("100106"),
  /** code: 100107, msg: 旧密码错误 */
  SYS_OLDPASSWORD_ERROR("100107"),
  /** code: 100108, msg:token验证失败 */
  SYS_TOKEN_FAIL_MSG ("100108"),
  /** code: 100109, msg: token验证获取数据为空 */
  SYS_TOKEN_IS_NULL_MSG ("100109"),
  /** code: 100110, msg: 账号或手机号或验证码错误，请重新输入。 */
  SYS_USER_NOT_EXIST_PHONE_ERROR ("100110"),
  /** code: 100111, msg: 输入参数为空，请检查参数。 */
  SYS_LOGIN_PARAM_IS_NULL ("100111"),
  /** code: 100112, msg: 用户不存在 */
  SYS_USER_NOT_EXIST("100112"),
  /** code: 100113, msg: 用户管理员角色超过规定数量 */
  COMPETENCE_ROLE_NUM_ERROR("100113"),
  /** 资源的父亲节点为必填项 */
  BIND_RESOURCE_PID("100114"),
  /** code: 100115, msg: 管理员角色已使用*/
  COMPETENCE_ROLE_USED("100115"),
  /** code: 100116, msg: 找回密码短信发送失败*/
  SMS_SEND_FAIL("100116"),
  /** code: 100117, msg: 找回密码短信时限*/
  SMS_SEND_TIME_LIMIT("100117"),
  /** code: 100118, msg: 客户端发送密码找回短信已超过阀值,请稍后再试！*/
  IP_SMS_SEND_THRESHOLD("100118"),
  /**code:100119,msg:每个自然日仅允许手动同步三次，您已超出今日手动同步次数！*/
  USER_MANUAL_SYNC_THRESHOLD("100119"),
  /**code:100122,msg:增量同步用户数据还在进行中请稍后再试！*/
  USER_MANUAL_SYNC_EXECUTION("100122"),
  /**code:100123,msg:新旧密码不能一样！*/
  SYS_NEW_OLDPASSWORD_CANNOT_ACCORD("100123"),
  /*------------------------EXCEL导入导出模块----------------------*/
  /** code: 100200, msg: 文件不存在 */
  EXCEL_NO_FILE("100200"),
  /** code: 100201, msg:文件超大 */
  EXCEL_OVERSIZED("100201"),
  /*------------------------公司模块----------------------*/
  COMPANY_SAVE_FAIL("1130"),
  /*------------------------三方-短信模块----------------------*/
  SMS_RECORD_NOT_EXIST("100400"),
  SMS_TEMPLATE_ID_IS_NULL("100401"),
  SMS_TEMPLATE_IS_EXIST("100402"),
  SMS_TEMPLATE_IS_NULL("100403"),
  SMS_SEND_UPPER_LIMIT("100404"),
  /*------------------------H5-超售模块----------------------*/
  /** code: 100500, msg: 超售赔偿单赔偿金额不能为小于1！ */
  OVER_PAY_MONEY_IS_NULL("100500"),
  /** code: 100501, msg: 超售旅客id查询无数据！ */
  OVER_PAX_ID_QUERY_IS_NULL("100501"),
  /** code: 100502, msg: 超售赔偿金额前后端计算不一致，请核对！ */
  OVER_PAY_MONEY_ATYPISM("100502"),
  /** code: 100503, msg: 超售列表数据详情查询-改签航班时差计算，异常！ */
  OVER_TIME_DIFFERENCE_ERROR("100503"),
  /** code: 100504, msg: 超售改签航班时差，查无对应规则！请联系管理员配置！*/
  OVER_TIME_DIFFERENCE_RULE_IS_NULL("100504"),
  /** code: 100505, msg: 超售退票查无对应规则！请联系管理员配置！*/
  OVER_REFUND_RULE_IS_NULL("100505"),
  /** code: 100506, msg: 改签航班计划起飞时间不能为空*/
  OVER_PLANEDATE_IS_NULL("100506"),
  /** code: 100507, msg: 验证失败，非本航站出发航班，请核对后再输入*/
  OVER_VERIFICATION_ORG_ERROR("100507"),
  /** code: 100508, msg: 验证失败，只能由发起人发放*/
  OVER_ORDER_GRANT_ERROR("100508"),
  /** code: 100509, msg: 超售服务航站—查询无对应航站数据*/
  OVER_SERVICE_CITY_IS_NULL("100509"),

  /*------------------------文件操作---------------------------*/
  /** code: 100600 ,msg: 文件上传失败*/
  FILE_UPLOAD_ERROR("100600"),
  /** code: 100601 ,msg: 文件不存在*/
  FILE_NOT_EXISTS("100601"),
  /** code: 100602 ,msg: 文件下载失败*/
  FILE_DOENLOAD_ERROR("100602"),
  /** code: 100603 ,msg: 文件上传超过大小*/
  FILE_SIZE_ERROR("100603"),
  /** code: 100604 ,msg: 文件后缀不符合规范*/
  FILE_SUFFIX_ERROR("100604"),
  /*------------------------审核操作---------------------------*/
  /** code: 100700 ,msg: 用户再次发起-赔偿单审核处理异常*/
  LAUNCH_REPEAT_AUDIT_ERROR("100700"),
  LAUNCH_PROCESS_ERROR("100701"),
  LAUNCH_REPEAT_ERROR("100702"),
  TASK_ID_IS_NULL("100703"),
  AUDIT_HANDLEUSER_IS_NULL("100704"),
  AUDIT_PUSH_USER_IS_NULL("100705"),
  ORDER_INFO_IS_NULL("100706"),
  SERVICECITY_IS_NULL("100707"),
  /** code: 100708 ,msg: 审核任务不存在或已被处理！*/
  AUDIT_REPEAT_ERROR("100708"),
  /** code: 100709 ,msg: 审核人员选择错误，不能选择用户本人！*/
  AUDIT_USER_ERROR("100709"),
  /** code: 100710 ,msg: 审核人员解密错误！*/
  AUDIT_HANDLEUSER_DECODE_ERROR("100710"),
  /** code: 100711 ,msg: 审核消息发送失败*/
  AUDIT_MESSAGE_ERROR("100711"),
  /*------------------------三方支付---------------------------*/
  /** code: 100800 ,msg: 授权code不能为空！*/
  WX_AUTH_CODE_IS_NULL("100800"),
  /** code: 100801 ,msg: 授权state不能为空！*/
  WX_AUTH_STATE_IS_NULL("100801"),
  /** code: 100802 ,msg: 授权openid不能为空！*/
  WX_OPENID_IS_NULL("100802"),
  /*------------------------微信公众号领取----------------------*/
  AUTH_PAX_FAIL("100900"),
  PAX_RANDOMCODE_IS_SEND("100901"),
  PAX_RANDOMCODE_FAIL("100902"),
  PAX_RANDOMCODE_EXPIRE("100903"),
  PAX_RANDOMCODE_INPUT_FAIL("100904"),
  PAX_ACT_RESUBMIT("100905"),
  PAX_UNIONPAY_AUTH_FAIL("100906"),
  PAX_IMG_FAIL("100907"),
  PAX_NORMAL_SUBMIT("100908"),
  PAX_RECEIVE_LIMIT("100909"),
  PAYMENT_SUCCESS_RECORD_EXISTS_FOR_CLAIM("100910"),
  PAX_TELEPHONE_VALIDATE_FAIL("100911"),
  PAX_APPLY_TELEPHONE_VALIDATE_FAIL("100912"),
  /*------------------------旅客现金领取领取----------------------*/
  PAX_CASH_AUTH_FAIL("102000"),
  PAX_CASH_SUBMIT("102001"),
  /*------------------------WEB服务补偿----------------------*/
  PAX_PKG_WEB_OPERATE_LIMIT("101900"),
  COMPENSATION_DUPLICATE_SUBMISSION("101901"), //补偿单已经提交成功，请勿重复提交，请刷新后查看!
  TRACE_DATA_IS_EMPTY("101902"), //未从鲁雁管家(TRACE接口)获取到旅客数据，请联系管理员



  /*------------------------前端表格设置的异常信息枚举---------------------------*/
  //为获取到当前用户信息
  NOT_FIND_USER_INFO("101000"),

  /*------------------------WEB航延住宿----------------------*/
  HOTEL_SAVE_ERROR("103000"),
  HOTEL_SAVE_REDIS_LOCK_ERROR("103001"),
  TASK_NOT_EXIST("103002"),
  HOTEL_BUSINESS_NOT_NULL("103003"),
  HOTEL_RLOCK_ERROR("103004"),//赔偿单-获取分布式锁失败
  HOTEL_BUSINESS_KEY_NOT_NULL("103005"),
  HOTEL_ORDER_NOT_NULL("103006"),//订单不存在
  HOTEL_ORDER_AUDIT_COMPLETE("103007"),//该审批任务已被处理
  HOTEL_SETTLE_REVIEW_NOT_FIND("103021"),//结算审批单不存在
  HOTEL_SETTLE_NOT_FIND("103022"),//结算单不存在
  HOTEL_REVIEW_NOT_FIND("103023"),//审批单不存在
  HOTEL_SETTLE_STATUS_NOT_SUPPORT("103024"),//结算单当前状态不支持该操作
  HOTEL_ACCOMMODATION_SAVE_ERROR("103025"),//酒店住宿单保存失败
  HOTEL_BUSINESS_ORDER_SAVE_ERROR("103026"),//h5-保障单提交失败
  HOTEL_BUSINESS_ORDER_STATUS_NOT_SUPPORT("103027"),//h5-保障单状态不支持当前操作

  REPORT_EXPORT_FAIL("103008"),   //报表导出失败

  ORDER_NOT_EXIST("103009")    //保障单不存在

  ;

  private String code;


  private MessageCode(String code) {
    this.code = code;
  }
}
