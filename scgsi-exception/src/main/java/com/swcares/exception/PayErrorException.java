package com.swcares.exception;

/**
 *
 * ClassName：com.swcares.core.exception.PayErrorException <br>
 * Description：支付自定义异常类 <br>
 * Copyright © 2019 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 上午10:00:51 <br>
 * @version v1.0 <br>
 */
public class PayErrorException extends GenericException{
    
    private static final long serialVersionUID = -7929953166234294810L;

    public PayErrorException(String code) {
        super(code);
    }

    public PayErrorException(String code, String msg) {
        super(code, msg);
    }

    public PayErrorException(String code, String[] params) {
        super(code, params);
    }

    public PayErrorException(String code, String msg, String... params) {
        super(code, msg, params);
    }

    public PayErrorException(String code, String[] params, Object data) {
        super(code, params, data);
    }
}
