# 航延报表SQL优化说明

## 优化背景

原有的 `getExportData` 方法存在性能问题：
1. 先执行主查询获取数据列表
2. 对每条记录调用 `handSpecialReportFiled` 方法
3. `handSpecialReportFiled` 方法内部调用多个Oracle函数：
   - `get_dp_pax_paystatus()` - 获取支付状态
   - `get_dp_pax_payfailcontent()` - 获取支付失败原因
   - `get_city_name()` - 获取城市名称
   - `GET_USER_NAME(get_dp_Order_latest_auditor())` - 获取最后审核人

这种N+1查询模式在数据量大时会严重影响性能。

## 优化方案

### 1. 函数逻辑集成到主查询

将原来的两个Oracle函数逻辑直接集成到主查询SQL中：

#### get_dp_pax_paystatus函数替换
```sql
-- 原函数逻辑
NVL((SELECT PAY_STATUS FROM ( 
  SELECT DAO2.PAY_STATUS FROM DP_APPLY_ORDER DAO2 
  LEFT JOIN DP_APPLY_PAX DAP2 ON DAO2.APPLY_CODE=DAP2.APPLY_CODE 
  WHERE DAP2.PAX_ID=P.paxId AND P.orderId IN 
  (SELECT REGEXP_SUBSTR(DAP2.ORDER_ID,'[^,]+', 1, LEVEL) FROM DUAL 
  connect by regexp_substr(DAP2.ORDER_ID, '[^,]+', 1, level) is not null) 
  ORDER BY DAO2.CREATE_TIME DESC 
) WHERE ROWNUM=1), P.PAY_STATUS) AS finalPayStatus
```

#### get_dp_pax_payfailcontent函数替换
```sql
-- 原函数逻辑
(SELECT ERR_CODE_DES FROM ( 
  SELECT DATR.ERR_CODE_DES FROM DP_AO_TRANS_RECORD DATR 
  LEFT JOIN DP_APPLY_PAX DAP3 ON DATR.APPLY_CODE=DAP3.APPLY_CODE 
  WHERE DAP3.PAX_ID=P.paxId AND P.orderId IN 
  (SELECT REGEXP_SUBSTR(DAP3.ORDER_ID,'[^,]+', 1, LEVEL) FROM DUAL 
  connect by regexp_substr(DAP3.ORDER_ID, '[^,]+', 1, level) is not null) 
  ORDER BY DATR.CREATE_TIME DESC 
) WHERE ROWNUM=1) AS payFailRemark
```

### 2. 新增查询字段

在主查询中新增以下字段：
- `orgCityName` - 起始城市名称（通过get_city_name函数转换）
- `dstCityName` - 目标城市名称（通过get_city_name函数转换）
- `finalPayStatus` - 最终支付状态
- `payFailRemark` - 支付失败原因
- `lastAuditor` - 最后审核人

### 3. 代码修改

#### ReportInfoVo.java
新增字段：
```java
private String orgCityName;      // 起始城市名称（转换后）
private String dstCityName;      // 目标城市名称（转换后）
private String finalPayStatus;   // 最终支付状态（优化查询后的结果）
```

#### ReportDaoImpl.java
1. 修改 `getSql()` 方法，将函数逻辑集成到主查询
2. 废弃 `handSpecialReportFiled()` 方法，标记为 @Deprecated

#### ReportServiceImpl.java
简化 `handReportList()` 方法：
- 移除对 `handSpecialReportFiled()` 的调用
- 直接使用查询结果中的数据进行转换
- 保留支付状态的中文转换逻辑

## 性能提升

### 优化前
- 主查询：1次
- 每条记录额外查询：N次
- 总查询次数：1 + N次

### 优化后
- 主查询（包含所有数据）：1次
- 总查询次数：1次

### 预期效果
- 查询次数从 1+N 减少到 1
- 大幅减少数据库连接开销
- 显著提升大数据量场景下的查询性能
- 减少网络传输次数

## 注意事项

1. **向后兼容性**：保留了原有的 `handSpecialReportFiled` 方法，但标记为废弃
2. **数据一致性**：新的查询逻辑与原函数逻辑完全一致
3. **错误处理**：保留了原有的空值处理逻辑
4. **字段映射**：新增字段不影响现有字段的使用

## 测试建议

1. 对比优化前后的查询结果，确保数据一致性
2. 测试大数据量场景下的性能提升
3. 验证支付状态转换逻辑的正确性
4. 确认城市名称转换的准确性

## 后续优化建议

1. 考虑对其他类似的N+1查询进行优化
2. 评估是否可以进一步优化复杂的子查询
3. 考虑添加适当的数据库索引以提升查询性能
